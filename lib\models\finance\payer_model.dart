class PayerModel {
  final String id;
  final String name;
  final String phoneNumber;
  final DateTime createdAt;
  final String uid; // User ID who owns this payer

  PayerModel({
    required this.id,
    required this.name,
    required this.phoneNumber,
    required this.createdAt,
    this.uid = '', // Default empty string for backward compatibility
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phoneNumber': phoneNumber,
      'createdAt': createdAt.toIso8601String(),
      'uid': uid, // Include UID in JSON
    };
  }

  factory PayerModel.fromJson(Map<String, dynamic> json) {
    return PayerModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      phoneNumber: json['phoneNumber'] ?? '',
      createdAt: json['createdAt'] != null
          ? (json['createdAt'] is String
              ? DateTime.parse(json['createdAt'])
              : DateTime.fromMillisecondsSinceEpoch(json['createdAt']))
          : DateTime.now(),
      uid: json['uid'] ?? '', // Extract UID from JSON
    );
  }

  // Create a copy of this model with updated fields
  PayerModel copyWith({
    String? id,
    String? name,
    String? phoneNumber,
    DateTime? createdAt,
    String? uid,
  }) {
    return PayerModel(
      id: id ?? this.id,
      name: name ?? this.name,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      createdAt: createdAt ?? this.createdAt,
      uid: uid ?? this.uid,
    );
  }
}
