import 'package:flutter/material.dart';

class PaginationWidget extends StatelessWidget {
  final int currentPage;
  final int totalPages;
  final int itemsPerPage;
  final Function(int) onPageChanged;
  final Function(int)? onItemsPerPageChanged;
  final List<int> availableItemsPerPage;
  final int? totalItems;
  final bool? isLoading;

  const PaginationWidget({
    super.key,
    required this.currentPage,
    required this.totalPages,
    required this.itemsPerPage,
    required this.onPageChanged,
    this.onItemsPerPageChanged,
    this.availableItemsPerPage = const [10, 25, 50],
    this.totalItems,
    this.isLoading,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              if (onItemsPerPageChanged != null) ...[
                const Text('Items per page: '),
                DropdownButton<int>(
                  value: itemsPerPage,
                  items: availableItemsPerPage.map((int value) {
                    return DropdownMenuItem<int>(
                      value: value,
                      child: Text(value.toString()),
                    );
                  }).toList(),
                  onChanged: (int? newValue) {
                    if (newValue != null) {
                      onItemsPerPageChanged!(newValue);
                    }
                  },
                ),
                const SizedBox(width: 16),
              ],
              Text('Page $currentPage of $totalPages'),
            ],
          ),
          Row(
            children: [
              IconButton(
                icon: const Icon(Icons.chevron_left),
                onPressed: currentPage > 1
                    ? () => onPageChanged(currentPage - 1)
                    : null,
              ),
              // Show page numbers
              for (int i = 1; i <= totalPages; i++)
                if (i == 1 ||
                    i == totalPages ||
                    (i >= currentPage - 1 && i <= currentPage + 1))
                  TextButton(
                    onPressed: i != currentPage ? () => onPageChanged(i) : null,
                    child: Text(
                      i.toString(),
                      style: TextStyle(
                        fontWeight: i == currentPage
                            ? FontWeight.bold
                            : FontWeight.normal,
                        color: i == currentPage
                            ? Theme.of(context).primaryColor
                            : null,
                      ),
                    ),
                  )
                else if (i == currentPage - 2 || i == currentPage + 2)
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 8),
                    child: Text('...'),
                  ),
              IconButton(
                icon: const Icon(Icons.chevron_right),
                onPressed: currentPage < totalPages
                    ? () => onPageChanged(currentPage + 1)
                    : null,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
