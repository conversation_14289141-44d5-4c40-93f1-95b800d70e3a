import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/profit_loss_controller.dart';

/// Summary cards widget for Profit & Loss report
class ProfitLossSummaryWidget extends StatelessWidget {
  const ProfitLossSummaryWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ProfitLossController>();

    return Obx(() {
      if (!controller.hasReport) {
        return const SizedBox.shrink();
      }

      final report = controller.currentReport!;

      return Column(
        children: [
          // Main Financial Metrics Row
          Row(
            children: [
              // Total Revenue Card
              Expanded(
                child: _buildSummaryCard(
                  context,
                  title: 'Total Revenue',
                  amount: report.totalRevenue,
                  icon: Icons.trending_up,
                  color: Colors.green,
                  subtitle: '${report.revenueGroups.length} revenue categories',
                ),
              ),
              const SizedBox(width: 16),
              // Total Expenses Card
              Expanded(
                child: _buildSummaryCard(
                  context,
                  title: 'Total Expenses',
                  amount: report.totalExpenses,
                  icon: Icons.trending_down,
                  color: Colors.orange,
                  subtitle: '${report.expenseGroups.length} expense categories',
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Net Income and Margin Row
          Row(
            children: [
              // Net Income Card
              Expanded(
                child: _buildSummaryCard(
                  context,
                  title: 'Net Income',
                  amount: report.netIncome,
                  icon: report.isProfitable ? Icons.arrow_upward : Icons.arrow_downward,
                  color: report.isProfitable ? Colors.green : Colors.red,
                  subtitle: report.isProfitable ? 'Profitable' : 'Loss',
                ),
              ),
              const SizedBox(width: 16),
              // Net Margin Card
              Expanded(
                child: _buildPercentageCard(
                  context,
                  title: 'Net Margin',
                  percentage: report.netMargin,
                  icon: Icons.percent,
                  color: report.isProfitable ? Colors.green : Colors.red,
                  subtitle: 'Profit margin percentage',
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Additional Metrics Row
          Row(
            children: [
              // Gross Profit Card
              Expanded(
                child: _buildSummaryCard(
                  context,
                  title: 'Gross Profit',
                  amount: report.grossProfit,
                  icon: Icons.account_balance,
                  color: Colors.blue,
                  subtitle: 'Revenue before expenses',
                ),
              ),
              const SizedBox(width: 16),
              // Revenue vs Expenses Ratio
              Expanded(
                child: _buildRatioCard(
                  context,
                  title: 'Revenue/Expense Ratio',
                  ratio: report.totalExpenses > 0 ? report.totalRevenue / report.totalExpenses : 0,
                  icon: Icons.compare_arrows,
                  color: Colors.purple,
                  subtitle: 'Revenue to expense ratio',
                ),
              ),
            ],
          ),
        ],
      );
    });
  }

  /// Build summary card for monetary amounts
  Widget _buildSummaryCard(
    BuildContext context, {
    required String title,
    required double amount,
    required IconData icon,
    required Color color,
    required String subtitle,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              _formatCurrency(amount),
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build percentage card
  Widget _buildPercentageCard(
    BuildContext context, {
    required String title,
    required double percentage,
    required IconData icon,
    required Color color,
    required String subtitle,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              '${percentage.toStringAsFixed(1)}%',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build ratio card
  Widget _buildRatioCard(
    BuildContext context, {
    required String title,
    required double ratio,
    required IconData icon,
    required Color color,
    required String subtitle,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              '${ratio.toStringAsFixed(2)}:1',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Format currency amount
  String _formatCurrency(double amount) {
    if (amount.abs() >= 1000000) {
      return 'PKR ${(amount / 1000000).toStringAsFixed(2)}M';
    } else if (amount.abs() >= 1000) {
      return 'PKR ${(amount / 1000).toStringAsFixed(1)}K';
    } else {
      return 'PKR ${amount.toStringAsFixed(2)}';
    }
  }
}
