import 'package:flutter/material.dart';

import '../../app_constants/colors/app_colors.dart';

const lightTextThemeHeadingColor = AppColors.onSurface;
const lightTextThemeTextColor = AppColors.onTertiaryFixedVariant;

class LightColorScheme {
  static ColorScheme lightColorScheme =
      ColorScheme.fromSeed(seedColor: AppColors.primary).copyWith(
          brightness: Brightness.light,
          primary: AppColors.primary,
          onPrimary: AppColors.onPrimary,
          secondary: AppColors.secondary,
          onSecondary: AppColors.onSecondary,
          secondaryContainer: AppColors.secondaryContainer,
          onSecondaryContainer: AppColors.onSecondaryContainer,
          tertiary: AppColors.tertiary,
          onTertiary: AppColors.onTertiary,
          onTertiaryFixedVariant: AppColors.onTertiaryFixedVariant,
          surface: AppColors.surface,
          onSurface: AppColors.onSurface,
          onSurfaceVariant: AppColors.onSurfaceVariant,
          error: AppColors.error,
          onError: AppColors.onError,
          errorContainer: AppColors.errorContainer,
          onErrorContainer: AppColors.onErrorContainer,
          shadow: AppColors.shadow);
}
