import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/colors/app_colors.dart';
import 'package:logestics/core/utils/constants/constants.dart';
import 'package:logestics/core/utils/widgets/custom_text_button.dart';
import 'package:logestics/features/authentication/presentation/controllers/login_component_controller.dart';
import 'package:logestics/features/authentication/presentation/screens/login_screen/components/signin_bar.dart';

import '../../../../../../core/utils/app_constants/texts/app_strings.dart';
import '../../../../../../core/utils/widgets/app_error_container.dart';
import 'account_button.dart';
import 'input_field.dart';

class LoginComponent extends StatelessWidget {
  LoginComponent({super.key});
  final controller = Get.find<LoginComponentController>();
  @override
  Widget build(BuildContext context) {
    var screenWidth = MediaQuery.of(context).size.width;
    var screenHeight = MediaQuery.of(context).size.height;
    final textTheme = Theme.of(context).textTheme;
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Form(
          key: controller.loginFormStateKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 30,
              ),
              const SignInBar(),
              const SizedBox(
                height: 20,
              ),
              Row(
                children: [
                  const Text(
                    'Don\'t have an account?',
                    style: TextStyle(color: Colors.black87),
                  ),
                  SizedBox(
                    width: 10,
                  ),
                  InkWell(
                    onTap: controller.navigateToSignup,
                    child: const Text(
                      'Register here',
                      style: TextStyle(color: AppColors.primary),
                    ),
                  )
                ],
              ),
              const SizedBox(
                height: 20,
              ),
              const Text(
                '  Email',
                style: TextStyle(
                    color: Colors.black,
                    fontWeight: FontWeight.w400,
                    fontSize: 17),
              ),
              const SizedBox(
                height: 10,
              ),
              Obx(() => InputField(
                    onTap: () => controller.onFocusEmail(),
                    focus: controller.emailFocus.value,
                    onTapOutSide: () => controller.onTapOutside(context),
                    hint: "Enter your Email",
                    controller: controller.emailTextController,
                  )),
              SizedBox(
                height: 10,
              ),
              const Text(
                '  Password',
                style: TextStyle(
                    color: Colors.black,
                    fontWeight: FontWeight.w400,
                    fontSize: 17),
              ),
              const SizedBox(
                height: 10,
              ),
              Obx(
                () => InputField(
                  onTap: () => controller.onFocusPassword(),
                  focus: controller.passwordFocus.value,
                  hint: "Enter Your Password",
                  controller: controller.passwordTextController,
                  hideText: !controller.isShowPassword.value,
                  onTapOutSide: () => controller.onTapOutside(context),
                  onChanged: (p0) => controller.password.value = p0,
                  suffix: IconButton(
                    onPressed: controller.toggleShowPassword,
                    icon: controller.password.isNotEmpty
                        ? Icon(
                            controller.isShowPassword.value == false
                                ? Icons.visibility
                                : Icons.visibility_off,
                            color: AppColors.primary,
                            size: 18,
                          )
                        : SizedBox.shrink(),
                  ),
                ),
              ),
              SizedBox(
                height: 5,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: TextButton.icon(
                      onPressed: controller.toggleRememberMeMark,
                      icon: Obx(
                        () => Icon(
                          Icons.check_circle,
                          color: controller.isRememberMeMark.value
                              ? darkOrange
                              : AppColors.onTertiaryFixedVariant,
                          size: 20,
                        ),
                      ),
                      label: Obx(
                        () => Text(
                          AppStrings.rememberMe,
                          style: textTheme.labelSmall?.copyWith(
                              color: controller.isRememberMeMark.value
                                  ? darkOrange
                                  : AppColors.onTertiaryFixedVariant,
                              fontSize: 14),
                        ),
                      ),
                    ),
                  ),
                  Flexible(
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(screenWidth),
                      child: CustomTextButton.primary(
                        onPressed: controller.navigateToForgotPassword,
                        text: AppStrings.forgetPassword,
                        minimumSize:
                            Size(screenWidth * 0.2, screenHeight * 0.05),
                      ),
                    ),
                  )
                ],
              ),
              SizedBox(
                height: 20,
              ),
              Obx(() => controller.errorMessages['general'] == null
                  ? const SizedBox()
                  : controller.errorMessages['general']!.isEmpty
                      ? const SizedBox()
                      : AppErrorContainer(
                          errorMessage:
                              controller.errorMessages['general'] ?? '')),
              Obx(() => AccountButton(
                    text: "Login Account",
                    loading: controller.isLoading.value,
                    onTap:
                        controller.isLoading.value ? () {} : controller.login,
                  )),
            ],
          ),
        ),
      ),
    );
  }
}
