import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/widgets/custom_button.dart';
import 'package:logestics/core/utils/widgets/my_text_field.dart';
import 'package:logestics/features/locations/domain/usecases/zone_use_case/create_zone_use_case.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';

import '../controller/add_zone_controller.dart';

class AddZoneView extends StatefulWidget {
  const AddZoneView({super.key});

  @override
  State<AddZoneView> createState() => _AddZoneViewState();
}

class _AddZoneViewState extends State<AddZoneView> {
  late AddZoneController controller;

  @override
  void initState() {
    super.initState();
    controller = Get.put(
        AddZoneController(createZoneUseCase: CreateZoneUseCase(Get.find())));

    // Refresh zones when the view is opened
  }

  @override
  Widget build(BuildContext context) {
    notifier = Provider.of(context, listen: false);
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 600;

    return Form(
      key: controller.formKey,
      child: Scaffold(
        backgroundColor: notifier.getBgColor,
        body: Dialog(
          backgroundColor: notifier.getBgColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          child: Container(
            width: size.width * 0.9,
            height: size.height * 0.9,
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              spacing: 16,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        AppStrings.addNewRegion,
                        overflow: TextOverflow.ellipsis,
                        style: AppTextStyles.titleStyle
                            .copyWith(color: notifier.text),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: Icon(Icons.close, color: notifier.text),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
                Divider(thickness: 5),
                Expanded(
                  child: SingleChildScrollView(
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        return Wrap(
                          spacing: 16,
                          runSpacing: 16,
                          children: [
                            // Zone Dropdown

                            MyTextFormField(
                              titleText: AppStrings.zoneName,
                              labelText: AppStrings.zoneName,
                              hintText: AppStrings.zoneNameHint,
                              controller: controller.zoneNameController,
                              validator: controller.validateZoneName,
                            ),

                            // Buttons
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Obx(
                                  () => CustomButton.danger(
                                    isDisabled: controller.isLoading.value,
                                    onPressed: () => Get.back(),
                                    text: AppStrings.cancel,
                                    minimumSize:
                                        Size(isSmallScreen ? 100 : 130, 50),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Obx(
                                  () => CustomButton.primary(
                                    onPressed: () async =>
                                        controller.saveZone(),
                                    isLoading: controller.isLoading.value,
                                    text: AppStrings.save,
                                    minimumSize:
                                        Size(isSmallScreen ? 100 : 130, 50),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
