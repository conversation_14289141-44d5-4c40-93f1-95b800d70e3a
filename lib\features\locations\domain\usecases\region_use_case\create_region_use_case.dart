import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/features/locations/repositories/region_repository.dart';
import 'package:logestics/models/locations/region_model.dart';

class CreateRegionUseCase {
  final RegionRepository _regionRepository;

  CreateRegionUseCase(this._regionRepository);

  /// Executes the use case to create an Region.
  Future<Either<FailureObj, SuccessObj>> call({
    required RegionModel region,
  }) async {
    return await _regionRepository.createRegion(
      region: region,
    );
  }
}
