import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/bindings/app_bindings.dart';

import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/widgets/data_table_cell.dart';
import 'package:logestics/core/utils/widgets/pagination_widget.dart';
import 'package:logestics/core/utils/widgets/my_text_field.dart';
import 'package:logestics/features/locations/repositories/district_repository.dart';
import 'package:logestics/features/locations/domain/usecases/district_use_case/delete_district_use_case.dart';
import 'package:logestics/features/locations/domain/usecases/district_use_case/get_district_use_case.dart';
import 'package:logestics/firebase_service/locations/district_firebase_service.dart';

import 'package:logestics/main.dart';
import 'package:provider/provider.dart';

import '../controller/district_list_controller.dart';
import 'add_district_view.dart';

class DistrictList extends StatelessWidget {
  const DistrictList({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    DistrictListController districtListController;
    try {
      districtListController = Get.find<DistrictListController>();
    } catch (e) {
      log('Creating temporary controller instance');
      districtListController = DistrictListController(
        getDistrictsUseCase: GetDistrictsUseCase(
          DistrictRepositoryImpl(
            DistrictFirebaseService(),
          ),
        ),
        deleteDistrictUseCase: DeleteDistrictUseCase(
          DistrictRepositoryImpl(
            DistrictFirebaseService(),
          ),
        ),
      );
    }

    notifier = Provider.of(context, listen: true);

    return GetBuilder<DistrictListController>(
      init: districtListController,
      builder: (districtListController) => LayoutBuilder(
        builder: (context, constraints) {
          return StatefulBuilder(
            builder: (context, setState) {
              return Container(
                decoration: BoxDecoration(
                  color: notifier.getBgColor,
                  borderRadius: BorderRadius.circular(10),
                ),
                padding: const EdgeInsets.symmetric(vertical: 15),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 15),
                      child: Get.width < 650
                          ? Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                InkWell(
                                  onTap: () {
                                    Get.to(() => AddDistrictView(),
                                        binding: AppBindings());
                                  },
                                  child: Text(
                                    AppStrings.addNewDistrictButton,
                                    style: AppTextStyles.addNewInvoiceStyle,
                                  ),
                                ),
                                MyTextFormField(
                                  labelText: AppStrings.searchHint,
                                  hintText: AppStrings.searchHint,
                                  controller:
                                      districtListController.searchController,
                                ),
                              ],
                            )
                          : Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                InkWell(
                                  onTap: () {
                                    Get.to(() => AddDistrictView(),
                                        binding: AppBindings());
                                  },
                                  child: Text(
                                    AppStrings.addNewDistrictButton,
                                    style: AppTextStyles.addNewInvoiceStyle,
                                  ),
                                ),
                                SizedBox(
                                  width: 300,
                                  child: MyTextFormField(
                                    labelText: AppStrings.searchHint,
                                    hintText: AppStrings.searchHint,
                                    controller:
                                        districtListController.searchController,
                                  ),
                                ),
                              ],
                            ),
                    ),
                    Expanded(
                      child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: SizedBox(
                          width: constraints.maxWidth,
                          child: Obx(() {
                            if (districtListController.isLoading.value) {
                              return const Center(
                                child: CircularProgressIndicator(),
                              );
                            }

                            final paginatedDistricts =
                                districtListController.paginatedDistricts;

                            return Column(
                              children: [
                                Expanded(
                                  child: ListView(
                                    shrinkWrap: true,
                                    children: [
                                      Table(
                                        border: TableBorder(
                                          horizontalInside: BorderSide(
                                            color: notifier.getfillborder,
                                          ),
                                        ),
                                        children: [
                                          TableRow(
                                            decoration: BoxDecoration(
                                              color: notifier.getHoverColor,
                                            ),
                                            children: [
                                              DataTableHeaderCell(
                                                text: 'Sr No.',
                                                textColor: notifier.text,
                                              ),
                                              DataTableHeaderCell(
                                                text: AppStrings.districtName,
                                                textColor: notifier.text,
                                              ),
                                              DataTableHeaderCell(
                                                text: 'Region Code',
                                                textColor: notifier.text,
                                              ),
                                              DataTableHeaderCell(
                                                text: 'Zone Name',
                                                textColor: notifier.text,
                                              ),
                                              DataTableHeaderCell(
                                                text: 'Actions',
                                                textColor: notifier.text,
                                              ),
                                            ],
                                          ),
                                          for (var i = 0;
                                              i < paginatedDistricts.length;
                                              i++)
                                            TableRow(
                                              children: [
                                                DataTableCell(
                                                  text: ((districtListController
                                                                      .currentPage
                                                                      .value -
                                                                  1) *
                                                              districtListController
                                                                  .itemsPerPage
                                                                  .value +
                                                          i +
                                                          1)
                                                      .toString(),
                                                ),
                                                DataTableCell(
                                                  text: paginatedDistricts[i]
                                                      .districtName,
                                                ),
                                                DataTableCell(
                                                  text: paginatedDistricts[i]
                                                      .regionCode,
                                                ),
                                                DataTableCell(
                                                  text: paginatedDistricts[i]
                                                      .zoneName,
                                                ),
                                                DataTableActionsCell(
                                                  menuItems: [
                                                    DataTablePopupMenuItem(
                                                      text: 'Delete',
                                                      icon:
                                                          Icons.delete_outline,
                                                      isDanger: true,
                                                      onTap: () {
                                                        if (!districtListController
                                                            .isLoading.value) {
                                                          Get.dialog(
                                                            AlertDialog(
                                                              title: Text(
                                                                  'Delete District'),
                                                              content: Text(
                                                                  'Are you sure you want to delete this district?'),
                                                              actions: [
                                                                TextButton(
                                                                  onPressed:
                                                                      () => Get
                                                                          .back(),
                                                                  child: Text(
                                                                      'Cancel'),
                                                                ),
                                                                TextButton(
                                                                  onPressed:
                                                                      () async {
                                                                    Get.back();
                                                                    await districtListController
                                                                        .deleteDistrict(
                                                                      paginatedDistricts[
                                                                              i]
                                                                          .districtId,
                                                                    );
                                                                  },
                                                                  child: Text(
                                                                    'Delete',
                                                                    style: TextStyle(
                                                                        color: Colors
                                                                            .red),
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          );
                                                        }
                                                      },
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                                Obx(() => PaginationWidget(
                                      currentPage: districtListController
                                          .currentPage.value,
                                      totalPages:
                                          districtListController.totalPages,
                                      itemsPerPage: districtListController
                                          .itemsPerPage.value,
                                      onPageChanged: (page) =>
                                          districtListController
                                              .setCurrentPage(page),
                                      onItemsPerPageChanged: (count) =>
                                          districtListController
                                              .setItemsPerPage(count),
                                    )),
                              ],
                            );
                          }),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }
}

/*arguments: {
                                          'invoice': Invoice(
                                              consignorName: 'sdf',
                                              consignorPickUpAddress: 'sdf',
                                              conveyNoteNumber: 'sdfd',
                                              customerCNIC: '34534534534534',
                                          customerGstNumber: 'gsdf',
                                            deliveryMode: 'By Road',
                                            destinationAddress: 'ssdf',
                                            customerName: 'ali',
                                            distanceInKilometers: 324,
                                            invoiceNumber: 34,
                                            invoiceStatus: 'pending',
                                            numberOfBags: 343,
                                            orderDate: '12/12/1223',
                                            orderNumber: '123',
                                            productName: 'sona',
                                            DistrictNumber: 'shahid',
                                            shipmentDate: '12/12/1212',
                                             shipmentNumber: '234',
                                            tasNumber: '343',
                                            truckNumber: '343'
                                          ),
                                          // Pass currentInvoice here
                                        }*/
