class FuelCardUsageModel {
  final String id;
  final String fuelCardId;
  final String fuelCardNumber;
  final String companyName;
  final String voucherId;
  final String voucherNumber;
  final double litersUsed;
  final double rateAtTime;
  final double totalAmount;
  final DateTime usageDate;
  final String driverName;
  final String truckNumber;
  final String notes;
  final String uid; // User ID who owns this usage record

  FuelCardUsageModel({
    required this.id,
    required this.fuelCardId,
    required this.fuelCardNumber,
    required this.companyName,
    required this.voucherId,
    required this.voucherNumber,
    required this.litersUsed,
    required this.rateAtTime,
    required this.totalAmount,
    required this.usageDate,
    required this.driverName,
    required this.truckNumber,
    this.notes = '',
    this.uid = '',
  });

  // Create from Firestore map
  factory FuelCardUsageModel.fromMap(Map<String, dynamic> map) {
    return FuelCardUsageModel(
      id: map['id'] ?? '',
      fuelCardId: map['fuelCardId'] ?? '',
      fuelCardNumber: map['fuelCardNumber'] ?? '',
      companyName: map['companyName'] ?? '',
      voucherId: map['voucherId'] ?? '',
      voucherNumber: map['voucherNumber'] ?? '',
      litersUsed: (map['litersUsed'] ?? 0.0).toDouble(),
      rateAtTime: (map['rateAtTime'] ?? 0.0).toDouble(),
      totalAmount: (map['totalAmount'] ?? 0.0).toDouble(),
      usageDate: map['usageDate'] != null 
          ? (map['usageDate'] is DateTime 
              ? map['usageDate'] 
              : DateTime.parse(map['usageDate']))
          : DateTime.now(),
      driverName: map['driverName'] ?? '',
      truckNumber: map['truckNumber'] ?? '',
      notes: map['notes'] ?? '',
      uid: map['uid'] ?? '',
    );
  }

  // Convert to Firestore map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'fuelCardId': fuelCardId,
      'fuelCardNumber': fuelCardNumber,
      'companyName': companyName,
      'voucherId': voucherId,
      'voucherNumber': voucherNumber,
      'litersUsed': litersUsed,
      'rateAtTime': rateAtTime,
      'totalAmount': totalAmount,
      'usageDate': usageDate.toIso8601String(),
      'driverName': driverName,
      'truckNumber': truckNumber,
      'notes': notes,
      'uid': uid,
    };
  }

  // Create a copy with updated fields
  FuelCardUsageModel copyWith({
    String? id,
    String? fuelCardId,
    String? fuelCardNumber,
    String? companyName,
    String? voucherId,
    String? voucherNumber,
    double? litersUsed,
    double? rateAtTime,
    double? totalAmount,
    DateTime? usageDate,
    String? driverName,
    String? truckNumber,
    String? notes,
    String? uid,
  }) {
    return FuelCardUsageModel(
      id: id ?? this.id,
      fuelCardId: fuelCardId ?? this.fuelCardId,
      fuelCardNumber: fuelCardNumber ?? this.fuelCardNumber,
      companyName: companyName ?? this.companyName,
      voucherId: voucherId ?? this.voucherId,
      voucherNumber: voucherNumber ?? this.voucherNumber,
      litersUsed: litersUsed ?? this.litersUsed,
      rateAtTime: rateAtTime ?? this.rateAtTime,
      totalAmount: totalAmount ?? this.totalAmount,
      usageDate: usageDate ?? this.usageDate,
      driverName: driverName ?? this.driverName,
      truckNumber: truckNumber ?? this.truckNumber,
      notes: notes ?? this.notes,
      uid: uid ?? this.uid,
    );
  }

  @override
  String toString() {
    return 'FuelCardUsageModel{id: $id, fuelCardNumber: $fuelCardNumber, voucherNumber: $voucherNumber, litersUsed: $litersUsed, totalAmount: $totalAmount, usageDate: $usageDate}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FuelCardUsageModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
