// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for ios - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDGdOS77VmAMG4QiRD12PIJgRqUrwXNCEw',
    appId: '1:757522381298:web:ee09b1331e30943bec536b',
    messagingSenderId: '757522381298',
    projectId: 'logistics-a8edb',
    authDomain: 'logistics-a8edb.firebaseapp.com',
    storageBucket: 'logistics-a8edb.firebasestorage.app',
    measurementId: 'G-RW9T9RBL12',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBecukkcve4iIT3MqMhS6Ob31_Z4bkQ6pY',
    appId: '1:757522381298:android:8e76abe1ada384baec536b',
    messagingSenderId: '757522381298',
    projectId: 'logistics-a8edb',
    storageBucket: 'logistics-a8edb.firebasestorage.app',
  );
}
