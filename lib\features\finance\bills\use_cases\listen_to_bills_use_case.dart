import 'package:cloud_firestore/cloud_firestore.dart';
import '../repositories/bill_repository.dart';

class ListenToBillsUseCase {
  final BillRepository _billRepository;

  ListenToBillsUseCase(this._billRepository);

  /// Executes the use case to listen to real-time bill changes.
  Stream<List<DocumentChange>> call(String uid) {
    // Validate input
    if (uid.isEmpty) {
      throw ArgumentError('Company ID cannot be empty.');
    }

    // Call the repository
    return _billRepository.listenToBills(uid: uid);
  }
}
