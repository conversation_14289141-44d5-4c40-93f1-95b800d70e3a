import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Utility class for standardized snackbars across the application
class SnackbarUtils {
  // Private constructor to prevent instantiation
  SnackbarUtils._();

  // Constants for snackbar styling
  static const Duration _defaultDuration = Duration(seconds: 3);
  static const SnackPosition _defaultPosition = SnackPosition.BOTTOM;

  static const double _borderRadius = 8.0;
  static const EdgeInsets _padding =
      EdgeInsets.symmetric(horizontal: 16.0, vertical: 10.0);

  /// Shows a success snackbar with standardized styling
  static void showSuccess(String title, String message) {
    Get.snackbar(
      title,
      message,
      snackPosition: _defaultPosition,
      backgroundColor: Colors.green.withAlpha(20),
      colorText: Colors.green.shade700,
      duration: _defaultDuration,
      margin: const EdgeInsets.all(8),
      borderRadius: _borderRadius,
      padding: _padding,
      icon: const Icon(Icons.check_circle, color: Colors.green),
      overlayBlur: 0,
      forwardAnimationCurve: Curves.easeOutCirc,
      reverseAnimationCurve: Curves.easeInCirc,
      boxShadows: [
        BoxShadow(
          color: Colors.black.withAlpha(20),
          blurRadius: 4,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  /// Shows an error snackbar with standardized styling
  static void showError(String title, String message) {
    Get.snackbar(
      title,
      message,
      snackPosition: _defaultPosition,
      backgroundColor: Colors.red.withAlpha(20),
      colorText: Colors.red.shade700,
      duration: _defaultDuration,
      margin: const EdgeInsets.all(8),
      borderRadius: _borderRadius,
      padding: _padding,
      icon: const Icon(Icons.error_outline, color: Colors.red),
      overlayBlur: 0,
      forwardAnimationCurve: Curves.easeOutCirc,
      reverseAnimationCurve: Curves.easeInCirc,
      boxShadows: [
        BoxShadow(
          color: Colors.black.withAlpha(20),
          blurRadius: 4,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  /// Shows an info snackbar with standardized styling
  static void showInfo(String title, String message) {
    Get.snackbar(
      title,
      message,
      snackPosition: _defaultPosition,
      backgroundColor: Colors.blue.withAlpha(20),
      colorText: Colors.blue.shade700,
      duration: _defaultDuration,
      margin: const EdgeInsets.all(8),
      borderRadius: _borderRadius,
      padding: _padding,
      icon: const Icon(Icons.info_outline, color: Colors.blue),
      overlayBlur: 0,
      forwardAnimationCurve: Curves.easeOutCirc,
      reverseAnimationCurve: Curves.easeInCirc,
      boxShadows: [
        BoxShadow(
          color: Colors.black.withAlpha(20),
          blurRadius: 4,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  /// Shows a warning snackbar with standardized styling
  static void showWarning(String title, String message) {
    Get.snackbar(
      title,
      message,
      snackPosition: _defaultPosition,
      backgroundColor: Colors.amber.withAlpha(20),
      colorText: Colors.amber.shade800,
      duration: _defaultDuration,
      margin: const EdgeInsets.all(8),
      borderRadius: _borderRadius,
      padding: _padding,
      icon: const Icon(Icons.warning_amber_outlined, color: Colors.amber),
      overlayBlur: 0,
      forwardAnimationCurve: Curves.easeOutCirc,
      reverseAnimationCurve: Curves.easeInCirc,
      boxShadows: [
        BoxShadow(
          color: Colors.black.withAlpha(20),
          blurRadius: 4,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }
}
