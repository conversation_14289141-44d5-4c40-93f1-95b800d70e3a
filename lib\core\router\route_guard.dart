import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/router/app_routes.dart';
import 'package:logestics/firebase_service/firebase_auth_service.dart';

class RouteGuard {
  static getInitialRoute() {
    try {
      final authService = Get.find<FirebaseAuthService>();
      if (authService.currentUser != null) {
        return AppRoutes.home;
      } else {
        return AppRoutes.login;
      }
    } catch (e) {
      return AppRoutes.login;
    }
  }
}

class UnknownRoutePage extends StatelessWidget {
  const UnknownRoutePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              'Page Not Found',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                try {
                  final authService = Get.find<FirebaseAuthService>();
                  if (authService.currentUser != null) {
                    Get.offAllNamed(AppRoutes.home);
                  } else {
                    Get.offAllNamed(AppRoutes.login);
                  }
                } catch (e) {
                  Get.offAllNamed(AppRoutes.login);
                }
              },
              child: const Text('Go to Home'),
            ),
          ],
        ),
      ),
    );
  }
}
