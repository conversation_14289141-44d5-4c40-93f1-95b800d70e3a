import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:logestics/core/router/app_routes.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';

import '../../domain/useCases/login_use_case.dart';

bool? isRemembered;

class LoginComponentController extends GetxController {
  final LoginUseCase _loginUseCase;

  LoginComponentController({required LoginUseCase loginUseCase})
      : _loginUseCase = loginUseCase;

  // Form key - initialize in onInit
  late final GlobalKey<FormState> loginFormStateKey;

  // Track disposal state to prevent double disposal
  bool _isDisposed = false;

  @override
  void onInit() {
    super.onInit();
    loginFormStateKey = GlobalKey<FormState>();
  }

  @override
  void onClose() {
    if (!_isDisposed) {
      _isDisposed = true;
      emailTextController.dispose();
      passwordTextController.dispose();
    }
    super.onClose();
  }

  final emailTextController = TextEditingController();
  final passwordTextController = TextEditingController();
  final errorMessages = <String, String>{}.obs;
  var isShowPassword = false.obs;
  var isRememberMeMark = false.obs;
  var isLoading = false.obs;
  var password = ''.obs;
  var loginAttempts = 0.obs; // Track login attempts

  void navigateToSignup() {
    Get.toNamed(AppRoutes.signup);
  }

  void navigateToForgotPassword() {
    Get.toNamed(AppRoutes.forgotPassword);
  }

  void toggleShowPassword() {
    isShowPassword.value = !isShowPassword.value;
  }

  RxBool emailFocus = false.obs;
  RxBool passwordFocus = false.obs;
  void toggleRememberMeMark() {
    isRememberMeMark.value = !isRememberMeMark.value;
    isRemembered = isRememberMeMark.value;
  }

  void onTapOutside(BuildContext context) {
    emailFocus.value = false;
    passwordFocus.value = false;
    FocusScope.of(context).unfocus();
  }

  void onFocusEmail() {
    emailFocus.value = true;
    passwordFocus.value = false;
  }

  void onFocusPassword() {
    emailFocus.value = false;
    passwordFocus.value = true;
  }

  String? emailValidator(String? value) {
    // Check if the input is empty
    if (value == null || value.isEmpty) {
      return AppStrings.emailEmpty;
    }

    // Define a regular expression for validating email
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );

    // Check if the input matches the regex
    if (!emailRegex.hasMatch(value)) {
      return AppStrings.emailInvalid;
    }

    // If everything is valid, return null
    return null;
  }

  String? passwordValidator(String? value) {
    // Check if the input is empty
    if (value == null || value.isEmpty) {
      return AppStrings.passwordEmpty;
    }

    // If everything is valid, return null
    return null;
  }

  Future<void> login() async {
    if (emailValidator(emailTextController.text) != null) {
      SnackbarUtils.showError(
        AppStrings.errorS,
        'Invalid Email!',
      );
      return;
    }
    if (passwordValidator(passwordTextController.text) != null) {
      SnackbarUtils.showError(
        AppStrings.errorS,
        'Invalid Password!',
      );
      return;
    }

    isLoading.value = true;
    errorMessages.clear();
    final email = emailTextController.text.trim();
    final password = passwordTextController.text.trim();

    if (loginFormStateKey.currentState?.validate() ?? false) {
      // Perform login using the LoginUseCase
      final result =
          await _loginUseCase.execute(email, password, isRememberMeMark.value);

      result.fold(
        (failure) {
          if (failure.message.contains(AppStrings.invalidCredential)) {
            loginAttempts.value++;
          } // Increment login attempts on failure
          if (failure.field != null) {
            errorMessages[failure.field!] = failure.message;
          } else {
            if (loginAttempts.value > 2 &&
                failure.message.contains(AppStrings.invalidCredential)) {
              // Append the additional suggestion
              errorMessages['general'] =
                  "${failure.message}:\n${AppStrings.resetTip}";
            } else {
              errorMessages['general'] = failure.message;
            }
          }
          isLoading.value = false;
        },
        (userCredential) {
          errorMessages.clear(); // Clear all errors on success
          isLoading.value = false;

          // Show success message
          SnackbarUtils.showSuccess(
            'Login Successful',
            'Welcome back, ${userCredential.user?.email ?? 'User'}!',
          );

          // Clear form data
          emailTextController.clear();
          passwordTextController.clear();

          // Don't manually navigate - let FirebaseAuthService handle it
          // The auth state listener will automatically navigate to home
        },
      );
    } else {
      isLoading.value = false;
    }
  }

  // Future<void> dummyLogin() async {
  //   isLoading.value = true;
  //   errorMessages.clear();

  //   final result = await _loginUseCase.execute(
  //     '<EMAIL>',
  //     'Abc12345@',
  //     true,
  //   );

  //   result.fold(
  //     (failure) {
  //       if (failure.field != null) {
  //         errorMessages[failure.field!] = failure.message;
  //       } else {
  //         errorMessages['general'] = failure.message;
  //       }
  //       isLoading.value = false;
  //     },
  //     (user) {
  //       errorMessages.clear();
  //       isLoading.value = false;
  //     },
  //   );
  // }
}
