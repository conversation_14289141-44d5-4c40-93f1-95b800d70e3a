import 'dart:developer';
import 'package:dartz/dartz.dart';
import '../../../../core/shared_services/failure_obj.dart';
import '../../../../core/shared_services/success_obj.dart';
import '../../../../models/finance/cash_flow_statement_model.dart';
import '../../../../firebase_service/accounting/cash_flow_statement_firebase_service.dart';

/// Repository interface for Cash Flow Statement operations
abstract class CashFlowStatementRepository {
  Future<Either<FailureObj, CashFlowStatementReport>> generateCashFlowStatement({
    required DateTime startDate,
    required DateTime endDate,
    required String companyName,
    bool includeInactiveAccounts = false,
    bool includeZeroBalances = false,
    bool useDirectMethod = false,
  });

  Future<Either<FailureObj, List<CashFlowStatementReport>>> getCashFlowStatements({
    DateTime? startDate,
    DateTime? endDate,
    int limit = 50,
  });

  Future<Either<FailureObj, SuccessObj>> saveCashFlowStatement(
    CashFlowStatementReport report);
}

/// Implementation of Cash Flow Statement repository
class CashFlowStatementRepositoryImpl implements CashFlowStatementRepository {
  final CashFlowStatementFirebaseService _firebaseService;

  CashFlowStatementRepositoryImpl(this._firebaseService);

  @override
  Future<Either<FailureObj, CashFlowStatementReport>> generateCashFlowStatement({
    required DateTime startDate,
    required DateTime endDate,
    required String companyName,
    bool includeInactiveAccounts = false,
    bool includeZeroBalances = false,
    bool useDirectMethod = false,
  }) async {
    try {
      log('Repository: Generating Cash Flow Statement report');

      // Validate input parameters
      if (startDate.isAfter(endDate)) {
        return Left(FailureObj(
          code: 'invalid-date-range',
          message: 'Start date cannot be after end date',
        ));
      }

      if (endDate.isAfter(DateTime.now())) {
        return Left(FailureObj(
          code: 'invalid-end-date',
          message: 'End date cannot be in the future',
        ));
      }

      if (companyName.trim().isEmpty) {
        return Left(FailureObj(
          code: 'invalid-company-name',
          message: 'Company name cannot be empty',
        ));
      }

      // Call Firebase service
      final result = await _firebaseService.generateCashFlowStatement(
        startDate: startDate,
        endDate: endDate,
        companyName: companyName,
        includeInactiveAccounts: includeInactiveAccounts,
        includeZeroBalances: includeZeroBalances,
        useDirectMethod: useDirectMethod,
      );

      return result.fold(
        (failure) {
          log('Repository: Cash Flow Statement generation failed: ${failure.message}');
          return Left(failure);
        },
        (report) {
          log('Repository: Cash Flow Statement generated successfully');
          return Right(report);
        },
      );
    } catch (e) {
      log('Repository: Error generating Cash Flow Statement: $e');
      return Left(FailureObj(
        code: 'repository-error',
        message: 'Repository error: $e',
      ));
    }
  }

  @override
  Future<Either<FailureObj, List<CashFlowStatementReport>>> getCashFlowStatements({
    DateTime? startDate,
    DateTime? endDate,
    int limit = 50,
  }) async {
    try {
      log('Repository: Fetching Cash Flow Statement reports');

      // TODO: Implement fetching saved reports from Firebase
      // For now, return empty list
      return Right([]);
    } catch (e) {
      log('Repository: Error fetching Cash Flow Statements: $e');
      return Left(FailureObj(
        code: 'repository-error',
        message: 'Failed to fetch Cash Flow Statements: $e',
      ));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> saveCashFlowStatement(
    CashFlowStatementReport report) async {
    try {
      log('Repository: Saving Cash Flow Statement report');

      // TODO: Implement saving report to Firebase
      // For now, return success
      return Right(SuccessObj(message: 'Cash Flow Statement saved successfully'));
    } catch (e) {
      log('Repository: Error saving Cash Flow Statement: $e');
      return Left(FailureObj(
        code: 'repository-error',
        message: 'Failed to save Cash Flow Statement: $e',
      ));
    }
  }
}
