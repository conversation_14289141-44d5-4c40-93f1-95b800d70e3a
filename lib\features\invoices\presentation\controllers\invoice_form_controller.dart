import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/firebase_service/firebase_auth_service.dart';
import 'package:logestics/firebase_service/firebase_services.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/constants/constants.dart';
import 'package:logestics/core/utils/constants/custom_dialogs.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/features/company/presentation/conrollers/company_controller.dart';
import 'package:logestics/features/invoices/use_cases/create_invoice_use_case.dart';
import 'package:logestics/features/invoices/use_cases/update_invoice_use_case.dart';
import 'package:logestics/features/invoices/use_cases/get_highest_invoice_number_use_case.dart';
import 'package:logestics/features/invoices/presentation/controllers/invoice_list_controller.dart';
import 'package:logestics/features/locations/domain/usecases/district_use_case/get_district_use_case.dart';
import 'package:logestics/features/locations/domain/usecases/station_use_case/get_station_use_case.dart';
import 'package:logestics/main.dart';
import 'package:logestics/models/invoice_model.dart';
import 'package:logestics/models/locations/district_model.dart';
import 'package:logestics/models/locations/from_place_model.dart';
import 'package:logestics/models/locations/station_model.dart';

class InvoiceFormController extends GetxController {
  var editMode = false;
  final InvoiceModel? currentInvoice;
  InvoiceModel? _editingInvoice; // Store the invoice being edited
  final CreateInvoiceUseCase createInvoiceUseCase;
  final UpdateInvoiceUseCase updateInvoiceUseCase;
  final FirebaseServices firebaseServices;
  final GetDistrictsUseCase getDistrictsUseCase;
  final GetStationByDistrictIdUseCase getStationUseCase;
  final GetHighestInvoiceNumberUseCase getHighestInvoiceNumberUseCase;
  final companyController = Get.find<CompanyController>();
  final _authService = Get.find<FirebaseAuthService>();

  String get uid => _authService.currentUser?.uid ?? '';

  InvoiceFormController({
    required this.firebaseServices,
    required this.createInvoiceUseCase,
    required this.updateInvoiceUseCase,
    required this.getDistrictsUseCase,
    required this.getStationUseCase,
    required this.getHighestInvoiceNumberUseCase,
    this.currentInvoice,
  }) {
    editMode = currentInvoice != null;
    _editingInvoice = currentInvoice; // Initialize with current invoice
  }

  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  // TextEditingControllers for each field
  late TextEditingController invoiceNumberController;
  final invoiceNumber = RxInt(0);

  late TextEditingController tasNumberController;
  late TextEditingController productNameController;
  late TextEditingController numberOfBagsController;
  late TextEditingController weightPerBagController;
  late TextEditingController customerNameController;
  late TextEditingController truckNumberController;
  late TextEditingController conveyNoteNumberController;
  late TextEditingController biltyNumberController;
  late TextEditingController orderDateController;
  late TextEditingController belongsToDateController;
  late TextEditingController consignorNameController;
  late TextEditingController consignorPickUpAddressController;

  // Rx values for dropdowns and other fields
  var invoiceStatusSelected = AppStrings.invoiceStatusOptions.first.obs;
  var deliveryModeSelected = AppStrings.deliveryModeList.first.obs;
  var orderDate = Rx<DateTime>(DateTime.now());
  var belongsToDate = Rx<DateTime>(DateTime.now());

  // Location management
  final districts = <DistrictModel>[].obs;
  final stations = <StationModel>[].obs;
  final places = <FromPlaceModel>[].obs;
  final selectedDistrict = Rxn<DistrictModel>();
  final selectedStation = Rxn<StationModel>();
  final selectedPlace = Rxn<FromPlaceModel>();
  final distance = RxDouble(0.0);
  final isLoading = false.obs;

  // Add form state tracking
  bool _isFormInitialized = false;
  bool _isDisposed = false;

  @override
  void onInit() {
    super.onInit();

    log('🚀 Controller onInit called - editMode: $editMode, currentInvoice: ${currentInvoice?.invoiceNumber}');

    if (!_isFormInitialized && !_isDisposed) {
      _initializeForm();
      _isFormInitialized = true;
    }
  }

  void _initializeForm() {
    // Initialize controllers first
    invoiceNumberController = TextEditingController();
    tasNumberController = TextEditingController();
    productNameController = TextEditingController();
    numberOfBagsController = TextEditingController();
    weightPerBagController = TextEditingController();
    customerNameController = TextEditingController();
    truckNumberController = TextEditingController();
    conveyNoteNumberController = TextEditingController();
    biltyNumberController = TextEditingController();
    orderDateController = TextEditingController();
    belongsToDateController = TextEditingController();
    consignorNameController = TextEditingController();
    consignorPickUpAddressController = TextEditingController();

    // Set default values
    weightPerBagController.text = '50';
    orderDate.value = DateTime.now();
    orderDateController.text = formatDate(DateTime.now());
    belongsToDate.value = DateTime.now();
    belongsToDateController.text = formatDate(DateTime.now());
    deliveryModeSelected.value = 'By Road';
    invoiceStatusSelected.value = AppStrings.invoiceStatusOptions.first;

    // Initialize with current invoice data or fetch next invoice number
    if (currentInvoice != null) {
      _editingInvoice = currentInvoice;
      editMode = true;
      initializeForEdit(currentInvoice!);
    } else if (_editingInvoice != null) {
      editMode = true;
      initializeForEdit(_editingInvoice!);
    } else {
      fetchNextInvoiceNumber();
    }

    // Fetch districts for location data
    fetchDistricts();

    // Add listener to convey note controller to trigger UI updates
    conveyNoteNumberController.addListener(() {
      update(); // Trigger UI rebuild when convey note changes
    });
  }

  Future<void> fetchNextInvoiceNumber() async {
    if (_isDisposed) return;

    log('📊 fetchNextInvoiceNumber called - editMode: $editMode');

    // Don't fetch new invoice number in edit mode
    if (editMode) {
      log('📊 Skipping fetchNextInvoiceNumber because in edit mode');
      return;
    }

    try {
      final result = await getHighestInvoiceNumberUseCase.call(uid);
      result.fold(
        (failure) =>
            log('Failed to fetch highest invoice number: ${failure.message}'),
        (highestNumber) {
          if (!_isDisposed && !editMode) {
            log('📊 Setting new invoice number: ${highestNumber + 1}');
            invoiceNumber.value = highestNumber + 1;
            invoiceNumberController.text = (highestNumber + 1).toString();
          } else {
            log('📊 Not setting invoice number - disposed: $_isDisposed, editMode: $editMode');
          }
        },
      );
    } catch (e) {
      log('Error fetching highest invoice number: $e');
    }
  }

  void initializeForEdit(InvoiceModel invoice) {
    if (_isDisposed) return;

    log('🔄 initializeForEdit called with invoice number: ${invoice.invoiceNumber}');
    log('🔄 Current invoiceNumber.value: ${invoiceNumber.value}');
    log('🔄 Current editMode: $editMode');

    // Store the original invoice number - this should never change in edit mode
    invoiceNumber.value = invoice.invoiceNumber;
    invoiceNumberController.text = invoice.invoiceNumber.toString();

    log('🔄 Set invoiceNumber.value to: ${invoiceNumber.value}');
    log('🔄 Set invoiceNumberController.text to: ${invoiceNumberController.text}');

    // Set all form fields with invoice data
    invoiceStatusSelected.value = invoice.invoiceStatus;
    tasNumberController.text = invoice.tasNumber;
    productNameController.text = invoice.productName;
    numberOfBagsController.text = invoice.numberOfBags.toString();
    weightPerBagController.text = invoice.weightPerBag.toString();
    customerNameController.text = invoice.customerName;
    truckNumberController.text = invoice.truckNumber;
    conveyNoteNumberController.text = invoice.conveyNoteNumber;
    biltyNumberController.text = invoice.biltyNumber;
    orderDate.value = invoice.orderDate ?? DateTime.now();
    orderDateController.text = formatDate(invoice.orderDate ?? DateTime.now());
    consignorNameController.text = invoice.consignorName;
    consignorPickUpAddressController.text = invoice.consignorPickUpAddress;
    deliveryModeSelected.value = invoice.deliveryMode;

    // Set edit mode to true and ensure _editingInvoice is set
    editMode = true;
    _editingInvoice = invoice;

    log('🔄 Final editMode: $editMode');
    log('🔄 Final _editingInvoice invoice number: ${_editingInvoice?.invoiceNumber}');

    // Initialize location data
    setLocation(invoice);
  }

  // Add this method to set current invoice after controller creation
  void setCurrentInvoice(InvoiceModel invoice) {
    if (_isDisposed) return;

    log('🔧 setCurrentInvoice called with invoice number: ${invoice.invoiceNumber}');
    log('🔧 Current editMode before: $editMode');
    log('🔧 Current invoiceNumber.value before: ${invoiceNumber.value}');

    editMode = true;
    _editingInvoice = invoice; // Store the invoice being edited

    log('🔧 Set editMode to: $editMode');
    log('🔧 Set _editingInvoice with invoice number: ${_editingInvoice?.invoiceNumber}');

    // Re-initialize the form with the invoice data
    initializeForEdit(invoice);
  }

  // Method to set location data for existing invoice
  Future<void> setLocation(InvoiceModel invoice) async {
    try {
      // Ensure districts are loaded first
      await fetchDistricts();

      // Find and select the matching district
      final matchingDistrict = districts.firstWhereOrNull(
        (d) => d.districtId == invoice.districtId,
      );

      if (matchingDistrict != null) {
        selectedDistrict.value = matchingDistrict;

        // Fetch stations for the district
        await fetchStations(matchingDistrict.districtId);

        // Find and select the matching station
        final matchingStation = stations.firstWhereOrNull(
          (s) => s.stationId == invoice.stationId,
        );

        if (matchingStation != null) {
          selectedStation.value = matchingStation;
          places.value = matchingStation.places;

          // Find and select the matching place
          final matchingPlace = places.firstWhereOrNull(
            (p) => p.placeId == invoice.fromPlaceId,
          );

          if (matchingPlace != null) {
            selectedPlace.value = matchingPlace;
            distance.value = matchingPlace.kilometers;
          }
        }
      }
    } catch (e) {
      log('Error setting location data: $e');
    }
  }

  set setStatus(String value) => invoiceStatusSelected.value = value;
  set setDeliveryMode(String value) => deliveryModeSelected.value = value;

  Future<void> selectOrderDate(BuildContext context) async {
    var date = await showDatePicker(
      context: context,
      initialDate: orderDate.value,
      firstDate: DateTime(DateTime.now().year - 1),
      lastDate: DateTime(2101),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            datePickerTheme: DatePickerThemeData(
              backgroundColor: notifier.getBgColor,
            ),
            colorScheme: ColorScheme.light(
              onSurface: notifier.text,
              surface: notifier.getBgColor,
              primary: const Color(0xFF2B79F3),
            ),
          ),
          child: child!,
        );
      },
    );
    if (date != null) {
      orderDate.value = date;
      orderDateController.text = formatDate(date);
    }
  }

  Future<void> fetchDistricts() async {
    isLoading.value = true;
    try {
      final result = await getDistrictsUseCase.call();
      result.fold(
        (failure) => log('Failed to fetch districts: ${failure.message}'),
        (districtList) => districts.value = districtList,
      );
    } catch (e) {
      log('Error fetching districts: $e');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> fetchStations(String districtId) async {
    try {
      final result = await getStationUseCase.call(districtId: districtId);
      result.fold(
        (failure) => log('Failed to fetch stations: ${failure.message}'),
        (stationList) => stations.value = stationList,
      );
    } catch (e) {
      log('Error fetching stations: $e');
    }
  }

  void onDistrictSelected(DistrictModel district) {
    selectedDistrict.value = district;
    selectedStation.value = null;
    selectedPlace.value = null;
    distance.value = 0.0;
    fetchStations(district.districtId);
  }

  void onStationSelected(StationModel station) {
    selectedStation.value = station;
    selectedPlace.value = null;
    distance.value = 0.0;
    places.value = station.places;
  }

  void onPlaceSelected(FromPlaceModel place) {
    selectedPlace.value = place;
    distance.value = place.kilometers;
  }

  InvoiceModel? getInvoiceFromInput() {
    if (_isDisposed) return null;

    log('💾 getInvoiceFromInput called');
    log('💾 editMode: $editMode');
    log('💾 invoiceNumber.value: ${invoiceNumber.value}');
    log('💾 _editingInvoice?.invoiceNumber: ${_editingInvoice?.invoiceNumber}');

    if (!formKey.currentState!.validate()) {
      showErrorSnackbar('Please fill all required fields correctly');
      return null;
    }

    if (selectedDistrict.value == null ||
        selectedStation.value == null ||
        selectedPlace.value == null) {
      showErrorSnackbar('Please select all location related details');
      return null;
    }

    // Determine which invoice number to use
    final int finalInvoiceNumber;
    if (editMode && _editingInvoice != null) {
      finalInvoiceNumber = _editingInvoice!.invoiceNumber;
      log('💾 Using original invoice number from _editingInvoice: $finalInvoiceNumber');
    } else {
      finalInvoiceNumber = invoiceNumber.value;
      log('💾 Using new invoice number: $finalInvoiceNumber');
    }

    log('💾 Final invoice number to use: $finalInvoiceNumber');

    return InvoiceModel(
      invoiceNumber: finalInvoiceNumber,
      invoiceStatus: invoiceStatusSelected.value,
      tasNumber: tasNumberController.text,
      productName: productNameController.text,
      numberOfBags: int.tryParse(numberOfBagsController.text) ?? 0,
      weightPerBag: double.tryParse(weightPerBagController.text) ?? 50.0,
      customerName: customerNameController.text,
      truckNumber: truckNumberController.text,
      conveyNoteNumber: conveyNoteNumberController.text,
      biltyNumber: biltyNumberController.text,
      orderDate: orderDate.value,
      consignorName: consignorNameController.text,
      deliveryMode: deliveryModeSelected.value,
      districtId: selectedDistrict.value!.districtId,
      districtName: selectedDistrict.value!.districtName,
      stationId: selectedStation.value!.stationId,
      stationName: selectedStation.value!.stationName,
      fromPlaceId: selectedPlace.value!.placeId,
      fromPlaceName: selectedPlace.value!.fromPlace,
      distanceInKilometers: distance.value,
      consignorPickUpAddress: consignorPickUpAddressController.text,
    );
  }

  Future<void> saveInvoice() async {
    var invoice = getInvoiceFromInput();
    if (invoice != null) {
      editMode
          ? await updateInvoice(invoice: invoice)
          : await createInvoice(invoice: invoice);
    }
  }

  /// Create an invoice and handle success or error responses.

  /// Updates an invoice in Firestore.
  Future<void> updateInvoice({
    required InvoiceModel invoice,
  }) async {
    log('🔄 updateInvoice called with invoice number: ${invoice.invoiceNumber}');
    log('🔄 Original invoice number in _editingInvoice: ${_editingInvoice?.invoiceNumber}');

    // Show loading dialog
    Get.dialog(
      const Center(child: CircularProgressIndicator()),
      barrierDismissible: false,
    );

    try {
      log('🔄 Calling updateInvoiceUseCase with invoice: ${invoice.tasNumber}, invoice number: ${invoice.invoiceNumber}');

      // Call the use case
      final result = await updateInvoiceUseCase.call(
        invoice: invoice,
        uid: uid,
      );

      // Close loading dialog
      Get.back();

      // Handle result
      result.fold(
        (failure) {
          log('🔄 Update failed: ${failure.message}');
          showErrorDialog(failure);
        },
        (success) {
          log('🔄 Update successful: ${success.message}');
          _showSuccessSnackbar(success);
        },
      );
    } catch (e) {
      log('🔄 Update error: $e');
      // Close loading dialog
      Get.back();

      // Show unexpected error dialog
      Get.defaultDialog(
        title: "Unexpected Error",
        middleText: "Something went wrong. Please try again later.",
        textConfirm: "OK",
        confirmTextColor: Colors.white,
        onConfirm: () => Get.back(),
      );
    }
  }

  Future<void> createInvoice({
    required InvoiceModel invoice,
  }) async {
    // Show loading dialog
    Get.dialog(
      const Center(child: CircularProgressIndicator()),
      barrierDismissible: false,
    );
    log('saveInvoice $invoice');

    try {
      // Call the use case
      final result = await createInvoiceUseCase.call(
        invoice: invoice,
        uid: uid,
      );

      // Close loading dialog
      Get.back();

      // Handle result
      result.fold(
        (failure) => showErrorDialog(failure),
        (success) {
          // Manually add the invoice to the company's list for immediate display
          log('InvoiceFormController: Manually adding invoice ${invoice.tasNumber}');
          companyController.company.addInvoice(invoice);
          _showSuccessSnackbar(success);
        },
      );
    } catch (e) {
      // Close loading dialog
      Get.back();

      // Show unexpected error
      Get.defaultDialog(
        title: "Unexpected Error",
        middleText: "Something went wrong. Please try again later.",
        textConfirm: "OK",
        confirmTextColor: Colors.white,
        onConfirm: () => Get.back(),
      );
    }
  }

  /// Show an error popup with details.

  void showErrorSnackbar(String errorMessage) {
    if (errorMessage.isNotEmpty) {
      (failure) => SnackbarUtils.showError(
            "Error", // Title of the snackbar
            errorMessage, // Error message content
          );
    }
  }

  /// Show a success snackbar.
  void _showSuccessSnackbar(SuccessObj success) {
    if (_isDisposed) return;

    // Close any active dialogs
    if (Get.isDialogOpen == true) {
      Get.back();
    }

    // Show success dialog
    Get.dialog(
      AlertDialog(
        title: const Text('Success'),
        content: Text(success.message),
        actions: [
          TextButton(
            onPressed: () {
              // Close the dialog
              Get.back();
              // Reset form for new invoice creation
              resetControllers();
              // Force refresh both company controller and invoice list controller
              _refreshInvoiceData();
              // Navigate back 2 steps
              Get.back();
              Get.back();
            },
            child: const Text('OK'),
          ),
        ],
      ),
      barrierDismissible: false,
    );
  }

  void _refreshInvoiceData() {
    // Immediately refresh the invoice list controller
    try {
      final invoiceListController = Get.find<InvoiceListController>();
      invoiceListController
          .searchInvoices(invoiceListController.searchQuery.value);
    } catch (e) {
      // Invoice list controller might not be initialized, ignore
    }

    // Add a small delay to ensure Firestore has processed the change, then refresh again
    Future.delayed(const Duration(milliseconds: 500), () {
      try {
        // Force update the company controller
        companyController.forceRefresh();

        // Force refresh the invoice list controller again
        final invoiceListController = Get.find<InvoiceListController>();
        invoiceListController
            .searchInvoices(invoiceListController.searchQuery.value);
      } catch (e) {
        // Controllers might not be initialized, ignore
      }
    });
  }

  String? validateWeightPerBag(String? p1) {
    if (p1 == null || p1.isEmpty) {
      return 'Weight per bag is required.';
    }
    if (double.tryParse(p1) == null || double.parse(p1) <= 0) {
      return 'Weight per bag must be a positive number.';
    }
    return null;
  }

  @override
  void onClose() {
    _isDisposed = true;
    resetControllers();
    disposeControllers();
    super.onClose();
  }

  void disposeControllers() {
    invoiceNumberController.dispose();
    tasNumberController.dispose();
    productNameController.dispose();
    numberOfBagsController.dispose();
    weightPerBagController.dispose();
    customerNameController.dispose();
    truckNumberController.dispose();
    conveyNoteNumberController.dispose();
    biltyNumberController.dispose();
    orderDateController.dispose();
    consignorNameController.dispose();
    consignorPickUpAddressController.dispose();
  }

  void resetControllers() {
    if (_isDisposed) return;

    try {
      invoiceNumberController.clear();
      tasNumberController.clear();
      productNameController.clear();
      numberOfBagsController.clear();
      weightPerBagController.clear();
      customerNameController.clear();
      truckNumberController.clear();
      conveyNoteNumberController.clear();
      biltyNumberController.clear();
      orderDateController.clear();
      consignorNameController.clear();
      consignorPickUpAddressController.clear();

      invoiceStatusSelected.value = 'In Progress';
      deliveryModeSelected.value = 'By Road';
      orderDate.value = DateTime.now();
      orderDateController.text = formatDate(DateTime.now());
      selectedDistrict.value = null;
      selectedStation.value = null;
      selectedPlace.value = null;
      distance.value = 0.0;
      places.value = [];
      invoiceNumber.value = 0;
      editMode = false;
      _editingInvoice = null; // Clear the editing invoice

      // Get next invoice number for fresh forms
      fetchNextInvoiceNumber();
    } catch (e) {
      log('Error resetting controllers: $e');
    }
  }

  void fillDummyDetails() {
    // Fill basic invoice info
    tasNumberController.text = '124578';

    // Fill product details
    productNameController.text = 'Rice Bags';
    numberOfBagsController.text = '100';
    weightPerBagController.text = '50';

    // Fill customer & transport details
    customerNameController.text = 'ABC Trading Company';
    truckNumberController.text = 'ABC1234';
    conveyNoteNumberController.text = '789456';
    biltyNumberController.text = '987654';
    orderDate.value = DateTime.now();
    orderDateController.text = formatDate(DateTime.now());

    // Fill consignor details
    consignorNameController.text = 'XYZ Suppliers Ltd';

    // Set dropdown values
    invoiceStatusSelected.value = 'In Progress';
    deliveryModeSelected.value = 'By Road';

    // Try to select first options for location if available
    if (districts.isNotEmpty) {
      onDistrictSelected(districts.first);

      // Wait briefly for stations to load
      Future.delayed(const Duration(milliseconds: 300), () {
        if (stations.isNotEmpty) {
          onStationSelected(stations.first);

          // Wait briefly for places to load
          Future.delayed(const Duration(milliseconds: 300), () {
            if (places.isNotEmpty) {
              onPlaceSelected(places.first);
            }
          });
        }
      });
    }
  }
}
