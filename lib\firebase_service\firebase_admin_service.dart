// import 'dart:async';
// import 'dart:developer';
// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:firebase_auth/firebase_auth.dart';
// import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';

// /// This service is for development purposes only
// /// It provides admin-level operations on Firebase
// class FirebaseAdminService {
//   final FirebaseFirestore _firestore = FirebaseFirestore.instance;
//   final FirebaseAuth _auth = FirebaseAuth.instance;

//   // Get current user UID or return a default value if not authenticated
//   String get _uid => _auth.currentUser?.uid ?? 'anonymous';

//   // List of collections to delete (add more as needed)
//   List<String> get _collections => [
//         AppCollection.accountsCollection,
//         AppCollection.depositsCollection,
//         AppCollection.expenseCategoriesCollection,
//         AppCollection.depositCategoriesCollection,
//         AppCollection.payeesCollection,
//         AppCollection.fuelCardsCollection,
//         AppCollection.fuelRatesCollection,
//         AppCollection.loansCollection,
//         AppCollection.vouchersCollection,
//         AppCollection.transactionsCollection,
//         AppCollection.payersCollection,
//         AppCollection.accountTypesCollection,
//         AppCollection.paymentMethodsCollection,
//         AppCollection.invoicesCollection,
//         AppCollection.withdrawalsCollection,
//         AppCollection.districtsCollection,
//         AppCollection.zonesCollection,
//         AppCollection.regionsCollection,
//         AppCollection.stationsCollection,
//         AppCollection.regionDestinationsCollection,
//         AppCollection.usersCollection,

//         // Add any additional collections from AppCollection here as needed
//       ];

//   /// Delete all collections for the current user
//   /// WARNING: This is destructive and should only be used in development
//   Future<void> deleteAllUserData() async {
//     try {
//       if (_uid == 'anonymous') {
//         throw Exception('User not authenticated');
//       }

//       log('Starting deletion of all collections for user: $_uid');

//       // Delete each collection's documents for the current user
//       for (final collection in _collections) {
//         log('Deleting documents in collection: $collection');

//         // Get all documents for the current user
//         final snapshot = await _firestore
//             .collection(collection)
//             // .where('uid', isEqualTo: _uid)
//             .get();

//         log('Found ${snapshot.docs.length} documents to delete in $collection');

//         // Delete in batches of 500 (Firestore limit)
//         final batches = <WriteBatch>[];
//         var currentBatch = _firestore.batch();
//         var operationCount = 0;

//         for (final doc in snapshot.docs) {
//           currentBatch.delete(doc.reference);
//           operationCount++;

//           // Create a new batch every 500 operations
//           if (operationCount >= 500) {
//             batches.add(currentBatch);
//             currentBatch = _firestore.batch();
//             operationCount = 0;
//           }
//         }

//         // Add the last batch if it has operations
//         if (operationCount > 0) {
//           batches.add(currentBatch);
//         }

//         // Commit all batches
//         for (final batch in batches) {
//           await batch.commit();
//         }

//         log('Successfully deleted documents in collection: $collection');
//       }

//       log('Successfully deleted all user data');
//     } catch (e) {
//       log('Error deleting user data: $e');
//       rethrow;
//     }
//   }
// }
