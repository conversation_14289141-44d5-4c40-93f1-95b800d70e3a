import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/constants/custom_dialogs.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/core/utils/mixins/pagination_mixin.dart';
import 'package:logestics/models/locations/district_model.dart';
import 'package:logestics/features/locations/domain/usecases/district_use_case/get_district_use_case.dart';
import 'package:logestics/features/locations/domain/usecases/district_use_case/delete_district_use_case.dart';

class DistrictListController extends GetxController with PaginationMixin {
  final GetDistrictsUseCase getDistrictsUseCase;
  final DeleteDistrictUseCase deleteDistrictUseCase;

  DistrictListController({
    required this.getDistrictsUseCase,
    required this.deleteDistrictUseCase,
  });

  var districts = <DistrictModel>[].obs;
  var filteredDistricts = <DistrictModel>[].obs;
  var isLoading = false.obs;
  final searchQuery = ''.obs;

  var searchController = TextEditingController();

  @override
  void onInit() {
    super.onInit();
    searchController.addListener(_onSearchChanged);
    fetchDistricts();
  }

  void _onSearchChanged() {
    searchQuery.value = searchController.text;
    _filterDistricts();
  }

  void _filterDistricts() {
    if (searchQuery.value.isEmpty) {
      filteredDistricts.value = districts;
    } else {
      final query = searchQuery.value.toLowerCase();
      filteredDistricts.value = districts.where((district) {
        return district.districtName.toLowerCase().contains(query) ||
            district.regionName.toLowerCase().contains(query) ||
            district.zoneName.toLowerCase().contains(query);
      }).toList();
    }
    setTotalItems(filteredDistricts.length);
  }

  Future<void> fetchDistricts() async {
    isLoading.value = true;
    try {
      log('Fetching Districts...');
      final result = await getDistrictsUseCase.call();
      result.fold(
        (failure) {
          log('Failed to fetch Districts: ${failure.message}');
          showErrorDialog(failure);
        },
        (districtList) {
          log('Successfully fetched ${districtList.length} Districts');
          districts.value = districtList;
          _filterDistricts();
        },
      );
    } catch (e) {
      log('Unexpected error occurred while fetching Districts: $e');
      showUnexpectedErrorDialog();
    } finally {
      isLoading.value = false;
    }
  }

  List<DistrictModel> get paginatedDistricts => paginateList(filteredDistricts);

  Future<void> deleteDistrict(String districtId) async {
    try {
      log('Deleting District: $districtId');
      // Remove district from local list first
      districts.removeWhere((district) => district.districtId == districtId);
      setTotalItems(districts.length);

      final result = await deleteDistrictUseCase.call(districtId: districtId);
      result.fold(
        (failure) {
          log('Failed to delete District: ${failure.message}');
          showErrorDialog(failure);
        },
        (success) {
          log('Successfully deleted District');
          SnackbarUtils.showSuccess(
            AppStrings.success,
            'District deleted successfully',
          );
        },
      );
    } catch (e) {
      log('Unexpected error occurred while deleting District: $e');
      showUnexpectedErrorDialog();
    } finally {
      isLoading.value = false;
    }
  }

  void addDistrictToList(DistrictModel district) {
    districts.insert(0, district);
    setTotalItems(districts.length);
  }

  void updateDistrictInList(DistrictModel district) {
    final index =
        districts.indexWhere((d) => d.districtId == district.districtId);
    if (index != -1) {
      districts[index] = district;
      setTotalItems(districts.length);
    }
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }
}
