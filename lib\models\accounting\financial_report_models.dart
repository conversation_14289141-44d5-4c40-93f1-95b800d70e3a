import 'package:cloud_firestore/cloud_firestore.dart';

/// Base class for all financial reports
abstract class FinancialReport {
  final String reportId;
  final String reportType;
  final String companyName;
  final String uid;
  final DateTime startDate;
  final DateTime endDate;
  final DateTime generatedAt;
  final String generatedBy;

  FinancialReport({
    required this.reportId,
    required this.reportType,
    required this.companyName,
    required this.uid,
    required this.startDate,
    required this.endDate,
    required this.generatedAt,
    required this.generatedBy,
  });

  Map<String, dynamic> toJson();
  String get reportTitle;
  String get reportPeriod => '${_formatDate(startDate)} to ${_formatDate(endDate)}';
  
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// Trial Balance Report Model
class TrialBalanceReport extends FinancialReport {
  final List<TrialBalanceAccount> accounts;
  final double totalDebits;
  final double totalCredits;
  final bool isBalanced;

  TrialBalanceReport({
    required super.reportId,
    required super.companyName,
    required super.uid,
    required super.startDate,
    required super.endDate,
    required super.generatedAt,
    required super.generatedBy,
    required this.accounts,
    required this.totalDebits,
    required this.totalCredits,
  }) : isBalanced = (totalDebits - totalCredits).abs() < 0.01,
       super(reportType: 'trial_balance');

  @override
  String get reportTitle => 'Trial Balance';

  @override
  Map<String, dynamic> toJson() {
    return {
      'reportId': reportId,
      'reportType': reportType,
      'companyName': companyName,
      'uid': uid,
      'startDate': Timestamp.fromDate(startDate),
      'endDate': Timestamp.fromDate(endDate),
      'generatedAt': Timestamp.fromDate(generatedAt),
      'generatedBy': generatedBy,
      'accounts': accounts.map((a) => a.toJson()).toList(),
      'totalDebits': totalDebits,
      'totalCredits': totalCredits,
      'isBalanced': isBalanced,
    };
  }

  factory TrialBalanceReport.fromJson(Map<String, dynamic> json) {
    return TrialBalanceReport(
      reportId: json['reportId'] ?? '',
      companyName: json['companyName'] ?? '',
      uid: json['uid'] ?? '',
      startDate: (json['startDate'] as Timestamp).toDate(),
      endDate: (json['endDate'] as Timestamp).toDate(),
      generatedAt: (json['generatedAt'] as Timestamp).toDate(),
      generatedBy: json['generatedBy'] ?? '',
      accounts: (json['accounts'] as List<dynamic>?)
          ?.map((a) => TrialBalanceAccount.fromJson(a))
          .toList() ?? [],
      totalDebits: (json['totalDebits'] ?? 0.0).toDouble(),
      totalCredits: (json['totalCredits'] ?? 0.0).toDouble(),
    );
  }
}

/// Trial Balance Account Entry
class TrialBalanceAccount {
  final String accountId;
  final String accountNumber;
  final String accountName;
  final String accountType;
  final double debitBalance;
  final double creditBalance;

  TrialBalanceAccount({
    required this.accountId,
    required this.accountNumber,
    required this.accountName,
    required this.accountType,
    required this.debitBalance,
    required this.creditBalance,
  });

  double get balance => debitBalance - creditBalance;
  bool get hasDebitBalance => debitBalance > creditBalance;
  bool get hasCreditBalance => creditBalance > debitBalance;

  Map<String, dynamic> toJson() {
    return {
      'accountId': accountId,
      'accountNumber': accountNumber,
      'accountName': accountName,
      'accountType': accountType,
      'debitBalance': debitBalance,
      'creditBalance': creditBalance,
    };
  }

  factory TrialBalanceAccount.fromJson(Map<String, dynamic> json) {
    return TrialBalanceAccount(
      accountId: json['accountId'] ?? '',
      accountNumber: json['accountNumber'] ?? '',
      accountName: json['accountName'] ?? '',
      accountType: json['accountType'] ?? '',
      debitBalance: (json['debitBalance'] ?? 0.0).toDouble(),
      creditBalance: (json['creditBalance'] ?? 0.0).toDouble(),
    );
  }
}

/// Profit & Loss Report Model
class ProfitLossReport extends FinancialReport {
  final List<PLAccountGroup> revenueGroups;
  final List<PLAccountGroup> expenseGroups;
  final double totalRevenue;
  final double totalExpenses;
  final double netIncome;
  final double grossProfit;

  ProfitLossReport({
    required super.reportId,
    required super.companyName,
    required super.uid,
    required super.startDate,
    required super.endDate,
    required super.generatedAt,
    required super.generatedBy,
    required this.revenueGroups,
    required this.expenseGroups,
    required this.totalRevenue,
    required this.totalExpenses,
    required this.grossProfit,
  }) : netIncome = totalRevenue - totalExpenses,
       super(reportType: 'profit_loss');

  @override
  String get reportTitle => 'Profit & Loss Statement';

  bool get isProfitable => netIncome > 0;
  double get netMargin => totalRevenue > 0 ? (netIncome / totalRevenue) * 100 : 0.0;

  @override
  Map<String, dynamic> toJson() {
    return {
      'reportId': reportId,
      'reportType': reportType,
      'companyName': companyName,
      'uid': uid,
      'startDate': Timestamp.fromDate(startDate),
      'endDate': Timestamp.fromDate(endDate),
      'generatedAt': Timestamp.fromDate(generatedAt),
      'generatedBy': generatedBy,
      'revenueGroups': revenueGroups.map((g) => g.toJson()).toList(),
      'expenseGroups': expenseGroups.map((g) => g.toJson()).toList(),
      'totalRevenue': totalRevenue,
      'totalExpenses': totalExpenses,
      'netIncome': netIncome,
      'grossProfit': grossProfit,
    };
  }

  factory ProfitLossReport.fromJson(Map<String, dynamic> json) {
    return ProfitLossReport(
      reportId: json['reportId'] ?? '',
      companyName: json['companyName'] ?? '',
      uid: json['uid'] ?? '',
      startDate: (json['startDate'] as Timestamp).toDate(),
      endDate: (json['endDate'] as Timestamp).toDate(),
      generatedAt: (json['generatedAt'] as Timestamp).toDate(),
      generatedBy: json['generatedBy'] ?? '',
      revenueGroups: (json['revenueGroups'] as List<dynamic>?)
          ?.map((g) => PLAccountGroup.fromJson(g))
          .toList() ?? [],
      expenseGroups: (json['expenseGroups'] as List<dynamic>?)
          ?.map((g) => PLAccountGroup.fromJson(g))
          .toList() ?? [],
      totalRevenue: (json['totalRevenue'] ?? 0.0).toDouble(),
      totalExpenses: (json['totalExpenses'] ?? 0.0).toDouble(),
      grossProfit: (json['grossProfit'] ?? 0.0).toDouble(),
    );
  }
}

/// P&L Account Group (Revenue or Expense categories)
class PLAccountGroup {
  final String groupName;
  final String groupType; // 'revenue' or 'expense'
  final List<PLAccountItem> accounts;
  final double groupTotal;

  PLAccountGroup({
    required this.groupName,
    required this.groupType,
    required this.accounts,
    required this.groupTotal,
  });

  Map<String, dynamic> toJson() {
    return {
      'groupName': groupName,
      'groupType': groupType,
      'accounts': accounts.map((a) => a.toJson()).toList(),
      'groupTotal': groupTotal,
    };
  }

  factory PLAccountGroup.fromJson(Map<String, dynamic> json) {
    return PLAccountGroup(
      groupName: json['groupName'] ?? '',
      groupType: json['groupType'] ?? '',
      accounts: (json['accounts'] as List<dynamic>?)
          ?.map((a) => PLAccountItem.fromJson(a))
          .toList() ?? [],
      groupTotal: (json['groupTotal'] ?? 0.0).toDouble(),
    );
  }
}

/// P&L Account Item
class PLAccountItem {
  final String accountId;
  final String accountNumber;
  final String accountName;
  final double amount;

  PLAccountItem({
    required this.accountId,
    required this.accountNumber,
    required this.accountName,
    required this.amount,
  });

  Map<String, dynamic> toJson() {
    return {
      'accountId': accountId,
      'accountNumber': accountNumber,
      'accountName': accountName,
      'amount': amount,
    };
  }

  factory PLAccountItem.fromJson(Map<String, dynamic> json) {
    return PLAccountItem(
      accountId: json['accountId'] ?? '',
      accountNumber: json['accountNumber'] ?? '',
      accountName: json['accountName'] ?? '',
      amount: (json['amount'] ?? 0.0).toDouble(),
    );
  }
}

/// Balance Sheet Report Model
class BalanceSheetReport extends FinancialReport {
  final List<BSAccountGroup> assetGroups;
  final List<BSAccountGroup> liabilityGroups;
  final List<BSAccountGroup> equityGroups;
  final double totalAssets;
  final double totalLiabilities;
  final double totalEquity;
  final bool isBalanced;

  BalanceSheetReport({
    required super.reportId,
    required super.companyName,
    required super.uid,
    required super.startDate,
    required super.endDate,
    required super.generatedAt,
    required super.generatedBy,
    required this.assetGroups,
    required this.liabilityGroups,
    required this.equityGroups,
    required this.totalAssets,
    required this.totalLiabilities,
    required this.totalEquity,
  }) : isBalanced = (totalAssets - (totalLiabilities + totalEquity)).abs() < 0.01,
       super(reportType: 'balance_sheet');

  @override
  String get reportTitle => 'Balance Sheet';

  double get totalLiabilitiesAndEquity => totalLiabilities + totalEquity;

  @override
  Map<String, dynamic> toJson() {
    return {
      'reportId': reportId,
      'reportType': reportType,
      'companyName': companyName,
      'uid': uid,
      'startDate': Timestamp.fromDate(startDate),
      'endDate': Timestamp.fromDate(endDate),
      'generatedAt': Timestamp.fromDate(generatedAt),
      'generatedBy': generatedBy,
      'assetGroups': assetGroups.map((g) => g.toJson()).toList(),
      'liabilityGroups': liabilityGroups.map((g) => g.toJson()).toList(),
      'equityGroups': equityGroups.map((g) => g.toJson()).toList(),
      'totalAssets': totalAssets,
      'totalLiabilities': totalLiabilities,
      'totalEquity': totalEquity,
      'isBalanced': isBalanced,
    };
  }

  factory BalanceSheetReport.fromJson(Map<String, dynamic> json) {
    return BalanceSheetReport(
      reportId: json['reportId'] ?? '',
      companyName: json['companyName'] ?? '',
      uid: json['uid'] ?? '',
      startDate: (json['startDate'] as Timestamp).toDate(),
      endDate: (json['endDate'] as Timestamp).toDate(),
      generatedAt: (json['generatedAt'] as Timestamp).toDate(),
      generatedBy: json['generatedBy'] ?? '',
      assetGroups: (json['assetGroups'] as List<dynamic>?)
          ?.map((g) => BSAccountGroup.fromJson(g))
          .toList() ?? [],
      liabilityGroups: (json['liabilityGroups'] as List<dynamic>?)
          ?.map((g) => BSAccountGroup.fromJson(g))
          .toList() ?? [],
      equityGroups: (json['equityGroups'] as List<dynamic>?)
          ?.map((g) => BSAccountGroup.fromJson(g))
          .toList() ?? [],
      totalAssets: (json['totalAssets'] ?? 0.0).toDouble(),
      totalLiabilities: (json['totalLiabilities'] ?? 0.0).toDouble(),
      totalEquity: (json['totalEquity'] ?? 0.0).toDouble(),
    );
  }
}

/// Balance Sheet Account Group
class BSAccountGroup {
  final String groupName;
  final String groupType; // 'current_assets', 'non_current_assets', 'current_liabilities', etc.
  final List<BSAccountItem> accounts;
  final double groupTotal;

  BSAccountGroup({
    required this.groupName,
    required this.groupType,
    required this.accounts,
    required this.groupTotal,
  });

  Map<String, dynamic> toJson() {
    return {
      'groupName': groupName,
      'groupType': groupType,
      'accounts': accounts.map((a) => a.toJson()).toList(),
      'groupTotal': groupTotal,
    };
  }

  factory BSAccountGroup.fromJson(Map<String, dynamic> json) {
    return BSAccountGroup(
      groupName: json['groupName'] ?? '',
      groupType: json['groupType'] ?? '',
      accounts: (json['accounts'] as List<dynamic>?)
          ?.map((a) => BSAccountItem.fromJson(a))
          .toList() ?? [],
      groupTotal: (json['groupTotal'] ?? 0.0).toDouble(),
    );
  }
}

/// Balance Sheet Account Item
class BSAccountItem {
  final String accountId;
  final String accountNumber;
  final String accountName;
  final double amount;

  BSAccountItem({
    required this.accountId,
    required this.accountNumber,
    required this.accountName,
    required this.amount,
  });

  Map<String, dynamic> toJson() {
    return {
      'accountId': accountId,
      'accountNumber': accountNumber,
      'accountName': accountName,
      'amount': amount,
    };
  }

  factory BSAccountItem.fromJson(Map<String, dynamic> json) {
    return BSAccountItem(
      accountId: json['accountId'] ?? '',
      accountNumber: json['accountNumber'] ?? '',
      accountName: json['accountName'] ?? '',
      amount: (json['amount'] ?? 0.0).toDouble(),
    );
  }
}

/// Financial Report Parameters for generating reports
class FinancialReportParameters {
  final DateTime startDate;
  final DateTime endDate;
  final String reportType;
  final List<String>? accountIds;
  final bool includeInactiveAccounts;
  final bool includeZeroBalances;
  final String sortBy; // 'account_number', 'account_name', 'balance'
  final String sortOrder; // 'asc', 'desc'

  FinancialReportParameters({
    required this.startDate,
    required this.endDate,
    required this.reportType,
    this.accountIds,
    this.includeInactiveAccounts = false,
    this.includeZeroBalances = false,
    this.sortBy = 'account_number',
    this.sortOrder = 'asc',
  });

  Map<String, dynamic> toJson() {
    return {
      'startDate': Timestamp.fromDate(startDate),
      'endDate': Timestamp.fromDate(endDate),
      'reportType': reportType,
      'accountIds': accountIds,
      'includeInactiveAccounts': includeInactiveAccounts,
      'includeZeroBalances': includeZeroBalances,
      'sortBy': sortBy,
      'sortOrder': sortOrder,
    };
  }

  factory FinancialReportParameters.fromJson(Map<String, dynamic> json) {
    return FinancialReportParameters(
      startDate: (json['startDate'] as Timestamp).toDate(),
      endDate: (json['endDate'] as Timestamp).toDate(),
      reportType: json['reportType'] ?? '',
      accountIds: (json['accountIds'] as List<dynamic>?)?.cast<String>(),
      includeInactiveAccounts: json['includeInactiveAccounts'] ?? false,
      includeZeroBalances: json['includeZeroBalances'] ?? false,
      sortBy: json['sortBy'] ?? 'account_number',
      sortOrder: json['sortOrder'] ?? 'asc',
    );
  }
}

/// Financial Dashboard Summary Data
class FinancialDashboardData {
  final double totalRevenue;
  final double totalExpenses;
  final double netIncome;
  final double totalAssets;
  final double totalLiabilities;
  final double totalEquity;
  final double cashBalance;
  final int totalTransactions;
  final DateTime lastUpdated;

  FinancialDashboardData({
    required this.totalRevenue,
    required this.totalExpenses,
    required this.netIncome,
    required this.totalAssets,
    required this.totalLiabilities,
    required this.totalEquity,
    required this.cashBalance,
    required this.totalTransactions,
    required this.lastUpdated,
  });

  double get profitMargin => totalRevenue > 0 ? (netIncome / totalRevenue) * 100 : 0.0;
  double get debtToEquityRatio => totalEquity > 0 ? totalLiabilities / totalEquity : 0.0;
  bool get isProfitable => netIncome > 0;
  bool get isBalanced => (totalAssets - (totalLiabilities + totalEquity)).abs() < 0.01;

  Map<String, dynamic> toJson() {
    return {
      'totalRevenue': totalRevenue,
      'totalExpenses': totalExpenses,
      'netIncome': netIncome,
      'totalAssets': totalAssets,
      'totalLiabilities': totalLiabilities,
      'totalEquity': totalEquity,
      'cashBalance': cashBalance,
      'totalTransactions': totalTransactions,
      'lastUpdated': Timestamp.fromDate(lastUpdated),
    };
  }

  factory FinancialDashboardData.fromJson(Map<String, dynamic> json) {
    return FinancialDashboardData(
      totalRevenue: (json['totalRevenue'] ?? 0.0).toDouble(),
      totalExpenses: (json['totalExpenses'] ?? 0.0).toDouble(),
      netIncome: (json['netIncome'] ?? 0.0).toDouble(),
      totalAssets: (json['totalAssets'] ?? 0.0).toDouble(),
      totalLiabilities: (json['totalLiabilities'] ?? 0.0).toDouble(),
      totalEquity: (json['totalEquity'] ?? 0.0).toDouble(),
      cashBalance: (json['cashBalance'] ?? 0.0).toDouble(),
      totalTransactions: json['totalTransactions'] ?? 0,
      lastUpdated: (json['lastUpdated'] as Timestamp).toDate(),
    );
  }
}
