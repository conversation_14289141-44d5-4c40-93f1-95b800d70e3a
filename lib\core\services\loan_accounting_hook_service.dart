import 'dart:developer';
import '../../models/finance/loan_model.dart';
import '../../firebase_service/accounting/general_ledger_firebase_service.dart';
import '../../firebase_service/accounting/chart_of_accounts_firebase_service.dart';
import '../../firebase_service/accounting/journal_entry_firebase_service.dart';
import 'automatic_journal_entry_service.dart';
import 'transaction_account_mapping_service.dart';
import 'loan_journal_integration_service.dart';

/// Service that hooks into loan creation and updates to automatically generate journal entries
class LoanAccountingHookService {
  static LoanAccountingHookService? _instance;
  late final LoanJournalIntegrationService _integrationService;
  late final AutomaticJournalEntryService _automaticJournalService;
  late final GeneralLedgerFirebaseService _generalLedgerService;
  late final TransactionAccountMappingService _mappingService;
  late final ChartOfAccountsFirebaseService _chartOfAccountsService;
  late final JournalEntryFirebaseService _journalEntryService;

  LoanAccountingHookService._internal() {
    _initializeServices();
  }

  factory LoanAccountingHookService() {
    _instance ??= LoanAccountingHookService._internal();
    return _instance!;
  }

  void _initializeServices() {
    _chartOfAccountsService = ChartOfAccountsFirebaseService();
    _journalEntryService = JournalEntryFirebaseService();
    _generalLedgerService = GeneralLedgerFirebaseService();
    _mappingService = TransactionAccountMappingService(_chartOfAccountsService);
    _automaticJournalService = AutomaticJournalEntryService(
      _journalEntryService,
      _chartOfAccountsService,
    );
    _integrationService = LoanJournalIntegrationService(
      _automaticJournalService,
      _generalLedgerService,
      _mappingService,
    );
  }

  /// Hook method to be called after loan approval (disbursement)
  Future<void> onLoanApproved(LoanModel loan) async {
    try {
      log('Loan accounting hook triggered for approval: ${loan.id}');

      // Validate loan for journal entry generation
      final validation =
          await _integrationService.validateLoanForJournalEntry(loan, loan.uid);
      if (!validation.isValid) {
        log('Loan validation failed: ${validation.issuesText}');
        return;
      }

      // Check if journal entries already exist for disbursement
      final hasExisting = await _integrationService.hasExistingJournalEntries(
        loan.id,
        loan.uid,
      );

      if (hasExisting) {
        log('Journal entries already exist for loan: ${loan.id}');
        return;
      }

      // Process the loan disbursement
      final success =
          await _integrationService.processLoanDisbursement(loan, loan.uid);
      if (success) {
        log('Successfully created journal entries for loan disbursement: ${loan.id}');
      } else {
        log('Failed to create journal entries for loan disbursement: ${loan.id}');
      }
    } catch (e) {
      log('Error in loan approval accounting hook: $e');
    }
  }

  /// Hook method to be called after loan repayment
  Future<void> onLoanRepaid(LoanModel loan) async {
    try {
      log('Loan accounting hook triggered for repayment: ${loan.id}');

      // Validate loan for journal entry generation
      final validation =
          await _integrationService.validateLoanForJournalEntry(loan, loan.uid);
      if (!validation.isValid) {
        log('Loan validation failed: ${validation.issuesText}');
        return;
      }

      // Process the loan repayment
      final success =
          await _integrationService.processLoanRepayment(loan, loan.uid);
      if (success) {
        log('Successfully created journal entries for loan repayment: ${loan.id}');
      } else {
        log('Failed to create journal entries for loan repayment: ${loan.id}');
      }
    } catch (e) {
      log('Error in loan repayment accounting hook: $e');
    }
  }

  /// Hook method to be called when loan is rejected or cancelled
  Future<void> onLoanCancelled(LoanModel loan) async {
    try {
      log('Loan cancellation hook triggered for: ${loan.id}');

      // Reverse any existing journal entries
      final success = await _integrationService.reverseLoanJournalEntries(
        loan.id,
        loan.uid,
      );

      if (success) {
        log('Successfully reversed journal entries for cancelled loan: ${loan.id}');
      } else {
        log('Failed to reverse journal entries for cancelled loan: ${loan.id}');
      }
    } catch (e) {
      log('Error in loan cancellation hook: $e');
    }
  }

  /// Hook method to be called when loan status changes
  Future<void> onLoanStatusChanged(LoanModel oldLoan, LoanModel newLoan) async {
    try {
      log('Loan status change hook triggered: ${oldLoan.status} -> ${newLoan.status}');

      // Handle status transitions
      if (oldLoan.status != 'approved' && newLoan.status == 'approved') {
        // Loan was just approved - create disbursement entries
        await onLoanApproved(newLoan);
      } else if (oldLoan.status != 'repaid' && newLoan.status == 'repaid') {
        // Loan was just repaid - create repayment entries
        await onLoanRepaid(newLoan);
      } else if ((oldLoan.status == 'approved' || oldLoan.status == 'repaid') &&
          (newLoan.status == 'rejected' || newLoan.status == 'pending')) {
        // Loan was cancelled or reverted - reverse entries
        await onLoanCancelled(oldLoan);
      }
    } catch (e) {
      log('Error in loan status change hook: $e');
    }
  }

  /// Batch process existing loans to create journal entries
  Future<BatchProcessResult> processExistingLoans(
    List<LoanModel> loans,
    String transactionType, // 'disbursement' or 'repayment'
    String uid,
  ) async {
    try {
      log('Processing ${loans.length} existing loans for journal entries');
      return await _integrationService.batchProcessLoanTransactions(
          loans, transactionType, uid);
    } catch (e) {
      log('Error processing existing loans: $e');
      return BatchProcessResult(
        totalProcessed: loans.length,
        successCount: 0,
        failureCount: loans.length,
        failedTransactionIds: loans.map((l) => l.id).toList(),
      );
    }
  }

  /// Get journal entries for a specific loan
  Future<List<dynamic>> getLoanJournalEntries(String loanId, String uid) async {
    return await _integrationService.getJournalEntriesForLoan(loanId, uid);
  }

  /// Get financial summary for a loan
  Future<LoanFinancialSummary> getLoanFinancialSummary(LoanModel loan) async {
    return await _integrationService.getLoanFinancialSummary(loan, loan.uid);
  }

  /// Check if accounting integration is properly configured
  Future<bool> isAccountingConfigured(String uid) async {
    try {
      final mapping = await _mappingService.getLoanAccountMapping(uid);
      return mapping != null;
    } catch (e) {
      log('Error checking accounting configuration: $e');
      return false;
    }
  }

  /// Get configuration status for UI display
  Future<AccountingConfigurationStatus> getConfigurationStatus(
      String uid) async {
    try {
      final mapping = await _mappingService.getLoanAccountMapping(uid);

      if (mapping == null) {
        return AccountingConfigurationStatus(
          isConfigured: false,
          missingAccounts: [
            'Cash Account',
            'Loans Receivable Account',
            'Loans Payable Account'
          ],
          message:
              'Required accounts for loan journal entries are not configured',
        );
      }

      return AccountingConfigurationStatus(
        isConfigured: true,
        missingAccounts: [],
        message: 'Accounting integration is properly configured',
      );
    } catch (e) {
      return AccountingConfigurationStatus(
        isConfigured: false,
        missingAccounts: [],
        message: 'Error checking configuration: $e',
      );
    }
  }

  /// Process loan based on current status
  Future<void> processLoanByStatus(LoanModel loan) async {
    switch (loan.status) {
      case 'approved':
        await onLoanApproved(loan);
        break;
      case 'repaid':
        await onLoanRepaid(loan);
        break;
      case 'rejected':
        await onLoanCancelled(loan);
        break;
      default:
        log('No accounting action needed for loan status: ${loan.status}');
    }
  }

  /// Check if loan has disbursement journal entries
  Future<bool> hasDisbursementEntries(String loanId, String uid) async {
    final entries =
        await _integrationService.getJournalEntriesForLoan(loanId, uid);
    return entries
        .any((entry) => entry.sourceTransactionType == 'loan_disbursement');
  }

  /// Check if loan has repayment journal entries
  Future<bool> hasRepaymentEntries(String loanId, String uid) async {
    final entries =
        await _integrationService.getJournalEntriesForLoan(loanId, uid);
    return entries
        .any((entry) => entry.sourceTransactionType == 'loan_repayment');
  }
}

/// Configuration status for accounting integration
class AccountingConfigurationStatus {
  final bool isConfigured;
  final List<String> missingAccounts;
  final String message;

  AccountingConfigurationStatus({
    required this.isConfigured,
    required this.missingAccounts,
    required this.message,
  });
}
