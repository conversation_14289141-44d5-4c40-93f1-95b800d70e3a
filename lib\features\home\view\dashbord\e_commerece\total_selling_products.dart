import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';

import '../../../Controllers/dashbord_controllers/e_commerece_controllers/ecomcontroller.dart';
import '../../../../../core/utils/widgets/next_page_button.dart';
import '../../../../../core/utils/widgets/popupbutton.dart';

Widget totalSellingProducts(context) {
  bool button = false;
  notifier = Provider.of(context, listen: true);
  return LayoutBuilder(
    builder: (context, constraints) {
      return StatefulBuilder(
        builder: (context, setState) {
          InvoiceController invoiceController = Get.put(InvoiceController());

          return Container(
            padding: const EdgeInsets.all(15),
            decoration: BoxDecoration(
              color: notifier.getBgColor,
              borderRadius: BorderRadius.circular(10),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "Total Selling Products",
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontFamily: "Outfit",
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: notifier.text,
                        // color: const Color(0xFF475569),
                      ),
                    ),
                    const Popupbutton(
                      title: "This Week",
                      items: [
                        "This Day",
                        "This Week",
                        "This Month",
                        "This Year",
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 15),
                Expanded(
                  child: ListView(
                    shrinkWrap: true,
                    scrollDirection: Axis.horizontal,
                    padding: EdgeInsets.zero,
                    children: [
                      Container(
                        decoration: const BoxDecoration(),
                        width: Get.width < 700
                            ? null
                            : constraints.maxWidth / 1 - 30,
                        child: Table(
                          border: TableBorder(
                            horizontalInside:
                                BorderSide(color: notifier.getfillborder),
                          ),
                          columnWidths: const {
                            0: FixedColumnWidth(50),
                            1: FixedColumnWidth(200),
                            2: FixedColumnWidth(200),
                            3: FixedColumnWidth(200),
                          },
                          children: [
                            for (var a = invoiceController.loadmore;
                                a <
                                    invoiceController.loadmore +
                                        invoiceController.selectindex;
                                a++)
                              TableRow(
                                children: [
                                  TableCell(
                                    verticalAlignment:
                                        TableCellVerticalAlignment.middle,
                                    child: Text(
                                      "${a.toString()}.",
                                      style: const TextStyle(
                                        letterSpacing: 1,
                                        fontFamily: "Outfit",
                                        fontSize: 15,
                                        color: Colors.grey,
                                        fontWeight: FontWeight.w300,
                                      ),
                                    ),
                                  ),
                                  TableCell(
                                    verticalAlignment:
                                        TableCellVerticalAlignment.middle,
                                    child: ListTile(
                                      leading: SizedBox(
                                        child: ClipOval(
                                          child: Image.network(
                                            invoiceController
                                                .sellingproductimages[a],
                                            fit: BoxFit.fill,
                                            width: 40,
                                            height: 40,
                                          ),
                                        ),
                                      ),
                                      title: Text(
                                        invoiceController
                                            .sellingproducttitle[a],
                                        style: TextStyle(
                                          fontFamily: "Outfit",
                                          fontSize: 17,
                                          fontWeight: FontWeight.bold,
                                          color: notifier.text,
                                          // color: const Color(0xFF475569),
                                        ),
                                      ),
                                      subtitle: Text(
                                        invoiceController
                                            .sellingproductsubtitle[a],
                                        style: const TextStyle(
                                          fontSize: 13,
                                          fontFamily: "Outfit",
                                          color: Colors.grey,
                                        ),
                                      ),
                                      dense: true,
                                      contentPadding: const EdgeInsets.all(0),
                                    ),
                                  ),
                                  TableCell(
                                    verticalAlignment:
                                        TableCellVerticalAlignment.middle,
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                        vertical: 20,
                                      ),
                                      child: Text(
                                        "Item:#${invoiceController.sellingproductmonths[a]}",
                                        textAlign: TextAlign.center,
                                        style: const TextStyle(
                                          fontSize: 15,
                                          fontFamily: "Outfit",
                                          color: Colors.grey,
                                        ),
                                      ),
                                    ),
                                  ),
                                  TableCell(
                                    verticalAlignment:
                                        TableCellVerticalAlignment.middle,
                                    child: Text(
                                      "PKR ${invoiceController.sellingproductprice[a]}",
                                      textAlign: TextAlign.end,
                                      style: const TextStyle(
                                        letterSpacing: 1,
                                        fontFamily: "Outfit",
                                        fontSize: 15,
                                        color: Colors.grey,
                                        fontWeight: FontWeight.w300,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                NextPageButton(
                  number1: "5",
                  number2: "10",
                  number3: "20",
                  numberofpages: "1 - 6 of 12",
                  backbutton: () {
                    if (button == true) {
                      setState(() {
                        button = !button;
                        invoiceController.sellingproductimages.shuffle();
                        invoiceController.sellingproductmonths.shuffle();
                        invoiceController.sellingproductprice.shuffle();
                        invoiceController.sellingproductsubtitle.shuffle();
                        invoiceController.sellingproducttitle.shuffle();
                      });
                      invoiceController
                          .setloadmore(invoiceController.selectpage);
                    }
                  },
                  nextbutton: () {
                    if (button == false) {
                      setState(() {
                        button = !button;
                        invoiceController.sellingproductimages.shuffle();
                        invoiceController.sellingproductmonths.shuffle();
                        invoiceController.sellingproductprice.shuffle();
                        invoiceController.sellingproductsubtitle.shuffle();
                        invoiceController.sellingproducttitle.shuffle();
                      });
                      invoiceController
                          .setloadmore(invoiceController.selectpage);
                    }
                  },
                ),
              ],
            ),
          );
        },
      );
    },
  );
}
