import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/firebase_service/locations/station_firebase_service.dart';
import 'package:logestics/models/locations/station_model.dart';

abstract class StationRepository {
  /// Creates an Zone and returns either a success or error object.
  Future<Either<FailureObj, SuccessObj>> createStation({
    required StationModel station,
  });

  Future<Either<FailureObj, List<StationModel>>> getStations();
  Stream<List<StationModel>> listenToStations();

  Future<Either<FailureObj, SuccessObj>> deleteStation({
    required String stationId,
  });

  Future<Either<FailureObj, SuccessObj>> updateStation({
    required StationModel station,
  });

  Future<Either<FailureObj, List<StationModel>>> getStationsByDistrict(
      String districtId);
}

class StationRepositoryImpl implements StationRepository {
  final StationFirebaseService firebaseService;

  StationRepositoryImpl(this.firebaseService);

  @override
  Future<Either<FailureObj, SuccessObj>> createStation({
    required StationModel station,
  }) async {
    try {
      final existingStation =
          await firebaseService.checkStationExists(station.stationName);
      if (existingStation) {
        return Left(FailureObj(
            code: 'station-exists',
            message: 'A station with the same name already exists.'));
      }

      await firebaseService.createStation(station);
      return Right(SuccessObj(message: 'Station created successfully'));
    } catch (e) {
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, List<StationModel>>> getStations() async {
    try {
      final stations = await firebaseService.getStations();
      return Right(stations);
    } catch (e) {
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> deleteStation(
      {required String stationId}) async {
    try {
      await firebaseService.deleteStation(stationId);
      return Right(SuccessObj(message: 'Station deleted successfully'));
    } catch (e) {
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Stream<List<StationModel>> listenToStations() {
    return firebaseService.listenToStations();
  }

  @override
  Future<Either<FailureObj, SuccessObj>> updateStation({
    required StationModel station,
  }) async {
    try {
      await firebaseService.updateStation(station);
      return Right(SuccessObj(message: 'Station updated successfully'));
    } catch (e) {
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, List<StationModel>>> getStationsByDistrict(
      String districtId) async {
    try {
      final stations = await firebaseService.getStationsByDistrict(districtId);
      return Right(stations);
    } catch (e) {
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }
}
