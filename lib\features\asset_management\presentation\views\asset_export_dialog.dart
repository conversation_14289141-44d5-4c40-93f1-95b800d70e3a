import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:logestics/features/asset_management/presentation/controllers/asset_export_controller.dart';
import 'package:logestics/features/asset_management/presentation/controllers/asset_list_controller.dart';
import 'package:logestics/models/asset/asset_model.dart';
import 'package:logestics/features/home/<USER>/theme.dart';
import 'package:logestics/core/utils/widgets/my_dropdown_field.dart';
import 'package:logestics/core/utils/widgets/my_text_field.dart';

class AssetExportDialog extends StatefulWidget {
  final AssetListController assetListController;

  const AssetExportDialog({
    Key? key,
    required this.assetListController,
  }) : super(key: key);

  @override
  State<AssetExportDialog> createState() => _AssetExportDialogState();
}

class _AssetExportDialogState extends State<AssetExportDialog> {
  late AssetExportController exportController;
  late ColorNotifier notifier;

  @override
  void initState() {
    super.initState();
    exportController = Get.put(AssetExportController());
  }

  @override
  void dispose() {
    Get.delete<AssetExportController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    notifier = Provider.of<ColorNotifier>(context, listen: true);
    final size = MediaQuery.of(context).size;

    return Dialog(
      backgroundColor: notifier.getBgColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        width: size.width * 0.9,
        height: size.height * 0.85,
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            _buildHeader(),
            const SizedBox(height: 24),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildFiltersSection(),
                    const SizedBox(height: 24),
                    _buildPreviewSection(),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.file_download,
          color: notifier.text,
          size: 28,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Export Assets to Excel',
                style: TextStyle(
                  color: notifier.text,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                'Configure filters and export asset data',
                style: TextStyle(
                  color: notifier.text.withOpacity(0.7),
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () => Get.back(),
          icon: Icon(Icons.close, color: notifier.text),
        ),
      ],
    );
  }

  Widget _buildFiltersSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Export Filters',
          style: TextStyle(
            color: notifier.text,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),

        // Date Range
        Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: exportController.selectStartDate,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                    color: notifier.textFileColor,
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.calendar_today,
                          color: notifier.text, size: 20),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Obx(() => Text(
                              exportController.startDate.value != null
                                  ? 'From: ${exportController.formatDate(exportController.startDate.value!)}'
                                  : 'Select start date',
                              style: TextStyle(color: notifier.text),
                            )),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: InkWell(
                onTap: exportController.selectEndDate,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                    color: notifier.textFileColor,
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.calendar_today,
                          color: notifier.text, size: 20),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Obx(() => Text(
                              exportController.endDate.value != null
                                  ? 'To: ${exportController.formatDate(exportController.endDate.value!)}'
                                  : 'Select end date',
                              style: TextStyle(color: notifier.text),
                            )),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Filter dropdowns
        Row(
          children: [
            Expanded(
              child: Obx(() => MyDropdownFormField(
                    titletext: 'Asset Type',
                    hinttext: 'All Types',
                    items: ['', ...AssetType.allTypes],
                    initalValue:
                        exportController.selectedTypeFilter.value.isNotEmpty
                            ? exportController.selectedTypeFilter.value
                            : null,
                    onChanged: exportController.setTypeFilter,
                  )),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Obx(() => MyDropdownFormField(
                    titletext: 'Status',
                    hinttext: 'All Statuses',
                    items: ['', ...AssetStatus.allStatuses],
                    initalValue:
                        exportController.selectedStatusFilter.value.isNotEmpty
                            ? exportController.selectedStatusFilter.value
                            : null,
                    onChanged: exportController.setStatusFilter,
                  )),
            ),
          ],
        ),

        const SizedBox(height: 16),

        Row(
          children: [
            Expanded(
              child: MyTextFormField(
                controller: TextEditingController(
                    text: exportController.selectedLocationFilter.value),
                titleText: 'Location',
                labelText: 'Filter by location',
                hintText: 'Enter location to filter',
                onChanged: exportController.setLocationFilter,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: MyTextFormField(
                controller: TextEditingController(
                    text: exportController.selectedDepartmentFilter.value),
                titleText: 'Department',
                labelText: 'Filter by department',
                hintText: 'Enter department to filter',
                onChanged: exportController.setDepartmentFilter,
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Clear filters button
        Align(
          alignment: Alignment.centerRight,
          child: TextButton.icon(
            onPressed: exportController.clearFilters,
            icon: const Icon(Icons.clear),
            label: const Text('Clear Filters'),
          ),
        ),
      ],
    );
  }

  Widget _buildPreviewSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Export Preview',
              style: TextStyle(
                color: notifier.text,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            Obx(() => Text(
                  '${exportController.filteredAssets.length} assets will be exported',
                  style: TextStyle(
                    color: notifier.text.withOpacity(0.7),
                    fontSize: 14,
                  ),
                )),
          ],
        ),
        const SizedBox(height: 16),

        // Preview table
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.withOpacity(0.3)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Obx(() {
            if (exportController.filteredAssets.isEmpty) {
              return Container(
                padding: const EdgeInsets.all(32),
                child: Column(
                  children: [
                    Icon(
                      Icons.inbox_outlined,
                      size: 48,
                      color: notifier.text.withOpacity(0.3),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No assets match the selected filters',
                      style: TextStyle(
                        color: notifier.text.withOpacity(0.6),
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              );
            }

            return Column(
              children: [
                // Header
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: notifier.text.withOpacity(0.1),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(8),
                      topRight: Radius.circular(8),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(flex: 2, child: _buildHeaderCell('Asset Name')),
                      Expanded(flex: 1, child: _buildHeaderCell('Type')),
                      Expanded(flex: 1, child: _buildHeaderCell('Status')),
                      Expanded(flex: 2, child: _buildHeaderCell('Location')),
                      Expanded(
                          flex: 1, child: _buildHeaderCell('Purchase Cost')),
                      Expanded(
                          flex: 1, child: _buildHeaderCell('Current Value')),
                    ],
                  ),
                ),

                // Preview rows (first 10)
                ...exportController.filteredAssets
                    .take(10)
                    .map((asset) => _buildPreviewRow(asset)),

                // Show more indicator
                if (exportController.filteredAssets.length > 10)
                  Container(
                    padding: const EdgeInsets.all(16),
                    child: Text(
                      '... and ${exportController.filteredAssets.length - 10} more assets',
                      style: TextStyle(
                        color: notifier.text.withOpacity(0.6),
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
              ],
            );
          }),
        ),
      ],
    );
  }

  Widget _buildHeaderCell(String text) {
    return Text(
      text,
      style: TextStyle(
        color: notifier.text,
        fontWeight: FontWeight.bold,
        fontSize: 12,
      ),
    );
  }

  Widget _buildPreviewRow(AssetModel asset) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.withOpacity(0.2)),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              asset.name,
              style: TextStyle(color: notifier.text, fontSize: 12),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              asset.type,
              style: TextStyle(color: notifier.text, fontSize: 12),
            ),
          ),
          Expanded(
            flex: 1,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: _getStatusColor(asset.status).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: _getStatusColor(asset.status)),
              ),
              child: Text(
                asset.status,
                style: TextStyle(
                  color: _getStatusColor(asset.status),
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              asset.location,
              style: TextStyle(color: notifier.text, fontSize: 12),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              'PKR ${exportController.formatCurrency(asset.purchaseCost)}',
              style: TextStyle(color: notifier.text, fontSize: 12),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              'PKR ${exportController.formatCurrency(asset.currentValue)}',
              style: TextStyle(color: notifier.text, fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'Active':
        return Colors.green;
      case 'Inactive':
        return Colors.orange;
      case 'Under Maintenance':
        return Colors.blue;
      case 'Disposed':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        TextButton(
          onPressed: () => Get.back(),
          child: Text(
            'Cancel',
            style: TextStyle(color: notifier.text),
          ),
        ),
        const SizedBox(width: 16),
        Obx(() => ElevatedButton.icon(
              onPressed: exportController.isLoading.value ||
                      exportController.filteredAssets.isEmpty
                  ? null
                  : exportController.generateExcel,
              icon: exportController.isLoading.value
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.file_download),
              label: Text(exportController.isLoading.value
                  ? 'Generating...'
                  : 'Export Assets'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            )),
      ],
    );
  }
}
