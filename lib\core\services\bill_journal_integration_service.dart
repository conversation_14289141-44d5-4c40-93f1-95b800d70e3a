import 'dart:developer';
import '../../models/finance/bill_model.dart';
import '../../models/finance/journal_entry_model.dart';
import '../../firebase_service/accounting/general_ledger_firebase_service.dart';
import 'automatic_journal_entry_service.dart';
import 'transaction_account_mapping_service.dart';

/// Service for integrating bill transactions with automatic journal entry generation
class BillJournalIntegrationService {
  final AutomaticJournalEntryService _automaticJournalService;
  final GeneralLedgerFirebaseService _generalLedgerService;
  final TransactionAccountMappingService _mappingService;

  BillJournalIntegrationService(
    this._automaticJournalService,
    this._generalLedgerService,
    this._mappingService,
  );

  /// Generate and create journal entry for a bill transaction
  Future<bool> processBillTransaction(BillModel bill) async {
    try {
      log('Processing bill transaction for journal entry: ${bill.billNumber}');

      // Generate journal entry for the bill
      final journalEntry =
          await _automaticJournalService.generateBillJournalEntry(
        bill: bill,
        uid: bill.companyUid,
        createdBy: 'system',
      );

      if (journalEntry == null) {
        log('Failed to generate journal entry for bill: ${bill.billId}');
        return false;
      }

      // Create the journal entry in Firebase
      final result =
          await _generalLedgerService.createJournalEntry(journalEntry);

      return result.fold(
        (failure) {
          log('Failed to create journal entry for bill: ${failure.message}');
          return false;
        },
        (success) {
          log('Successfully created journal entry for bill: ${bill.billId}');
          return true;
        },
      );
    } catch (e) {
      log('Error processing bill transaction: $e');
      return false;
    }
  }

  /// Generate journal entry for bill status update (e.g., when bill is paid)
  Future<bool> processBillStatusUpdate(
      BillModel bill, String oldStatus, String newStatus) async {
    try {
      log('Processing bill status update: ${bill.billNumber} from $oldStatus to $newStatus');

      // Only process payment completion
      if (newStatus == 'Completed' && oldStatus != 'Completed') {
        return await _processBillPayment(bill);
      }

      return true; // No journal entry needed for other status changes
    } catch (e) {
      log('Error processing bill status update: $e');
      return false;
    }
  }

  /// Process bill payment (when status changes to Completed)
  Future<bool> _processBillPayment(BillModel bill) async {
    try {
      log('Processing bill payment for: ${bill.billNumber}');

      // Get account mapping for bill payments
      final mapping =
          await _mappingService.getBillAccountMapping(bill.companyUid);
      if (mapping == null) {
        log('Bill account mapping not found for company: ${bill.companyUid}');
        return false;
      }

      // Create journal entry for bill payment
      // Debit: Accounts Payable (to reduce liability)
      // Credit: Cash/Bank Account (to reduce asset)
      final journalEntry = JournalEntryModel(
        id: '',
        entryNumber: '', // Will be auto-generated
        entryDate: DateTime.now(),
        description: 'Bill payment - ${bill.billNumber}',
        totalDebits: bill.totalAmount,
        totalCredits: bill.totalAmount,
        status: JournalEntryStatus.posted,
        entryType: JournalEntryType.automatic,
        sourceTransactionId: bill.billId,
        sourceTransactionType: 'bill_payment',
        lines: [
          JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: mapping.accountsReceivableAccount.id,
            accountNumber: mapping.accountsReceivableAccount.accountNumber,
            accountName: mapping.accountsReceivableAccount.accountName,
            debitAmount: bill.totalAmount,
            creditAmount: 0.0,
            description: 'Payment of bill ${bill.billNumber}',
            createdAt: DateTime.now(),
          ),
          JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: mapping.serviceRevenueAccount.id,
            accountNumber: mapping.serviceRevenueAccount.accountNumber,
            accountName: mapping.serviceRevenueAccount.accountName,
            debitAmount: 0.0,
            creditAmount: bill.totalAmount,
            description: 'Payment of bill ${bill.billNumber}',
            createdAt: DateTime.now(),
          ),
        ],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        createdBy: 'system',
        uid: bill.companyUid,
      );

      // Create the journal entry in Firebase
      final result =
          await _generalLedgerService.createJournalEntry(journalEntry);

      return result.fold(
        (failure) {
          log('Failed to create payment journal entry for bill: ${failure.message}');
          return false;
        },
        (success) {
          log('Successfully created payment journal entry for bill: ${bill.billId}');
          return true;
        },
      );
    } catch (e) {
      log('Error processing bill payment: $e');
      return false;
    }
  }

  /// Batch process multiple bill transactions
  Future<BatchProcessResult> batchProcessBillTransactions(
    List<BillModel> bills,
  ) async {
    int successCount = 0;
    int failureCount = 0;
    final List<String> failedBillIds = [];

    log('Batch processing ${bills.length} bill transactions');

    for (final bill in bills) {
      final success = await processBillTransaction(bill);
      if (success) {
        successCount++;
      } else {
        failureCount++;
        failedBillIds.add(bill.billId);
      }
    }

    log('Batch processing completed: $successCount successful, $failureCount failed');

    return BatchProcessResult(
      totalProcessed: bills.length,
      successCount: successCount,
      failureCount: failureCount,
      failedTransactionIds: failedBillIds,
    );
  }

  /// Validate bill transaction for journal entry generation
  Future<BillValidationResult> validateBillForJournalEntry(
    BillModel bill,
  ) async {
    try {
      final issues = <String>[];

      // Check if bill has valid amount
      if (bill.totalAmount <= 0) {
        issues.add('Bill amount must be greater than zero');
      }

      // Check if bill has valid number
      if (bill.billNumber.isEmpty) {
        issues.add('Bill must have a valid bill number');
      }

      // Check if bill has linked invoices
      if (bill.linkedInvoiceIds.isEmpty) {
        issues.add('Bill must have linked invoices');
      }

      // Check if required accounts exist for journal entry generation
      final mapping =
          await _mappingService.getBillAccountMapping(bill.companyUid);
      if (mapping == null) {
        issues.add('Required accounts for bill journal entries not found');
      }

      return BillValidationResult(
        isValid: issues.isEmpty,
        issues: issues,
      );
    } catch (e) {
      log('Error validating bill for journal entry: $e');
      return BillValidationResult(
        isValid: false,
        issues: ['Validation error: $e'],
      );
    }
  }

  /// Get journal entries associated with a bill
  Future<List<JournalEntryModel>> getJournalEntriesForBill(
    String billId,
    String uid,
  ) async {
    try {
      final allEntries =
          await _generalLedgerService.getJournalEntries(uid, null, null);

      return allEntries.fold(
        (failure) {
          log('Failed to fetch journal entries: ${failure.message}');
          return <JournalEntryModel>[];
        },
        (entries) {
          return entries
              .where((entry) =>
                  (entry.sourceTransactionId == billId &&
                      (entry.sourceTransactionType == 'bill' ||
                          entry.sourceTransactionType == 'bill_payment')) &&
                  entry.uid == uid)
              .toList();
        },
      );
    } catch (e) {
      log('Error fetching journal entries for bill: $e');
      return <JournalEntryModel>[];
    }
  }

  /// Check if a bill already has associated journal entries
  Future<bool> hasExistingJournalEntries(String billId, String uid) async {
    final entries = await getJournalEntriesForBill(billId, uid);
    return entries.isNotEmpty;
  }

  /// Reverse journal entries for a bill (when bill is deleted/modified)
  Future<bool> reverseBillJournalEntries(String billId, String uid) async {
    try {
      final entries = await getJournalEntriesForBill(billId, uid);

      if (entries.isEmpty) {
        log('No journal entries found for bill: $billId');
        return true; // Nothing to reverse
      }

      bool allReversed = true;
      for (final entry in entries) {
        final result = await _generalLedgerService.reverseJournalEntry(
          entry.id,
          'Bill transaction reversed',
          'system', // createdBy parameter
        );

        result.fold(
          (failure) {
            log('Failed to reverse journal entry ${entry.id}: ${failure.message}');
            allReversed = false;
          },
          (success) {
            log('Successfully reversed journal entry: ${entry.id}');
          },
        );
      }

      return allReversed;
    } catch (e) {
      log('Error reversing bill journal entries: $e');
      return false;
    }
  }
}

/// Result class for batch processing operations
class BatchProcessResult {
  final int totalProcessed;
  final int successCount;
  final int failureCount;
  final List<String> failedTransactionIds;

  BatchProcessResult({
    required this.totalProcessed,
    required this.successCount,
    required this.failureCount,
    required this.failedTransactionIds,
  });

  double get successRate =>
      totalProcessed > 0 ? successCount / totalProcessed : 0.0;
  bool get hasFailures => failureCount > 0;
  bool get allSuccessful => failureCount == 0;
}

/// Result class for bill validation
class BillValidationResult {
  final bool isValid;
  final List<String> issues;

  BillValidationResult({
    required this.isValid,
    required this.issues,
  });

  String get issuesText => issues.join(', ');
}
