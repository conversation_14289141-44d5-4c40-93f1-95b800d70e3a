import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/features/home/<USER>/drawer_controllers.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/widgets/navigation/drawer_page_scaffold.dart';

import 'bills_list.dart';

class BillsScreenView extends StatefulWidget {
  const BillsScreenView({super.key});

  @override
  State<BillsScreenView> createState() => _BillsScreenViewState();
}

class _BillsScreenViewState extends State<BillsScreenView> {
  late MainDrawerController mainDrawerController;

  @override
  void initState() {
    super.initState();
    mainDrawerController = Get.find<MainDrawerController>();
  }

  @override
  Widget build(BuildContext context) {
    notifier = Provider.of(context, listen: true);

    return DrawerPageScaffold(
      pageTitle: AppStrings.billsTitle,
      breadcrumbItems: const [AppStrings.system, "Billing"],
      body: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(height: 20),
            SizedBox(
              height: 570,
              child: const BillsList(titleShow: true),
            ),
          ],
        ),
      ),
    );
  }
}
