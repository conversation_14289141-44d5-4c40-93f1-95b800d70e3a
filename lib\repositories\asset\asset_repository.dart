import 'dart:io';
import 'dart:typed_data';
import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/models/asset/asset_model.dart';
import 'package:logestics/models/asset/asset_maintenance_model.dart';

abstract class AssetRepository {
  /// Asset CRUD operations
  Future<Either<FailureObj, SuccessObj>> createAsset(
    AssetModel asset, {
    List<File>? files,
    List<Uint8List>? fileBytes,
    List<String>? fileNames,
  });

  Future<Either<FailureObj, List<AssetModel>>> getAssets();

  Stream<List<AssetModel>> listenToAssets();

  Future<Either<FailureObj, AssetModel>> getAssetById(String assetId);

  Future<Either<FailureObj, SuccessObj>> updateAsset(
    AssetModel asset, {
    List<File>? files,
    List<Uint8List>? fileBytes,
    List<String>? fileNames,
  });

  Future<Either<FailureObj, SuccessObj>> deleteAsset(String assetId);

  Future<Either<FailureObj, SuccessObj>> removeAttachment(
    String assetId,
    String attachmentUrl,
  );

  Future<Either<FailureObj, List<AssetModel>>> searchAssets(String query);

  /// Maintenance operations
  Future<Either<FailureObj, SuccessObj>> createMaintenance(
    AssetMaintenanceModel maintenance, {
    List<File>? files,
    List<Uint8List>? fileBytes,
    List<String>? fileNames,
  });

  Future<Either<FailureObj, List<AssetMaintenanceModel>>>
      getMaintenanceByAssetId(
    String assetId,
  );

  Future<Either<FailureObj, List<AssetMaintenanceModel>>> getAllMaintenance();

  Future<Either<FailureObj, AssetMaintenanceModel>> getMaintenanceById(
    String maintenanceId,
  );

  Future<Either<FailureObj, SuccessObj>> updateMaintenance(
    AssetMaintenanceModel maintenance, {
    List<File>? files,
    List<Uint8List>? fileBytes,
    List<String>? fileNames,
  });

  Future<Either<FailureObj, SuccessObj>> deleteMaintenance(
      String maintenanceId);

  Future<Either<FailureObj, double>> getTotalMaintenanceCost(String assetId);

  Future<Either<FailureObj, SuccessObj>> removeMaintenanceAttachment(
    String maintenanceId,
    String attachmentUrl,
  );
}
