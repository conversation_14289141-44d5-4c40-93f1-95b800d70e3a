import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import 'package:logestics/firebase_service/firebase_auth_service.dart';
import 'package:logestics/features/company/domain/entities/company.dart';
import 'package:logestics/features/company/domain/use_cases/listen_to_invoice_use_case.dart';
import 'package:logestics/features/company/domain/use_cases/listen_to_voucher_use_case.dart';
import 'package:logestics/models/invoice_model.dart';
import 'package:logestics/models/voucher_model.dart';

class CompanyController extends GetxController {
  final ListenToInvoicesUseCase _listenToInvoicesUseCase;
  final ListenToVouchersUseCase _listenToVoucherUseCase;
  final _authService = Get.find<FirebaseAuthService>();
  late final Company company;

  // Loading states
  final isLoadingInvoices = true.obs;
  final isLoadingVouchers = true.obs;
  final hasInitialInvoiceLoad = false.obs;
  final hasInitialVoucherLoad = false.obs;

  String get currentUserId => _authService.currentUser?.uid ?? '';

  /// Check if initial data loading is complete
  bool get isInitialLoadComplete =>
      hasInitialInvoiceLoad.value && hasInitialVoucherLoad.value;

  /// Check if any data is currently loading
  bool get isAnyLoading => isLoadingInvoices.value || isLoadingVouchers.value;

  CompanyController(
      this._listenToInvoicesUseCase, this._listenToVoucherUseCase);

  @override
  void onInit() {
    super.onInit();
    company = Company(uid: currentUserId);
    _listenToInvoices();
    _listenToVouchers();
  }

  void _listenToInvoices() {
    _listenToInvoicesUseCase.execute(currentUserId).listen((docChanges) {
      try {
        bool hasChanges = false;
        for (var change in docChanges) {
          if (change.type == DocumentChangeType.added) {
            final invoice = InvoiceModel.fromJson(
                change.doc.data() as Map<String, dynamic>);
            company.addInvoice(invoice);
            hasChanges = true;
            log('CompanyController: Added invoice ${invoice.tasNumber}');
          } else if (change.type == DocumentChangeType.modified) {
            final invoice = InvoiceModel.fromJson(
                change.doc.data() as Map<String, dynamic>);
            company.updateInvoice(invoice);
            hasChanges = true;
            log('CompanyController: Updated invoice ${invoice.tasNumber}');
          } else if (change.type == DocumentChangeType.removed) {
            company.removeInvoice(change.doc.id);
            hasChanges = true;
            log('CompanyController: Removed invoice ${change.doc.id}');
          }
        }

        // Mark initial load as complete after first batch of changes
        if (!hasInitialInvoiceLoad.value) {
          hasInitialInvoiceLoad.value = true;
          isLoadingInvoices.value = false;
          log('CompanyController: Initial invoice load complete');
        }

        if (hasChanges) {
          update();
        }
      } catch (e) {
        log('CompanyController: Error in invoice listener: $e');
        isLoadingInvoices.value = false;
      }
    });
  }

  void _listenToVouchers() {
    _listenToVoucherUseCase.execute(currentUserId).listen((docChanges) {
      try {
        bool hasChanges = false;
        for (var change in docChanges) {
          if (change.type == DocumentChangeType.added) {
            final voucher = VoucherModel.fromJson(
                change.doc.data() as Map<String, dynamic>);
            company.addVoucher(voucher);
            hasChanges = true;
            log('CompanyController: Added voucher ${voucher.voucherNumber}');
          } else if (change.type == DocumentChangeType.modified) {
            final voucher = VoucherModel.fromJson(
                change.doc.data() as Map<String, dynamic>);
            company.updateVoucher(voucher);
            hasChanges = true;
            log('CompanyController: Updated voucher ${voucher.voucherNumber}');
          } else if (change.type == DocumentChangeType.removed) {
            company.removeVoucher(change.doc.id);
            hasChanges = true;
            log('CompanyController: Removed voucher ${change.doc.id}');
          }
        }

        // Mark initial load as complete after first batch of changes
        if (!hasInitialVoucherLoad.value) {
          hasInitialVoucherLoad.value = true;
          isLoadingVouchers.value = false;
          log('CompanyController: Initial voucher load complete');
        }

        if (hasChanges) {
          update();
        }
      } catch (e) {
        log('CompanyController: Error in voucher listener: $e');
        isLoadingVouchers.value = false;
      }
    });
  }

  /// Force refresh data from Firestore
  void forceRefresh() {
    // Trigger UI update for all dependent controllers
    update();

    // If we have data, ensure loading states are properly reset
    if (company.invoices.isNotEmpty) {
      isLoadingInvoices.value = false;
      hasInitialInvoiceLoad.value = true;
    }

    if (company.vouchers.isNotEmpty) {
      isLoadingVouchers.value = false;
      hasInitialVoucherLoad.value = true;
    }

    log('CompanyController: Force refresh triggered - Invoices: ${company.invoices.length}, Vouchers: ${company.vouchers.length}');
  }
}
