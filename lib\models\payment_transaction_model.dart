import 'package:cloud_firestore/cloud_firestore.dart';

enum PaymentMethod { cash, check, accountTransfer, fuelCard }

enum PaymentStatus { paid, partial }

class PaymentTransactionModel {
  final String id;
  final String voucherId;
  final PaymentMethod method;
  final PaymentStatus status;
  final double amount;
  final double pendingAmount; // Calculated field based on total and paid amount
  final DateTime
      transactionDate; // Date this transaction belongs to (user-selected)
  final DateTime createdAt; // System creation date

  // Common fields
  final String? notes;

  // For account transfers
  final String? accountId;
  final String? accountName;

  // For checks
  final String? checkNumber;
  final String? bankName;
  final DateTime? checkIssueDate;
  final DateTime? checkExpiryDate;

  // For fuel cards
  final String? fuelCardId;
  final String? fuelCardNumber;
  final String? fuelCompany;
  final double? fuelLiters;
  final double? fuelRate;

  // For file attachments (receipts, etc.)
  final String? attachmentUrl;

  PaymentTransactionModel({
    required this.id,
    required this.voucherId,
    required this.method,
    required this.status,
    required this.amount,
    required this.pendingAmount,
    required this.transactionDate,
    DateTime? createdAt,
    this.notes,
    this.accountId,
    this.accountName,
    this.checkNumber,
    this.bankName,
    this.checkIssueDate,
    this.checkExpiryDate,
    this.fuelCardId,
    this.fuelCardNumber,
    this.fuelCompany,
    this.fuelLiters,
    this.fuelRate,
    this.attachmentUrl,
  }) : createdAt = createdAt ?? DateTime.now();

  // Create a PaymentTransactionModel from a map (for Firestore)
  factory PaymentTransactionModel.fromMap(Map<String, dynamic> map) {
    return PaymentTransactionModel(
      id: map['id'] ?? '',
      voucherId: map['voucherId'] ?? '',
      method: PaymentMethod.values.firstWhere(
        (e) => e.toString() == 'PaymentMethod.${map['method']}',
        orElse: () => PaymentMethod.cash,
      ),
      status: PaymentStatus.values.firstWhere(
        (e) => e.toString() == 'PaymentStatus.${map['status']}',
        orElse: () => PaymentStatus.paid,
      ),
      amount: (map['amount'] ?? 0).toDouble(),
      pendingAmount: (map['pendingAmount'] ?? 0).toDouble(),
      transactionDate: map['transactionDate'] is DateTime
          ? map['transactionDate'] as DateTime
          : (map['transactionDate'] as Timestamp).toDate(),
      createdAt: map['createdAt'] != null
          ? (map['createdAt'] is DateTime
              ? map['createdAt'] as DateTime
              : (map['createdAt'] as Timestamp).toDate())
          : DateTime.now(),
      notes: map['notes'],
      accountId: map['accountId'],
      accountName: map['accountName'],
      checkNumber: map['checkNumber'],
      bankName: map['bankName'],
      checkIssueDate: map['checkIssueDate'] != null
          ? (map['checkIssueDate'] is DateTime
              ? map['checkIssueDate'] as DateTime
              : (map['checkIssueDate'] as Timestamp).toDate())
          : null,
      checkExpiryDate: map['checkExpiryDate'] != null
          ? (map['checkExpiryDate'] is DateTime
              ? map['checkExpiryDate'] as DateTime
              : (map['checkExpiryDate'] as Timestamp).toDate())
          : null,
      fuelCardId: map['fuelCardId'],
      fuelCardNumber: map['fuelCardNumber'],
      fuelCompany: map['fuelCompany'],
      fuelLiters:
          map['fuelLiters'] != null ? (map['fuelLiters']).toDouble() : null,
      fuelRate: map['fuelRate'] != null ? (map['fuelRate']).toDouble() : null,
      attachmentUrl: map['attachmentUrl'],
    );
  }

  // Convert PaymentTransactionModel to a map (for Firestore)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'voucherId': voucherId,
      'method': method.toString().split('.').last,
      'status': status.toString().split('.').last,
      'amount': amount,
      'pendingAmount': pendingAmount,
      'transactionDate': transactionDate,
      'createdAt': createdAt,
      'notes': notes,
      'accountId': accountId,
      'accountName': accountName,
      'checkNumber': checkNumber,
      'bankName': bankName,
      'checkIssueDate': checkIssueDate,
      'checkExpiryDate': checkExpiryDate,
      'fuelCardId': fuelCardId,
      'fuelCardNumber': fuelCardNumber,
      'fuelCompany': fuelCompany,
      'fuelLiters': fuelLiters,
      'fuelRate': fuelRate,
      'attachmentUrl': attachmentUrl,
    };
  }

  // Create a copy of the PaymentTransactionModel with updated fields
  PaymentTransactionModel copyWith({
    String? id,
    String? voucherId,
    PaymentMethod? method,
    PaymentStatus? status,
    double? amount,
    double? pendingAmount,
    DateTime? transactionDate,
    DateTime? createdAt,
    String? notes,
    String? accountId,
    String? accountName,
    String? checkNumber,
    String? bankName,
    DateTime? checkIssueDate,
    DateTime? checkExpiryDate,
    String? fuelCardId,
    String? fuelCardNumber,
    String? fuelCompany,
    double? fuelLiters,
    double? fuelRate,
    String? attachmentUrl,
  }) {
    return PaymentTransactionModel(
      id: id ?? this.id,
      voucherId: voucherId ?? this.voucherId,
      method: method ?? this.method,
      status: status ?? this.status,
      amount: amount ?? this.amount,
      pendingAmount: pendingAmount ?? this.pendingAmount,
      transactionDate: transactionDate ?? this.transactionDate,
      createdAt: createdAt ?? this.createdAt,
      notes: notes ?? this.notes,
      accountId: accountId ?? this.accountId,
      accountName: accountName ?? this.accountName,
      checkNumber: checkNumber ?? this.checkNumber,
      bankName: bankName ?? this.bankName,
      checkIssueDate: checkIssueDate ?? this.checkIssueDate,
      checkExpiryDate: checkExpiryDate ?? this.checkExpiryDate,
      fuelCardId: fuelCardId ?? this.fuelCardId,
      fuelCardNumber: fuelCardNumber ?? this.fuelCardNumber,
      fuelCompany: fuelCompany ?? this.fuelCompany,
      fuelLiters: fuelLiters ?? this.fuelLiters,
      fuelRate: fuelRate ?? this.fuelRate,
      attachmentUrl: attachmentUrl ?? this.attachmentUrl,
    );
  }
}
