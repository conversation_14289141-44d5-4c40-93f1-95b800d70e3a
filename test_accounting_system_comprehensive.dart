import 'dart:developer';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// Comprehensive test script for the entire accounting system
/// This script tests all accounting modules after Firebase indexes are created
void main() async {
  try {
    // Initialize Firebase
    await Firebase.initializeApp();
    log('🔥 Firebase initialized successfully');
    
    // Check authentication
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) {
      log('❌ No authenticated user found');
      return;
    }
    
    log('👤 Testing with user: ${currentUser.email}');
    log('🆔 UID: ${currentUser.uid}');
    
    final firestore = FirebaseFirestore.instance;
    final uid = currentUser.uid;
    
    // Test all accounting modules
    await testChartOfAccounts(firestore, uid);
    await testJournalEntries(firestore, uid);
    await testFiscalPeriods(firestore, uid);
    await testFinancialReports(firestore, uid);
    await testGeneralLedger(firestore, uid);
    
    log('✅ All accounting system tests completed');
    
  } catch (e) {
    log('❌ Error in comprehensive accounting test: $e');
  }
}

/// Test Chart of Accounts functionality
Future<void> testChartOfAccounts(FirebaseFirestore firestore, String uid) async {
  log('\n📊 Testing Chart of Accounts...');
  
  try {
    // Test 1: Basic accounts query
    log('🔍 Test 1: Basic accounts query');
    final basicQuery = await firestore
        .collection('chart_of_accounts')
        .where('uid', isEqualTo: uid)
        .orderBy('accountNumber')
        .limit(5)
        .get();
    log('✅ Basic query: ${basicQuery.docs.length} accounts found');
    
    // Test 2: Active accounts query (requires Index 1)
    log('🔍 Test 2: Active accounts query');
    final activeQuery = await firestore
        .collection('chart_of_accounts')
        .where('uid', isEqualTo: uid)
        .where('isActive', isEqualTo: true)
        .orderBy('accountNumber')
        .limit(5)
        .get();
    log('✅ Active accounts query: ${activeQuery.docs.length} accounts found');
    
    // Test 3: Category filter query (requires Index 3)
    log('🔍 Test 3: Category filter query');
    final categoryQuery = await firestore
        .collection('chart_of_accounts')
        .where('uid', isEqualTo: uid)
        .where('category', isEqualTo: 'assets')
        .where('isActive', isEqualTo: true)
        .orderBy('accountNumber')
        .limit(5)
        .get();
    log('✅ Category filter query: ${categoryQuery.docs.length} accounts found');
    
    // Test 4: Account type filter query (requires Index 5)
    log('🔍 Test 4: Account type filter query');
    final typeQuery = await firestore
        .collection('chart_of_accounts')
        .where('uid', isEqualTo: uid)
        .where('accountType', isEqualTo: 'cash')
        .where('isActive', isEqualTo: true)
        .orderBy('accountNumber')
        .limit(5)
        .get();
    log('✅ Account type filter query: ${typeQuery.docs.length} accounts found');
    
    log('✅ Chart of Accounts tests passed');
    
  } catch (e) {
    log('❌ Chart of Accounts test failed: $e');
  }
}

/// Test Journal Entries functionality
Future<void> testJournalEntries(FirebaseFirestore firestore, String uid) async {
  log('\n📝 Testing Journal Entries...');
  
  try {
    // Test 1: Basic journal entries query (requires Index 6)
    log('🔍 Test 1: Basic journal entries query');
    final basicQuery = await firestore
        .collection('journal_entries')
        .where('uid', isEqualTo: uid)
        .orderBy('entryDate', descending: true)
        .limit(5)
        .get();
    log('✅ Basic journal entries: ${basicQuery.docs.length} entries found');
    
    // Test 2: Date range query (requires Index 7)
    log('🔍 Test 2: Date range query');
    final now = DateTime.now();
    final startDate = now.subtract(const Duration(days: 30));
    final dateRangeQuery = await firestore
        .collection('journal_entries')
        .where('uid', isEqualTo: uid)
        .where('entryDate', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
        .where('entryDate', isLessThanOrEqualTo: Timestamp.fromDate(now))
        .orderBy('entryDate')
        .limit(5)
        .get();
    log('✅ Date range query: ${dateRangeQuery.docs.length} entries found');
    
    // Test 3: Status filter query (requires Index 8)
    log('🔍 Test 3: Status filter query');
    final statusQuery = await firestore
        .collection('journal_entries')
        .where('uid', isEqualTo: uid)
        .where('status', isEqualTo: 'posted')
        .orderBy('entryDate', descending: true)
        .limit(5)
        .get();
    log('✅ Status filter query: ${statusQuery.docs.length} entries found');
    
    // Test 4: Journal entry lines by account (requires Index 11)
    log('🔍 Test 4: Journal entry lines by account');
    final linesQuery = await firestore
        .collection('journal_entry_lines')
        .where('accountId', isEqualTo: 'test-account-id')
        .orderBy('createdAt', descending: true)
        .limit(5)
        .get();
    log('✅ Journal entry lines: ${linesQuery.docs.length} lines found');
    
    log('✅ Journal Entries tests passed');
    
  } catch (e) {
    log('❌ Journal Entries test failed: $e');
  }
}

/// Test Fiscal Periods functionality
Future<void> testFiscalPeriods(FirebaseFirestore firestore, String uid) async {
  log('\n📅 Testing Fiscal Periods...');
  
  try {
    // Test 1: Fiscal years query (requires Index 15)
    log('🔍 Test 1: Fiscal years query');
    final yearsQuery = await firestore
        .collection('fiscal_years')
        .where('uid', isEqualTo: uid)
        .orderBy('startDate', descending: true)
        .limit(5)
        .get();
    log('✅ Fiscal years: ${yearsQuery.docs.length} years found');
    
    // Test 2: Fiscal periods by year (requires Index 16)
    log('🔍 Test 2: Fiscal periods by year');
    final periodsQuery = await firestore
        .collection('fiscal_periods')
        .where('uid', isEqualTo: uid)
        .where('fiscalYearId', isEqualTo: 'test-year-id')
        .orderBy('startDate')
        .limit(5)
        .get();
    log('✅ Fiscal periods: ${periodsQuery.docs.length} periods found');
    
    log('✅ Fiscal Periods tests passed');
    
  } catch (e) {
    log('❌ Fiscal Periods test failed: $e');
  }
}

/// Test Financial Reports functionality
Future<void> testFinancialReports(FirebaseFirestore firestore, String uid) async {
  log('\n📈 Testing Financial Reports...');
  
  try {
    // Test 1: Financial reports by type (requires Index 14)
    log('🔍 Test 1: Financial reports by type');
    final reportsQuery = await firestore
        .collection('financial_reports')
        .where('uid', isEqualTo: uid)
        .where('reportType', isEqualTo: 'trial_balance')
        .orderBy('generatedAt', descending: true)
        .limit(5)
        .get();
    log('✅ Financial reports: ${reportsQuery.docs.length} reports found');
    
    // Test 2: Balance sheet reports (requires Index 18)
    log('🔍 Test 2: Balance sheet reports');
    final balanceSheetQuery = await firestore
        .collection('balance_sheet_reports')
        .where('uid', isEqualTo: uid)
        .orderBy('generatedAt', descending: true)
        .limit(5)
        .get();
    log('✅ Balance sheet reports: ${balanceSheetQuery.docs.length} reports found');
    
    log('✅ Financial Reports tests passed');
    
  } catch (e) {
    log('❌ Financial Reports test failed: $e');
  }
}

/// Test General Ledger functionality
Future<void> testGeneralLedger(FirebaseFirestore firestore, String uid) async {
  log('\n📚 Testing General Ledger...');
  
  try {
    // Test 1: Account transactions query
    log('🔍 Test 1: Account transactions query');
    final transactionsQuery = await firestore
        .collection('journal_entries')
        .where('uid', isEqualTo: uid)
        .where('status', isEqualTo: 'posted')
        .orderBy('entryDate', descending: true)
        .limit(5)
        .get();
    log('✅ Account transactions: ${transactionsQuery.docs.length} transactions found');
    
    log('✅ General Ledger tests passed');
    
  } catch (e) {
    log('❌ General Ledger test failed: $e');
  }
}
