import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:web/web.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/core/router/app_routes.dart';
import 'package:get/get.dart';

class FirebaseAuthService extends GetxController {
  var isUserAlreadyLogin = false;
  final Rx<User?> _user = Rx<User?>(null);
  FirebaseAuth auth = FirebaseAuth.instance;
  User? get currentUser => _user.value;

  // Add a flag to prevent multiple navigations
  bool _isNavigating = false;
  StreamSubscription<User?>? _authSubscription;

  @override
  void onInit() {
    super.onInit();
    // Enable persistent login by default
    enableLocalPersistence(persistence: Persistence.LOCAL);
  }

  Future<void> enableLocalPersistence(
      {Persistence persistence = Persistence.LOCAL}) async {
    try {
      await auth.setPersistence(persistence);
      log('Firebase Auth persistence set to: $persistence');
    } catch (e) {
      log('Error setting Firebase Auth persistence: $e');
    }
  }

  @override
  void onReady() {
    super.onReady();

    // Check for existing user on app start
    _user.value = auth.currentUser;

    // If we have a user from Firebase Auth, update local storage immediately
    if (_user.value != null) {
      _updateLocalStorage(_user.value);
      log('User found in Firebase Auth: ${_user.value!.email}');
    } else {
      // Check local storage for user data and verify with Firebase
      _checkLocalStorageForUser();
    }

    // Set up real-time auth state listener with proper cleanup
    _setupAuthListener();

    // Delay initial navigation to allow proper initialization
    Future.delayed(const Duration(milliseconds: 200), () {
      if (!_isNavigating) {
        initialScreen(_user.value);
      }
    });
  }

  void _setupAuthListener() {
    // Cancel existing subscription
    _authSubscription?.cancel();

    // Set up new subscription
    _authSubscription = auth.authStateChanges().listen(
      (User? user) {
        log('Auth state changed: ${user?.email ?? 'null'}');
        _user.value = user;
        _updateLocalStorage(user);

        // Only navigate if we're not on the root screen initially and not already navigating
        if (!_isNavigating && Get.currentRoute != AppRoutes.root) {
          initialScreen(user);
        }
      },
      onError: (error) {
        log('Auth state listener error: $error');
      },
    );
  }

  void _checkLocalStorageForUser() {
    try {
      final userDataString = window.localStorage.getItem('user');
      if (userDataString != null && userDataString.isNotEmpty) {
        final userData = json.decode(userDataString);
        final isLoggedIn = userData['isLoggedIn'] as bool? ?? false;

        if (isLoggedIn) {
          // Check if user is still authenticated with Firebase
          if (auth.currentUser != null) {
            // User is logged in according to local storage and Firebase
            log('User session restored from local storage: ${userData['email']}');
            _user.value = auth.currentUser;
          } else {
            // User data in local storage but not in Firebase Auth - attempt to restore
            log('Found user in local storage but not in Firebase, attempting to restore session');
            // Force a token refresh to check if the session is still valid
            auth.currentUser?.reload().then((_) {
              if (auth.currentUser != null) {
                _user.value = auth.currentUser;
                log('Session restored successfully');
              } else {
                log('Session expired, clearing local storage');
                window.localStorage.removeItem('user');
              }
            }).catchError((error) {
              log('Session restoration failed: $error');
              window.localStorage.removeItem('user');
            });
          }
        }
      }
    } catch (e) {
      log('Error checking local storage for user: $e');
      // Clear corrupted data
      try {
        window.localStorage.removeItem('user');
      } catch (clearError) {
        log('Error clearing corrupted user data: $clearError');
      }
    }
  }

  void _updateLocalStorage(User? user) {
    try {
      if (user != null) {
        // Store user data for client-side routing and persistence
        final userData = {
          'uid': user.uid,
          'email': user.email,
          'displayName': user.displayName,
          'photoURL': user.photoURL,
          'emailVerified': user.emailVerified,
          'isLoggedIn': true,
          'lastLogin': DateTime.now().toIso8601String(),
        };
        window.localStorage.setItem('user', json.encode(userData));
        log('User data stored in localStorage');
      } else {
        // Clear user data when logged out
        window.localStorage.removeItem('user');
        log('User data cleared from localStorage');
      }
    } catch (e) {
      log('Error updating localStorage: $e');
    }
  }

  void initialScreen(User? user) {
    // Don't navigate if we're currently on the root screen - let RootScreen handle initial navigation
    if (Get.currentRoute == AppRoutes.root || _isNavigating) {
      return;
    }

    _isNavigating = true;

    Future.delayed(const Duration(milliseconds: 100), () {
      try {
        if (user == null) {
          if (Get.currentRoute != AppRoutes.login) {
            log('Navigating to login - user not authenticated');
            Get.offAllNamed(AppRoutes.login);
          }
        } else {
          if (Get.currentRoute != AppRoutes.home) {
            log('Navigating to home - user authenticated: ${user.email}');
            Get.offAllNamed(AppRoutes.home);
          }
        }
      } catch (e) {
        log('Error during navigation: $e');
      } finally {
        // Reset navigation flag after a short delay
        Future.delayed(const Duration(milliseconds: 500), () {
          _isNavigating = false;
        });
      }
    });
  }

  Future<UserCredential> login(
      {required String email, required String password}) async {
    try {
      log('Attempting login for user: $email');

      // Attempt to sign in with email and password
      var result = await auth.signInWithEmailAndPassword(
          email: email, password: password);

      log('Login successful for user: ${result.user?.email}');

      // Force update the user state
      _user.value = result.user;

      return result;
    } on FirebaseAuthException catch (e) {
      log('Firebase Auth Exception during login: ${e.code} - ${e.message}');
      rethrow;
    } catch (e) {
      log('Unknown error during login: $e');
      rethrow;
    }
  }

  Future<void> signOut() async {
    try {
      log('Signing out user');
      _isNavigating = true;

      await auth.signOut();

      // Ensure localStorage is cleared
      window.localStorage.removeItem('user');

      // Clear any other relevant local data
      _clearSessionData();

      // Force update user state
      _user.value = null;

      log('User signed out successfully');

      // Navigate to login after sign out
      Future.delayed(const Duration(milliseconds: 100), () {
        Get.offAllNamed(AppRoutes.login);
        _isNavigating = false;
      });
    } catch (e) {
      log('Error during sign out: $e');
      _isNavigating = false;
      rethrow;
    }
  }

  void _clearSessionData() {
    try {
      // Clear any session-specific data
      window.sessionStorage.clear();
    } catch (e) {
      log('Error clearing session data: $e');
    }
  }

  // Method to check if user is authenticated (useful for guards)
  bool get isAuthenticated => currentUser != null;

  // Method to get user ID safely
  String get currentUserId => currentUser?.uid ?? '';

  @override
  void onClose() {
    _authSubscription?.cancel();
    super.onClose();
  }
}
