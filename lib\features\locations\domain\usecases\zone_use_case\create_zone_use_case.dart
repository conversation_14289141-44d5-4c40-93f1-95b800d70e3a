import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/features/locations/repositories/zone_repository.dart';
import 'package:logestics/models/locations/zone_model.dart';

class CreateZoneUseCase {
  final ZoneRepository _zoneRepository;

  CreateZoneUseCase(this._zoneRepository);

  /// Executes the use case to create an Zone.
  Future<Either<FailureObj, SuccessObj>> call({
    required ZoneModel zone,
  }) async {
    return await _zoneRepository.createZone(
      zone: zone,
    );
  }
}
