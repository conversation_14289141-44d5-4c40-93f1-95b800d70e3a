import 'package:flutter/material.dart';
import 'package:logestics/core/utils/widgets/custom_button.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';
import 'package:textfield_tags/textfield_tags.dart';

// ignore: must_be_immutable
class ChipsAutocomplete extends StatefulWidget {
  ChipsAutocomplete({
    super.key,
    required this.title,
    required this.hintText,
    required this.labelText,
    required this.items,
    this.showControl,
    required this.stringTagController,
    this.validator,
  });

  final String title;
  final String hintText;
  final String labelText;
  final List<String> items;
  final bool? showControl;
  final StringTagController stringTagController;
  final Validator? validator;

  bool disableInput = true;

  // static const List<String> _initialTags = <String>["Lemon"];
  List controlble = ["Disable", "Enable"];

  @override
  State<ChipsAutocomplete> createState() => _ChipsAutocompleteState();
}

class _ChipsAutocompleteState extends State<ChipsAutocomplete> {
  // Flag to track if we already registered the controller
  bool _controllerRegistered = false;

  @override
  void initState() {
    super.initState();
    // In initState we only do setup work - actual controller registration happens in didChangeDependencies
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // This ensures the controller registration happens after dependencies are updated
    if (!_controllerRegistered) {
      _controllerRegistered = true;
    }
  }

  @override
  void dispose() {
    _controllerRegistered = false;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    notifier = Provider.of(context, listen: true);
    return LayoutBuilder(
      builder: (context, constraints) {
        return Container(
          width: constraints.maxWidth,
          padding: const EdgeInsets.all(15),
          decoration: BoxDecoration(
            color: notifier.getBgColor,
            borderRadius: BorderRadius.circular(10),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.title,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: notifier.text,
                ),
              ),
              const SizedBox(height: 15),
              widget.showControl == true
                  ? Row(
                      children: [
                        for (var i = 0; i < widget.controlble.length; i++)
                          Padding(
                            padding: const EdgeInsets.only(right: 20),
                            child: CustomButton(
                              text: widget.controlble[i],
                              backgroundColor: notifier.isDark
                                  ? Colors.black
                                  : const Color(0xFFfdfbff),
                              onPressed: () {},
                            ),
                          ),
                      ],
                    )
                  : Container(),
              widget.showControl == true
                  ? const SizedBox(height: 15)
                  : Container(),
              widget.showControl == true
                  ? const Text(
                      "Enter video keywords",
                      style: TextStyle(
                        fontStyle: FontStyle.italic,
                        color: Colors.grey,
                        fontSize: 15,
                      ),
                    )
                  : Container(),
              widget.showControl == true
                  ? const SizedBox(height: 15)
                  : Container(),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: notifier.getfillborder,
                  ),
                  borderRadius: BorderRadius.circular(5),
                ),
                child: KeyedSubtree(
                  key: ValueKey(widget.stringTagController.hashCode),
                  child: Center(
                    child: TextFieldTags<String>(
                      textfieldTagsController: widget.stringTagController,
                      initialTags: widget.items,
                      letterCase: LetterCase.normal,
                      // validator: widget.validator,
                      inputFieldBuilder: (context, inputFieldValues) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              decoration: const BoxDecoration(),
                              child: SingleChildScrollView(
                                controller:
                                    inputFieldValues.tagScrollController,
                                scrollDirection: Axis.vertical,
                                child: Padding(
                                  padding: const EdgeInsets.only(
                                    top: 8,
                                    bottom: 8,
                                    left: 8,
                                  ),
                                  child: Wrap(
                                    runSpacing: 4.0,
                                    spacing: 4.0,
                                    children: inputFieldValues.tags.map(
                                      (String tag) {
                                        return Container(
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(8),
                                            border: Border.all(
                                              color: notifier.getfillborder,
                                            ),
                                          ),
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 10.0,
                                            vertical: 5.0,
                                          ),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.start,
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              InkWell(
                                                child: Text(
                                                  tag,
                                                  style: TextStyle(
                                                    color:
                                                        widget.disableInput ==
                                                                false
                                                            ? Colors.grey
                                                            : notifier.text,
                                                  ),
                                                ),
                                                onTap: () {},
                                              ),
                                              const SizedBox(width: 4.0),
                                              InkWell(
                                                onTap: widget.disableInput ==
                                                        false
                                                    ? null
                                                    : () {
                                                        inputFieldValues
                                                            .onTagRemoved(tag);
                                                      },
                                                child: Icon(
                                                  Icons.cancel,
                                                  size: 14.0,
                                                  color: widget.disableInput ==
                                                          false
                                                      ? Colors.grey
                                                      : notifier.text,
                                                ),
                                              )
                                            ],
                                          ),
                                        );
                                      },
                                    ).toList(),
                                  ),
                                ),
                              ),
                            ),
                            TextField(
                              onTap: () {
                                widget.stringTagController.getFocusNode
                                    ?.requestFocus();
                              },
                              scrollPadding:
                                  const EdgeInsets.symmetric(horizontal: 10),
                              cursorColor: const Color(0xff0165FC),
                              controller:
                                  inputFieldValues.textEditingController,
                              focusNode: inputFieldValues.focusNode,
                              style: TextStyle(color: notifier.text),
                              enabled: widget.disableInput,
                              decoration: InputDecoration(
                                contentPadding:
                                    const EdgeInsets.symmetric(horizontal: 10),
                                isDense: false,
                                enabledBorder: UnderlineInputBorder(
                                  borderSide: BorderSide(
                                    color: notifier.getfillborder,
                                  ),
                                  borderRadius: BorderRadius.circular(5),
                                ),
                                alignLabelWithHint: true,
                                focusedBorder: UnderlineInputBorder(
                                  borderSide: const BorderSide(
                                    color: Color(0xff0165FC),
                                  ),
                                  borderRadius: BorderRadius.circular(5),
                                ),
                                errorText: inputFieldValues.error,
                                hintText: widget.hintText,
                                hintStyle: TextStyle(
                                  color: widget.disableInput == false
                                      ? Colors.grey
                                      : notifier.text,
                                ),
                                labelText: widget.labelText,
                                labelStyle: TextStyle(
                                  color: widget.disableInput == false
                                      ? Colors.grey
                                      : const Color(0xff0165FC),
                                ),
                                floatingLabelBehavior:
                                    FloatingLabelBehavior.always,
                              ),
                              onChanged: inputFieldValues.onTagChanged,
                              onSubmitted: inputFieldValues.onTagSubmitted,
                            )
                          ],
                        );
                      },
                    ),
                  ),
                ),
              ),
              widget.showControl == true
                  ? const SizedBox(height: 15)
                  : Container(),
              widget.showControl == true
                  ? RichText(
                      text: TextSpan(
                        text: "The following keywords are entered : ",
                        style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w600,
                          color: notifier.text,
                          fontFamily: "Outfit",
                        ),
                        children: <TextSpan>[
                          for (var value = 0;
                              value < widget.items.length;
                              value++)
                            TextSpan(
                              text: "${widget.items[value]}, ",
                              style: const TextStyle(
                                color: Colors.grey,
                                fontWeight: FontWeight.normal,
                              ),
                            ),
                        ],
                      ),
                    )
                  : Container(),
            ],
          ),
        );
      },
    );
  }
}
