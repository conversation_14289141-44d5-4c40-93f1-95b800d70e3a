class ZoneModel {
  String zoneId;
  final String zoneName;
  final DateTime createdAt;

  ZoneModel({
    required this.zoneId,
    required this.zoneName,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'zoneId': zoneId,
      'zoneName': zoneName,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory ZoneModel.fromJson(Map<String, dynamic> json) {
    return ZoneModel(
      zoneId: json['zoneId'] ?? '',
      zoneName: json['zoneName'] ?? '',
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  @override
  String toString() {
    return 'ZoneModel{zoneId: $zoneId, zoneName: $zoneName, createdAt: $createdAt}';
  }
}
