import 'dart:developer';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../../core/utils/snackbar_utils.dart';

import '../../../../../models/accounting/financial_report_models.dart';
import '../../../../../services/financial_report_export_service.dart';
import '../../repositories/trial_balance_repository.dart';

/// Controller for Trial Balance functionality
class TrialBalanceController extends GetxController {
  final TrialBalanceRepository _repository;
  final FinancialReportExportService exportService =
      FinancialReportExportService();

  TrialBalanceController(this._repository);

  // Form keys and controllers
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final TextEditingController companyNameController = TextEditingController();
  final TextEditingController startDateController = TextEditingController();
  final TextEditingController endDateController = TextEditingController();

  // Observable state
  final RxBool isLoading = false.obs;
  final RxBool isGenerating = false.obs;
  final RxBool isSaving = false.obs;
  final RxBool isDeleting = false.obs;
  final RxBool isValidating = false.obs;

  // Data
  final Rx<TrialBalanceReport?> currentReport = Rx<TrialBalanceReport?>(null);
  final RxList<TrialBalanceReport> savedReports = <TrialBalanceReport>[].obs;
  final RxList<TrialBalanceAccount> filteredAccounts =
      <TrialBalanceAccount>[].obs;

  // Filter options
  final RxBool includeInactiveAccounts = false.obs;
  final RxBool includeZeroBalances = false.obs;
  final RxString searchQuery = ''.obs;
  final RxString selectedAccountType = 'All'.obs;

  // Date range
  final Rx<DateTime?> startDate = Rx<DateTime?>(null);
  final Rx<DateTime?> endDate = Rx<DateTime?>(null);

  // Validation state
  final RxBool isTrialBalanceValid = true.obs;
  final RxString validationMessage = ''.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeDates();
    _loadSavedReports();

    // Listen to search query changes
    debounce(searchQuery, (String value) => _filterAccounts(),
        time: const Duration(milliseconds: 300));

    // Listen to account type filter changes
    ever(selectedAccountType, (_) => _filterAccounts());
  }

  @override
  void onClose() {
    companyNameController.dispose();
    startDateController.dispose();
    endDateController.dispose();
    super.onClose();
  }

  /// Initialize default date range (current month)
  void _initializeDates() {
    final now = DateTime.now();
    startDate.value = DateTime(now.year, now.month, 1);
    endDate.value = DateTime(now.year, now.month + 1, 0);

    startDateController.text = _formatDate(startDate.value!);
    endDateController.text = _formatDate(endDate.value!);
  }

  /// Format date for display
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  /// Load saved trial balance reports
  Future<void> _loadSavedReports() async {
    try {
      isLoading.value = true;

      final result = await _repository.getSavedTrialBalanceReports();

      result.fold(
        (failure) {
          log('Failed to load saved reports: ${failure.message}');
          SnackbarUtils.showError('Error', failure.message);
        },
        (reports) {
          savedReports.assignAll(reports);
          log('Loaded ${reports.length} saved trial balance reports');
        },
      );
    } catch (e) {
      log('Error loading saved reports: $e');
      SnackbarUtils.showError('Error', 'Something went wrong');
    } finally {
      isLoading.value = false;
    }
  }

  /// Generate trial balance report
  Future<void> generateTrialBalance() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    if (startDate.value == null || endDate.value == null) {
      SnackbarUtils.showError('Error', 'Please select start and end dates');
      return;
    }

    if (companyNameController.text.trim().isEmpty) {
      SnackbarUtils.showError('Error', 'Please enter company name');
      return;
    }

    try {
      isGenerating.value = true;

      final result = await _repository.generateTrialBalance(
        startDate: startDate.value!,
        endDate: endDate.value!,
        companyName: companyNameController.text.trim(),
        includeInactiveAccounts: includeInactiveAccounts.value,
        includeZeroBalances: includeZeroBalances.value,
      );

      result.fold(
        (failure) {
          log('Failed to generate trial balance: ${failure.message}');
          SnackbarUtils.showError('Error', failure.message);
        },
        (report) {
          currentReport.value = report;
          _filterAccounts();
          _validateTrialBalance();
          SnackbarUtils.showSuccess(
              'Success', 'Trial balance generated successfully');
          log('Generated trial balance with ${report.accounts.length} accounts');
        },
      );
    } catch (e) {
      log('Error generating trial balance: $e');
      SnackbarUtils.showError('Error', 'Something went wrong');
    } finally {
      isGenerating.value = false;
    }
  }

  /// Save current trial balance report
  Future<void> saveTrialBalanceReport() async {
    if (currentReport.value == null) {
      SnackbarUtils.showError('Error', 'No trial balance report to save');
      return;
    }

    try {
      isSaving.value = true;

      final result =
          await _repository.saveTrialBalanceReport(currentReport.value!);

      result.fold(
        (failure) {
          log('Failed to save trial balance report: ${failure.message}');
          SnackbarUtils.showError('Error', failure.message);
        },
        (success) {
          SnackbarUtils.showSuccess('Success', success.message);
          _loadSavedReports(); // Refresh saved reports list
          log('Trial balance report saved successfully');
        },
      );
    } catch (e) {
      log('Error saving trial balance report: $e');
      SnackbarUtils.showError('Error', 'Something went wrong');
    } finally {
      isSaving.value = false;
    }
  }

  /// Delete a saved trial balance report
  Future<void> deleteTrialBalanceReport(String reportId) async {
    try {
      isDeleting.value = true;

      final result = await _repository.deleteTrialBalanceReport(reportId);

      result.fold(
        (failure) {
          log('Failed to delete trial balance report: ${failure.message}');
          SnackbarUtils.showError('Error', failure.message);
        },
        (success) {
          SnackbarUtils.showSuccess('Success', success.message);
          _loadSavedReports(); // Refresh saved reports list

          // Clear current report if it was deleted
          if (currentReport.value?.reportId == reportId) {
            currentReport.value = null;
            filteredAccounts.clear();
          }

          log('Trial balance report deleted successfully');
        },
      );
    } catch (e) {
      log('Error deleting trial balance report: $e');
      SnackbarUtils.showError('Error', 'Something went wrong');
    } finally {
      isDeleting.value = false;
    }
  }

  /// Load a saved trial balance report
  Future<void> loadTrialBalanceReport(String reportId) async {
    try {
      isLoading.value = true;

      final result = await _repository.getTrialBalanceReportById(reportId);

      result.fold(
        (failure) {
          log('Failed to load trial balance report: ${failure.message}');
          SnackbarUtils.showError('Error', failure.message);
        },
        (report) {
          currentReport.value = report;
          _filterAccounts();
          _validateTrialBalance();

          // Update form fields
          companyNameController.text = report.companyName;
          startDate.value = report.startDate;
          endDate.value = report.endDate;
          startDateController.text = _formatDate(report.startDate);
          endDateController.text = _formatDate(report.endDate);

          SnackbarUtils.showSuccess(
              'Success', 'Trial balance report loaded successfully');
          log('Loaded trial balance report: ${report.reportId}');
        },
      );
    } catch (e) {
      log('Error loading trial balance report: $e');
      SnackbarUtils.showError('Error', 'Something went wrong');
    } finally {
      isLoading.value = false;
    }
  }

  /// Validate trial balance (ensure debits equal credits)
  Future<void> _validateTrialBalance() async {
    if (currentReport.value == null) return;

    try {
      isValidating.value = true;

      final result =
          await _repository.validateTrialBalance(currentReport.value!.endDate);

      result.fold(
        (failure) {
          log('Failed to validate trial balance: ${failure.message}');
          isTrialBalanceValid.value = false;
          validationMessage.value = failure.message;
        },
        (isValid) {
          isTrialBalanceValid.value = isValid;
          validationMessage.value = isValid
              ? 'Trial balance is balanced (debits equal credits)'
              : 'Trial balance is not balanced (debits do not equal credits)';
          log('Trial balance validation: ${isValid ? 'BALANCED' : 'UNBALANCED'}');
        },
      );
    } catch (e) {
      log('Error validating trial balance: $e');
      isTrialBalanceValid.value = false;
      validationMessage.value = 'Error validating trial balance';
    } finally {
      isValidating.value = false;
    }
  }

  /// Filter accounts based on search query and account type
  void _filterAccounts() {
    if (currentReport.value == null) {
      filteredAccounts.clear();
      return;
    }

    var accounts = currentReport.value!.accounts;

    // Filter by search query
    if (searchQuery.value.isNotEmpty) {
      final query = searchQuery.value.toLowerCase();
      accounts = accounts
          .where((account) =>
              account.accountName.toLowerCase().contains(query) ||
              account.accountNumber.toLowerCase().contains(query))
          .toList();
    }

    // Filter by account type
    if (selectedAccountType.value != 'All') {
      accounts = accounts
          .where((account) => account.accountType == selectedAccountType.value)
          .toList();
    }

    filteredAccounts.assignAll(accounts);
  }

  /// Select date range
  Future<void> selectDateRange(BuildContext context) async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: startDate.value != null && endDate.value != null
          ? DateTimeRange(start: startDate.value!, end: endDate.value!)
          : null,
    );

    if (picked != null) {
      startDate.value = picked.start;
      endDate.value = picked.end;
      startDateController.text = _formatDate(picked.start);
      endDateController.text = _formatDate(picked.end);
    }
  }

  /// Get account type options for filter
  List<String> get accountTypeOptions {
    if (currentReport.value == null) return ['All'];

    final types = currentReport.value!.accounts
        .map((account) => account.accountType)
        .toSet()
        .toList();

    types.sort();
    return ['All', ...types];
  }

  /// Get total debits
  double get totalDebits {
    return currentReport.value?.totalDebits ?? 0.0;
  }

  /// Get total credits
  double get totalCredits {
    return currentReport.value?.totalCredits ?? 0.0;
  }

  /// Check if trial balance is balanced
  bool get isBalanced {
    return (totalDebits - totalCredits).abs() <
        0.01; // Allow for small rounding differences
  }

  /// Set loading state
  void setLoading(bool loading) {
    isLoading.value = loading;
  }

  /// Export current report to PDF
  Future<Uint8List> exportToPDF() async {
    if (currentReport.value == null) {
      throw Exception('No report available to export');
    }

    return await exportService.generateTrialBalancePDF(currentReport.value!);
  }

  /// Export current report to Excel
  Future<void> exportToExcel() async {
    if (currentReport.value == null) {
      throw Exception('No report available to export');
    }

    await exportService.generateTrialBalanceExcel(currentReport.value!);
  }
}
