import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';

/// Debug helper class to test Firebase connectivity and authentication
class FirebaseDebugHelper {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Test Firebase authentication status
  static Future<void> testAuthentication() async {
    log('=== FIREBASE AUTHENTICATION DEBUG ===');

    try {
      final currentUser = _auth.currentUser;

      if (currentUser == null) {
        log('❌ No authenticated user found');
        log('User is: null');
        return;
      }

      log('✅ User authenticated');
      log('User UID: ${currentUser.uid}');
      log('User Email: ${currentUser.email}');
      log('User Display Name: ${currentUser.displayName}');
      log('Email Verified: ${currentUser.emailVerified}');

      // Test token refresh
      try {
        final token = await currentUser.getIdToken(true);
        log('✅ Token refresh successful');
        log('Token length: ${token?.length ?? 0}');
      } catch (e) {
        log('❌ Token refresh failed: $e');
      }
    } catch (e) {
      log('❌ Authentication test failed: $e');
    }
  }

  /// Test Firebase Firestore connectivity
  static Future<void> testFirestoreConnectivity() async {
    log('=== FIREBASE FIRESTORE CONNECTIVITY DEBUG ===');

    try {
      // Test basic connectivity with a simple read
      final testDoc =
          await _firestore.collection('test').doc('connectivity').get();

      log('✅ Firestore connectivity successful');
      log('Test document exists: ${testDoc.exists}');
    } catch (e) {
      log('❌ Firestore connectivity failed: $e');
    }
  }

  /// Test asset collection access
  static Future<void> testAssetCollectionAccess() async {
    log('=== ASSET COLLECTION ACCESS DEBUG ===');

    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        log('❌ Cannot test asset access - no authenticated user');
        return;
      }

      final uid = currentUser.uid;
      log('Testing asset access for UID: $uid');

      // Test the exact query used in AssetFirebaseService
      final snapshot = await _firestore
          .collection(AppCollection.assetsCollection)
          .where('uid', isEqualTo: uid)
          .orderBy('createdAt', descending: true)
          .limit(1) // Limit to 1 for testing
          .get();

      log('✅ Asset query successful');
      log('Collection: ${AppCollection.assetsCollection}');
      log('Query filter: uid == $uid');
      log('Documents found: ${snapshot.docs.length}');

      if (snapshot.docs.isNotEmpty) {
        final doc = snapshot.docs.first;
        log('Sample document ID: ${doc.id}');
        log('Sample document data keys: ${doc.data().keys.toList()}');
      }
    } catch (e) {
      log('❌ Asset collection access failed: $e');
      log('Error type: ${e.runtimeType}');

      if (e is FirebaseException) {
        log('Firebase error code: ${e.code}');
        log('Firebase error message: ${e.message}');
      }
    }
  }

  /// Test Firebase indexes
  static Future<void> testFirebaseIndexes() async {
    log('=== FIREBASE INDEXES DEBUG ===');

    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        log('❌ Cannot test indexes - no authenticated user');
        return;
      }

      final uid = currentUser.uid;

      // Test different query patterns to see which indexes are working
      final queries = [
        'uid only',
        'uid + createdAt',
        'uid + status + createdAt',
        'uid + type + createdAt',
        'uid + name',
        'uid + purchaseDate',
      ];

      for (final queryName in queries) {
        try {
          Query query = _firestore
              .collection(AppCollection.assetsCollection)
              .where('uid', isEqualTo: uid);

          switch (queryName) {
            case 'uid + createdAt':
              query = query.orderBy('createdAt', descending: true);
              break;
            case 'uid + status + createdAt':
              query = query
                  .where('status', isEqualTo: 'In Use')
                  .orderBy('createdAt', descending: true);
              break;
            case 'uid + type + createdAt':
              query = query
                  .where('type', isEqualTo: 'Vehicle')
                  .orderBy('createdAt', descending: true);
              break;
            case 'uid + name':
              query = query.orderBy('name');
              break;
            case 'uid + purchaseDate':
              query = query.orderBy('purchaseDate', descending: true);
              break;
          }

          final snapshot = await query.limit(1).get();
          log('✅ Query "$queryName" successful - ${snapshot.docs.length} docs');
        } catch (e) {
          log('❌ Query "$queryName" failed: $e');
        }
      }
    } catch (e) {
      log('❌ Index testing failed: $e');
    }
  }

  /// Test asset status updates
  static Future<void> testAssetStatusUpdates() async {
    log('=== ASSET STATUS UPDATE DEBUG ===');

    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        log('❌ Cannot test status updates - no authenticated user');
        return;
      }

      final uid = currentUser.uid;

      // Get first asset to test status update
      final snapshot = await _firestore
          .collection(AppCollection.assetsCollection)
          .where('uid', isEqualTo: uid)
          .limit(1)
          .get();

      if (snapshot.docs.isEmpty) {
        log('❌ No assets found for testing status updates');
        return;
      }

      final doc = snapshot.docs.first;
      final assetData = doc.data();
      final currentStatus = assetData['status'] ?? 'Unknown';

      log('✅ Found test asset: ${doc.id}');
      log('Current status: $currentStatus');
      log('Asset name: ${assetData['name']}');
      log('Asset type: ${assetData['type']}');
      log('Created at: ${assetData['createdAt']}');
      log('Updated at: ${assetData['updatedAt']}');

      // Test reading the asset after potential update
      final refreshedDoc = await _firestore
          .collection(AppCollection.assetsCollection)
          .doc(doc.id)
          .get();

      if (refreshedDoc.exists) {
        final refreshedData = refreshedDoc.data()!;
        log('✅ Asset re-read successful');
        log('Re-read status: ${refreshedData['status']}');
        log('Re-read updated at: ${refreshedData['updatedAt']}');
      } else {
        log('❌ Asset not found on re-read');
      }
    } catch (e) {
      log('❌ Asset status update test failed: $e');
    }
  }

  /// Test real-time stream subscription
  static Future<void> testRealTimeStream() async {
    log('=== REAL-TIME STREAM DEBUG ===');

    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        log('❌ Cannot test real-time stream - no authenticated user');
        return;
      }

      final uid = currentUser.uid;

      log('Setting up real-time stream for UID: $uid');

      // Set up a temporary stream listener
      final streamSubscription = _firestore
          .collection(AppCollection.assetsCollection)
          .where('uid', isEqualTo: uid)
          .orderBy('createdAt', descending: true)
          .limit(5)
          .snapshots()
          .listen(
        (snapshot) {
          log('🔄 Stream update received: ${snapshot.docs.length} assets');
          for (var doc in snapshot.docs) {
            final data = doc.data();
            log('Stream asset: ${doc.id} - ${data['name']} (${data['status']})');
          }
        },
        onError: (error) {
          log('❌ Stream error: $error');
        },
      );

      // Let the stream run for 3 seconds
      await Future.delayed(const Duration(seconds: 3));

      // Cancel the subscription
      await streamSubscription.cancel();
      log('✅ Real-time stream test completed');
    } catch (e) {
      log('❌ Real-time stream test failed: $e');
    }
  }

  /// Run all debug tests
  static Future<void> runAllTests() async {
    log('🔍 Starting Firebase Debug Tests...');

    await testAuthentication();
    await testFirestoreConnectivity();
    await testAssetCollectionAccess();
    await testFirebaseIndexes();
    await testAssetStatusUpdates();
    await testRealTimeStream();

    log('🔍 Firebase Debug Tests Complete');
  }
}
