/// Data models for Financial Dashboard

/// Financial summary model containing key financial metrics
class FinancialSummaryModel {
  final double totalRevenue;
  final double totalExpenses;
  final double netIncome;
  final double totalAssets;
  final double totalLiabilities;
  final double totalEquity;
  final double cashBalance;
  final double accountsReceivable;
  final double accountsPayable;
  final double currentRatio;
  final double debtToEquityRatio;
  final double profitMargin;
  final DateTime asOfDate;

  FinancialSummaryModel({
    required this.totalRevenue,
    required this.totalExpenses,
    required this.netIncome,
    required this.totalAssets,
    required this.totalLiabilities,
    required this.totalEquity,
    required this.cashBalance,
    required this.accountsReceivable,
    required this.accountsPayable,
    required this.currentRatio,
    required this.debtToEquityRatio,
    required this.profitMargin,
    required this.asOfDate,
  });

  factory FinancialSummaryModel.fromJson(Map<String, dynamic> json) {
    return FinancialSummaryModel(
      totalRevenue: (json['totalRevenue'] as num?)?.toDouble() ?? 0.0,
      totalExpenses: (json['totalExpenses'] as num?)?.toDouble() ?? 0.0,
      netIncome: (json['netIncome'] as num?)?.toDouble() ?? 0.0,
      totalAssets: (json['totalAssets'] as num?)?.toDouble() ?? 0.0,
      totalLiabilities: (json['totalLiabilities'] as num?)?.toDouble() ?? 0.0,
      totalEquity: (json['totalEquity'] as num?)?.toDouble() ?? 0.0,
      cashBalance: (json['cashBalance'] as num?)?.toDouble() ?? 0.0,
      accountsReceivable: (json['accountsReceivable'] as num?)?.toDouble() ?? 0.0,
      accountsPayable: (json['accountsPayable'] as num?)?.toDouble() ?? 0.0,
      currentRatio: (json['currentRatio'] as num?)?.toDouble() ?? 0.0,
      debtToEquityRatio: (json['debtToEquityRatio'] as num?)?.toDouble() ?? 0.0,
      profitMargin: (json['profitMargin'] as num?)?.toDouble() ?? 0.0,
      asOfDate: DateTime.fromMillisecondsSinceEpoch(json['asOfDate'] ?? 0),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalRevenue': totalRevenue,
      'totalExpenses': totalExpenses,
      'netIncome': netIncome,
      'totalAssets': totalAssets,
      'totalLiabilities': totalLiabilities,
      'totalEquity': totalEquity,
      'cashBalance': cashBalance,
      'accountsReceivable': accountsReceivable,
      'accountsPayable': accountsPayable,
      'currentRatio': currentRatio,
      'debtToEquityRatio': debtToEquityRatio,
      'profitMargin': profitMargin,
      'asOfDate': asOfDate.millisecondsSinceEpoch,
    };
  }
}

/// Account balance model for dashboard display
class AccountBalanceModel {
  final String accountId;
  final String accountNumber;
  final String accountName;
  final String accountType;
  final String category;
  final double balance;
  final bool isActive;
  final DateTime lastTransactionDate;

  AccountBalanceModel({
    required this.accountId,
    required this.accountNumber,
    required this.accountName,
    required this.accountType,
    required this.category,
    required this.balance,
    required this.isActive,
    required this.lastTransactionDate,
  });

  factory AccountBalanceModel.fromJson(Map<String, dynamic> json) {
    return AccountBalanceModel(
      accountId: json['accountId'] ?? '',
      accountNumber: json['accountNumber'] ?? '',
      accountName: json['accountName'] ?? '',
      accountType: json['accountType'] ?? '',
      category: json['category'] ?? '',
      balance: (json['balance'] as num?)?.toDouble() ?? 0.0,
      isActive: json['isActive'] ?? true,
      lastTransactionDate: DateTime.fromMillisecondsSinceEpoch(
        json['lastTransactionDate'] ?? 0,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'accountId': accountId,
      'accountNumber': accountNumber,
      'accountName': accountName,
      'accountType': accountType,
      'category': category,
      'balance': balance,
      'isActive': isActive,
      'lastTransactionDate': lastTransactionDate.millisecondsSinceEpoch,
    };
  }
}

/// Dashboard transaction model for recent activity
class DashboardTransactionModel {
  final String transactionId;
  final String entryNumber;
  final DateTime transactionDate;
  final String description;
  final String accountName;
  final double amount;
  final String type; // 'debit' or 'credit'
  final String? referenceNumber;
  final String? sourceType; // 'expense', 'deposit', 'voucher', etc.

  DashboardTransactionModel({
    required this.transactionId,
    required this.entryNumber,
    required this.transactionDate,
    required this.description,
    required this.accountName,
    required this.amount,
    required this.type,
    this.referenceNumber,
    this.sourceType,
  });

  factory DashboardTransactionModel.fromJson(Map<String, dynamic> json) {
    return DashboardTransactionModel(
      transactionId: json['transactionId'] ?? '',
      entryNumber: json['entryNumber'] ?? '',
      transactionDate: DateTime.fromMillisecondsSinceEpoch(
        json['transactionDate'] ?? 0,
      ),
      description: json['description'] ?? '',
      accountName: json['accountName'] ?? '',
      amount: (json['amount'] as num?)?.toDouble() ?? 0.0,
      type: json['type'] ?? '',
      referenceNumber: json['referenceNumber'],
      sourceType: json['sourceType'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'transactionId': transactionId,
      'entryNumber': entryNumber,
      'transactionDate': transactionDate.millisecondsSinceEpoch,
      'description': description,
      'accountName': accountName,
      'amount': amount,
      'type': type,
      'referenceNumber': referenceNumber,
      'sourceType': sourceType,
    };
  }
}

/// Cash flow data model for charts
class CashFlowDataModel {
  final DateTime date;
  final double cashIn;
  final double cashOut;
  final double netCashFlow;
  final double cumulativeBalance;

  CashFlowDataModel({
    required this.date,
    required this.cashIn,
    required this.cashOut,
    required this.netCashFlow,
    required this.cumulativeBalance,
  });

  factory CashFlowDataModel.fromJson(Map<String, dynamic> json) {
    return CashFlowDataModel(
      date: DateTime.fromMillisecondsSinceEpoch(json['date'] ?? 0),
      cashIn: (json['cashIn'] as num?)?.toDouble() ?? 0.0,
      cashOut: (json['cashOut'] as num?)?.toDouble() ?? 0.0,
      netCashFlow: (json['netCashFlow'] as num?)?.toDouble() ?? 0.0,
      cumulativeBalance: (json['cumulativeBalance'] as num?)?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date.millisecondsSinceEpoch,
      'cashIn': cashIn,
      'cashOut': cashOut,
      'netCashFlow': netCashFlow,
      'cumulativeBalance': cumulativeBalance,
    };
  }
}

/// Expense breakdown model for pie charts
class ExpenseBreakdownModel {
  final String category;
  final String accountName;
  final double amount;
  final double percentage;
  final int transactionCount;

  ExpenseBreakdownModel({
    required this.category,
    required this.accountName,
    required this.amount,
    required this.percentage,
    required this.transactionCount,
  });

  factory ExpenseBreakdownModel.fromJson(Map<String, dynamic> json) {
    return ExpenseBreakdownModel(
      category: json['category'] ?? '',
      accountName: json['accountName'] ?? '',
      amount: (json['amount'] as num?)?.toDouble() ?? 0.0,
      percentage: (json['percentage'] as num?)?.toDouble() ?? 0.0,
      transactionCount: json['transactionCount'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'category': category,
      'accountName': accountName,
      'amount': amount,
      'percentage': percentage,
      'transactionCount': transactionCount,
    };
  }
}
