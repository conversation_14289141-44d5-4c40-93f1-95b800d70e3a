import 'package:flutter/material.dart';
import 'package:logestics/core/utils/app_constants/assets/app_assets.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';

class Searchfield extends StatefulWidget {
  final Function(String)? onChanged;
  final TextEditingController? controller;

  const Searchfield({
    super.key,
    this.onChanged,
    this.controller,
  });

  @override
  State<Searchfield> createState() => _SearchfieldState();
}

class _SearchfieldState extends State<Searchfield> {
  @override
  Widget build(BuildContext context) {
    notifier = Provider.of(context, listen: true);

    return TextField(
      controller: widget.controller,
      onChanged: widget.onChanged,
      cursorColor: notifier.text,
      style: TextStyle(color: notifier.text),
      decoration: InputDecoration(
        filled: true,
        fillColor: notifier.textFileColor,
        hintText: 'Search here...',
        prefixIcon: Image.asset(
          AppAssets.searchIcon,
          scale: 1.5,
          color: notifier.text,
        ),
        hintStyle: const TextStyle(
          color: Colors.grey,
          fontFamily: "Outfit",
        ),
        contentPadding: const EdgeInsets.only(left: 20, right: 20),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
        ),
        enabledBorder: OutlineInputBorder(
          borderSide: BorderSide.none,
          borderRadius: BorderRadius.circular(6),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: const BorderSide(color: Colors.blue),
          borderRadius: BorderRadius.circular(6),
        ),
      ),
    );
  }
}
