import 'package:get/get.dart';

mixin PaginationMixin {
  final RxInt currentPage = 1.obs;
  final RxInt itemsPerPage = 10.obs;
  final RxInt totalItems = 0.obs;

  int get totalPages => (totalItems.value / itemsPerPage.value).ceil();

  List<T> paginateList<T>(List<T> items) {
    final startIndex = (currentPage.value - 1) * itemsPerPage.value;
    final endIndex = startIndex + itemsPerPage.value;

    if (startIndex >= items.length) return [];

    return items.sublist(
      startIndex,
      endIndex > items.length ? items.length : endIndex,
    );
  }

  void setTotalItems(int count) {
    totalItems.value = count;
  }

  void setCurrentPage(int page) {
    if (page < 1 || page > totalPages) return;
    currentPage.value = page;
  }

  void setItemsPerPage(int count) {
    itemsPerPage.value = count;
    // Reset to first page when changing items per page
    currentPage.value = 1;
  }

  void resetPagination() {
    currentPage.value = 1;
    itemsPerPage.value = 10;
  }
}
