class SlabModel {
  String slabId;
  final String slabName;
  final DateTime startDate;
  final DateTime expiryDate;
  final DateTime createdAt;
  final bool isActive;
  final List<SlabRateModel> rates;

  SlabModel({
    required this.slabId,
    required this.slabName,
    required this.startDate,
    required this.expiryDate,
    required this.createdAt,
    this.isActive = true,
    required this.rates,
  });

  /// Check if slab is valid for a given date
  bool isValidForDate(DateTime date) {
    return isActive &&
        date.isAfter(startDate.subtract(const Duration(days: 1))) &&
        date.isBefore(expiryDate.add(const Duration(days: 1)));
  }

  /// Check if slab is expired
  bool get isExpired {
    return DateTime.now().isAfter(expiryDate);
  }

  /// Get rate for a specific district
  SlabRateModel? getRateForDistrict(String districtId) {
    try {
      return rates.firstWhere((rate) => rate.districtId == districtId);
    } catch (e) {
      return null;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'slabId': slabId,
      'slabName': slabName,
      'startDate': startDate.toIso8601String(),
      'expiryDate': expiryDate.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'isActive': isActive,
      'rates': rates.map((rate) => rate.toJson()).toList(),
    };
  }

  factory SlabModel.fromJson(Map<String, dynamic> json) {
    return SlabModel(
      slabId: json['slabId'] ?? '',
      slabName: json['slabName'] ?? '',
      startDate: DateTime.parse(json['startDate']),
      expiryDate: DateTime.parse(json['expiryDate']),
      createdAt: DateTime.parse(json['createdAt']),
      isActive: json['isActive'] ?? true,
      rates: (json['rates'] as List<dynamic>?)
              ?.map((rateJson) => SlabRateModel.fromJson(rateJson))
              .toList() ??
          [],
    );
  }

  @override
  String toString() {
    return 'SlabModel{slabId: $slabId, slabName: $slabName, startDate: $startDate, expiryDate: $expiryDate, isActive: $isActive, ratesCount: ${rates.length}}';
  }
}

class SlabRateModel {
  final String regionId;
  final String regionName;
  final String districtId;
  final String districtName;
  final double hmtRate; // HMT (Fuel + Non-Fuel) Rate
  final double nonFuelRate; // Non-Fuel Rate
  final Map<String, dynamic>
      customColumns; // For dynamic columns like WHT, GST, etc.

  SlabRateModel({
    required this.regionId,
    required this.regionName,
    required this.districtId,
    required this.districtName,
    required this.hmtRate,
    required this.nonFuelRate,
    this.customColumns = const {},
  });

  /// Get value from custom column
  dynamic getCustomValue(String columnName) {
    return customColumns[columnName];
  }

  /// Create a copy with updated custom columns
  SlabRateModel copyWithCustomColumns(Map<String, dynamic> newCustomColumns) {
    return SlabRateModel(
      regionId: regionId,
      regionName: regionName,
      districtId: districtId,
      districtName: districtName,
      hmtRate: hmtRate,
      nonFuelRate: nonFuelRate,
      customColumns: {...customColumns, ...newCustomColumns},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'regionId': regionId,
      'regionName': regionName,
      'districtId': districtId,
      'districtName': districtName,
      'hmtRate': hmtRate,
      'nonFuelRate': nonFuelRate,
      'customColumns': customColumns,
    };
  }

  factory SlabRateModel.fromJson(Map<String, dynamic> json) {
    return SlabRateModel(
      regionId: json['regionId'] ?? '',
      regionName: json['regionName'] ?? '',
      districtId: json['districtId'] ?? '',
      districtName: json['districtName'] ?? '',
      hmtRate: (json['hmtRate'] ?? 0.0).toDouble(),
      nonFuelRate: (json['nonFuelRate'] ?? 0.0).toDouble(),
      customColumns: Map<String, dynamic>.from(json['customColumns'] ?? {}),
    );
  }

  @override
  String toString() {
    return 'SlabRateModel{regionId: $regionId, regionName: $regionName, districtId: $districtId, districtName: $districtName, hmtRate: $hmtRate, nonFuelRate: $nonFuelRate, customColumns: $customColumns}';
  }
}
