class AppStrings {
  ////////////////authentication screens text
  //login screen text
  static const String next = 'Next';
  static const String automateBusiness = "Automate your Business";

  static const String welcomeBack = "Welcome Back";
  static const String dontHaveAccount = "Don't Have Account? ";
  static const String contactUs = "Contact us";
  static const String yourEmailAddress = "Your Email Address";
  static const String password = "Password";
  static const String forgetPassword = "Forget Password?";

  static const String emailHint = '<EMAIL>';
  static const String emailLabel = 'Email';
  static const String passwordLabel = 'Password';
  static const String passwordHint = 'Aazy@#123';
  static const String rememberMe = 'Remember Me';
  // static const String demo = 'Demmo';
  static const String signIn = 'Sign In';
  //////////////// Authentication Errors
  // login Email validation messages
  static const String emailEmpty = "Please enter your email";
  static const String emailInvalid = "Please enter a valid email address";

  // Password validation messages
  static const String passwordEmpty = "Please enter your password";
  //  login login errors
  static const String noUserFound = "No user found for the given email.";
  static const String incorrectPassword = "Incorrect password.";
  static const String invalidEmail = "The email address is invalid.";
  static const String userDisabled = "This user account has been disabled.";
  static const String tooManyRequests =
      "Too many login attempts. Try again later.";
  static const String networkError = "Please check your internet connection.";
  static const String invalidCredential = "Invalid password or email";
  static const String authError = "Authentication error:";
  static const String timeoutError =
      "Request timeout. Please check your internet connection.";
  static const String noInternet =
      "No internet connection. Please check your network.";
  static const String unexpectedError = "An unexpected error occurred:";
  static const String timeout = "Timeout";
  static const String resetTip = "If you're not sure,Reset your password.";
  //forget screen text
  static const String usePhone = "Use Phone";
  static const String continueLabel = "Continue";
  static const String dontKnowEmail = "Don't Know Email?";
  static const String forgetPasswordTitle = "Forget Password";
  static const String passwordResetEmailSent =
      "check your email box email is sented to user";
  static const String subHeading =
      "enter valid email address . we will send forget password link to your account";
  static const String resetEmailTitle = 'Reset Email Sent';
  static const String resetEmailMessage = 'Click here:';
  static const String errorOpeningLink = 'Error opening the link.';
  static const String companySigne = '@2025 PluttoX Software House';

  // Invoice Form Constants
  static const String addNewInvoice = "Add New Invoice";

  // Form Field Labels
  static const String invoiceNumber = "Invoice #";
  static const String number = "#";
  static const String invoiceStatus = "Invoice Status";
  static const String productName = "Product Name";
  static const String numberOfBags = "Number of Bags";
  static const String consignorName = "Consignor Name";
  static const String consignorPickupAddress = "Consignor Pickup Address";
  static const String customerAgencyName = "Customer/Agency Name";
  static const String customerCNIC = "Customer CNIC";
  static const String deliveryMode = "Delivery Mode";
  static const String truckWagonNumber = "Truck/Wagon Number";
  static const String tasNumber = "TAS Number";
  static const String conveyNoteNumber = "Convey Number";
  static const String regions = "Regions";
  static const String destinationAddress = "Destination Address";
  static const String distanceKm = "Distance (KM)";

  // Form Field Hints
  static const String select = "Select";
  static const String invoiceNumberHint = "Enter invoice number";
  static const String productNameHint = "Enter product name (e.g., Sona Urea)";
  static const String numberOfBagsHint = "Enter number of bags";
  static const String consignorNameHint = "Enter consignor name";
  static const String pickupAddressHint = "Enter pickup address";
  static const String customerNameHint = "Enter customer/agency name";
  static const String customerCNICHint = "Enter CNIC number";
  static const String truckNumberHint =
      "Enter truck/wagon number (e.g., ABC1234)";
  static const String tasNumberHint = "Enter TAS number";
  static const String conveyanceNoteHint = "Enter conveyance note number";
  static const String destinationAddressHint = "Enter destination address";
  static const String distanceHint = "Enter distance in kilometers";

  // Status Options
  static const List<String> invoiceStatusOptions = [
    'Dispatched',
    'Received',
    'Pending Billing',
    'Payment Received'
  ];

  // Button Labels
  static const String cancel = "Cancel";
  static const String save = "Save";

  // Form Titles
  static const String addNewTask = "Add New Task";

  // Field Labels
  static const String consignorPickUpAddress = "Consignor Pickup address";
  static const String distanceInKms = "Distance in Kms";
  static const String shipmentDate = "Shipment Date";

  // Hints
  static const String pickUpAddressHint = "e.g Karachi Port";
  static const String conveyNoteNumberHint = "e.g 3000709833";

  static const String orderNumber = 'Order Number';
  static const String orderNumberHint = 'e.g 350000705';

  // Order related
  static const String orderDate = 'Order Date';
  static const String orderDateHint = 'DD/MM/YYYY';

  // Consignor related

  static const String consignorPickUpAddressHint = 'Enter pickup address';

  // Customer related
  static const String customerGstNumber = 'Customer GST Number';
  static const String customerGstNumberHint = 'Enter GST number';
  static const String customerName = 'Customer Name';

  // Delivery related
  static const List<String> deliveryModeOptions = ['By Road', 'By Air'];

  // Distance related
  static const String distanceInKilometers = 'Distance (KM)';
  static const String distanceInKilometersHint = 'Enter distance';

  // Shipment related
  static const String shipmentNumber = 'Shipment Number';
  static const String shipmentNumberHint = 'Enter shipment number';
  static const String shipmentDateHint = 'DD/MM/YYYY';

  // Label texts
  static const String gstNo = 'GST No';
  static const String cusAgeName = 'Customer/Agency Name';
  static const String cusIDNo = 'Customer ID No';
  static const String address = 'Address';
  static const String distance = 'Distance';
  static const String orderNo = 'Order No';
  static const String quantity = 'Quantity';
  static const String pickUpAddress = 'PickUp address';
  static const String shipmentNo = 'Shipment No';

  // Hint texts
  static const String gstFbrTrackingNumberHint = 'e.g 232406451192';
  static const String customerCnicHint = 'e.g 3530445565645';

  // Region options

  // Region List View
  static const String noRegionsFound =
      'No regions found. Add your first region!';
  static const String regionNumberLabel = 'Region Number: ';

  // Invoice List View
  static const String addNewInvoiceButton = "+ Add New Invoice";

  static const String invoiceTitle = "Invoice";
  static const String searchHint = "Search here...";

  // Invoice Table Headers
  static const String invoiceTasNo = "Invoice TAS No";
  static const String customerNameHeader = "Customer Name";
  static const String destinationAddressHeader = "Destination Address";
  static const String kmsHeader = "Kms";
  static const String regionHeader = "Region";
  static const String shipmentDateHeader = "Shipment Date";
  static const String statusHeader = "Status";
  static const String actionHeader = "Action";

  // Invoice Status Colors Map Keys
  static const String inProgress = "In Progress";
  static const String completed = "Completed";
  static const String notStarted = "Not Started";
  static const String pending = "Pending";

  // Asset Paths
  static const String searchIconPath = 'assets/images/search.png';
  static const String eyeIconPath = "assets/images/eye.png";
  static const String penIconPath = "assets/images/pen.png";
  static const String trashIconPath = "assets/images/trash.png";

  // Voucher List View
  static const String addNewVoucherButton = "+ Add New Voucher";
  static const String addNewVouchersButton = "+ Add New Vouchers";
  static const String voucherTitle = "Voucher";

  // Voucher Table Headers
  static const String voucherNumber = "Voucher No";
  static const String voucherDate = "Voucher Date";
  static const String voucherType = "Type";
  static const String voucherAmount = "Amount";
  static const String voucherDescription = "Description";
  static const String voucherStatus = "Status";
  static const String voucherAction = "Action";

  // Voucher Form Labels
  static const String voucherNumberLabel = "Voucher Number";
  static const String voucherDateLabel = "Voucher Date";
  static const String voucherTypeLabel = "Voucher Type";
  static const String voucherAmountLabel = "Amount";
  static const String voucherDescriptionLabel = "Description";
  static const String voucherStatusLabel = "Status";
  static const String truckWagonNumberLabel = "Truck/wagon Number";
  static const String conveyNoteNumberLabel = "Convey Note Number important";
  static const String departureDateLabel = "departure date";
  static const String invoicesLabel = "Invoices";
  static const String productNameLabel = "Product Name";
  static const String totalNumberOfBagsLabel = "Total Quantity";
  static const String totalBagsLabel = "Total Quantity";
  static const String weightInTonsLabel = "Number of Tons";
  static const String brokerNameLabel = "Broker name";
  static const String brokerNameTitleLabel =
      "Broker Name/vehicle supplier Name";
  static const String driverNameLabel = "Driver Name";
  static const String driverPhoneNumberLabel = "Driver Contact No";
  static const String driverPhoneLabel = "Driver Contact No";
  static const String totalFreightLabel = "Freight Rupees";
  static const String totalFreightTitleLabel = "Total Truck/wagon freight";
  static const String totalFreight = "Total freight";
  // Voucher Form Hints
  static const String voucherNumberHint = "e.g 315";
  static const String voucherTruckNumberHint = "e.g TlX076";
  static const String voucherConveyNoteHint = "e.g 3000709833";
  static const String departureDateHint = "select";
  static const String voucherInvoicesHint = "Enter Manually Invoice Tas Number";
  static const String voucherProductNameHint = "e.g Sona Urea";
  static const String totalBagsHint = "e.g 7293";
  static const String weightInTonsHint = "e.g 33";
  static const String brokerNameHint = "e.g Shah Nawaz Goods Transporter";
  static const String driverNameHint = "e.g Ali Lahore";
  static const String driverPhoneHint = "03123456789";
  static const String totalFreightHint = "e.g 38000";
  static const String totalTruckFreightHint = "e.g 36000";

  // Voucher Types
  static const List<String> voucherTypeOptions = [
    'Payment',
    'Receipt',
    'Journal',
    'Contra'
  ];

  // Voucher Status Options
  static const List<String> voucherStatusOptions = [
    'Draft',
    'Posted',
    'Approved',
    'Rejected'
  ];

  // Bill Status Options
  static const List<String> billStatusOptions = ['Pending', 'Completed'];

  // Voucher Status Map Keys
  static const String voucherDraft = "Draft";
  static const String voucherPosted = "Posted";
  static const String voucherApproved = "Approved";
  static const String voucherRejected = "Rejected";

  // Voucher Messages
  static const String noVouchersFound =
      "No vouchers found. Add your first voucher!";
  static const String deleteVoucherConfirmation =
      "Are you sure you want to delete this voucher?";

  // Navigation Labels
  static const String dashboard = " Dashboard";
  static const String system = "system";
  static const String vouchers = "Vouchers";
  static const String vouchersLowerCase = "vouchers";

  // Region Form Strings
  static const String addRegion = "Add Region";
  static const String regionName = "Region Name";
  static const String regionNameHint = "e.g., Okara, Sukhur";
  static const String regionNameRequired = "Region name is required";
  static const String addDestination = "Add Destination";
  static const String destinationHint = "Enter destination";
  static const String saveRegion = "Save Region";
  static const String regionNumberHint = "Enter region number";
  static const String regionNumberRequired = "Region number is required";

  static const String destinationNameHint = "Enter destination name";

  static const String destinationName = "Destination Name";

  static const String destinationNameRequired = "Destination name is required";

  static const String destination = "Destination";

  static const String regionNumber = "Region Number";

  static const String weightPerBag = "Weight Per Bag";

  static const String weightPerBagHint = "Enter weight per bag";

  static const String zones = "Zones";

  static const String zoneName = "Zone Name";

  static const String zoneNameHint = "Enter zone name";

  static const String addNewZoneButton = "+ Add New Zone";

  static const String addNewRegionButton = "+ Add New Region";

  static const String addNewZone = "+ Add New Zone";

  static const String addNewRegion = "+ Add New Region";

  static const String regionCode = "Region Code";

  static const String regionCodeHint = "Enter region code";

  static const String districts = "Districts";

  static const String addNewDistrictButton = "+ Add New District";

  static const String districtName = "District Name";

  static const String addNewDistrict = "+ Add New District";

  static const String districtNameHint = "Enter district name";

  static const String selectRegion = "Select Region";

  static const String selectRegionHint = "Select a region";

  static const String selectZone = "Select Zone";

  static const String selectZoneHint = "Select a zone";

  static const String stations = "Stations";

  static const String addNewStationButton = "+ Add New Station";

  static const String stationName = "Station Name";

  static const String addNewStation = "+ Add New Station";

  static const String stationNameHint = "Enter station name";

  static const String region = "Region";

  static const String enterRegionName = "Enter region name";

  static const String zone = "Zone";

  static const String enterZoneName = "Enter zone name";

  static const String enterStationName = "Enter station name";

  static const String enterPlaceName = "Enter place name";

  static const String placeName = "Place Name";

  static const String kilometers = "Kilometers";

  static const String enterKilometers = "Enter kilometers";

  static const String noPlacesAddedYet = "No places added yet";

  static const String actions = "Actions";

  static const String selectDistrict = "Select District";

  static const String selectADistrict = "Select a district";

  static const String district = "District";

  static const String totalNumberOfBags = "Total Number of Bags";
  static const String munshianaTitleLabel = "Munshiana ";
  static const String munshianaLabel = "Munshiana";
  static const String munshianaHint = "Enter munshiana details";
  static const String brokerFeesHint = "Enter broker fees";
  static const String brokerFeesLabel = "Broker Fees";
  static const String brokerFeesTitleLabel = "Broker Fees Title";
  static const String cashLabel = "Cash";
  static const String dieselCardLabel = "Diesel Card";
  static const String cashAmountLabel = "Cash Amount";
  static const String dieselLitersLabel = "Diesel Liters";
  static const String dieselCompanyLabel = "Diesel Company";
  static const String dieselCompanyHint = "Enter diesel company name";
  static const String chequeLabel = "Cheque";
  static const String chequeAmountLabel = "Cheque Amount";
  static const String bankNameLabel = "Bank Name";
  static const String chequeNumberLabel = "Cheque Number";
  static const String companyFreightTitleLabel = "Company Freight (NLC Amount)";
  static const String companyFreightHint =
      "Amount received from NLC (e.g. 250000)";
  static const String companyFreightLabel = "Company Freight";
  static const String voucherStatusHint = "Enter voucher status";

  static const String cashAmountHint = "Enter cash amount";

  static const String dieselLitersHint = "Enter diesel liters";

  static const String chequeNumberHint = "Enter cheque number";

  static const String bankNameHint = "Enter bank name";

  static const String chequeAmountHint = "Enter cheque amount";

  static const String success = "Success";

  static const String error = "Error";

  static const String noAttachment = "No Attachment";

  static const String fileOpening = "File Opening";

  static const String downloadFailed = "Download Failed";

  static const String loadFailed = "Load Failed";

  static const String uploadFailed = "Upload Failed";

  static const String errorS = "error";

  static const String date = "Date";

  // Payment restriction messages
  static const String cannotEditPaidTransaction =
      "Cannot Edit Paid Transaction";
  static const String cannotDeletePaidTransaction =
      "Cannot Delete Paid Transaction";
  static const String paidTransactionEditMessage =
      "Paid transactions cannot be edited. Only partial payments can be modified.";
  static const String paidTransactionDeleteMessage =
      "Paid transactions cannot be deleted. Only partial payments can be modified.";
  static const String totalFreightValidationMessage =
      "Total freight cannot be less than already paid amount.";

  static var deliveryModeList = ['By Road', 'By Air'];

  // Bills Section Constants
  static const String billsTitle = "Bills";
  static const String addNewBillButton = "+ Add New Bill";
  static const String saveBillButton = "Save Bill";
  static const String generateBillButton = "Generate Bill";
  static const String billNumber = "Bill Number";
  static const String billDate = "Bill Date";
  static const String billStatus = "Bill Status";
  static const String totalAmount = "Total Amount";
  static const String linkedInvoices = "Linked Invoices";
  static const String numberOfInvoices = "Number of Invoices";
  static const String billNotes = "Bill Notes";
  static const String markAsCompleted = "Mark as Completed";
  static const String markAsPending = "Mark as Pending";
  static const String viewBillDetails = "View Bill Details";
  static const String editBill = "Edit Bill";
  static const String deleteBill = "Delete Bill";
  static const String deleteBillTitle = "Delete Bill";
  static const String noBillsFound = "No bills found. Create your first bill!";
  static const String billCreatedSuccessfully = "Bill created successfully";
  static const String billUpdatedSuccessfully = "Bill updated successfully";
  static const String billDeletedSuccessfully = "Bill deleted successfully";
  static const String confirmDeleteBill =
      "Are you sure you want to delete this bill?";
  static const String billStatusUpdated = "Bill status updated successfully";

  // Bill Table Headers
  static const String billNumberHeader = "Bill Number";
  static const String billDateHeader = "Bill Date";
  static const String totalAmountHeader = "Total Amount";
  static const String numberOfInvoicesHeader = "Invoices";
  static const String billStatusHeader = "Status";
  static const String billActionHeader = "Action";
}
