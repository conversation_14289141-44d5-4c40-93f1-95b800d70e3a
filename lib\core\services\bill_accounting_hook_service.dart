import 'dart:developer';
import '../../models/finance/bill_model.dart';
import '../../firebase_service/accounting/general_ledger_firebase_service.dart';
import '../../firebase_service/accounting/chart_of_accounts_firebase_service.dart';
import '../../firebase_service/accounting/journal_entry_firebase_service.dart';
import 'automatic_journal_entry_service.dart';
import 'transaction_account_mapping_service.dart';
import 'bill_journal_integration_service.dart';

/// Service that hooks into bill creation and updates to automatically generate journal entries
class BillAccountingHookService {
  static BillAccountingHookService? _instance;
  late final BillJournalIntegrationService _integrationService;
  late final AutomaticJournalEntryService _automaticJournalService;
  late final GeneralLedgerFirebaseService _generalLedgerService;
  late final TransactionAccountMappingService _mappingService;
  late final ChartOfAccountsFirebaseService _chartOfAccountsService;
  late final JournalEntryFirebaseService _journalEntryService;

  BillAccountingHookService._internal() {
    _initializeServices();
  }

  factory BillAccountingHookService() {
    _instance ??= BillAccountingHookService._internal();
    return _instance!;
  }

  void _initializeServices() {
    _chartOfAccountsService = ChartOfAccountsFirebaseService();
    _journalEntryService = JournalEntryFirebaseService();
    _generalLedgerService = GeneralLedgerFirebaseService();
    _mappingService = TransactionAccountMappingService(_chartOfAccountsService);
    _automaticJournalService = AutomaticJournalEntryService(
      _journalEntryService,
      _chartOfAccountsService,
    );
    _integrationService = BillJournalIntegrationService(
      _automaticJournalService,
      _generalLedgerService,
      _mappingService,
    );
  }

  /// Hook method to be called after bill creation
  Future<void> onBillCreated(BillModel bill) async {
    try {
      log('Bill accounting hook triggered for: ${bill.billNumber}');

      // Validate bill for journal entry generation
      final validation =
          await _integrationService.validateBillForJournalEntry(bill);
      if (!validation.isValid) {
        log('Bill validation failed: ${validation.issuesText}');
        return;
      }

      // Check if journal entries already exist
      final hasExisting = await _integrationService.hasExistingJournalEntries(
        bill.billId,
        bill.companyUid,
      );

      if (hasExisting) {
        log('Journal entries already exist for bill: ${bill.billId}');
        return;
      }

      // Process the bill transaction
      final success = await _integrationService.processBillTransaction(bill);
      if (success) {
        log('Successfully created journal entries for bill: ${bill.billId}');
      } else {
        log('Failed to create journal entries for bill: ${bill.billId}');
      }
    } catch (e) {
      log('Error in bill accounting hook: $e');
    }
  }

  /// Hook method to be called before bill deletion
  Future<void> onBillDeleted(BillModel bill) async {
    try {
      log('Bill deletion hook triggered for: ${bill.billNumber}');

      // Reverse any existing journal entries
      final success = await _integrationService.reverseBillJournalEntries(
        bill.billId,
        bill.companyUid,
      );

      if (success) {
        log('Successfully reversed journal entries for deleted bill: ${bill.billId}');
      } else {
        log('Failed to reverse journal entries for deleted bill: ${bill.billId}');
      }
    } catch (e) {
      log('Error in bill deletion hook: $e');
    }
  }

  /// Hook method to be called after bill update
  Future<void> onBillUpdated(BillModel oldBill, BillModel newBill) async {
    try {
      log('Bill update hook triggered for: ${newBill.billNumber}');

      // If the amount or status changed, we need to handle journal entries
      if (oldBill.totalAmount != newBill.totalAmount ||
          oldBill.billStatus != newBill.billStatus) {
        // If amount changed, reverse old entries and create new ones
        if (oldBill.totalAmount != newBill.totalAmount) {
          await _integrationService.reverseBillJournalEntries(
            oldBill.billId,
            oldBill.companyUid,
          );
          await onBillCreated(newBill);
        }

        // If status changed, handle payment journal entries
        if (oldBill.billStatus != newBill.billStatus) {
          await _integrationService.processBillStatusUpdate(
            newBill,
            oldBill.billStatus,
            newBill.billStatus,
          );
        }
      }
    } catch (e) {
      log('Error in bill update hook: $e');
    }
  }

  /// Hook method to be called when bill status is updated
  Future<void> onBillStatusUpdated(
      String billId, String uid, String oldStatus, String newStatus) async {
    try {
      log('Bill status update hook triggered for bill: $billId from $oldStatus to $newStatus');

      // Get the bill details first
      // Note: We would need to add a getBillById method to BillFirebaseService
      // For now, we'll create a placeholder implementation

      // If status changed to Completed, create payment journal entry
      if (newStatus == 'Completed' && oldStatus != 'Completed') {
        // Create a minimal bill model for payment processing
        // In a real implementation, we would fetch the full bill details
        log('Processing bill payment journal entry for bill: $billId');

        // This would require the full bill details to create proper journal entries
        // For now, we'll log the action
        log('Bill payment journal entry processing would happen here');
      }
    } catch (e) {
      log('Error in bill status update hook: $e');
    }
  }

  /// Batch process existing bills to create journal entries
  Future<BatchProcessResult> processExistingBills(List<BillModel> bills) async {
    try {
      log('Processing ${bills.length} existing bills for journal entries');
      return await _integrationService.batchProcessBillTransactions(bills);
    } catch (e) {
      log('Error processing existing bills: $e');
      return BatchProcessResult(
        totalProcessed: bills.length,
        successCount: 0,
        failureCount: bills.length,
        failedTransactionIds: bills.map((b) => b.billId).toList(),
      );
    }
  }

  /// Get journal entries for a specific bill
  Future<List<dynamic>> getBillJournalEntries(String billId, String uid) async {
    return await _integrationService.getJournalEntriesForBill(billId, uid);
  }

  /// Check if accounting integration is properly configured
  Future<bool> isAccountingConfigured(String uid) async {
    try {
      final mapping = await _mappingService.getBillAccountMapping(uid);
      return mapping != null;
    } catch (e) {
      log('Error checking accounting configuration: $e');
      return false;
    }
  }

  /// Get configuration status for UI display
  Future<AccountingConfigurationStatus> getConfigurationStatus(
      String uid) async {
    try {
      final mapping = await _mappingService.getBillAccountMapping(uid);

      if (mapping == null) {
        return AccountingConfigurationStatus(
          isConfigured: false,
          missingAccounts: [
            'Accounts Payable Account',
            'Cash Account',
            'Expense Account'
          ],
          message:
              'Required accounts for bill journal entries are not configured',
        );
      }

      return AccountingConfigurationStatus(
        isConfigured: true,
        missingAccounts: [],
        message: 'Accounting integration is properly configured',
      );
    } catch (e) {
      return AccountingConfigurationStatus(
        isConfigured: false,
        missingAccounts: [],
        message: 'Error checking configuration: $e',
      );
    }
  }
}

/// Configuration status for accounting integration
class AccountingConfigurationStatus {
  final bool isConfigured;
  final List<String> missingAccounts;
  final String message;

  AccountingConfigurationStatus({
    required this.isConfigured,
    required this.missingAccounts,
    required this.message,
  });
}
