import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/widgets/searchfield.dart';
import 'package:logestics/firebase_service/invoices/invoice_crud_firebase_service.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';

import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/widgets/data_table_cell.dart';
import 'package:logestics/core/utils/widgets/pagination_widget.dart';
import 'package:logestics/core/utils/widgets/loading_indicator.dart';
import 'package:intl/intl.dart';
import 'package:logestics/bindings/app_bindings.dart';
import 'package:logestics/models/invoice_model.dart';

import '../../../company/presentation/conrollers/company_controller.dart';
import '../../../invoices/repositories/invoice_repository.dart';
import '../../../invoices/use_cases/delete_invoice_use_case.dart';
import '../../../invoices/use_cases/update_invoice_status_use_case.dart';
import '../../../invoices/presentation/views/invoice_form_view.dart';
import '../controllers/billing_list_controller.dart';
import 'billing_excel_export_dialog.dart';

class BillingList extends StatelessWidget {
  const BillingList({super.key, required this.titleShow});

  String formatDate(DateTime? date) {
    if (date == null) return '';
    return DateFormat('dd/MM/yyyy').format(date);
  }

  void _navigateToInvoiceForm({InvoiceModel? invoice, bool readOnly = false}) {
    // Reset the bindings to ensure all dependencies are properly registered
    AppBindings().dependencies();

    // Navigate to the invoice form
    Get.to(
      () => InvoiceFormView(
        currentInvoice: invoice,
        readOnly: readOnly,
      ),
    )?.then((_) {
      // Refresh the billing list when returning from the form
      final controller = Get.find<BillingListController>();
      controller.refreshData();
    });
  }

  void _showExcelExportModal() {
    Get.dialog(
      BillingExcelExportDialog(),
      barrierDismissible: true,
    );
  }

  final bool titleShow;
  @override
  Widget build(BuildContext context) {
    BillingListController billingListController = Get.put(
      BillingListController(
        deleteInvoiceUseCase: Get.put(
          DeleteInvoiceUseCase(
            Get.put(
              InvoiceRepositoryImpl(
                Get.put(
                  InvoiceCrudFirebaseService(),
                ),
              ),
            ),
          ),
        ),
        updateInvoiceStatusUseCase: Get.put(
          UpdateInvoiceStatusUseCase(
            Get.put(
              InvoiceRepositoryImpl(
                Get.put(
                  InvoiceCrudFirebaseService(),
                ),
              ),
            ),
          ),
        ),
      ),
    );

    // Handle screen activation and data refresh
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Call onScreenActivated to handle navigation scenarios
      billingListController.onScreenActivated();

      // Only call refreshData if we don't have any invoices loaded
      if (billingListController.companyController.company.invoices.isEmpty) {
        billingListController.refreshData();
      }
    });

    var width = Get.width;

    notifier = Provider.of(context, listen: true);

    return Container(
      decoration: BoxDecoration(
        color: notifier.getBgColor,
        borderRadius: BorderRadius.circular(10),
      ),
      child: GetBuilder<CompanyController>(
        builder: (companyController) {
          return GetBuilder<BillingListController>(
            builder: (billingListController) {
              return Container(
                padding: const EdgeInsets.symmetric(vertical: 15),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 15),
                      child: width < 650
                          ? Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                titleShow == false
                                    ? InkWell(
                                        onTap: () {
                                          _showExcelExportModal();
                                        },
                                        child: Text(
                                          "Create Bill",
                                          style:
                                              AppTextStyles.addNewInvoiceStyle,
                                        ),
                                      )
                                    : Text(
                                        "Billing", // Custom title for billing section
                                        overflow: TextOverflow.ellipsis,
                                        style: AppTextStyles.invoiceTitleStyle
                                            .copyWith(
                                          color: notifier.text,
                                        ),
                                      ),
                                const SizedBox(height: 5),
                                Searchfield()
                              ],
                            )
                          : Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                titleShow == false
                                    ? InkWell(
                                        onTap: () {
                                          _showExcelExportModal();
                                        },
                                        child: Text(
                                          "Create Bill",
                                          style:
                                              AppTextStyles.addNewInvoiceStyle,
                                        ),
                                      )
                                    : Text(
                                        "Billing",
                                        overflow: TextOverflow.ellipsis,
                                        style: AppTextStyles.invoiceTitleStyle
                                            .copyWith(
                                          color: notifier.text,
                                        ),
                                      ),
                                const Spacer(),
                                SizedBox(
                                  width: 300,
                                  child: Searchfield(),
                                ),
                              ],
                            ),
                    ),
                    const SizedBox(height: 20),
                    // Status indicator showing current filter
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 15),
                      child: Obx(() => Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              color: const Color(0xFFe85542)
                                  .withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(
                                color: const Color(0xFFe85542)
                                    .withValues(alpha: 0.3),
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Container(
                                  width: 8,
                                  height: 8,
                                  decoration: const BoxDecoration(
                                    color: Color(0xFFe85542),
                                    shape: BoxShape.circle,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Showing: ${billingListController.selectedStatus.value ?? "All"} Invoices',
                                  style: TextStyle(
                                    color: const Color(0xFFe85542),
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          )),
                    ),
                    const SizedBox(height: 15),
                    // Data table
                    Expanded(
                      child: Obx(() {
                        if (billingListController.isAnyLoading) {
                          return const Center(child: LoadingIndicator());
                        }

                        if (billingListController.paginatedInvoices.isEmpty) {
                          return Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.receipt_long_outlined,
                                  size: 64,
                                  color: Colors.grey.shade400,
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'No billing invoices found',
                                  style: TextStyle(
                                    fontSize: 18,
                                    color: Colors.grey.shade600,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Invoices with "Pending Billing" status will appear here',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey.shade500,
                                  ),
                                ),
                              ],
                            ),
                          );
                        }

                        return SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: SizedBox(
                            width: width < 1200 ? 1200 : width,
                            child: ListView(
                              shrinkWrap: true,
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 15),
                              children: [
                                Table(
                                  border: TableBorder.all(
                                    color: notifier.isDark
                                        ? Colors.grey.shade700
                                        : Colors.grey.shade300,
                                    width: 1,
                                  ),
                                  columnWidths: const {
                                    0: FlexColumnWidth(0.8), // SN.
                                    1: FlexColumnWidth(1.2), // Order Date
                                    2: FlexColumnWidth(1.2), // Truck No
                                    3: FlexColumnWidth(1.2), // Bilty No
                                    4: FlexColumnWidth(
                                        1.5), // Convey Note Number
                                    5: FlexColumnWidth(1.3), // Product TAS NO
                                    6: FlexColumnWidth(1.3), // Destination
                                    7: FlexColumnWidth(1.0), // No of Bags
                                    8: FlexColumnWidth(1.0), // Weight
                                    9: FlexColumnWidth(0.8), // KM
                                    10: FlexColumnWidth(1.2), // District
                                    11: FlexColumnWidth(1.5), // Actions
                                  },
                                  children: [
                                    // Table header
                                    TableRow(
                                      decoration: BoxDecoration(
                                        color: notifier.getHoverColor,
                                      ),
                                      children: [
                                        DataTableHeaderCell(
                                          text: 'SN.',
                                          textColor: notifier.text,
                                        ),
                                        DataTableHeaderCell(
                                          text: 'Order Date',
                                          textColor: notifier.text,
                                        ),
                                        DataTableHeaderCell(
                                          text: 'Truck No',
                                          textColor: notifier.text,
                                        ),
                                        DataTableHeaderCell(
                                          text: 'Bilty No',
                                          textColor: notifier.text,
                                        ),
                                        DataTableHeaderCell(
                                          text: 'Convey Note Number',
                                          textColor: notifier.text,
                                        ),
                                        DataTableHeaderCell(
                                          text: 'Product TAS NO',
                                          textColor: notifier.text,
                                        ),
                                        DataTableHeaderCell(
                                          text: 'Destination',
                                          textColor: notifier.text,
                                        ),
                                        DataTableHeaderCell(
                                          text: 'No of Bags',
                                          textColor: notifier.text,
                                        ),
                                        DataTableHeaderCell(
                                          text: 'Weight',
                                          textColor: notifier.text,
                                        ),
                                        DataTableHeaderCell(
                                          text: 'KM',
                                          textColor: notifier.text,
                                        ),
                                        DataTableHeaderCell(
                                          text: 'District',
                                          textColor: notifier.text,
                                        ),
                                        DataTableHeaderCell(
                                          text: 'Actions',
                                          textColor: notifier.text,
                                        ),
                                      ],
                                    ),
                                    // Table rows
                                    ...billingListController.paginatedInvoices
                                        .asMap()
                                        .entries
                                        .map((entry) {
                                      final index = entry.key;
                                      final invoice = entry.value;
                                      return TableRow(
                                        children: [
                                          DataTableCell(
                                            text: ((billingListController
                                                                .currentPage
                                                                .value -
                                                            1) *
                                                        billingListController
                                                            .itemsPerPage
                                                            .value +
                                                    index +
                                                    1)
                                                .toString(), // SN.
                                          ),
                                          DataTableCell(
                                            text: formatDate(invoice
                                                .orderDate), // Order Date
                                          ),
                                          DataTableCell(
                                            text:
                                                invoice.truckNumber, // Truck No
                                          ),
                                          DataTableCell(
                                            text:
                                                invoice.biltyNumber, // Bilty No
                                          ),
                                          DataTableCell(
                                            text: invoice
                                                .conveyNoteNumber, // Convey Note Number
                                          ),
                                          DataTableCell(
                                            text: invoice
                                                .tasNumber, // Product TAS NO
                                          ),
                                          DataTableCell(
                                            text: invoice
                                                .stationName, // Destination
                                          ),
                                          DataTableCell(
                                            text: invoice.numberOfBags
                                                .toString(), // No of Bags
                                          ),
                                          DataTableCell(
                                            text: invoice.weightPerBag
                                                .toString(), // Weight
                                          ),
                                          DataTableCell(
                                            text: invoice.distanceInKilometers
                                                .toString(), // KM
                                          ),
                                          DataTableCell(
                                            text: invoice
                                                .districtName, // District
                                          ),
                                          TableCell(
                                            child: Padding(
                                              padding:
                                                  const EdgeInsets.all(8.0),
                                              child: Row(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  IconButton(
                                                    icon: const Icon(
                                                        Icons.visibility,
                                                        size: 16),
                                                    onPressed: () =>
                                                        _navigateToInvoiceForm(
                                                      invoice: invoice,
                                                      readOnly: true,
                                                    ),
                                                    tooltip: 'View Invoice',
                                                  ),
                                                  IconButton(
                                                    icon: const Icon(Icons.edit,
                                                        size: 16),
                                                    onPressed: () =>
                                                        _navigateToInvoiceForm(
                                                      invoice: invoice,
                                                    ),
                                                    tooltip: 'Edit Invoice',
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      );
                                    }),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        );
                      }),
                    ),
                    const SizedBox(height: 20),
                    // Pagination
                    Obx(() => PaginationWidget(
                          currentPage: billingListController.currentPage.value,
                          totalPages: billingListController.totalPages,
                          itemsPerPage:
                              billingListController.itemsPerPage.value,
                          onPageChanged: (page) =>
                              billingListController.setCurrentPage(page),
                          onItemsPerPageChanged: (count) =>
                              billingListController.setItemsPerPage(count),
                        )),
                    titleShow == false
                        ? Container()
                        : Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 15),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                InkWell(
                                  onTap: () {
                                    _showExcelExportModal();
                                  },
                                  child: Text(
                                    "Create Bill",
                                    style: AppTextStyles.addNewInvoiceStyle,
                                  ),
                                ),
                              ],
                            ),
                          ),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }
}
