import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';
import 'package:logestics/models/locations/region_model.dart';

class RegionFirebaseService {
  late FirebaseFirestore _firestore;

  RegionFirebaseService() {
    _firestore = FirebaseFirestore.instance;
  }

  Future<bool> checkRegionExists(String regionName) async {
    try {
      final snapshot = await _firestore
          .collection(AppCollection.regionsCollection)
          .where('regionName', isEqualTo: regionName)
          .limit(1)
          .get();

      return snapshot.docs.isNotEmpty;
    } catch (e) {
      log('Error checking region existence: $e');
      rethrow;
    }
  }

  Future<void> createRegion(RegionModel region) async {
    try {
      final regionRef =
          _firestore.collection(AppCollection.regionsCollection).doc();
      final regionId = regionRef.id;

      // Create a new region
      final newRegion = RegionModel(
        regionId: regionId,
        regionName: region.regionName,
        zoneId: region.zoneId,
        zoneName: region.zoneName,
        regionCode: region.regionCode,
        createdAt: region.createdAt,
      );

      log('Creating region: $regionId in zone: ${region.zoneId}');
      await regionRef.set(newRegion.toJson());
      log('Successfully created region: $regionId');
    } catch (e) {
      log('Error creating region: $e');
      rethrow;
    }
  }

  Future<List<RegionModel>> getRegions() async {
    try {
      log('Fetching regions');
      final snapshot = await _firestore
          .collection(AppCollection.regionsCollection)
          .orderBy('createdAt', descending: true)
          .get();

      final regions =
          snapshot.docs.map((doc) => RegionModel.fromJson(doc.data())).toList();

      log('Successfully fetched ${regions.length} regions');
      return regions;
    } catch (e) {
      log('Error fetching regions: $e');
      rethrow;
    }
  }

  Stream<List<RegionModel>> listenToRegions() {
    return _firestore
        .collection(AppCollection.regionsCollection)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map((doc) => RegionModel.fromJson(doc.data()))
              .toList(),
        );
  }

  Future<void> updateRegion(RegionModel region) async {
    try {
      log('Updating region: ${region.regionId}');

      // Check if region exists
      final regionDoc = await _firestore
          .collection(AppCollection.regionsCollection)
          .doc(region.regionId)
          .get();

      if (!regionDoc.exists) {
        throw Exception('Region not found');
      }

      await _firestore
          .collection(AppCollection.regionsCollection)
          .doc(region.regionId)
          .update(region.toJson());

      log('Successfully updated region: ${region.regionId}');
    } catch (e) {
      log('Error updating region: $e');
      rethrow;
    }
  }

  Future<void> deleteRegion(String regionId) async {
    try {
      log('Deleting region: $regionId');

      // Check if region exists
      final regionDoc = await _firestore
          .collection(AppCollection.regionsCollection)
          .doc(regionId)
          .get();

      if (!regionDoc.exists) {
        throw Exception('Region not found');
      }

      await _firestore
          .collection(AppCollection.regionsCollection)
          .doc(regionId)
          .delete();

      log('Successfully deleted region: $regionId');
    } catch (e) {
      log('Error deleting region: $e');
      rethrow;
    }
  }
}
