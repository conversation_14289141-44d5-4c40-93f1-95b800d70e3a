import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/firebase_service/firebase_auth_service.dart';
import 'package:logestics/models/asset/asset_model.dart';
import 'package:logestics/repositories/asset/asset_repository.dart';

class AssetFormController extends GetxController {
  final AssetRepository _assetRepository;
  final _authService = Get.find<FirebaseAuthService>();

  AssetFormController(this._assetRepository);

  String get currentUserId => _authService.currentUser?.uid ?? '';

  // Form key for validation
  final formKey = GlobalKey<FormState>();

  // Text controllers
  final nameController = TextEditingController();
  final brandController = TextEditingController();
  final modelController = TextEditingController();
  final registrationNumberController = TextEditingController();
  final serialNumberController = TextEditingController();
  final purchaseCostController = TextEditingController();
  final vendorController = TextEditingController();
  final supplierController = TextEditingController();
  final locationController = TextEditingController();
  final departmentController = TextEditingController();
  final estimatedUsefulLifeController = TextEditingController();
  final currentValueController = TextEditingController();
  final notesController = TextEditingController();

  // Observable variables
  final selectedType = ''.obs;
  final selectedStatus = ''.obs;
  final selectedDepreciationMethod = ''.obs;
  final purchaseDate = Rx<DateTime?>(null);
  final isCurrentValueManual = false.obs;
  final attachmentUrls = <String>[].obs;
  final isLoading = false.obs;
  final isEditMode = false.obs;

  // File handling
  final selectedFiles = <File>[].obs;
  final selectedFileBytes = <Uint8List>[].obs;
  final selectedFileNames = <String>[].obs;

  // Current asset being edited
  AssetModel? currentAsset;

  @override
  void onInit() {
    super.onInit();
    // Set default values
    selectedType.value = AssetType.allTypes.first;
    selectedStatus.value = AssetStatus.allStatuses.first;
    selectedDepreciationMethod.value = DepreciationMethod.allMethods.first;
    purchaseDate.value = DateTime.now();

    // Listen to purchase cost changes for auto-calculation
    purchaseCostController.addListener(_onPurchaseCostChanged);
    estimatedUsefulLifeController.addListener(_onUsefulLifeChanged);

    // Listen to manual value toggle
    isCurrentValueManual.listen(_onManualValueToggle);
  }

  @override
  void onClose() {
    nameController.dispose();
    brandController.dispose();
    modelController.dispose();
    registrationNumberController.dispose();
    serialNumberController.dispose();
    purchaseCostController.dispose();
    vendorController.dispose();
    supplierController.dispose();
    locationController.dispose();
    departmentController.dispose();
    estimatedUsefulLifeController.dispose();
    currentValueController.dispose();
    notesController.dispose();
    super.onClose();
  }

  /// Initialize form for editing an existing asset
  void initializeForEdit(AssetModel asset) {
    currentAsset = asset;
    isEditMode.value = true;

    nameController.text = asset.name;
    brandController.text = asset.brand;
    modelController.text = asset.model;
    registrationNumberController.text = asset.registrationNumber;
    serialNumberController.text = asset.serialNumber;
    purchaseCostController.text = asset.purchaseCost.toString();
    vendorController.text = asset.vendor;
    supplierController.text = asset.supplier;
    locationController.text = asset.location;
    departmentController.text = asset.department;
    estimatedUsefulLifeController.text = asset.estimatedUsefulLife.toString();
    currentValueController.text = asset.currentValue.toString();
    notesController.text = asset.notes;

    selectedType.value = asset.type;
    selectedStatus.value = asset.status;
    selectedDepreciationMethod.value = asset.depreciationMethod;
    purchaseDate.value = asset.purchaseDate;
    isCurrentValueManual.value = asset.isCurrentValueManual;
    attachmentUrls.value = List<String>.from(asset.attachmentUrls);
  }

  /// Clear form for new asset creation
  void clearForm() {
    currentAsset = null;
    isEditMode.value = false;

    nameController.clear();
    brandController.clear();
    modelController.clear();
    registrationNumberController.clear();
    serialNumberController.clear();
    purchaseCostController.clear();
    vendorController.clear();
    supplierController.clear();
    locationController.clear();
    departmentController.clear();
    estimatedUsefulLifeController.clear();
    currentValueController.clear();
    notesController.clear();

    selectedType.value = AssetType.allTypes.first;
    selectedStatus.value = AssetStatus.allStatuses.first;
    selectedDepreciationMethod.value = DepreciationMethod.allMethods.first;
    purchaseDate.value = DateTime.now();
    isCurrentValueManual.value = false;
    attachmentUrls.clear();
    selectedFiles.clear();
    selectedFileBytes.clear();
    selectedFileNames.clear();
  }

  /// Auto-calculate current value when purchase cost or useful life changes
  void _onPurchaseCostChanged() {
    if (!isCurrentValueManual.value) {
      _calculateCurrentValue();
    }
  }

  void _onUsefulLifeChanged() {
    if (!isCurrentValueManual.value) {
      _calculateCurrentValue();
    }
  }

  void _onManualValueToggle(bool isManual) {
    if (!isManual) {
      _calculateCurrentValue();
    }
  }

  void _calculateCurrentValue() {
    final purchaseCost = double.tryParse(purchaseCostController.text) ?? 0.0;
    final usefulLife = int.tryParse(estimatedUsefulLifeController.text) ?? 1;

    if (purchaseCost > 0 && usefulLife > 0 && purchaseDate.value != null) {
      final tempAsset = AssetModel(
        id: '',
        name: '',
        type: '',
        brand: '',
        model: '',
        registrationNumber: '',
        serialNumber: '',
        purchaseDate: purchaseDate.value!,
        purchaseCost: purchaseCost,
        vendor: '',
        supplier: '',
        location: '',
        department: '',
        status: '',
        depreciationMethod: selectedDepreciationMethod.value,
        estimatedUsefulLife: usefulLife,
        currentValue: purchaseCost,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final calculatedValue = tempAsset.calculateDepreciatedValue();
      currentValueController.text = calculatedValue.toStringAsFixed(2);
    }
  }

  /// Add files for upload
  void addFiles(List<File> files) {
    selectedFiles.addAll(files);
  }

  /// Add file bytes for web upload
  void addFileBytes(List<Uint8List> bytes, List<String> names) {
    selectedFileBytes.addAll(bytes);
    selectedFileNames.addAll(names);
  }

  /// Remove selected file
  void removeSelectedFile(int index) {
    if (index < selectedFiles.length) {
      selectedFiles.removeAt(index);
    } else {
      final webIndex = index - selectedFiles.length;
      if (webIndex < selectedFileBytes.length) {
        selectedFileBytes.removeAt(webIndex);
        selectedFileNames.removeAt(webIndex);
      }
    }
  }

  /// Remove existing attachment
  Future<void> removeAttachment(String attachmentUrl) async {
    if (currentAsset == null) return;

    try {
      isLoading.value = true;
      final result = await _assetRepository.removeAttachment(
        currentAsset!.id,
        attachmentUrl,
      );

      result.fold(
        (failure) => SnackbarUtils.showError('Error', failure.message),
        (success) {
          attachmentUrls.remove(attachmentUrl);
          SnackbarUtils.showSuccess(
              'Success', 'Attachment removed successfully');
        },
      );
    } catch (e) {
      log('Error removing attachment: $e');
      SnackbarUtils.showError('Error', 'Failed to remove attachment');
    } finally {
      isLoading.value = false;
    }
  }

  /// Validate form
  bool validateForm() {
    if (!formKey.currentState!.validate()) {
      return false;
    }

    if (selectedType.value.isEmpty) {
      SnackbarUtils.showError('Error', 'Please select asset type');
      return false;
    }

    if (selectedStatus.value.isEmpty) {
      SnackbarUtils.showError('Error', 'Please select asset status');
      return false;
    }

    if (selectedDepreciationMethod.value.isEmpty) {
      SnackbarUtils.showError('Error', 'Please select depreciation method');
      return false;
    }

    if (purchaseDate.value == null) {
      SnackbarUtils.showError('Error', 'Please select purchase date');
      return false;
    }

    final purchaseCost = double.tryParse(purchaseCostController.text);
    if (purchaseCost == null || purchaseCost <= 0) {
      SnackbarUtils.showError('Error', 'Please enter valid purchase cost');
      return false;
    }

    final usefulLife = int.tryParse(estimatedUsefulLifeController.text);
    if (usefulLife == null || usefulLife <= 0) {
      SnackbarUtils.showError('Error', 'Please enter valid useful life');
      return false;
    }

    final currentValue = double.tryParse(currentValueController.text);
    if (currentValue == null || currentValue < 0) {
      SnackbarUtils.showError('Error', 'Please enter valid current value');
      return false;
    }

    return true;
  }

  /// Save asset (create or update)
  Future<bool> saveAsset() async {
    if (!validateForm()) return false;

    try {
      isLoading.value = true;

      final asset = AssetModel(
        id: currentAsset?.id ?? '',
        name: nameController.text.trim(),
        type: selectedType.value,
        brand: brandController.text.trim(),
        model: modelController.text.trim(),
        registrationNumber: registrationNumberController.text.trim(),
        serialNumber: serialNumberController.text.trim(),
        purchaseDate: purchaseDate.value!,
        purchaseCost: double.parse(purchaseCostController.text),
        vendor: vendorController.text.trim(),
        supplier: supplierController.text.trim(),
        location: locationController.text.trim(),
        department: departmentController.text.trim(),
        status: selectedStatus.value,
        depreciationMethod: selectedDepreciationMethod.value,
        estimatedUsefulLife: int.parse(estimatedUsefulLifeController.text),
        currentValue: double.parse(currentValueController.text),
        isCurrentValueManual: isCurrentValueManual.value,
        attachmentUrls: attachmentUrls.toList(),
        notes: notesController.text.trim(),
        createdAt: currentAsset?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
        uid: currentAsset?.uid ??
            currentUserId, // CRITICAL FIX: Preserve UID during updates, use current user for new assets
      );

      final result = isEditMode.value
          ? await _assetRepository.updateAsset(
              asset,
              files: selectedFiles.isNotEmpty ? selectedFiles : null,
              fileBytes:
                  selectedFileBytes.isNotEmpty ? selectedFileBytes : null,
              fileNames:
                  selectedFileNames.isNotEmpty ? selectedFileNames : null,
            )
          : await _assetRepository.createAsset(
              asset,
              files: selectedFiles.isNotEmpty ? selectedFiles : null,
              fileBytes:
                  selectedFileBytes.isNotEmpty ? selectedFileBytes : null,
              fileNames:
                  selectedFileNames.isNotEmpty ? selectedFileNames : null,
            );

      return result.fold(
        (failure) {
          log('Error saving asset: ${failure.message}');
          SnackbarUtils.showError('Error', failure.message);
          return false;
        },
        (success) {
          log('Asset saved successfully');
          SnackbarUtils.showSuccess(
            'Success',
            isEditMode.value
                ? 'Asset updated successfully'
                : 'Asset created successfully',
          );
          return true;
        },
      );
    } catch (e) {
      log('Error saving asset: $e');
      SnackbarUtils.showError('Error', 'Failed to save asset');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// Validation methods
  String? validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }

  String? validateNumber(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    if (double.tryParse(value) == null) {
      return 'Please enter a valid number';
    }
    if (double.parse(value) < 0) {
      return '$fieldName cannot be negative';
    }
    return null;
  }

  String? validatePositiveNumber(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    final number = double.tryParse(value);
    if (number == null) {
      return 'Please enter a valid number';
    }
    if (number <= 0) {
      return '$fieldName must be greater than 0';
    }
    return null;
  }

  String? validateInteger(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    final number = int.tryParse(value);
    if (number == null) {
      return 'Please enter a valid whole number';
    }
    if (number <= 0) {
      return '$fieldName must be greater than 0';
    }
    return null;
  }
}
