import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../../../core/utils/widgets/my_text_field.dart';
import '../controllers/cash_flow_statement_controller.dart';

/// Form widget for Cash Flow Statement parameters
class CashFlowStatementFormWidget extends StatelessWidget {
  final CashFlowStatementController controller;

  const CashFlowStatementFormWidget({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Form(
      key: controller.formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Form header
          Row(
            children: [
              Icon(Icons.settings, color: Theme.of(context).primaryColor),
              const SizedBox(width: 8),
              Text(
                'Report Parameters',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Company name field
          MyTextFormField(
            controller: controller.companyNameController,
            labelText: 'Company Name',
            hintText: 'Enter company name',
            validator: controller.validateCompanyName,
          ),
          const SizedBox(height: 16),

          // Date range fields
          Row(
            children: [
              Expanded(
                child: MyTextFormField(
                  controller: controller.startDateController,
                  labelText: 'Start Date',
                  hintText: 'dd/MM/yyyy',
                  readOnly: true,
                  validator: controller.validateDateRange,
                  onTap: () => _selectDate(context, true),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: MyTextFormField(
                  controller: controller.endDateController,
                  labelText: 'End Date',
                  hintText: 'dd/MM/yyyy',
                  readOnly: true,
                  validator: controller.validateDateRange,
                  onTap: () => _selectDate(context, false),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Options section
          Text(
            'Report Options',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 12),

          // Method selection
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Cash Flow Method',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
                const SizedBox(height: 8),
                Obx(() => Column(
                      children: [
                        RadioListTile<bool>(
                          title: const Text('Indirect Method'),
                          subtitle: const Text(
                              'Starts with net income and adjusts for non-cash items'),
                          value: false,
                          groupValue: controller.useDirectMethod,
                          onChanged: (value) => controller.toggleDirectMethod(),
                          dense: true,
                        ),
                        RadioListTile<bool>(
                          title: const Text('Direct Method'),
                          subtitle: const Text(
                              'Shows actual cash receipts and payments'),
                          value: true,
                          groupValue: controller.useDirectMethod,
                          onChanged: (value) => controller.toggleDirectMethod(),
                          dense: true,
                        ),
                      ],
                    )),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // Additional options
          Obx(() => Column(
                children: [
                  CheckboxListTile(
                    title: const Text('Include Inactive Accounts'),
                    subtitle: const Text(
                        'Include accounts that are marked as inactive'),
                    value: controller.includeInactiveAccounts,
                    onChanged: (value) =>
                        controller.toggleIncludeInactiveAccounts(),
                    dense: true,
                  ),
                  CheckboxListTile(
                    title: const Text('Include Zero Balances'),
                    subtitle: const Text(
                        'Include accounts with zero balances in the report'),
                    value: controller.includeZeroBalances,
                    onChanged: (value) =>
                        controller.toggleIncludeZeroBalances(),
                    dense: true,
                  ),
                ],
              )),
          const SizedBox(height: 24),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: controller.generateReport,
                  icon: const Icon(Icons.analytics),
                  label: const Text('Generate Report'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              if (controller.hasReportData) ...[
                ElevatedButton.icon(
                  onPressed: controller.clearReport,
                  icon: const Icon(Icons.clear),
                  label: const Text('Clear'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey[600],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                        vertical: 12, horizontal: 16),
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  /// Show date picker
  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final controller = this.controller;
    final initialDate = DateTime.now();

    final selectedDate = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
      helpText: isStartDate ? 'Select Start Date' : 'Select End Date',
    );

    if (selectedDate != null) {
      final formattedDate = DateFormat('dd/MM/yyyy').format(selectedDate);
      if (isStartDate) {
        controller.startDateController.text = formattedDate;
      } else {
        controller.endDateController.text = formattedDate;
      }
    }
  }
}
