import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../../../../firebase_service/firebase_auth_service.dart';
import '../../../../../firebase_service/finance/company_firebase_service.dart';
import '../../../../../models/finance/account_model.dart';
import '../../../../../models/finance/account_transaction_model.dart';
import '../../../../../models/user_model.dart';
import '../../../../../services/pdf_generation_service.dart';
import '../../../../../core/utils/snackbar_utils.dart';
import '../../../../../core/utils/app_constants/texts/app_strings.dart';
import '../../repositories/account_repository.dart';
import '../../repositories/account_transaction_repository.dart';

class PDFGenerationController extends GetxController {
  final AccountRepository accountRepository;
  final AccountTransactionRepository transactionRepository;
  final CompanyFirebaseService companyService;
  final FirebaseAuthService authService;
  final PDFGenerationService pdfService;

  PDFGenerationController({
    required this.accountRepository,
    required this.transactionRepository,
    required this.companyService,
    required this.authService,
    required this.pdfService,
  });

  // Observable variables
  final isLoading = false.obs;
  final accounts = <AccountModel>[].obs;
  final selectedAccount = Rx<AccountModel?>(null);
  final transactions = <AccountTransactionModel>[].obs;
  final filteredTransactions = <AccountTransactionModel>[].obs;

  // Date range
  final startDate = Rx<DateTime?>(null);
  final endDate = Rx<DateTime?>(null);

  // PDF customization options
  final companyNameController = TextEditingController();
  final companyAddressController = TextEditingController();
  final customTitleController = TextEditingController();

  // Current user
  final currentUser = Rx<UserModel?>(null);

  // Account types for filtering
  final accountTypes = <String>[
    'All Accounts',
    'Cash',
    'Bank',
    'Loan',
    'Investment',
    'Other'
  ].obs;
  final selectedAccountType = 'All Accounts'.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeCurrentUser();
    loadAccounts();

    // Set default date range (last 30 days)
    final now = DateTime.now();
    endDate.value = now;
    startDate.value = now.subtract(const Duration(days: 30));
  }

  @override
  void onClose() {
    companyNameController.dispose();
    companyAddressController.dispose();
    customTitleController.dispose();
    super.onClose();
  }

  /// Initialize current user
  void _initializeCurrentUser() {
    final authUser = authService.currentUser;
    if (authUser != null) {
      _loadUserDetailsFromDatabase(authUser);
    } else {
      log('No authenticated user found');
      Get.snackbar('Error', 'Please log in to generate PDFs');
    }
  }

  /// Load user details from database to get proper company name
  Future<void> _loadUserDetailsFromDatabase(User authUser) async {
    try {
      final companyService = CompanyFirebaseService();
      final result = await companyService.getUserById(authUser.uid);

      result.fold(
        (failure) {
          // If user not found in Firestore, create basic user from auth
          log('User not found in Firestore, using auth data: ${failure.message}');
          _createUserFromAuth(authUser);
        },
        (userModel) {
          // User found in Firestore - use registered company name
          currentUser.value = userModel;
          companyNameController.text = userModel.companyName;
          log('PDF Controller: User loaded from Firestore with company: ${userModel.companyName}');
        },
      );
    } catch (e) {
      log('Error loading user details for PDF generation: $e');
      _createUserFromAuth(authUser);
    }
  }

  /// Fallback method to create user from auth data
  void _createUserFromAuth(User authUser) {
    currentUser.value = UserModel(
      uid: authUser.uid,
      email: authUser.email ?? '',
      companyName:
          authUser.displayName ?? _extractNameFromEmail(authUser.email ?? ''),
      phoneNumber: authUser.phoneNumber ?? '',
    );

    // Set default company name
    companyNameController.text = currentUser.value?.companyName ?? '';
    log('PDF Controller: Created user from auth: ${currentUser.value?.companyName}');
  }

  /// Extract name from email as fallback
  String _extractNameFromEmail(String email) {
    if (email.isEmpty) return 'Current User';

    // Extract name from email (before @)
    final name = email.split('@').first;

    // Capitalize first letter and replace dots/underscores with spaces
    return name
        .replaceAll(RegExp(r'[._]'), ' ')
        .split(' ')
        .map((word) => word.isNotEmpty
            ? '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}'
            : '')
        .join(' ')
        .trim();
  }

  /// Load all accounts
  Future<void> loadAccounts() async {
    try {
      isLoading.value = true;

      final result = await accountRepository.getAccounts();

      result.fold(
        (failure) {
          SnackbarUtils.showError(AppStrings.errorS, failure.message);
        },
        (accountList) {
          accounts.value = accountList;
          log('Loaded ${accountList.length} accounts');
        },
      );
    } catch (e) {
      log('Error loading accounts: $e');
      SnackbarUtils.showError(AppStrings.errorS, 'Failed to load accounts: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// Get filtered accounts by type
  List<AccountModel> get filteredAccounts {
    if (selectedAccountType.value == 'All Accounts') {
      return accounts;
    }

    return accounts.where((account) {
      // Simple filtering based on account name containing the type
      // You can enhance this based on your account categorization logic
      final accountName = account.name.toLowerCase();
      final type = selectedAccountType.value.toLowerCase();

      switch (type) {
        case 'cash':
          return accountName.contains('cash') || accountName.contains('petty');
        case 'bank':
          return accountName.contains('bank') ||
              accountName.contains('account');
        case 'loan':
          return accountName.contains('loan') || accountName.contains('credit');
        case 'investment':
          return accountName.contains('investment') ||
              accountName.contains('saving');
        default:
          return true;
      }
    }).toList();
  }

  /// Set selected account and load its transactions
  Future<void> setSelectedAccount(AccountModel account) async {
    selectedAccount.value = account;
    await loadTransactionsForAccount(account.id);
  }

  /// Load transactions for selected account
  Future<void> loadTransactionsForAccount(String accountId) async {
    try {
      isLoading.value = true;

      final result =
          await transactionRepository.getTransactionsForAccount(accountId);

      result.fold(
        (failure) {
          SnackbarUtils.showError(AppStrings.errorS, failure.message);
        },
        (transactionList) {
          transactions.value = transactionList;
          _applyDateFilter();
          log('Loaded ${transactionList.length} transactions for account');
        },
      );
    } catch (e) {
      log('Error loading transactions: $e');
      SnackbarUtils.showError(
          AppStrings.errorS, 'Failed to load transactions: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// Apply date filter to transactions
  void _applyDateFilter() {
    if (startDate.value == null || endDate.value == null) {
      filteredTransactions.value = transactions;
      return;
    }

    final start = startDate.value!;
    final end = endDate.value!;

    // Create start and end of day for accurate filtering
    final startOfDay = DateTime(start.year, start.month, start.day, 0, 0, 0);
    final endOfDay = DateTime(end.year, end.month, end.day, 23, 59, 59, 999);

    filteredTransactions.value = transactions.where((transaction) {
      final transactionDate = transaction.transactionDate;
      // Include transactions that fall within the selected date range (inclusive)
      // Use isAtSameMomentAs or comparison for exact date matching
      return (transactionDate.isAfter(startOfDay) ||
              transactionDate.isAtSameMomentAs(startOfDay)) &&
          (transactionDate.isBefore(endOfDay) ||
              transactionDate.isAtSameMomentAs(endOfDay));
    }).toList();

    log('Filtered ${filteredTransactions.length} transactions for date range: ${DateFormat('dd/MM/yyyy').format(start)} to ${DateFormat('dd/MM/yyyy').format(end)}');
  }

  /// Set date range
  void setDateRange(DateTime start, DateTime end) {
    startDate.value = start;
    endDate.value = end;
    _applyDateFilter();
  }

  /// Generate PDF
  Future<void> generatePDF() async {
    try {
      // Validation
      if (selectedAccount.value == null) {
        SnackbarUtils.showError('Error', 'Please select an account');
        return;
      }

      if (currentUser.value == null) {
        SnackbarUtils.showError('Error', 'User information not available');
        return;
      }

      if (startDate.value == null || endDate.value == null) {
        SnackbarUtils.showError('Error', 'Please select a date range');
        return;
      }

      isLoading.value = true;

      // Generate PDF
      final pdfBytes = await pdfService.generateAccountStatementPDF(
        account: selectedAccount.value!,
        transactions: filteredTransactions,
        currentUser: currentUser.value!,
        startDate: startDate.value!,
        endDate: endDate.value!,
        companyName: companyNameController.text.isNotEmpty
            ? companyNameController.text
            : null,
        companyAddress: companyAddressController.text.isNotEmpty
            ? companyAddressController.text
            : null,
        customTitle: customTitleController.text.isNotEmpty
            ? customTitleController.text
            : null,
      );

      // Generate filename
      final dateFormat = DateFormat('yyyy-MM-dd');
      final fileName = 'Account_Statement_${selectedAccount.value!.name}_'
          '${dateFormat.format(startDate.value!)}_to_'
          '${dateFormat.format(endDate.value!)}.pdf';

      // Preview PDF
      await pdfService.previewPDF(pdfBytes, fileName);

      SnackbarUtils.showSuccess('Success', 'PDF generated successfully');
    } catch (e) {
      log('Error generating PDF: $e');
      SnackbarUtils.showError('Error', 'Failed to generate PDF: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// Share PDF
  Future<void> sharePDF() async {
    try {
      // Validation
      if (selectedAccount.value == null) {
        SnackbarUtils.showError('Error', 'Please select an account');
        return;
      }

      if (currentUser.value == null) {
        SnackbarUtils.showError('Error', 'User information not available');
        return;
      }

      if (startDate.value == null || endDate.value == null) {
        SnackbarUtils.showError('Error', 'Please select a date range');
        return;
      }

      isLoading.value = true;

      // Generate PDF
      final pdfBytes = await pdfService.generateAccountStatementPDF(
        account: selectedAccount.value!,
        transactions: filteredTransactions,
        currentUser: currentUser.value!,
        startDate: startDate.value!,
        endDate: endDate.value!,
        companyName: companyNameController.text.isNotEmpty
            ? companyNameController.text
            : null,
        companyAddress: companyAddressController.text.isNotEmpty
            ? companyAddressController.text
            : null,
        customTitle: customTitleController.text.isNotEmpty
            ? customTitleController.text
            : null,
      );

      // Generate filename
      final dateFormat = DateFormat('yyyy-MM-dd');
      final fileName = 'Account_Statement_${selectedAccount.value!.name}_'
          '${dateFormat.format(startDate.value!)}_to_'
          '${dateFormat.format(endDate.value!)}.pdf';

      // Share PDF
      await pdfService.sharePDF(pdfBytes, fileName);

      SnackbarUtils.showSuccess('Success', 'PDF shared successfully');
    } catch (e) {
      log('Error sharing PDF: $e');
      SnackbarUtils.showError('Error', 'Failed to share PDF: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// Reset form
  void resetForm() {
    selectedAccount.value = null;
    selectedAccountType.value = 'All Accounts';
    transactions.clear();
    filteredTransactions.clear();

    // Reset date range to last 30 days
    final now = DateTime.now();
    endDate.value = now;
    startDate.value = now.subtract(const Duration(days: 30));

    // Reset customization fields
    companyNameController.text = currentUser.value?.companyName ?? '';
    companyAddressController.clear();
    customTitleController.clear();
  }
}
