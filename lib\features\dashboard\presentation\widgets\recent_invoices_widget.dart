import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';
import 'package:logestics/core/utils/app_constants/colors/app_colors.dart';
import 'package:intl/intl.dart';
import 'package:logestics/models/invoice_model.dart';
import 'package:logestics/features/invoices/presentation/views/invoice_form_view.dart';
import '../controllers/dashboard_data_controller.dart';

class RecentInvoicesWidget extends StatelessWidget {
  const RecentInvoicesWidget({super.key});

  @override
  Widget build(BuildContext context) {
    notifier = Provider.of(context, listen: true);
    log('RecentInvoicesWidget: Building widget');

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: notifier.getBgColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Invoices',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: notifier.text,
                ),
              ),
              Row(
                children: [
                  Icon(
                    Icons.receipt_long,
                    color: notifier.text.withValues(alpha: 0.6),
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  TextButton(
                    onPressed: () => _navigateToInvoices(),
                    child: Text(
                      'View All',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Content
          GetBuilder<DashboardDataController>(
            builder: (controller) {
              log('RecentInvoicesWidget: GetBuilder rebuilding');
              log('RecentInvoicesWidget: Loading state: ${controller.isLoadingInvoices.value}');

              if (controller.isLoadingInvoices.value) {
                return const Center(
                  child: CircularProgressIndicator(),
                );
              }

              if (controller.invoiceError.value.isNotEmpty) {
                return Center(
                  child: Text(
                    'Error: ${controller.invoiceError.value}',
                    style: TextStyle(
                      color: Colors.red,
                      fontSize: 14,
                    ),
                  ),
                );
              }

              if (controller.invoices.isEmpty) {
                return _buildEmptyState();
              }

              // Get recent invoices (last 5)
              final recentInvoices = _getRecentInvoices(controller.invoices);

              return Column(
                children: [
                  // Summary header
                  _buildSummaryHeader(controller),
                  const SizedBox(height: 16),

                  // Recent invoices list
                  ...recentInvoices
                      .map((invoice) => _buildInvoiceCard(invoice)),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryHeader(DashboardDataController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primary.withValues(alpha: 0.1),
            AppColors.primary.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildSummaryItem(
            'This Week',
            '${_getThisWeekCount(controller.invoices)}',
            Icons.calendar_today,
            AppColors.primary,
          ),
          _buildSummaryItem(
            'Pending',
            '${controller.unpaidInvoices.value}',
            Icons.pending_actions,
            Colors.orange,
          ),
          _buildSummaryItem(
            'Overdue',
            '${controller.overdueInvoices.value}',
            Icons.warning,
            Colors.red,
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(
      String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: notifier.text,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: notifier.text.withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }

  Widget _buildInvoiceCard(InvoiceModel invoice) {
    final status = invoice.invoiceStatus;
    final amount =
        invoice.numberOfBags * invoice.weightPerBag * 1.0; // Basic calculation
    final date = invoice.belongsToDate ?? invoice.createdAt;
    final invoiceNumber = invoice.invoiceNumber.toString();
    final customerName = invoice.customerName;

    // Determine status color and icon
    Color statusColor;
    IconData statusIcon;

    if (status == 'Completed') {
      statusColor = Colors.green;
      statusIcon = Icons.check_circle;
    } else if (_isOverdue(date)) {
      statusColor = Colors.red;
      statusIcon = Icons.warning;
    } else {
      statusColor = Colors.orange;
      statusIcon = Icons.pending;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: notifier.getBgColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: notifier.text.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Status indicator
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: statusColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              statusIcon,
              color: statusColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),

          // Invoice details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      invoiceNumber,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: notifier.text,
                      ),
                    ),
                    Text(
                      'PKR ${NumberFormat('#,##0.00').format(amount)}',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  customerName,
                  style: TextStyle(
                    fontSize: 12,
                    color: notifier.text.withValues(alpha: 0.7),
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  _formatDate(date),
                  style: TextStyle(
                    fontSize: 11,
                    color: notifier.text.withValues(alpha: 0.5),
                  ),
                ),
              ],
            ),
          ),

          // Action button
          IconButton(
            onPressed: () => _viewInvoice(invoice),
            icon: Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: notifier.text.withValues(alpha: 0.5),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        children: [
          Icon(
            Icons.receipt_long_outlined,
            size: 64,
            color: notifier.text.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'No invoices found',
            style: TextStyle(
              fontSize: 16,
              color: notifier.text.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create your first invoice to see it here',
            style: TextStyle(
              fontSize: 14,
              color: notifier.text.withValues(alpha: 0.5),
            ),
          ),
        ],
      ),
    );
  }

  List<InvoiceModel> _getRecentInvoices(RxList<InvoiceModel> invoices) {
    // Sort by date (most recent first) and take first 5
    final sortedInvoices = List<InvoiceModel>.from(invoices);
    sortedInvoices.sort((a, b) {
      final dateA = a.belongsToDate ?? a.createdAt;
      final dateB = b.belongsToDate ?? b.createdAt;
      return dateB.compareTo(dateA);
    });

    return sortedInvoices.take(5).toList();
  }

  int _getThisWeekCount(RxList<InvoiceModel> invoices) {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));

    return invoices.where((invoice) {
      final date = invoice.belongsToDate ?? invoice.createdAt;
      return date.isAfter(startOfWeek) &&
          date.isBefore(endOfWeek.add(const Duration(days: 1)));
    }).length;
  }

  bool _isOverdue(DateTime date) {
    final now = DateTime.now();
    final daysDifference = now.difference(date).inDays;
    return daysDifference > 30; // Consider overdue after 30 days
  }

  String _formatDate(DateTime date) {
    return DateFormat('MMM dd, yyyy').format(date);
  }

  void _navigateToInvoices() {
    // Navigate to the main home page where invoices are managed
    Get.offAllNamed('/home');

    // Show a snackbar to inform user
    Get.snackbar(
      'Navigation',
      'Opening invoices section',
      backgroundColor: AppColors.primary,
      colorText: Colors.white,
      duration: const Duration(seconds: 2),
    );
  }

  void _viewInvoice(InvoiceModel invoice) {
    // Navigate to invoice form in view mode
    // Use the same pattern as in invoice_list.dart
    Get.to(
      () => InvoiceFormView(
        currentInvoice: invoice,
        readOnly: true,
      ),
    );
  }
}
