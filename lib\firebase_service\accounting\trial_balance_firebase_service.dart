import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:either_dart/either.dart';
import 'package:uuid/uuid.dart';
import '../../core/shared_services/failure_obj.dart';
import '../../core/shared_services/success_obj.dart';
import '../../models/accounting/financial_report_models.dart';
import '../../models/finance/chart_of_accounts_model.dart';
import '../../models/finance/journal_entry_model.dart';
import '../../core/utils/app_constants/firebase/collection_names.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// Firebase service for generating and managing Trial Balance reports
class TrialBalanceFirebaseService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final String _uid = FirebaseAuth.instance.currentUser?.uid ?? 'anonymous';

  /// Generate Trial Balance report for a specific date range
  Future<Either<FailureObj, TrialBalanceReport>> generateTrialBalance({
    required DateTime startDate,
    required DateTime endDate,
    required String companyName,
    bool includeInactiveAccounts = false,
    bool includeZeroBalances = false,
  }) async {
    try {
      log('Generating Trial Balance for period: ${startDate.toIso8601String()} to ${endDate.toIso8601String()}');

      if (_uid == 'anonymous') {
        return Left(FailureObj(
          code: 'auth-error',
          message: 'User not authenticated',
        ));
      }

      // Get all accounts for the user
      final accountsSnapshot = await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .where('uid', isEqualTo: _uid)
          .orderBy('accountNumber')
          .get();

      if (accountsSnapshot.docs.isEmpty) {
        return Left(FailureObj(
          code: 'no-accounts',
          message: 'No accounts found for trial balance generation',
        ));
      }

      final accounts = accountsSnapshot.docs
          .map((doc) => ChartOfAccountsModel.fromJson(doc.data()))
          .toList();

      // Filter accounts based on parameters
      final filteredAccounts = accounts.where((account) {
        if (!includeInactiveAccounts && !account.isActive) {
          return false;
        }
        return true;
      }).toList();

      // Calculate balances for each account
      final trialBalanceAccounts = <TrialBalanceAccount>[];
      double totalDebits = 0.0;
      double totalCredits = 0.0;

      for (final account in filteredAccounts) {
        final balance = await _calculateAccountBalance(account.id, endDate);

        // Skip zero balances if requested
        if (!includeZeroBalances &&
            balance.debitBalance == 0.0 &&
            balance.creditBalance == 0.0) {
          continue;
        }

        trialBalanceAccounts.add(balance);
        totalDebits += balance.debitBalance;
        totalCredits += balance.creditBalance;
      }

      // Sort accounts by account number
      trialBalanceAccounts
          .sort((a, b) => a.accountNumber.compareTo(b.accountNumber));

      // Create the trial balance report
      final report = TrialBalanceReport(
        reportId: const Uuid().v4(),
        companyName: companyName,
        uid: _uid,
        startDate: startDate,
        endDate: endDate,
        generatedAt: DateTime.now(),
        generatedBy: _uid,
        accounts: trialBalanceAccounts,
        totalDebits: totalDebits,
        totalCredits: totalCredits,
      );

      log('Trial Balance generated successfully: ${trialBalanceAccounts.length} accounts, Debits: $totalDebits, Credits: $totalCredits');

      return Right(report);
    } catch (e) {
      log('Error generating trial balance: $e');
      return Left(FailureObj(
        code: 'generation-error',
        message: 'Failed to generate trial balance: $e',
      ));
    }
  }

  /// Calculate account balance up to a specific date
  Future<TrialBalanceAccount> _calculateAccountBalance(
      String accountId, DateTime asOfDate) async {
    try {
      // Get account details
      final accountDoc = await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .doc(accountId)
          .get();

      if (!accountDoc.exists) {
        throw Exception('Account not found: $accountId');
      }

      final account = ChartOfAccountsModel.fromJson(accountDoc.data()!);

      // Get all journal entry lines for this account up to the specified date
      final linesSnapshot = await _firestore
          .collection(AppCollection.journalEntryLinesCollection)
          .where('accountId', isEqualTo: accountId)
          .where('uid', isEqualTo: _uid)
          .get();

      double totalDebits = 0.0;
      double totalCredits = 0.0;

      for (final lineDoc in linesSnapshot.docs) {
        final lineData = lineDoc.data();
        final line = JournalEntryLineModel.fromJson(lineData);

        // Get the parent journal entry to check date and status
        final entryDoc = await _firestore
            .collection(AppCollection.journalEntriesCollection)
            .doc(line.journalEntryId)
            .get();

        if (!entryDoc.exists) continue;

        final entryData = entryDoc.data()!;
        final entryDate = (entryData['entryDate'] as Timestamp).toDate();
        final entryStatus = entryData['status'] as String;

        // Only include posted entries up to the specified date
        if (entryStatus == 'posted' &&
            entryDate.isBefore(asOfDate.add(const Duration(days: 1)))) {
          totalDebits += line.debitAmount;
          totalCredits += line.creditAmount;
        }
      }

      // Determine final debit/credit balance based on account type
      double debitBalance = 0.0;
      double creditBalance = 0.0;

      final netBalance = totalDebits - totalCredits;

      // Assets, Expenses have normal debit balances
      if (_isDebitNormalAccount(account.accountType)) {
        if (netBalance >= 0) {
          debitBalance = netBalance;
        } else {
          creditBalance = -netBalance;
        }
      } else {
        // Liabilities, Equity, Revenue have normal credit balances
        if (netBalance <= 0) {
          creditBalance = -netBalance;
        } else {
          debitBalance = netBalance;
        }
      }

      return TrialBalanceAccount(
        accountId: accountId,
        accountNumber: account.accountNumber,
        accountName: account.accountName,
        accountType: account.accountType.toString(),
        debitBalance: debitBalance,
        creditBalance: creditBalance,
      );
    } catch (e) {
      log('Error calculating balance for account $accountId: $e');
      // Return zero balance account on error
      return TrialBalanceAccount(
        accountId: accountId,
        accountNumber: 'ERROR',
        accountName: 'Error calculating balance',
        accountType: 'unknown',
        debitBalance: 0.0,
        creditBalance: 0.0,
      );
    }
  }

  /// Check if account type has normal debit balance
  bool _isDebitNormalAccount(AccountType accountType) {
    switch (accountType) {
      case AccountType.currentAssets:
      case AccountType.cash:
      case AccountType.bank:
      case AccountType.inventory:
      case AccountType.accountsReceivable:
      case AccountType.fixedAssets:
      case AccountType.operatingExpenses:
      case AccountType.administrativeExpenses:
      case AccountType.interestExpense:
      case AccountType.taxExpense:
        return true;
      case AccountType.currentLiabilities:
      case AccountType.longTermLiabilities:
      case AccountType.accountsPayable:
      case AccountType.loansPayable:
      case AccountType.ownersEquity:
      case AccountType.retainedEarnings:
      case AccountType.serviceRevenue:
      case AccountType.salesRevenue:
      case AccountType.otherRevenue:
        return false;
    }
  }

  /// Save trial balance report to Firestore
  Future<Either<FailureObj, SuccessObj>> saveTrialBalanceReport(
      TrialBalanceReport report) async {
    try {
      log('Saving trial balance report: ${report.reportId}');

      if (_uid == 'anonymous') {
        return Left(FailureObj(
          code: 'auth-error',
          message: 'User not authenticated',
        ));
      }

      await _firestore
          .collection(AppCollection.financialReportsCollection)
          .doc(report.reportId)
          .set(report.toJson());

      log('Trial balance report saved successfully: ${report.reportId}');

      return Right(SuccessObj(
        message: 'Trial balance report saved successfully',
      ));
    } catch (e) {
      log('Error saving trial balance report: $e');
      return Left(FailureObj(
        code: 'save-error',
        message: 'Failed to save trial balance report: $e',
      ));
    }
  }

  /// Get saved trial balance reports
  Future<Either<FailureObj, List<TrialBalanceReport>>>
      getSavedTrialBalanceReports() async {
    try {
      log('Fetching saved trial balance reports');

      if (_uid == 'anonymous') {
        return Left(FailureObj(
          code: 'auth-error',
          message: 'User not authenticated',
        ));
      }

      final snapshot = await _firestore
          .collection(AppCollection.financialReportsCollection)
          .where('uid', isEqualTo: _uid)
          .where('reportType', isEqualTo: 'trial_balance')
          .orderBy('generatedAt', descending: true)
          .get();

      final reports = snapshot.docs
          .map((doc) => TrialBalanceReport.fromJson(doc.data()))
          .toList();

      log('Fetched ${reports.length} saved trial balance reports');

      return Right(reports);
    } catch (e) {
      log('Error fetching saved trial balance reports: $e');
      return Left(FailureObj(
        code: 'fetch-error',
        message: 'Failed to fetch saved trial balance reports: $e',
      ));
    }
  }

  /// Delete a saved trial balance report
  Future<Either<FailureObj, SuccessObj>> deleteTrialBalanceReport(
      String reportId) async {
    try {
      log('Deleting trial balance report: $reportId');

      if (_uid == 'anonymous') {
        return Left(FailureObj(
          code: 'auth-error',
          message: 'User not authenticated',
        ));
      }

      // Verify the report belongs to the current user
      final reportDoc = await _firestore
          .collection(AppCollection.financialReportsCollection)
          .doc(reportId)
          .get();

      if (!reportDoc.exists) {
        return Left(FailureObj(
          code: 'not-found',
          message: 'Trial balance report not found',
        ));
      }

      final reportData = reportDoc.data()!;
      if (reportData['uid'] != _uid) {
        return Left(FailureObj(
          code: 'permission-denied',
          message: 'You do not have permission to delete this report',
        ));
      }

      await _firestore
          .collection(AppCollection.financialReportsCollection)
          .doc(reportId)
          .delete();

      log('Trial balance report deleted successfully: $reportId');

      return Right(SuccessObj(
        message: 'Trial balance report deleted successfully',
      ));
    } catch (e) {
      log('Error deleting trial balance report: $e');
      return Left(FailureObj(
        code: 'delete-error',
        message: 'Failed to delete trial balance report: $e',
      ));
    }
  }

  /// Get trial balance report by ID
  Future<Either<FailureObj, TrialBalanceReport>> getTrialBalanceReportById(
      String reportId) async {
    try {
      log('Fetching trial balance report by ID: $reportId');

      if (_uid == 'anonymous') {
        return Left(FailureObj(
          code: 'auth-error',
          message: 'User not authenticated',
        ));
      }

      final reportDoc = await _firestore
          .collection(AppCollection.financialReportsCollection)
          .doc(reportId)
          .get();

      if (!reportDoc.exists) {
        return Left(FailureObj(
          code: 'not-found',
          message: 'Trial balance report not found',
        ));
      }

      final reportData = reportDoc.data()!;
      if (reportData['uid'] != _uid) {
        return Left(FailureObj(
          code: 'permission-denied',
          message: 'You do not have permission to access this report',
        ));
      }

      final report = TrialBalanceReport.fromJson(reportData);
      log('Trial balance report fetched successfully: $reportId');

      return Right(report);
    } catch (e) {
      log('Error fetching trial balance report: $e');
      return Left(FailureObj(
        code: 'fetch-error',
        message: 'Failed to fetch trial balance report: $e',
      ));
    }
  }

  /// Validate trial balance (ensure debits equal credits)
  Future<Either<FailureObj, bool>> validateTrialBalance(
      DateTime asOfDate) async {
    try {
      log('Validating trial balance as of: ${asOfDate.toIso8601String()}');

      final trialBalanceResult = await generateTrialBalance(
        startDate: DateTime(2000), // Use a very early start date
        endDate: asOfDate,
        companyName: 'Validation',
        includeZeroBalances: true,
      );

      return trialBalanceResult.fold(
        (failure) => Left(failure),
        (report) {
          final isBalanced = report.isBalanced;
          log('Trial balance validation result: ${isBalanced ? 'BALANCED' : 'UNBALANCED'} - Debits: ${report.totalDebits}, Credits: ${report.totalCredits}');
          return Right(isBalanced);
        },
      );
    } catch (e) {
      log('Error validating trial balance: $e');
      return Left(FailureObj(
        code: 'validation-error',
        message: 'Failed to validate trial balance: $e',
      ));
    }
  }
}
