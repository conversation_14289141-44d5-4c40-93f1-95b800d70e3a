import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/core/utils/mixins/pagination_mixin.dart';
import 'package:logestics/models/finance/payee_model.dart';
import 'package:logestics/features/finance/payees/repositories/payee_repository.dart';
import 'package:uuid/uuid.dart';

class PayeeController extends GetxController with PaginationMixin {
  final PayeeRepository repository;

  // Form controllers
  final nameController = TextEditingController();
  final phoneNumberController = TextEditingController();

  // Observable variables
  final payees = <PayeeModel>[].obs;
  final isLoading = false.obs;
  final isDeleting = false.obs;
  final isDialogOpen = false.obs;
  final searchQuery = ''.obs;
  final filteredPayees = <PayeeModel>[].obs;

  final searchController = TextEditingController();
  final formKey = GlobalKey<FormState>();

  PayeeController({required this.repository});

  @override
  void onInit() {
    super.onInit();
    searchController.addListener(_onSearchChanged);
    loadPayees();
  }

  Future<void> loadPayees() async {
    try {
      isLoading.value = true;
      final result = await repository.getPayees();
      result.fold(
        (failure) => SnackbarUtils.showError(
          AppStrings.errorS,
          failure.message,
        ),
        (payeesList) {
          payees.value = payeesList;
          _filterPayees();
        },
      );
    } catch (e) {
      log('Error loading payees: $e');
      SnackbarUtils.showError(
        AppStrings.errorS,
        'Failed to load payees',
      );
    } finally {
      isLoading.value = false;
    }
  }

  void _onSearchChanged() {
    searchQuery.value = searchController.text;
    _filterPayees();
  }

  void _filterPayees() {
    if (searchQuery.value.isEmpty) {
      filteredPayees.value = payees;
    } else {
      final query = searchQuery.value.toLowerCase();
      filteredPayees.value = payees.where((payee) {
        return payee.name.toLowerCase().contains(query) ||
            (payee.phoneNumber?.toLowerCase() ?? '').contains(query);
      }).toList();
    }
    setTotalItems(filteredPayees.length);
  }

  List<PayeeModel> get paginatedPayees => paginateList(filteredPayees);

  void openDialog() {
    clearForm();
    isDialogOpen.value = true;
  }

  void closeDialog() {
    isDialogOpen.value = false;
  }

  void clearForm() {
    nameController.clear();
    phoneNumberController.clear();
  }

  String? validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Name is required';
    }
    return null;
  }

  String? validatePhoneNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'Phone number is required';
    }
    // Simple validation for phone number format
    if (!RegExp(r'^\d{10,15}$').hasMatch(value)) {
      return 'Please enter a valid phone number';
    }
    return null;
  }

  Future<void> addPayee() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    if (isLoading.value) {
      return;
    }

    isLoading.value = true;

    try {
      final newPayee = PayeeModel(
        id: const Uuid().v4(),
        name: nameController.text,
        phoneNumber: phoneNumberController.text,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final result = await repository.createPayee(newPayee);
      result.fold(
        (failure) => SnackbarUtils.showError(
          AppStrings.errorS,
          failure.message,
        ),
        (success) {
          payees.add(newPayee);
          _filterPayees();
          clearForm();
          closeDialog();
          SnackbarUtils.showSuccess(
            AppStrings.success,
            success.message,
          );
          // Force refresh to ensure pagination updates immediately
          _refreshPayeeData();
        },
      );
    } catch (e) {
      log('Error adding payee: $e');
      SnackbarUtils.showError(
        AppStrings.errorS,
        'Failed to add payee: $e',
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> deletePayee(String payeeId) async {
    if (isDeleting.value) {
      log('Delete already in progress after dialog, ignoring request');
      return;
    }

    log('Starting payee deletion for ID: $payeeId');
    isDeleting.value = true;

    try {
      final result = await repository.deletePayee(payeeId);
      result.fold(
        (failure) {
          log('Failed to delete payee: ${failure.message}');
          SnackbarUtils.showError(
            AppStrings.errorS,
            failure.message,
          );
        },
        (success) {
          log('Successfully deleted payee: $payeeId');
          payees.removeWhere((payee) => payee.id == payeeId);
          _filterPayees();
          SnackbarUtils.showSuccess(
            AppStrings.success,
            success.message,
          );
          // Force refresh to ensure pagination updates immediately
          _refreshPayeeData();
        },
      );
    } catch (e) {
      log('Error deleting payee: $e');
      SnackbarUtils.showError(
        AppStrings.errorS,
        'Failed to delete payee: $e',
      );
    } finally {
      log('Resetting deletion state');
      isDeleting.value = false;
    }
  }

  Future<void> updatePayee(PayeeModel payee) async {
    if (isLoading.value) {
      return;
    }

    isLoading.value = true;

    try {
      final result = await repository.updatePayee(payee);
      result.fold(
        (failure) => SnackbarUtils.showError(
          AppStrings.errorS,
          failure.message,
        ),
        (success) {
          final index = payees.indexWhere((p) => p.id == payee.id);
          if (index != -1) {
            payees[index] = payee;
            _filterPayees();
          }
          SnackbarUtils.showSuccess(
            AppStrings.success,
            success.message,
          );
          // Force refresh to ensure pagination updates immediately
          _refreshPayeeData();
        },
      );
    } catch (e) {
      log('Error updating payee: $e');
      SnackbarUtils.showError(
        AppStrings.errorS,
        'Failed to update payee: $e',
      );
    } finally {
      isLoading.value = false;
    }
  }

  void _refreshPayeeData() {
    // Immediately refresh the filtered list and pagination
    _filterPayees();

    // Add a small delay to ensure any Firestore listeners have processed the change
    Future.delayed(const Duration(milliseconds: 500), () {
      // Force refresh the data from Firestore
      loadPayees();
    });
  }

  @override
  void onClose() {
    nameController.dispose();
    phoneNumberController.dispose();
    searchController.dispose();
    super.onClose();
  }
}
