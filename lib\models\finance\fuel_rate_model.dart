import 'package:cloud_firestore/cloud_firestore.dart';

class FuelRateModel {
  final String id;
  final String companyName; // Raj<PERSON>, <PERSON><PERSON>, <PERSON>, etc.
  final double rate; // Rate per liter
  final DateTime effectiveDate; // When this rate becomes effective
  final DateTime createdAt;
  final DateTime? updatedAt;
  final bool isActive;

  FuelRateModel({
    required this.id,
    required this.companyName,
    required this.rate,
    required this.effectiveDate,
    required this.createdAt,
    this.updatedAt,
    this.isActive = true,
  });

  // Create a FuelRateModel from a map (for Firestore)
  factory FuelRateModel.fromMap(Map<String, dynamic> map) {
    return FuelRateModel(
      id: map['id'] ?? '',
      companyName: map['companyName'] ?? '',
      rate: map['rate']?.toDouble() ?? 0.0,
      effectiveDate: map['effectiveDate'] is DateTime
          ? map['effectiveDate'] as DateTime
          : (map['effectiveDate'] as Timestamp).toDate(),
      createdAt: map['createdAt'] is DateTime
          ? map['createdAt'] as DateTime
          : (map['createdAt'] as Timestamp).toDate(),
      updatedAt: map['updatedAt'] != null
          ? (map['updatedAt'] is DateTime
              ? map['updatedAt'] as DateTime
              : (map['updatedAt'] as Timestamp).toDate())
          : null,
      isActive: map['isActive'] ?? true,
    );
  }

  // Convert FuelRateModel to a map (for Firestore)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'companyName': companyName,
      'rate': rate,
      'effectiveDate': effectiveDate,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'isActive': isActive,
    };
  }

  // Create a copy of the FuelRateModel with updated fields
  FuelRateModel copyWith({
    String? id,
    String? companyName,
    double? rate,
    DateTime? effectiveDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
  }) {
    return FuelRateModel(
      id: id ?? this.id,
      companyName: companyName ?? this.companyName,
      rate: rate ?? this.rate,
      effectiveDate: effectiveDate ?? this.effectiveDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
    );
  }
}
