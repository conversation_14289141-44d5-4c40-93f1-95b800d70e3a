import 'dart:developer';
import 'package:dartz/dartz.dart';
import '../../../../core/shared_services/failure_obj.dart';
import '../../../../core/shared_services/success_obj.dart';
import '../../../../core/utils/app_constants/texts/app_strings.dart';
import '../../../../firebase_service/accounting/fiscal_period_firebase_service.dart';
import '../../../../models/finance/fiscal_period_model.dart';

abstract class FiscalPeriodRepository {
  // Fiscal Year methods
  Future<Either<FailureObj, List<FiscalYearModel>>> getFiscalYears();
  Future<Either<FailureObj, FiscalYearModel?>> getActiveFiscalYear();
  Future<Either<FailureObj, SuccessObj>> createFiscalYear(FiscalYearModel fiscalYear);
  Future<Either<FailureObj, SuccessObj>> updateFiscalYear(FiscalYearModel fiscalYear);
  Future<Either<FailureObj, SuccessObj>> setActiveFiscalYear(String fiscalYearId);
  
  // Fiscal Period methods
  Future<Either<FailureObj, List<FiscalPeriodModel>>> getFiscalPeriods(String fiscalYearId);
  Future<Either<FailureObj, FiscalPeriodModel?>> getCurrentFiscalPeriod();
  Future<Either<FailureObj, SuccessObj>> createFiscalPeriod(FiscalPeriodModel fiscalPeriod);
  Future<Either<FailureObj, SuccessObj>> updateFiscalPeriod(FiscalPeriodModel fiscalPeriod);
  Future<Either<FailureObj, SuccessObj>> closeFiscalPeriod(String periodId, String closedBy);
  Future<Either<FailureObj, SuccessObj>> reopenFiscalPeriod(String periodId);
  
  // Streams
  Stream<List<FiscalYearModel>> listenToFiscalYears();
  Stream<List<FiscalPeriodModel>> listenToFiscalPeriods(String fiscalYearId);
}

class FiscalPeriodRepositoryImpl implements FiscalPeriodRepository {
  final FiscalPeriodFirebaseService _firebaseService;

  FiscalPeriodRepositoryImpl(this._firebaseService);

  // Fiscal Year implementations
  @override
  Future<Either<FailureObj, SuccessObj>> createFiscalYear(FiscalYearModel fiscalYear) async {
    try {
      log('Creating fiscal year in repository: ${fiscalYear.yearName}');
      await _firebaseService.createFiscalYear(fiscalYear);
      return Right(SuccessObj(message: 'Fiscal year created successfully'));
    } catch (e) {
      log('Error creating fiscal year: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, List<FiscalYearModel>>> getFiscalYears() async {
    try {
      log('Fetching fiscal years from repository');
      final years = await _firebaseService.getFiscalYears();
      return Right(years);
    } catch (e) {
      log('Error fetching fiscal years: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, FiscalYearModel?>> getActiveFiscalYear() async {
    try {
      log('Fetching active fiscal year from repository');
      final year = await _firebaseService.getActiveFiscalYear();
      return Right(year);
    } catch (e) {
      log('Error fetching active fiscal year: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> updateFiscalYear(FiscalYearModel fiscalYear) async {
    try {
      log('Updating fiscal year in repository: ${fiscalYear.yearName}');
      await _firebaseService.updateFiscalYear(fiscalYear);
      return Right(SuccessObj(message: 'Fiscal year updated successfully'));
    } catch (e) {
      log('Error updating fiscal year: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> setActiveFiscalYear(String fiscalYearId) async {
    try {
      log('Setting active fiscal year in repository: $fiscalYearId');
      await _firebaseService.setActiveFiscalYear(fiscalYearId);
      return Right(SuccessObj(message: 'Active fiscal year set successfully'));
    } catch (e) {
      log('Error setting active fiscal year: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  // Fiscal Period implementations
  @override
  Future<Either<FailureObj, SuccessObj>> createFiscalPeriod(FiscalPeriodModel fiscalPeriod) async {
    try {
      log('Creating fiscal period in repository: ${fiscalPeriod.periodName}');
      await _firebaseService.createFiscalPeriod(fiscalPeriod);
      return Right(SuccessObj(message: 'Fiscal period created successfully'));
    } catch (e) {
      log('Error creating fiscal period: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, List<FiscalPeriodModel>>> getFiscalPeriods(String fiscalYearId) async {
    try {
      log('Fetching fiscal periods from repository for year: $fiscalYearId');
      final periods = await _firebaseService.getFiscalPeriods(fiscalYearId);
      return Right(periods);
    } catch (e) {
      log('Error fetching fiscal periods: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, FiscalPeriodModel?>> getCurrentFiscalPeriod() async {
    try {
      log('Fetching current fiscal period from repository');
      final period = await _firebaseService.getCurrentFiscalPeriod();
      return Right(period);
    } catch (e) {
      log('Error fetching current fiscal period: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> updateFiscalPeriod(FiscalPeriodModel fiscalPeriod) async {
    try {
      log('Updating fiscal period in repository: ${fiscalPeriod.periodName}');
      await _firebaseService.updateFiscalPeriod(fiscalPeriod);
      return Right(SuccessObj(message: 'Fiscal period updated successfully'));
    } catch (e) {
      log('Error updating fiscal period: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> closeFiscalPeriod(String periodId, String closedBy) async {
    try {
      log('Closing fiscal period in repository: $periodId');
      await _firebaseService.closeFiscalPeriod(periodId, closedBy);
      return Right(SuccessObj(message: 'Fiscal period closed successfully'));
    } catch (e) {
      log('Error closing fiscal period: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> reopenFiscalPeriod(String periodId) async {
    try {
      log('Reopening fiscal period in repository: $periodId');
      await _firebaseService.reopenFiscalPeriod(periodId);
      return Right(SuccessObj(message: 'Fiscal period reopened successfully'));
    } catch (e) {
      log('Error reopening fiscal period: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  // Stream implementations
  @override
  Stream<List<FiscalYearModel>> listenToFiscalYears() {
    try {
      log('Setting up real-time listener for fiscal years');
      return _firebaseService.listenToFiscalYears();
    } catch (e) {
      log('Error setting up fiscal years listener: $e');
      return Stream.value([]);
    }
  }

  @override
  Stream<List<FiscalPeriodModel>> listenToFiscalPeriods(String fiscalYearId) {
    try {
      log('Setting up real-time listener for fiscal periods');
      return _firebaseService.listenToFiscalPeriods(fiscalYearId);
    } catch (e) {
      log('Error setting up fiscal periods listener: $e');
      return Stream.value([]);
    }
  }
}
