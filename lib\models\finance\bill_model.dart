class BillModel {
  // Bill Information
  final String billId;
  final String billNumber;
  final DateTime billDate;
  final double totalAmount;
  final int numberOfLinkedInvoices;
  final String billStatus;
  final List<String> linkedInvoiceIds; // Array of invoice TAS numbers
  final String companyUid;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Additional metadata
  final String? notes;
  final String?
      customerName; // Primary customer if all invoices are for same customer
  final DateTime? dueDate;
  final DateTime? paidDate;

  // Slab information
  final String? selectedSlabId;
  final String? selectedSlabName;
  final String? selectedDistrictId;
  final String? selectedDistrictName;

  BillModel({
    required this.billId,
    required this.billNumber,
    required this.billDate,
    required this.totalAmount,
    required this.numberOfLinkedInvoices,
    required this.billStatus,
    required this.linkedInvoiceIds,
    required this.companyUid,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.notes,
    this.customerName,
    this.dueDate,
    this.paidDate,
    this.selectedSlabId,
    this.selectedSlabName,
    this.selectedDistrictId,
    this.selectedDistrictName,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  /// Factory constructor to create BillModel from JSON
  factory BillModel.fromJson(Map<String, dynamic> json) {
    return BillModel(
      billId: json['billId']?.toString() ?? '',
      billNumber: json['billNumber']?.toString() ?? '',
      billDate: json['billDate'] != null
          ? DateTime.parse(json['billDate'])
          : DateTime.now(),
      totalAmount: json['totalAmount'] != null
          ? (json['totalAmount'] as num).toDouble()
          : 0.0,
      numberOfLinkedInvoices: json['numberOfLinkedInvoices'] is int
          ? json['numberOfLinkedInvoices']
          : int.tryParse(json['numberOfLinkedInvoices']?.toString() ?? '0') ??
              0,
      billStatus: json['billStatus']?.toString() ?? 'Pending',
      linkedInvoiceIds: json['linkedInvoiceIds'] != null
          ? List<String>.from(json['linkedInvoiceIds'])
          : [],
      companyUid: json['companyUid']?.toString() ?? '',
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'])
          : DateTime.now(),
      notes: json['notes']?.toString(),
      customerName: json['customerName']?.toString(),
      dueDate: json['dueDate'] != null ? DateTime.parse(json['dueDate']) : null,
      paidDate:
          json['paidDate'] != null ? DateTime.parse(json['paidDate']) : null,
      selectedSlabId: json['selectedSlabId']?.toString(),
      selectedSlabName: json['selectedSlabName']?.toString(),
      selectedDistrictId: json['selectedDistrictId']?.toString(),
      selectedDistrictName: json['selectedDistrictName']?.toString(),
    );
  }

  /// Convert BillModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'billId': billId,
      'billNumber': billNumber,
      'billDate': billDate.toIso8601String(),
      'totalAmount': totalAmount,
      'numberOfLinkedInvoices': numberOfLinkedInvoices,
      'billStatus': billStatus,
      'linkedInvoiceIds': linkedInvoiceIds,
      'companyUid': companyUid,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'notes': notes,
      'customerName': customerName,
      'dueDate': dueDate?.toIso8601String(),
      'paidDate': paidDate?.toIso8601String(),
      'selectedSlabId': selectedSlabId,
      'selectedSlabName': selectedSlabName,
      'selectedDistrictId': selectedDistrictId,
      'selectedDistrictName': selectedDistrictName,
    };
  }

  /// Create a copy of BillModel with updated fields
  BillModel copyWith({
    String? billId,
    String? billNumber,
    DateTime? billDate,
    double? totalAmount,
    int? numberOfLinkedInvoices,
    String? billStatus,
    List<String>? linkedInvoiceIds,
    String? companyUid,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? notes,
    String? customerName,
    DateTime? dueDate,
    DateTime? paidDate,
    String? selectedSlabId,
    String? selectedSlabName,
    String? selectedDistrictId,
    String? selectedDistrictName,
  }) {
    return BillModel(
      billId: billId ?? this.billId,
      billNumber: billNumber ?? this.billNumber,
      billDate: billDate ?? this.billDate,
      totalAmount: totalAmount ?? this.totalAmount,
      numberOfLinkedInvoices:
          numberOfLinkedInvoices ?? this.numberOfLinkedInvoices,
      billStatus: billStatus ?? this.billStatus,
      linkedInvoiceIds: linkedInvoiceIds ?? this.linkedInvoiceIds,
      companyUid: companyUid ?? this.companyUid,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      notes: notes ?? this.notes,
      customerName: customerName ?? this.customerName,
      dueDate: dueDate ?? this.dueDate,
      paidDate: paidDate ?? this.paidDate,
      selectedSlabId: selectedSlabId ?? this.selectedSlabId,
      selectedSlabName: selectedSlabName ?? this.selectedSlabName,
      selectedDistrictId: selectedDistrictId ?? this.selectedDistrictId,
      selectedDistrictName: selectedDistrictName ?? this.selectedDistrictName,
    );
  }

  /// Generate bill number in format BILL-YYYY-NNNN
  static String generateBillNumber(int billCounter) {
    final year = DateTime.now().year;
    final paddedCounter = billCounter.toString().padLeft(4, '0');
    return 'BILL-$year-$paddedCounter';
  }

  /// Check if bill is overdue (for bills with due dates)
  bool get isOverdue {
    if (dueDate == null || billStatus == 'Completed') return false;
    return DateTime.now().isAfter(dueDate!);
  }

  /// Get days until due (negative if overdue)
  int? get daysUntilDue {
    if (dueDate == null) return null;
    return dueDate!.difference(DateTime.now()).inDays;
  }

  /// Get bill age in days
  int get billAgeInDays {
    return DateTime.now().difference(billDate).inDays;
  }

  /// Check if bill is pending
  bool get isPending => billStatus == 'Pending';

  /// Check if bill is completed
  bool get isCompleted => billStatus == 'Completed';

  @override
  String toString() {
    return 'BillModel(billId: $billId, billNumber: $billNumber, billDate: $billDate, totalAmount: $totalAmount, numberOfLinkedInvoices: $numberOfLinkedInvoices, billStatus: $billStatus, linkedInvoiceIds: $linkedInvoiceIds, companyUid: $companyUid, createdAt: $createdAt, updatedAt: $updatedAt, notes: $notes, customerName: $customerName, dueDate: $dueDate, paidDate: $paidDate)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BillModel &&
        other.billId == billId &&
        other.billNumber == billNumber &&
        other.companyUid == companyUid;
  }

  @override
  int get hashCode {
    return billId.hashCode ^ billNumber.hashCode ^ companyUid.hashCode;
  }
}

/// Bill status constants
class BillStatus {
  static const String pending = 'Pending';
  static const String completed = 'Completed';

  static const List<String> allStatuses = [pending, completed];

  /// Get color for bill status
  static String getStatusColor(String status) {
    switch (status) {
      case pending:
        return '#FFA726'; // Orange
      case completed:
        return '#66BB6A'; // Green
      default:
        return '#9E9E9E'; // Grey
    }
  }

  /// Get display text for bill status
  static String getDisplayText(String status) {
    switch (status) {
      case pending:
        return 'Pending';
      case completed:
        return 'Completed';
      default:
        return 'Unknown';
    }
  }
}
