import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/trial_balance_controller.dart';

/// Summary widget for Trial Balance report
class TrialBalanceSummaryWidget extends StatelessWidget {
  const TrialBalanceSummaryWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<TrialBalanceController>();

    return Obx(() {
      final report = controller.currentReport.value;
      if (report == null) return const SizedBox.shrink();

      return Card(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Trial Balance Summary',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  // Balance status indicator
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: controller.isBalanced 
                        ? Colors.green.withOpacity(0.1)
                        : Colors.red.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: controller.isBalanced 
                          ? Colors.green
                          : Colors.red,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          controller.isBalanced 
                            ? Icons.check_circle
                            : Icons.error,
                          size: 16,
                          color: controller.isBalanced 
                            ? Colors.green
                            : Colors.red,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          controller.isBalanced ? 'BALANCED' : 'UNBALANCED',
                          style: TextStyle(
                            color: controller.isBalanced 
                              ? Colors.green
                              : Colors.red,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Report details
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Column(
                  children: [
                    _buildDetailRow('Company:', report.companyName),
                    _buildDetailRow('Period:', '${_formatDate(report.startDate)} to ${_formatDate(report.endDate)}'),
                    _buildDetailRow('Generated:', _formatDateTime(report.generatedAt)),
                    _buildDetailRow('Total Accounts:', '${report.accounts.length}'),
                  ],
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Financial summary
              Row(
                children: [
                  // Total Debits
                  Expanded(
                    child: _buildSummaryCard(
                      context,
                      'Total Debits',
                      controller.totalDebits,
                      Icons.trending_up,
                      Colors.blue,
                    ),
                  ),
                  
                  const SizedBox(width: 16),
                  
                  // Total Credits
                  Expanded(
                    child: _buildSummaryCard(
                      context,
                      'Total Credits',
                      controller.totalCredits,
                      Icons.trending_down,
                      Colors.green,
                    ),
                  ),
                  
                  const SizedBox(width: 16),
                  
                  // Difference
                  Expanded(
                    child: _buildSummaryCard(
                      context,
                      'Difference',
                      controller.totalDebits - controller.totalCredits,
                      Icons.balance,
                      controller.isBalanced ? Colors.green : Colors.red,
                    ),
                  ),
                ],
              ),
              
              // Validation message
              if (controller.validationMessage.value.isNotEmpty) ...[
                const SizedBox(height: 12),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: controller.isTrialBalanceValid.value
                      ? Colors.green.withOpacity(0.1)
                      : Colors.orange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: controller.isTrialBalanceValid.value
                        ? Colors.green
                        : Colors.orange,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        controller.isTrialBalanceValid.value
                          ? Icons.info
                          : Icons.warning,
                        size: 16,
                        color: controller.isTrialBalanceValid.value
                          ? Colors.green
                          : Colors.orange,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          controller.validationMessage.value,
                          style: TextStyle(
                            color: controller.isTrialBalanceValid.value
                              ? Colors.green.shade700
                              : Colors.orange.shade700,
                            fontSize: 13,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      );
    });
  }

  /// Build detail row
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build summary card
  Widget _buildSummaryCard(
    BuildContext context,
    String title,
    double amount,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 20,
                color: color,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: color.withOpacity(0.8),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            _formatCurrency(amount),
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
              fontFamily: 'monospace',
            ),
          ),
        ],
      ),
    );
  }

  /// Format date
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  /// Format date and time
  String _formatDateTime(DateTime dateTime) {
    return '${_formatDate(dateTime)} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// Format currency amount
  String _formatCurrency(double amount) {
    final isNegative = amount < 0;
    final absAmount = amount.abs();
    
    if (absAmount == 0) return '0.00';
    
    // Format with commas and 2 decimal places
    final formatted = absAmount.toStringAsFixed(2);
    final parts = formatted.split('.');
    final integerPart = parts[0];
    final decimalPart = parts[1];
    
    // Add commas to integer part
    final regex = RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))');
    final integerWithCommas = integerPart.replaceAllMapped(
      regex,
      (Match match) => '${match[1]},',
    );
    
    final result = '$integerWithCommas.$decimalPart';
    return isNegative ? '($result)' : result;
  }
}
