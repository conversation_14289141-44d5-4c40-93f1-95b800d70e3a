import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/widgets/data_table_cell.dart';
import 'package:logestics/core/utils/widgets/pagination_widget.dart';
import 'package:logestics/features/finance/payers/presentation/payer_controller.dart';
import 'package:logestics/features/finance/payers/repository/payer_repository.dart';
import 'package:logestics/firebase_service/finance/payer_firebase_service.dart';
import 'package:logestics/models/finance/payer_model.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';

class PayersView extends StatefulWidget {
  const PayersView({super.key});

  @override
  State<PayersView> createState() => _PayersViewState();
}

class _PayersViewState extends State<PayersView> {
  late final PayerController payerController;

  @override
  void initState() {
    super.initState();
    // Initialize Firebase API
    final firebaseService = PayerFirebaseService();
    // Initialize repository with Firebase API
    final repository = PayerRepositoryImpl(firebaseService);
    // Initialize controller with repository
    payerController = Get.put(PayerController(repository: repository));
  }

  @override
  Widget build(BuildContext context) {
    var width = Get.width;
    notifier = Provider.of(context, listen: true);

    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          child: Column(
            children: [
              SizedBox(
                height: width < 650 ? 55 : 40,
                width: width,
                child: width < 650
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Payers',
                            overflow: TextOverflow.ellipsis,
                            style: AppTextStyles.titleStyle
                                .copyWith(color: notifier.text),
                          ),
                          const Spacer(),
                        ],
                      )
                    : Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Payers',
                            overflow: TextOverflow.ellipsis,
                            style: AppTextStyles.titleStyle
                                .copyWith(color: notifier.text),
                          ),
                          const Spacer(),
                        ],
                      ),
              ),
              const SizedBox(height: 20),
              SizedBox(
                height: 570,
                child: _buildPayersList(),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPayersList() {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            color: notifier.getBgColor,
            borderRadius: BorderRadius.circular(10),
          ),
          padding: const EdgeInsets.symmetric(vertical: 15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 15),
                child: Get.width < 650
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          InkWell(
                            onTap: () => payerController.openDialog(),
                            child: Text(
                              'Add New Payer',
                              style: AppTextStyles.addNewInvoiceStyle,
                            ),
                          ),
                          TextField(
                            controller: payerController.searchController,
                            decoration: InputDecoration(
                              hintText: 'Search payers...',
                              prefixIcon: const Icon(Icons.search),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                              filled: true,
                              fillColor: notifier.textFileColor,
                            ),
                          ),
                        ],
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          InkWell(
                            onTap: () => payerController.openDialog(),
                            child: Text(
                              'Add New Payer',
                              style: AppTextStyles.addNewInvoiceStyle,
                            ),
                          ),
                          SizedBox(
                            width: Get.width < 850
                                ? Get.width / 2
                                : Get.width / 3.5,
                            child: TextField(
                              controller: payerController.searchController,
                              decoration: InputDecoration(
                                hintText: 'Search payers...',
                                prefixIcon: const Icon(Icons.search),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8.0),
                                ),
                                filled: true,
                                fillColor: notifier.textFileColor,
                              ),
                            ),
                          ),
                        ],
                      ),
              ),
              const SizedBox(height: 20),
              Expanded(
                child: SizedBox(
                  width: Get.width,
                  child: Obx(() {
                    if (payerController.isLoading.value) {
                      return const Center(
                        child: CircularProgressIndicator(),
                      );
                    }

                    return ListView(
                      shrinkWrap: true,
                      children: [
                        Table(
                          columnWidths: const {
                            0: FlexColumnWidth(0.5), // S.No
                            1: FlexColumnWidth(1.5), // Name
                            2: FlexColumnWidth(1.5), // Phone Number
                            3: FlexColumnWidth(1.0), // Actions
                          },
                          border: TableBorder(
                            horizontalInside: BorderSide(
                              color: notifier.getfillborder,
                            ),
                          ),
                          children: [
                            TableRow(
                              decoration: BoxDecoration(
                                color: notifier.getHoverColor,
                              ),
                              children: [
                                DataTableCell(
                                  text: 'S.No',
                                  style:
                                      AppTextStyles.invoiceDataStyle.copyWith(
                                    color: notifier.text,
                                  ),
                                ),
                                DataTableCell(
                                  text: 'Name',
                                  style:
                                      AppTextStyles.invoiceDataStyle.copyWith(
                                    color: notifier.text,
                                  ),
                                ),
                                DataTableCell(
                                  text: 'Phone Number',
                                  style:
                                      AppTextStyles.invoiceDataStyle.copyWith(
                                    color: notifier.text,
                                  ),
                                ),
                                DataTableCell(
                                  text: 'Actions',
                                  style:
                                      AppTextStyles.invoiceDataStyle.copyWith(
                                    color: notifier.text,
                                  ),
                                ),
                              ],
                            ),
                            ...payerController.paginatedPayers
                                .asMap()
                                .entries
                                .map((entry) {
                              final i = entry.key;
                              final payer = entry.value;
                              return TableRow(
                                children: [
                                  DataTableCell(
                                    text: ((payerController.currentPage.value -
                                                    1) *
                                                payerController
                                                    .itemsPerPage.value +
                                            i +
                                            1)
                                        .toString(),
                                    style:
                                        AppTextStyles.invoiceDataStyle.copyWith(
                                      color: notifier.text,
                                    ),
                                  ),
                                  DataTableCell(
                                    text: payer.name,
                                    style:
                                        AppTextStyles.invoiceDataStyle.copyWith(
                                      color: notifier.text,
                                      decoration: TextDecoration.underline,
                                    ),
                                    onTap: () => _showPayerDetails(payer),
                                  ),
                                  DataTableCell(
                                    text: payer.phoneNumber,
                                    style:
                                        AppTextStyles.invoiceDataStyle.copyWith(
                                      color: notifier.text,
                                    ),
                                  ),
                                  DataTableActionsCell(
                                    menuItems: [
                                      DataTablePopupMenuItem(
                                        text: 'Edit',
                                        icon: Icons.edit_outlined,
                                        onTap: () => _showPayerDetails(payer),
                                      ),
                                      DataTablePopupMenuItem(
                                        text: 'Delete',
                                        icon: Icons.delete_outline,
                                        isDanger: true,
                                        onTap: () async {
                                          if (!payerController
                                              .isLoading.value) {
                                            await _confirmDelete(payer.id);
                                          }
                                        },
                                      ),
                                    ],
                                  ),
                                ],
                              );
                            }),
                          ],
                        ),
                      ],
                    );
                  }),
                ),
              ),
              Obx(() => PaginationWidget(
                    currentPage: payerController.currentPage.value,
                    totalPages: payerController.totalPages,
                    itemsPerPage: payerController.itemsPerPage.value,
                    onPageChanged: (page) =>
                        payerController.setCurrentPage(page),
                    onItemsPerPageChanged: (count) =>
                        payerController.setItemsPerPage(count),
                  )),
            ],
          ),
        ),
        Obx(() {
          if (!payerController.isDialogOpen.value) {
            return const SizedBox.shrink();
          }

          return Positioned(
            right: 0,
            top: 0,
            bottom: 0,
            width: Get.width < 650 ? Get.width : 400,
            child: Container(
              decoration: BoxDecoration(
                color: notifier.getBgColor,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(10),
                    blurRadius: 10,
                    offset: const Offset(0, 0),
                  ),
                ],
              ),
              child: Form(
                key: payerController.formKey,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Add New Payer',
                            style: AppTextStyles.titleStyle.copyWith(
                              color: notifier.text,
                            ),
                          ),
                          IconButton(
                            onPressed: payerController.closeDialog,
                            icon: const Icon(Icons.close),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      TextFormField(
                        controller: payerController.nameController,
                        decoration: InputDecoration(
                          labelText: 'Name',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          filled: true,
                          fillColor: notifier.textFileColor,
                        ),
                        validator: payerController.validateName,
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: payerController.phoneNumberController,
                        decoration: InputDecoration(
                          labelText: 'Phone Number',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          filled: true,
                          fillColor: notifier.textFileColor,
                        ),
                        keyboardType: TextInputType.phone,
                        validator: payerController.validatePhoneNumber,
                      ),
                      const SizedBox(height: 24),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: payerController.addPayer,
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text('Add Payer'),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }),
      ],
    );
  }

  Future<void> _confirmDelete(String id) {
    return Get.dialog(
      AlertDialog(
        title: const Text('Confirm Delete'),
        content: const Text('Are you sure you want to delete this payer?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Get.back();
              Get.dialog(
                const Center(
                  child: CircularProgressIndicator(),
                ),
                barrierDismissible: false,
              );
              await payerController.deletePayer(id);
              Get.back();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showPayerDetails(PayerModel payer) {
    payerController.nameController.text = payer.name;
    payerController.phoneNumberController.text = payer.phoneNumber;

    Get.dialog(
      Dialog(
        insetPadding: const EdgeInsets.all(20),
        child: Container(
          width: Get.width < 650 ? Get.width : 500,
          padding: const EdgeInsets.all(20),
          child: Form(
            key: payerController.formKey,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Payer Details',
                        style: AppTextStyles.titleStyle.copyWith(
                          color: notifier.text,
                        ),
                      ),
                      IconButton(
                        onPressed: () => Get.back(),
                        icon: const Icon(Icons.close),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  TextFormField(
                    controller: payerController.nameController,
                    decoration: InputDecoration(
                      labelText: 'Name',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      filled: true,
                      fillColor: notifier.textFileColor,
                    ),
                    validator: payerController.validateName,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: payerController.phoneNumberController,
                    decoration: InputDecoration(
                      labelText: 'Phone Number',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      filled: true,
                      fillColor: notifier.textFileColor,
                    ),
                    keyboardType: TextInputType.phone,
                    validator: payerController.validatePhoneNumber,
                  ),
                  const SizedBox(height: 24),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        if (payerController.formKey.currentState!.validate()) {
                          final updatedPayer = PayerModel(
                            id: payer.id,
                            name: payerController.nameController.text,
                            phoneNumber:
                                payerController.phoneNumberController.text,
                            createdAt: payer.createdAt,
                          );
                          payerController.updatePayer(updatedPayer);
                          Get.back();
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('Update Payer'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
