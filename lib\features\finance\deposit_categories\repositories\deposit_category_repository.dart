import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/firebase_service/finance/deposit_category_firebase_service.dart';
import 'package:logestics/models/finance/deposit_category_model.dart';

abstract class DepositCategoryRepository {
  Future<Either<FailureObj, List<DepositCategoryModel>>> getCategories();
  Future<Either<FailureObj, SuccessObj>> createCategory(
      DepositCategoryModel category);
  Future<Either<FailureObj, SuccessObj>> deleteCategory(String categoryId);
  Future<Either<FailureObj, bool>> checkCategoryExists(String name);
  Future<Either<FailureObj, void>> addCategory(DepositCategoryModel category);
  Future<Either<FailureObj, void>> updateCategory(
      DepositCategoryModel category);
  Future<Either<FailureObj, DepositCategoryModel>> getCategoryById(
      String categoryId);

  // Real-time listening methods
  Stream<List<DepositCategoryModel>> listenToCategories();
  Stream<List<DocumentChange>> listenToCategoryChanges();
}

class DepositCategoryRepositoryImpl implements DepositCategoryRepository {
  final DepositCategoryFirebaseService _firebaseService;

  DepositCategoryRepositoryImpl(this._firebaseService);

  @override
  Future<Either<FailureObj, SuccessObj>> createCategory(
      DepositCategoryModel category) async {
    try {
      log('Checking if category exists: ${category.name}');
      final exists = await _firebaseService.checkCategoryExists(category.name);
      if (exists) {
        return Left(FailureObj(
          code: 'category-exists',
          message: 'A category with this name already exists',
        ));
      }

      log('Creating category: ${category.name}');
      await _firebaseService.createCategory(
          category.name, category.description);
      return Right(SuccessObj(message: 'Category created successfully'));
    } catch (e) {
      log('Error creating category: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, List<DepositCategoryModel>>> getCategories() async {
    try {
      log('Fetching deposit categories from repository');
      final categories = await _firebaseService.getCategories();
      log('Successfully fetched ${categories.length} categories from Firebase');
      return Right(categories);
    } catch (e) {
      log('Error fetching categories: $e');
      return Left(FailureObj(
          code: AppStrings.errorS, message: 'Failed to fetch categories: $e'));
    }
  }

  @override
  Future<Either<FailureObj, void>> addCategory(
      DepositCategoryModel category) async {
    try {
      log('Adding category through repository: ${category.name}');
      await _firebaseService.createCategory(
          category.name, category.description);
      return const Right(null);
    } catch (e) {
      log('Error adding category: $e');
      return Left(FailureObj(
          code: AppStrings.errorS, message: 'Failed to add category: $e'));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> deleteCategory(
      String categoryId) async {
    try {
      log('Deleting category: $categoryId');
      await _firebaseService.deleteCategory(categoryId);
      return Right(SuccessObj(message: 'Category deleted successfully'));
    } catch (e) {
      log('Error deleting category: $e');
      return Left(FailureObj(
          code: AppStrings.errorS, message: 'Failed to delete category: $e'));
    }
  }

  @override
  Future<Either<FailureObj, void>> updateCategory(
      DepositCategoryModel category) async {
    try {
      log('Updating category: ${category.id}');
      await _firebaseService.updateCategory(category);
      return const Right(null);
    } catch (e) {
      log('Error updating category: $e');
      return Left(FailureObj(
          code: AppStrings.errorS, message: 'Failed to update category: $e'));
    }
  }

  @override
  Future<Either<FailureObj, bool>> checkCategoryExists(String name) async {
    try {
      log('Checking if category exists: $name');
      final exists = await _firebaseService.checkCategoryExists(name);
      return Right(exists);
    } catch (e) {
      log('Error checking category existence: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, DepositCategoryModel>> getCategoryById(
      String categoryId) async {
    try {
      log('Fetching category by ID: $categoryId');
      final category = await _firebaseService.getCategoryById(categoryId);
      if (category == null) {
        return Left(
            FailureObj(code: 'not-found', message: 'Category not found'));
      }
      return Right(category);
    } catch (e) {
      log('Error fetching category: $e');
      return Left(FailureObj(
          code: AppStrings.errorS, message: 'Failed to get category: $e'));
    }
  }

  @override
  Stream<List<DepositCategoryModel>> listenToCategories() {
    return _firebaseService.listenToCategories();
  }

  @override
  Stream<List<DocumentChange>> listenToCategoryChanges() {
    return _firebaseService.listenToCategoryChanges();
  }
}
