import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/firebase_service/finance/check_usage_firebase_service.dart';
import 'package:logestics/models/finance/check_usage_model.dart';

class CheckUsageRepository {
  final CheckUsageFirebaseService _firebaseService;

  CheckUsageRepository(this._firebaseService);

  Future<Either<FailureObj, void>> createCheckUsage(
      CheckUsageModel checkUsage) async {
    try {
      await _firebaseService.createCheckUsage(checkUsage);
      return const Right(null);
    } catch (e) {
      return Left(FailureObj(
          code: 'check-usage-creation-error', message: e.toString()));
    }
  }

  Future<Either<FailureObj, List<CheckUsageModel>>> getCheckUsages() async {
    try {
      final checkUsages = await _firebaseService.getCheckUsages();
      return Right(checkUsages);
    } catch (e) {
      return Left(
          FailureObj(code: 'check-usage-fetch-error', message: e.toString()));
    }
  }

  Future<Either<FailureObj, List<CheckUsageModel>>> getActiveChecks() async {
    try {
      final activeChecks = await _firebaseService.getActiveChecks();
      return Right(activeChecks);
    } catch (e) {
      return Left(
          FailureObj(code: 'active-checks-fetch-error', message: e.toString()));
    }
  }

  Future<Either<FailureObj, void>> updateCheckStatus(
      String checkUsageId, String newStatus) async {
    try {
      await _firebaseService.updateCheckStatus(checkUsageId, newStatus);
      return const Right(null);
    } catch (e) {
      return Left(
          FailureObj(code: 'check-status-update-error', message: e.toString()));
    }
  }

  Future<Either<FailureObj, void>> deleteCheckUsage(String checkUsageId) async {
    try {
      await _firebaseService.deleteCheckUsage(checkUsageId);
      return const Right(null);
    } catch (e) {
      return Left(
          FailureObj(code: 'check-usage-delete-error', message: e.toString()));
    }
  }

  Stream<List<CheckUsageModel>> listenToCheckUsages() {
    return _firebaseService.listenToCheckUsages();
  }

  Stream<List<CheckUsageModel>> listenToActiveChecks() {
    return _firebaseService.listenToActiveChecks();
  }
}
