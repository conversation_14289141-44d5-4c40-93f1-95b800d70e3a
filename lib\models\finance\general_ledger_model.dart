import 'package:cloud_firestore/cloud_firestore.dart';
import 'chart_of_accounts_model.dart';
import 'journal_entry_model.dart';

/// Model for General Ledger Account Entry
class GeneralLedgerEntry {
  final String id;
  final String accountId;
  final String accountNumber;
  final String accountName;
  final AccountType accountType;
  final AccountCategory accountCategory;
  final DateTime transactionDate;
  final String description;
  final String reference;
  final double debitAmount;
  final double creditAmount;
  final double runningBalance;
  final String journalEntryId;
  final String uid;
  final DateTime createdAt;

  GeneralLedgerEntry({
    required this.id,
    required this.accountId,
    required this.accountNumber,
    required this.accountName,
    required this.accountType,
    required this.accountCategory,
    required this.transactionDate,
    required this.description,
    required this.reference,
    required this.debitAmount,
    required this.creditAmount,
    required this.runningBalance,
    required this.journalEntryId,
    required this.uid,
    required this.createdAt,
  });

  /// Convert to JSON for Firestore
  Map<String, dynamic> to<PERSON>son() {
    return {
      'id': id,
      'accountId': accountId,
      'accountNumber': accountNumber,
      'accountName': accountName,
      'accountType': accountType.name,
      'accountCategory': accountCategory.name,
      'transactionDate': Timestamp.fromDate(transactionDate),
      'description': description,
      'reference': reference,
      'debitAmount': debitAmount,
      'creditAmount': creditAmount,
      'runningBalance': runningBalance,
      'journalEntryId': journalEntryId,
      'uid': uid,
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }

  /// Create from JSON (Firestore document)
  factory GeneralLedgerEntry.fromJson(Map<String, dynamic> json) {
    return GeneralLedgerEntry(
      id: json['id'] ?? '',
      accountId: json['accountId'] ?? '',
      accountNumber: json['accountNumber'] ?? '',
      accountName: json['accountName'] ?? '',
      accountType: AccountType.fromString(json['accountType'] ?? 'cash'),
      accountCategory: AccountCategory.fromString(json['accountCategory'] ?? 'assets'),
      transactionDate: (json['transactionDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      description: json['description'] ?? '',
      reference: json['reference'] ?? '',
      debitAmount: (json['debitAmount'] ?? 0.0).toDouble(),
      creditAmount: (json['creditAmount'] ?? 0.0).toDouble(),
      runningBalance: (json['runningBalance'] ?? 0.0).toDouble(),
      journalEntryId: json['journalEntryId'] ?? '',
      uid: json['uid'] ?? '',
      createdAt: (json['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  /// Copy with method for creating modified instances
  GeneralLedgerEntry copyWith({
    String? id,
    String? accountId,
    String? accountNumber,
    String? accountName,
    AccountType? accountType,
    AccountCategory? accountCategory,
    DateTime? transactionDate,
    String? description,
    String? reference,
    double? debitAmount,
    double? creditAmount,
    double? runningBalance,
    String? journalEntryId,
    String? uid,
    DateTime? createdAt,
  }) {
    return GeneralLedgerEntry(
      id: id ?? this.id,
      accountId: accountId ?? this.accountId,
      accountNumber: accountNumber ?? this.accountNumber,
      accountName: accountName ?? this.accountName,
      accountType: accountType ?? this.accountType,
      accountCategory: accountCategory ?? this.accountCategory,
      transactionDate: transactionDate ?? this.transactionDate,
      description: description ?? this.description,
      reference: reference ?? this.reference,
      debitAmount: debitAmount ?? this.debitAmount,
      creditAmount: creditAmount ?? this.creditAmount,
      runningBalance: runningBalance ?? this.runningBalance,
      journalEntryId: journalEntryId ?? this.journalEntryId,
      uid: uid ?? this.uid,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// Get net amount (debit - credit)
  double get netAmount => debitAmount - creditAmount;

  /// Check if this is a debit entry
  bool get isDebit => debitAmount > 0;

  /// Check if this is a credit entry
  bool get isCredit => creditAmount > 0;

  @override
  String toString() {
    return 'GeneralLedgerEntry(id: $id, accountNumber: $accountNumber, accountName: $accountName, debit: $debitAmount, credit: $creditAmount, balance: $runningBalance)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GeneralLedgerEntry && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Model for General Ledger Account Summary
class GeneralLedgerAccountSummary {
  final ChartOfAccountsModel account;
  final List<GeneralLedgerEntry> entries;
  final double openingBalance;
  final double closingBalance;
  final double totalDebits;
  final double totalCredits;
  final int transactionCount;

  GeneralLedgerAccountSummary({
    required this.account,
    required this.entries,
    required this.openingBalance,
    required this.closingBalance,
    required this.totalDebits,
    required this.totalCredits,
    required this.transactionCount,
  });

  /// Calculate net change for the period
  double get netChange => totalDebits - totalCredits;

  /// Check if account has activity
  bool get hasActivity => entries.isNotEmpty;

  /// Get entries for a specific date range
  List<GeneralLedgerEntry> getEntriesForPeriod(DateTime startDate, DateTime endDate) {
    return entries.where((entry) {
      return entry.transactionDate.isAfter(startDate.subtract(const Duration(days: 1))) &&
             entry.transactionDate.isBefore(endDate.add(const Duration(days: 1)));
    }).toList();
  }

  @override
  String toString() {
    return 'GeneralLedgerAccountSummary(account: ${account.accountName}, entries: ${entries.length}, balance: $closingBalance)';
  }
}

/// Model for General Ledger Report
class GeneralLedgerReport {
  final String reportId;
  final String companyName;
  final String uid;
  final DateTime startDate;
  final DateTime endDate;
  final DateTime generatedAt;
  final String generatedBy;
  final List<GeneralLedgerAccountSummary> accountSummaries;
  final double totalDebits;
  final double totalCredits;

  GeneralLedgerReport({
    required this.reportId,
    required this.companyName,
    required this.uid,
    required this.startDate,
    required this.endDate,
    required this.generatedAt,
    required this.generatedBy,
    required this.accountSummaries,
    required this.totalDebits,
    required this.totalCredits,
  });

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'reportId': reportId,
      'companyName': companyName,
      'uid': uid,
      'startDate': Timestamp.fromDate(startDate),
      'endDate': Timestamp.fromDate(endDate),
      'generatedAt': Timestamp.fromDate(generatedAt),
      'generatedBy': generatedBy,
      'accountSummaries': accountSummaries.map((summary) => {
        'account': summary.account.toJson(),
        'entries': summary.entries.map((entry) => entry.toJson()).toList(),
        'openingBalance': summary.openingBalance,
        'closingBalance': summary.closingBalance,
        'totalDebits': summary.totalDebits,
        'totalCredits': summary.totalCredits,
        'transactionCount': summary.transactionCount,
      }).toList(),
      'totalDebits': totalDebits,
      'totalCredits': totalCredits,
    };
  }

  /// Create from JSON
  factory GeneralLedgerReport.fromJson(Map<String, dynamic> json) {
    final accountSummariesData = json['accountSummaries'] as List<dynamic>? ?? [];
    final accountSummaries = accountSummariesData.map((summaryData) {
      final account = ChartOfAccountsModel.fromJson(summaryData['account']);
      final entriesData = summaryData['entries'] as List<dynamic>? ?? [];
      final entries = entriesData.map((entryData) => GeneralLedgerEntry.fromJson(entryData)).toList();
      
      return GeneralLedgerAccountSummary(
        account: account,
        entries: entries,
        openingBalance: (summaryData['openingBalance'] ?? 0.0).toDouble(),
        closingBalance: (summaryData['closingBalance'] ?? 0.0).toDouble(),
        totalDebits: (summaryData['totalDebits'] ?? 0.0).toDouble(),
        totalCredits: (summaryData['totalCredits'] ?? 0.0).toDouble(),
        transactionCount: summaryData['transactionCount'] ?? 0,
      );
    }).toList();

    return GeneralLedgerReport(
      reportId: json['reportId'] ?? '',
      companyName: json['companyName'] ?? '',
      uid: json['uid'] ?? '',
      startDate: (json['startDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      endDate: (json['endDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      generatedAt: (json['generatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      generatedBy: json['generatedBy'] ?? '',
      accountSummaries: accountSummaries,
      totalDebits: (json['totalDebits'] ?? 0.0).toDouble(),
      totalCredits: (json['totalCredits'] ?? 0.0).toDouble(),
    );
  }

  /// Check if debits equal credits (balanced)
  bool get isBalanced => (totalDebits - totalCredits).abs() < 0.01;

  /// Get accounts with activity only
  List<GeneralLedgerAccountSummary> get activeAccounts {
    return accountSummaries.where((summary) => summary.hasActivity).toList();
  }

  @override
  String toString() {
    return 'GeneralLedgerReport(reportId: $reportId, accounts: ${accountSummaries.length}, totalDebits: $totalDebits, totalCredits: $totalCredits)';
  }
}
