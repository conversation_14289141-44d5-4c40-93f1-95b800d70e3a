import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/features/accounting/aged_reports/presentation/controllers/aged_reports_controller.dart';
import 'package:logestics/models/finance/aged_report_model.dart';

/// Table widget for displaying Aged Receivables data
/// Follows the existing table patterns used in other financial modules
class AgedReceivablesTable extends StatelessWidget {
  const AgedReceivablesTable({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<AgedReportsController>();

    return Obx(() {
      final report = controller.currentReceivablesReport.value;
      if (report == null) {
        return const Center(
          child: Text(
            'No data to display',
            style: TextStyle(color: Colors.grey),
          ),
        );
      }

      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            // Table header
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: Row(
                children: [
                  const Expanded(
                    flex: 3,
                    child: Text(
                      'Customer',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                  const Expanded(
                    flex: 2,
                    child: Text(
                      'Current (0-30)',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                      textAlign: TextAlign.right,
                    ),
                  ),
                  const Expanded(
                    flex: 2,
                    child: Text(
                      '31-60 Days',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                      textAlign: TextAlign.right,
                    ),
                  ),
                  const Expanded(
                    flex: 2,
                    child: Text(
                      '61-90 Days',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                      textAlign: TextAlign.right,
                    ),
                  ),
                  const Expanded(
                    flex: 2,
                    child: Text(
                      'Over 90 Days',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                      textAlign: TextAlign.right,
                    ),
                  ),
                  const Expanded(
                    flex: 2,
                    child: Text(
                      'Total Outstanding',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                      textAlign: TextAlign.right,
                    ),
                  ),
                ],
              ),
            ),

            // Table body
            Expanded(
              child: ListView.builder(
                itemCount: report.customerSummaries.length,
                itemBuilder: (context, index) {
                  final summary = report.customerSummaries[index];
                  return _buildCustomerRow(summary, index);
                },
              ),
            ),

            // Table footer with totals
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(8),
                  bottomRight: Radius.circular(8),
                ),
                border: Border(
                  top: BorderSide(color: Colors.grey.shade400, width: 2),
                ),
              ),
              child: Row(
                children: [
                  const Expanded(
                    flex: 3,
                    child: Text(
                      'TOTAL',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      '\$${report.totalCurrentAmount.toStringAsFixed(2)}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.black87,
                      ),
                      textAlign: TextAlign.right,
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      '\$${report.totalPeriod30Amount.toStringAsFixed(2)}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.black87,
                      ),
                      textAlign: TextAlign.right,
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      '\$${report.totalPeriod60Amount.toStringAsFixed(2)}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.black87,
                      ),
                      textAlign: TextAlign.right,
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      '\$${report.totalPeriod90PlusAmount.toStringAsFixed(2)}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.black87,
                      ),
                      textAlign: TextAlign.right,
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      '\$${report.grandTotalOutstanding.toStringAsFixed(2)}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.green,
                      ),
                      textAlign: TextAlign.right,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    });
  }

  /// Build individual customer row
  Widget _buildCustomerRow(AgedReceivableCustomerSummary summary, int index) {
    final isEven = index % 2 == 0;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isEven ? Colors.white : Colors.grey.shade50,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  summary.customerName,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ),
                if (summary.totalInvoices > 0)
                  Text(
                    '${summary.totalInvoices} invoice${summary.totalInvoices > 1 ? 's' : ''}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
              ],
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              '\$${summary.currentAmount.toStringAsFixed(2)}',
              style: TextStyle(
                color: summary.currentAmount > 0
                    ? Colors.green.shade700
                    : Colors.grey,
                fontWeight: summary.currentAmount > 0
                    ? FontWeight.w500
                    : FontWeight.normal,
              ),
              textAlign: TextAlign.right,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              '\$${summary.period30Amount.toStringAsFixed(2)}',
              style: TextStyle(
                color: summary.period30Amount > 0
                    ? Colors.orange.shade700
                    : Colors.grey,
                fontWeight: summary.period30Amount > 0
                    ? FontWeight.w500
                    : FontWeight.normal,
              ),
              textAlign: TextAlign.right,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              '\$${summary.period60Amount.toStringAsFixed(2)}',
              style: TextStyle(
                color: summary.period60Amount > 0
                    ? Colors.red.shade600
                    : Colors.grey,
                fontWeight: summary.period60Amount > 0
                    ? FontWeight.w500
                    : FontWeight.normal,
              ),
              textAlign: TextAlign.right,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              '\$${summary.period90PlusAmount.toStringAsFixed(2)}',
              style: TextStyle(
                color: summary.period90PlusAmount > 0
                    ? Colors.red.shade800
                    : Colors.grey,
                fontWeight: summary.period90PlusAmount > 0
                    ? FontWeight.bold
                    : FontWeight.normal,
              ),
              textAlign: TextAlign.right,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              '\$${summary.totalOutstanding.toStringAsFixed(2)}',
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }
}
