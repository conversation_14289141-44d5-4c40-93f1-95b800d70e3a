import 'dart:developer';
import 'package:get/get.dart';
import 'package:logestics/models/finance/fuel_card_usage_model.dart';
import 'package:logestics/firebase_service/finance/fuel_card_usage_firebase_service.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';

class FuelCardUsageController extends GetxController {
  final FuelCardUsageFirebaseService _usageService =
      FuelCardUsageFirebaseService();

  // Observable variables
  final RxList<FuelCardUsageModel> allUsageRecords = <FuelCardUsageModel>[].obs;
  final RxList<FuelCardUsageModel> filteredUsageRecords =
      <FuelCardUsageModel>[].obs;
  final RxBool isLoading = false.obs;
  final RxString error = ''.obs;
  final RxString selectedFuelCardId = ''.obs;
  final RxString selectedFuelCardNumber = ''.obs;

  @override
  void onInit() {
    super.onInit();
    loadAllUsageRecords();
  }

  // Load all fuel card usage records
  Future<void> loadAllUsageRecords() async {
    try {
      isLoading.value = true;
      error.value = '';

      final result = await _usageService.getFuelCardUsage();
      result.fold(
        (failure) {
          error.value = failure.message;
          log('Error loading fuel card usage: ${failure.message}');
          SnackbarUtils.showError(AppStrings.error, failure.message);
        },
        (usageRecords) {
          allUsageRecords.value = usageRecords;
          filteredUsageRecords.value = usageRecords;
          log('Loaded ${usageRecords.length} fuel card usage records');
        },
      );
    } catch (e) {
      error.value = 'An unexpected error occurred: $e';
      log('Unexpected error loading fuel card usage: $e');
      SnackbarUtils.showError(
          AppStrings.error, 'Failed to load usage records: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Load usage records for a specific fuel card
  Future<void> loadUsageForFuelCard(
      String fuelCardId, String cardNumber) async {
    try {
      isLoading.value = true;
      error.value = '';
      selectedFuelCardId.value = fuelCardId;
      selectedFuelCardNumber.value = cardNumber;

      final result = await _usageService.getFuelCardUsageByCard(fuelCardId);
      result.fold(
        (failure) {
          error.value = failure.message;
          log('Error loading usage for fuel card $fuelCardId: ${failure.message}');
          SnackbarUtils.showError(AppStrings.error, failure.message);
        },
        (usageRecords) {
          filteredUsageRecords.value = usageRecords;
          log('Loaded ${usageRecords.length} usage records for fuel card: $cardNumber');
        },
      );
    } catch (e) {
      error.value = 'An unexpected error occurred: $e';
      log('Unexpected error loading usage for fuel card: $e');
      SnackbarUtils.showError(
          AppStrings.error, 'Failed to load usage records: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Load usage records for a specific voucher
  Future<void> loadUsageForVoucher(String voucherId) async {
    try {
      isLoading.value = true;
      error.value = '';

      final result = await _usageService.getFuelCardUsageByVoucher(voucherId);
      result.fold(
        (failure) {
          error.value = failure.message;
          log('Error loading usage for voucher $voucherId: ${failure.message}');
          SnackbarUtils.showError(AppStrings.error, failure.message);
        },
        (usageRecords) {
          filteredUsageRecords.value = usageRecords;
          log('Loaded ${usageRecords.length} usage records for voucher: $voucherId');
        },
      );
    } catch (e) {
      error.value = 'An unexpected error occurred: $e';
      log('Unexpected error loading usage for voucher: $e');
      SnackbarUtils.showError(
          AppStrings.error, 'Failed to load usage records: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Filter records by fuel card
  void filterByFuelCard(String fuelCardId) {
    if (fuelCardId.isEmpty) {
      filteredUsageRecords.value = allUsageRecords;
    } else {
      filteredUsageRecords.value = allUsageRecords
          .where((record) => record.fuelCardId == fuelCardId)
          .toList();
    }
  }

  // Clear filters and show all records
  void clearFilters() {
    selectedFuelCardId.value = '';
    selectedFuelCardNumber.value = '';
    filteredUsageRecords.value = allUsageRecords;
  }

  // Refresh data
  @override
  Future<void> refresh() async {
    await loadAllUsageRecords();
  }

  // Get total liters used for a fuel card
  double getTotalLitersForCard(String fuelCardId) {
    return allUsageRecords
        .where((record) => record.fuelCardId == fuelCardId)
        .fold(0.0, (sum, record) => sum + record.litersUsed);
  }

  // Get total amount spent for a fuel card
  double getTotalAmountForCard(String fuelCardId) {
    return allUsageRecords
        .where((record) => record.fuelCardId == fuelCardId)
        .fold(0.0, (sum, record) => sum + record.totalAmount);
  }

  // Get usage count for a fuel card
  int getUsageCountForCard(String fuelCardId) {
    return allUsageRecords
        .where((record) => record.fuelCardId == fuelCardId)
        .length;
  }

  // Delete a usage record (if needed for admin purposes)
  Future<void> deleteUsageRecord(String usageId) async {
    try {
      isLoading.value = true;

      final result = await _usageService.deleteFuelCardUsage(usageId);
      result.fold(
        (failure) {
          error.value = failure.message;
          SnackbarUtils.showError(AppStrings.error, failure.message);
        },
        (success) {
          // Remove from local lists
          allUsageRecords.removeWhere((record) => record.id == usageId);
          filteredUsageRecords.removeWhere((record) => record.id == usageId);
          SnackbarUtils.showSuccess(
              AppStrings.success, 'Usage record deleted successfully');
        },
      );
    } catch (e) {
      error.value = 'Failed to delete usage record: $e';
      SnackbarUtils.showError(
          AppStrings.error, 'Failed to delete usage record: $e');
    } finally {
      isLoading.value = false;
    }
  }
}
