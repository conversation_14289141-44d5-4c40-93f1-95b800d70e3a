import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:logestics/models/finance/chart_of_accounts_model.dart';

void main() {
  group('Chart of Accounts Basic Tests', () {
    setUp(() {
      Get.testMode = true;
    });

    tearDown(() {
      Get.reset();
    });

    test('AccountCategory enum should have correct values', () {
      expect(AccountCategory.assets, isNotNull);
      expect(AccountCategory.liabilities, isNotNull);
      expect(AccountCategory.equity, isNotNull);
      expect(AccountCategory.revenue, isNotNull);
      expect(AccountCategory.expenses, isNotNull);
    });

    test('AccountType enum should have correct values', () {
      expect(AccountType.currentAssets, isNotNull);
      expect(AccountType.fixedAssets, isNotNull);
      expect(AccountType.currentLiabilities, isNotNull);
      expect(AccountType.longTermLiabilities, isNotNull);
      expect(AccountType.ownersEquity, isNotNull);
      expect(AccountType.salesRevenue, isNotNull);
      expect(AccountType.operatingExpenses, isNotNull);
      expect(AccountType.cash, isNotNull);
      expect(AccountType.bank, isNotNull);
    });

    test('ChartOfAccountsModel should be creatable', () {
      final account = ChartOfAccountsModel(
        id: 'test-id',
        accountName: 'Test Account',
        accountNumber: '1001',
        category: AccountCategory.assets,
        accountType: AccountType.currentAssets,
        description: 'Test Description',
        isActive: true,
        createdAt: DateTime.now(),
        uid: 'test-uid',
      );

      expect(account.id, 'test-id');
      expect(account.accountName, 'Test Account');
      expect(account.accountNumber, '1001');
      expect(account.category, AccountCategory.assets);
      expect(account.accountType, AccountType.currentAssets);
      expect(account.description, 'Test Description');
      expect(account.isActive, true);
      expect(account.uid, 'test-uid');
    });

    test('ChartOfAccountsModel should handle JSON serialization', () {
      final account = ChartOfAccountsModel(
        id: 'test-id',
        accountName: 'Test Account',
        accountNumber: '1001',
        category: AccountCategory.assets,
        accountType: AccountType.currentAssets,
        description: 'Test Description',
        isActive: true,
        createdAt: DateTime.now(),
        uid: 'test-uid',
      );

      final json = account.toJson();
      expect(json, isA<Map<String, dynamic>>());
      expect(json['id'], 'test-id');
      expect(json['accountName'], 'Test Account');
      expect(json['accountNumber'], '1001');
      expect(json['isActive'], true);
      expect(json['uid'], 'test-uid');
    });

    test('ChartOfAccountsModel should handle JSON deserialization', () {
      final now = DateTime.now();
      final json = {
        'id': 'test-id',
        'accountName': 'Test Account',
        'accountNumber': '1001',
        'category': 'assets',
        'accountType': 'currentAssets',
        'description': 'Test Description',
        'isActive': true,
        'createdAt': now.millisecondsSinceEpoch,
        'uid': 'test-uid',
      };

      final account = ChartOfAccountsModel.fromJson(json);
      expect(account.id, 'test-id');
      expect(account.accountName, 'Test Account');
      expect(account.accountNumber, '1001');
      expect(account.category, AccountCategory.assets);
      expect(account.accountType, AccountType.currentAssets);
      expect(account.description, 'Test Description');
      expect(account.isActive, true);
      expect(account.uid, 'test-uid');
    });
  });
}
