import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/features/slab/repositories/slab_repository.dart';

class DeleteSlabUseCase {
  final SlabRepository repository;

  DeleteSlabUseCase(this.repository);

  Future<Either<FailureObj, void>> call({
    required String slabId,
  }) async {
    return await repository.deleteSlab(slabId: slabId);
  }
}
