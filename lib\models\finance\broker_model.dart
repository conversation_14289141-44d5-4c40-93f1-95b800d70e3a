class BrokerModel {
  final String id;
  final String name;
  final String? description;
  final String? phoneNumber;
  final String? email;
  final String? address;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String uid; // User ID who owns this broker

  BrokerModel({
    required this.id,
    required this.name,
    this.description,
    this.phoneNumber,
    this.email,
    this.address,
    required this.createdAt,
    required this.updatedAt,
    this.uid = '', // Default empty string for backward compatibility
  });

  factory BrokerModel.fromJson(Map<String, dynamic> json) {
    return BrokerModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] as String?,
      phoneNumber: json['phoneNumber'] ?? '',
      email: json['email'] as String?,
      address: json['address'] as String?,
      createdAt: json['createdAt'] != null
          ? (json['createdAt'] is DateTime
              ? (json['createdAt'] as DateTime)
              : json['createdAt'] is String
                  ? DateTime.parse(json['createdAt'])
                  : json['createdAt'] is int
                      ? DateTime.fromMillisecondsSinceEpoch(json['createdAt'])
                      : DateTime.now())
          : DateTime.now(),
      updatedAt: json['updatedAt'] is DateTime
          ? (json['updatedAt'] as DateTime)
          : json['updatedAt'] is String
              ? DateTime.parse(json['updatedAt'])
              : json['updatedAt'] is int
                  ? DateTime.fromMillisecondsSinceEpoch(json['updatedAt'])
                  : DateTime.now(),
      uid: json['uid'] ?? '', // Extract UID from JSON
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'phoneNumber': phoneNumber,
      'email': email,
      'address': address,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'uid': uid, // Include UID in JSON
    };
  }

  BrokerModel copyWith({
    String? id,
    String? name,
    String? description,
    String? phoneNumber,
    String? email,
    String? address,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? uid,
  }) {
    return BrokerModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      address: address ?? this.address,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      uid: uid ?? this.uid,
    );
  }

  @override
  String toString() {
    return 'BrokerModel(id: $id, name: $name, phoneNumber: $phoneNumber, email: $email, address: $address, createdAt: $createdAt, updatedAt: $updatedAt, uid: $uid)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BrokerModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
