import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/features/locations/repositories/district_repository.dart';

class DeleteDistrictUseCase {
  final DistrictRepository _districtRepository;

  DeleteDistrictUseCase(this._districtRepository);

  /// Executes the use case to delete an District.
  Future<Either<FailureObj, SuccessObj>> call({
    required String districtId,
  }) async {
    // Validate inputs before calling the repository

    if (districtId.isEmpty) {
      return Left(FailureObj(
        code: 'invalid-District',
        message: 'District "districtId" must not be empty.',
      ));
    }

    // Call the repository
    return await _districtRepository.deleteDistrict(
      districtId: districtId,
    );
  }
}
