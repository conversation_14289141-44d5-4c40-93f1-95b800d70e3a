import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../../core/utils/app_constants/firebase/collection_names.dart';
import '../../../../models/accounting/financial_dashboard_models.dart';

/// Firebase service for Financial Dashboard data operations
class FinancialDashboardFirebaseService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Get financial summary for dashboard
  Future<FinancialSummaryModel> getFinancialSummary(
    String uid,
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      log('Firebase: Getting financial summary for period: $startDate to $endDate');

      // Get all journal entries for the period
      final journalEntriesQuery = await _firestore
          .collection(AppCollection.journalEntriesCollection)
          .where('uid', isEqualTo: uid)
          .where('entryDate',
              isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
          .where('entryDate', isLessThanOrEqualTo: Timestamp.fromDate(endDate))
          .where('status', isEqualTo: 'posted')
          .get();

      // Get chart of accounts for categorization
      final accountsQuery = await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .where('uid', isEqualTo: uid)
          .where('isActive', isEqualTo: true)
          .get();

      // Create account lookup map
      final accountMap = <String, Map<String, dynamic>>{};
      for (final doc in accountsQuery.docs) {
        final data = doc.data();
        accountMap[doc.id] = data;
      }

      // Calculate financial metrics
      double totalRevenue = 0.0;
      double totalExpenses = 0.0;
      double totalAssets = 0.0;
      double totalLiabilities = 0.0;
      double totalEquity = 0.0;
      double cashBalance = 0.0;
      double accountsReceivable = 0.0;
      double accountsPayable = 0.0;

      // Process journal entries
      for (final entryDoc in journalEntriesQuery.docs) {
        final entryData = entryDoc.data();
        final lines = entryData['lines'] as List<dynamic>? ?? [];

        for (final line in lines) {
          final accountId = line['accountId'] as String;
          final amount = (line['amount'] as num?)?.toDouble() ?? 0.0;
          final type = line['type'] as String; // 'debit' or 'credit'

          final account = accountMap[accountId];
          if (account == null) continue;

          final category = account['category'] as String;
          final accountType = account['accountType'] as String;

          // Calculate based on account type and transaction type
          switch (category.toLowerCase()) {
            case 'assets':
              if (type == 'debit') {
                totalAssets += amount;
                if (accountType.toLowerCase().contains('cash') ||
                    accountType.toLowerCase().contains('bank')) {
                  cashBalance += amount;
                } else if (accountType.toLowerCase().contains('receivable')) {
                  accountsReceivable += amount;
                }
              } else {
                totalAssets -= amount;
                if (accountType.toLowerCase().contains('cash') ||
                    accountType.toLowerCase().contains('bank')) {
                  cashBalance -= amount;
                } else if (accountType.toLowerCase().contains('receivable')) {
                  accountsReceivable -= amount;
                }
              }
              break;

            case 'liabilities':
              if (type == 'credit') {
                totalLiabilities += amount;
                if (accountType.toLowerCase().contains('payable')) {
                  accountsPayable += amount;
                }
              } else {
                totalLiabilities -= amount;
                if (accountType.toLowerCase().contains('payable')) {
                  accountsPayable -= amount;
                }
              }
              break;

            case 'equity':
              if (type == 'credit') {
                totalEquity += amount;
              } else {
                totalEquity -= amount;
              }
              break;

            case 'revenue':
              if (type == 'credit') {
                totalRevenue += amount;
              } else {
                totalRevenue -= amount;
              }
              break;

            case 'expenses':
              if (type == 'debit') {
                totalExpenses += amount;
              } else {
                totalExpenses -= amount;
              }
              break;
          }
        }
      }

      // Calculate derived metrics
      final netIncome = totalRevenue - totalExpenses;
      final currentRatio =
          totalLiabilities > 0 ? totalAssets / totalLiabilities : 0.0;
      final debtToEquityRatio =
          totalEquity > 0 ? totalLiabilities / totalEquity : 0.0;
      final profitMargin =
          totalRevenue > 0 ? (netIncome / totalRevenue) * 100 : 0.0;

      final summary = FinancialSummaryModel(
        totalRevenue: totalRevenue,
        totalExpenses: totalExpenses,
        netIncome: netIncome,
        totalAssets: totalAssets,
        totalLiabilities: totalLiabilities,
        totalEquity: totalEquity,
        cashBalance: cashBalance,
        accountsReceivable: accountsReceivable,
        accountsPayable: accountsPayable,
        currentRatio: currentRatio,
        debtToEquityRatio: debtToEquityRatio,
        profitMargin: profitMargin,
        asOfDate: endDate,
      );

      log('Firebase: Financial summary calculated successfully');
      return summary;
    } catch (e) {
      log('Firebase: Error getting financial summary: $e');
      rethrow;
    }
  }

  /// Get account balances for dashboard
  Future<List<AccountBalanceModel>> getAccountBalances(
    String uid,
    bool includeInactive,
  ) async {
    try {
      log('Firebase: Getting account balances (includeInactive: $includeInactive)');

      // Get chart of accounts
      Query accountsQuery = _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .where('uid', isEqualTo: uid);

      if (!includeInactive) {
        accountsQuery = accountsQuery.where('isActive', isEqualTo: true);
      }

      final accountsSnapshot = await accountsQuery.get();
      final balances = <AccountBalanceModel>[];

      for (final doc in accountsSnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;

        // Calculate current balance from journal entries
        final balance = await _calculateAccountBalance(doc.id, uid);

        // Get last transaction date
        final lastTransactionDate = await _getLastTransactionDate(doc.id, uid);

        balances.add(AccountBalanceModel(
          accountId: doc.id,
          accountNumber: data['accountNumber'] ?? '',
          accountName: data['accountName'] ?? '',
          accountType: data['accountType'] ?? '',
          category: data['category'] ?? '',
          balance: balance,
          isActive: data['isActive'] ?? true,
          lastTransactionDate: lastTransactionDate,
        ));
      }

      // Sort by account number
      balances.sort((a, b) => a.accountNumber.compareTo(b.accountNumber));

      log('Firebase: Retrieved ${balances.length} account balances');
      return balances;
    } catch (e) {
      log('Firebase: Error getting account balances: $e');
      rethrow;
    }
  }

  /// Calculate account balance from journal entries
  Future<double> _calculateAccountBalance(String accountId, String uid) async {
    try {
      final journalLinesQuery = await _firestore
          .collectionGroup(AppCollection.journalEntryLinesCollection)
          .where('accountId', isEqualTo: accountId)
          .where('uid', isEqualTo: uid)
          .get();

      double balance = 0.0;
      for (final doc in journalLinesQuery.docs) {
        final data = doc.data();
        final amount = (data['amount'] as num?)?.toDouble() ?? 0.0;
        final type = data['type'] as String;

        if (type == 'debit') {
          balance += amount;
        } else {
          balance -= amount;
        }
      }

      return balance;
    } catch (e) {
      log('Firebase: Error calculating account balance: $e');
      return 0.0;
    }
  }

  /// Get last transaction date for account
  Future<DateTime> _getLastTransactionDate(String accountId, String uid) async {
    try {
      final query = await _firestore
          .collectionGroup(AppCollection.journalEntryLinesCollection)
          .where('accountId', isEqualTo: accountId)
          .where('uid', isEqualTo: uid)
          .orderBy('createdAt', descending: true)
          .limit(1)
          .get();

      if (query.docs.isNotEmpty) {
        final data = query.docs.first.data();
        final timestamp = data['createdAt'] as Timestamp?;
        return timestamp?.toDate() ?? DateTime.now();
      }

      return DateTime.now().subtract(const Duration(days: 365));
    } catch (e) {
      log('Firebase: Error getting last transaction date: $e');
      return DateTime.now().subtract(const Duration(days: 365));
    }
  }

  /// Get recent transactions for dashboard
  Future<List<DashboardTransactionModel>> getRecentTransactions(
    String uid,
    int limit,
  ) async {
    try {
      log('Firebase: Getting recent transactions (limit: $limit)');

      final journalEntriesQuery = await _firestore
          .collection(AppCollection.journalEntriesCollection)
          .where('uid', isEqualTo: uid)
          .where('status', isEqualTo: 'posted')
          .orderBy('entryDate', descending: true)
          .limit(limit)
          .get();

      final transactions = <DashboardTransactionModel>[];

      for (final entryDoc in journalEntriesQuery.docs) {
        final entryData = entryDoc.data();
        final lines = entryData['lines'] as List<dynamic>? ?? [];

        for (final line in lines) {
          transactions.add(DashboardTransactionModel(
            transactionId: entryDoc.id,
            entryNumber: entryData['entryNumber'] ?? '',
            transactionDate: (entryData['entryDate'] as Timestamp).toDate(),
            description: entryData['description'] ?? '',
            accountName: line['accountName'] ?? '',
            amount: (line['amount'] as num?)?.toDouble() ?? 0.0,
            type: line['type'] ?? '',
            referenceNumber: entryData['referenceNumber'],
            sourceType: entryData['sourceTransactionType'],
          ));
        }
      }

      // Sort by date and limit
      transactions
          .sort((a, b) => b.transactionDate.compareTo(a.transactionDate));
      final limitedTransactions = transactions.take(limit).toList();

      log('Firebase: Retrieved ${limitedTransactions.length} recent transactions');
      return limitedTransactions;
    } catch (e) {
      log('Firebase: Error getting recent transactions: $e');
      rethrow;
    }
  }

  /// Get cash flow data for charts
  Future<List<CashFlowDataModel>> getCashFlowData(
    String uid,
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      log('Firebase: Getting cash flow data for period: $startDate to $endDate');

      // Get cash and bank accounts
      final cashAccountsQuery = await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .where('uid', isEqualTo: uid)
          .where('isActive', isEqualTo: true)
          .where('category', isEqualTo: 'Assets')
          .get();

      final cashAccountIds = <String>[];
      for (final doc in cashAccountsQuery.docs) {
        final data = doc.data();
        final accountType = (data['accountType'] as String).toLowerCase();
        if (accountType.contains('cash') || accountType.contains('bank')) {
          cashAccountIds.add(doc.id);
        }
      }

      if (cashAccountIds.isEmpty) {
        return [];
      }

      // Get journal entries for cash accounts
      final journalEntriesQuery = await _firestore
          .collection(AppCollection.journalEntriesCollection)
          .where('uid', isEqualTo: uid)
          .where('entryDate',
              isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
          .where('entryDate', isLessThanOrEqualTo: Timestamp.fromDate(endDate))
          .where('status', isEqualTo: 'posted')
          .orderBy('entryDate')
          .get();

      // Group transactions by date
      final dailyCashFlow = <String, Map<String, double>>{};

      for (final entryDoc in journalEntriesQuery.docs) {
        final entryData = entryDoc.data();
        final entryDate = (entryData['entryDate'] as Timestamp).toDate();
        final dateKey =
            '${entryDate.year}-${entryDate.month.toString().padLeft(2, '0')}-${entryDate.day.toString().padLeft(2, '0')}';

        if (!dailyCashFlow.containsKey(dateKey)) {
          dailyCashFlow[dateKey] = {
            'cashIn': 0.0,
            'cashOut': 0.0,
            'date': entryDate.millisecondsSinceEpoch.toDouble()
          };
        }

        final lines = entryData['lines'] as List<dynamic>? ?? [];
        for (final line in lines) {
          final accountId = line['accountId'] as String;
          if (cashAccountIds.contains(accountId)) {
            final amount = (line['amount'] as num?)?.toDouble() ?? 0.0;
            final type = line['type'] as String;

            if (type == 'debit') {
              dailyCashFlow[dateKey]!['cashIn'] =
                  (dailyCashFlow[dateKey]!['cashIn'] ?? 0.0) + amount;
            } else {
              dailyCashFlow[dateKey]!['cashOut'] =
                  (dailyCashFlow[dateKey]!['cashOut'] ?? 0.0) + amount;
            }
          }
        }
      }

      // Convert to CashFlowDataModel list
      final cashFlowData = <CashFlowDataModel>[];
      double cumulativeBalance = 0.0;

      final sortedDates = dailyCashFlow.keys.toList()..sort();
      for (final dateKey in sortedDates) {
        final data = dailyCashFlow[dateKey]!;
        final cashIn = data['cashIn'] ?? 0.0;
        final cashOut = data['cashOut'] ?? 0.0;
        final netCashFlow = cashIn - cashOut;
        cumulativeBalance += netCashFlow;

        cashFlowData.add(CashFlowDataModel(
          date: DateTime.fromMillisecondsSinceEpoch(data['date']!.toInt()),
          cashIn: cashIn,
          cashOut: cashOut,
          netCashFlow: netCashFlow,
          cumulativeBalance: cumulativeBalance,
        ));
      }

      log('Firebase: Retrieved ${cashFlowData.length} cash flow data points');
      return cashFlowData;
    } catch (e) {
      log('Firebase: Error getting cash flow data: $e');
      rethrow;
    }
  }

  /// Get expense breakdown for pie charts
  Future<List<ExpenseBreakdownModel>> getExpenseBreakdown(
    String uid,
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      log('Firebase: Getting expense breakdown for period: $startDate to $endDate');

      // Get expense accounts
      final expenseAccountsQuery = await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .where('uid', isEqualTo: uid)
          .where('isActive', isEqualTo: true)
          .where('category', isEqualTo: 'Expenses')
          .get();

      final expenseAccounts = <String, Map<String, dynamic>>{};
      for (final doc in expenseAccountsQuery.docs) {
        expenseAccounts[doc.id] = doc.data();
      }

      if (expenseAccounts.isEmpty) {
        return [];
      }

      // Get journal entries for expense accounts
      final journalEntriesQuery = await _firestore
          .collection(AppCollection.journalEntriesCollection)
          .where('uid', isEqualTo: uid)
          .where('entryDate',
              isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
          .where('entryDate', isLessThanOrEqualTo: Timestamp.fromDate(endDate))
          .where('status', isEqualTo: 'posted')
          .get();

      // Calculate expense totals by account
      final expenseTotals = <String, Map<String, dynamic>>{};
      double totalExpenses = 0.0;

      for (final entryDoc in journalEntriesQuery.docs) {
        final entryData = entryDoc.data();
        final lines = entryData['lines'] as List<dynamic>? ?? [];

        for (final line in lines) {
          final accountId = line['accountId'] as String;
          if (expenseAccounts.containsKey(accountId)) {
            final amount = (line['amount'] as num?)?.toDouble() ?? 0.0;
            final type = line['type'] as String;

            // Only count debit amounts for expense accounts
            if (type == 'debit') {
              if (!expenseTotals.containsKey(accountId)) {
                expenseTotals[accountId] = {
                  'amount': 0.0,
                  'transactionCount': 0,
                  'accountData': expenseAccounts[accountId],
                };
              }

              expenseTotals[accountId]!['amount'] =
                  (expenseTotals[accountId]!['amount'] as double) + amount;
              expenseTotals[accountId]!['transactionCount'] =
                  (expenseTotals[accountId]!['transactionCount'] as int) + 1;
              totalExpenses += amount;
            }
          }
        }
      }

      // Convert to ExpenseBreakdownModel list
      final breakdown = <ExpenseBreakdownModel>[];
      for (final entry in expenseTotals.entries) {
        final data = entry.value;
        final accountData = data['accountData'] as Map<String, dynamic>;
        final amount = data['amount'] as double;
        final transactionCount = data['transactionCount'] as int;
        final percentage =
            totalExpenses > 0 ? (amount / totalExpenses) * 100 : 0.0;

        breakdown.add(ExpenseBreakdownModel(
          category: accountData['accountType'] ?? 'Other',
          accountName: accountData['accountName'] ?? '',
          amount: amount,
          percentage: percentage,
          transactionCount: transactionCount,
        ));
      }

      // Sort by amount descending
      breakdown.sort((a, b) => b.amount.compareTo(a.amount));

      log('Firebase: Retrieved ${breakdown.length} expense breakdown items');
      return breakdown;
    } catch (e) {
      log('Firebase: Error getting expense breakdown: $e');
      rethrow;
    }
  }
}
