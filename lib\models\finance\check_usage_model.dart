class CheckUsageModel {
  final String id;
  final String uid; // User ID for ownership tracking
  final String checkNumber;
  final String bankName;
  final String accountId; // Account from which check was issued
  final String accountName;
  final String voucherId;
  final String voucherNumber;
  final double amount;
  final DateTime issueDate;
  final DateTime? expiryDate;
  final DateTime usageDate; // When the check was used in voucher
  final String payeeName; // Who the check was paid to
  final String notes;
  final String status; // "issued", "cleared", "bounced", "cancelled"
  final String checkType; // "own", "other"
  final String? externalCompanyId; // If check type is "other"
  final String? externalCompanyName;
  final DateTime createdAt;

  CheckUsageModel({
    required this.id,
    required this.uid,
    required this.checkNumber,
    required this.bankName,
    required this.accountId,
    required this.accountName,
    required this.voucherId,
    required this.voucherNumber,
    required this.amount,
    required this.issueDate,
    this.expiryDate,
    required this.usageDate,
    required this.payeeName,
    required this.notes,
    this.status = 'issued',
    this.checkType = 'own',
    this.externalCompanyId,
    this.externalCompanyName,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  factory CheckUsageModel.fromJson(Map<String, dynamic> json) {
    return CheckUsageModel(
      id: json['id'] ?? '',
      uid: json['uid'] ?? '',
      checkNumber: json['checkNumber'] ?? '',
      bankName: json['bankName'] ?? '',
      accountId: json['accountId'] ?? '',
      accountName: json['accountName'] ?? '',
      voucherId: json['voucherId'] ?? '',
      voucherNumber: json['voucherNumber'] ?? '',
      amount: (json['amount'] as num?)?.toDouble() ?? 0.0,
      issueDate: json['issueDate'] != null
          ? DateTime.parse(json['issueDate'])
          : DateTime.now(),
      expiryDate: json['expiryDate'] != null
          ? DateTime.parse(json['expiryDate'])
          : null,
      usageDate: json['usageDate'] != null
          ? DateTime.parse(json['usageDate'])
          : DateTime.now(),
      payeeName: json['payeeName'] ?? '',
      notes: json['notes'] ?? '',
      status: json['status'] ?? 'issued',
      checkType: json['checkType'] ?? 'own',
      externalCompanyId: json['externalCompanyId'],
      externalCompanyName: json['externalCompanyName'],
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'uid': uid,
      'checkNumber': checkNumber,
      'bankName': bankName,
      'accountId': accountId,
      'accountName': accountName,
      'voucherId': voucherId,
      'voucherNumber': voucherNumber,
      'amount': amount,
      'issueDate': issueDate.toIso8601String(),
      'expiryDate': expiryDate?.toIso8601String(),
      'usageDate': usageDate.toIso8601String(),
      'payeeName': payeeName,
      'notes': notes,
      'status': status,
      'checkType': checkType,
      'externalCompanyId': externalCompanyId,
      'externalCompanyName': externalCompanyName,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  CheckUsageModel copyWith({
    String? id,
    String? uid,
    String? checkNumber,
    String? bankName,
    String? accountId,
    String? accountName,
    String? voucherId,
    String? voucherNumber,
    double? amount,
    DateTime? issueDate,
    DateTime? expiryDate,
    DateTime? usageDate,
    String? payeeName,
    String? notes,
    String? status,
    String? checkType,
    String? externalCompanyId,
    String? externalCompanyName,
    DateTime? createdAt,
  }) {
    return CheckUsageModel(
      id: id ?? this.id,
      uid: uid ?? this.uid,
      checkNumber: checkNumber ?? this.checkNumber,
      bankName: bankName ?? this.bankName,
      accountId: accountId ?? this.accountId,
      accountName: accountName ?? this.accountName,
      voucherId: voucherId ?? this.voucherId,
      voucherNumber: voucherNumber ?? this.voucherNumber,
      amount: amount ?? this.amount,
      issueDate: issueDate ?? this.issueDate,
      expiryDate: expiryDate ?? this.expiryDate,
      usageDate: usageDate ?? this.usageDate,
      payeeName: payeeName ?? this.payeeName,
      notes: notes ?? this.notes,
      status: status ?? this.status,
      checkType: checkType ?? this.checkType,
      externalCompanyId: externalCompanyId ?? this.externalCompanyId,
      externalCompanyName: externalCompanyName ?? this.externalCompanyName,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'CheckUsageModel(id: $id, checkNumber: $checkNumber, bankName: $bankName, amount: $amount, voucherNumber: $voucherNumber, status: $status, checkType: $checkType)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CheckUsageModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
