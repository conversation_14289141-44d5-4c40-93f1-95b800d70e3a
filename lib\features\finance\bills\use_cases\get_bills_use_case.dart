import 'package:logestics/models/finance/bill_model.dart';
import '../repositories/bill_repository.dart';

class GetBillsUseCase {
  final BillRepository _billRepository;

  GetBillsUseCase(this._billRepository);

  /// Executes the use case to get all bills for a company.
  Future<List<BillModel>> call(String uid) async {
    // Validate input
    if (uid.isEmpty) {
      throw ArgumentError('Company ID cannot be empty.');
    }

    // Call the repository
    return await _billRepository.getBillsForCompany(uid);
  }
}
