import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:logestics/core/utils/mixins/pagination_mixin.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/models/finance/loan_model.dart';
import 'package:logestics/features/finance/loans/usecases/get_loans_use_case.dart';

class LoanHistoryController extends GetxController with PaginationMixin {
  final GetLoanHistoryUseCase getLoanHistoryUseCase;

  LoanHistoryController({
    required this.getLoanHistoryUseCase,
  });

  // Loading states
  final isLoading = false.obs;
  final isInitialLoading = true.obs;
  final isRefreshing = false.obs;
  final isLoadingPage = false.obs;
  final hasError = false.obs;
  final errorMessage = ''.obs;

  // Data
  final allLoans = <LoanModel>[].obs;
  final filteredLoans = <LoanModel>[].obs;

  // Search functionality
  final searchQuery = ''.obs;
  final searchController = TextEditingController();

  // Filter states
  final selectedStatus = RxnString();
  final selectedLoanType = RxnString();
  final dateFilterType =
      'all'.obs; // all, today, week, month, last30days, custom
  final selectedStartDate = DateTime.now().obs;
  final selectedEndDate = DateTime.now().obs;

  // Available filter options
  final List<String> statusOptions = [
    'All',
    'pending',
    'approved',
    'rejected',
    'repaid'
  ];

  final List<String> loanTypeOptions = [
    'All',
    'Personal',
    'Business',
    'Emergency',
    'Investment'
  ];

  @override
  void onInit() {
    super.onInit();
    _initializeFilters();
    _setupSearchListener();
    loadLoanHistory();
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  void _initializeFilters() {
    selectedStatus.value = 'All';
    selectedLoanType.value = 'All';
    dateFilterType.value = 'all';
    selectedStartDate.value = DateTime.now().subtract(const Duration(days: 30));
    selectedEndDate.value = DateTime.now();
  }

  void _setupSearchListener() {
    searchController.addListener(() {
      searchQuery.value = searchController.text;
      _applyFilters();
    });
  }

  /// Load loan history data
  Future<void> loadLoanHistory() async {
    if (isLoading.value) return;

    isLoading.value = true;
    hasError.value = false;
    errorMessage.value = '';

    if (allLoans.isEmpty) {
      isInitialLoading.value = true;
    }

    try {
      final result = await getLoanHistoryUseCase.execute();

      result.fold(
        (failure) {
          hasError.value = true;
          errorMessage.value = failure.message;
          SnackbarUtils.showError('Error', failure.message);
        },
        (loans) {
          hasError.value = false;
          errorMessage.value = '';
          allLoans.value = loans;
          _applyFilters();
        },
      );
    } catch (e) {
      hasError.value = true;
      errorMessage.value = 'Failed to load loan history: $e';
      SnackbarUtils.showError('Error', 'Failed to load loan history: $e');
    } finally {
      isLoading.value = false;
      isInitialLoading.value = false;
    }
  }

  /// Refresh data
  Future<void> refreshData() async {
    isRefreshing.value = true;
    await loadLoanHistory();
    isRefreshing.value = false;
  }

  /// Retry loading data after error
  Future<void> retryLoading() async {
    hasError.value = false;
    errorMessage.value = '';
    await loadLoanHistory();
  }

  /// Apply all filters to the loan data
  void _applyFilters() {
    var filtered = List<LoanModel>.from(allLoans);

    // Apply search filter
    if (searchQuery.value.isNotEmpty) {
      final query = searchQuery.value.toLowerCase();
      filtered = filtered.where((loan) {
        return loan.requestedByName.toLowerCase().contains(query) ||
            loan.requestedToName.toLowerCase().contains(query) ||
            loan.fromAccountName.toLowerCase().contains(query) ||
            loan.toAccountName.toLowerCase().contains(query) ||
            loan.status.toLowerCase().contains(query) ||
            loan.id.toLowerCase().contains(query);
      }).toList();
    }

    // Apply status filter
    if (selectedStatus.value != null &&
        selectedStatus.value != 'All' &&
        selectedStatus.value!.isNotEmpty) {
      filtered = filtered
          .where((loan) =>
              loan.status.toLowerCase() == selectedStatus.value!.toLowerCase())
          .toList();
    }

    // Apply loan type filter (based on notes or amount ranges)
    if (selectedLoanType.value != null &&
        selectedLoanType.value != 'All' &&
        selectedLoanType.value!.isNotEmpty) {
      filtered = _filterByLoanType(filtered, selectedLoanType.value!);
    }

    // Apply date filter
    filtered = _applyDateFilter(filtered);

    // Sort by most recent first
    filtered.sort((a, b) => b.requestDate.compareTo(a.requestDate));

    filteredLoans.value = filtered;
    setTotalItems(filtered.length);

    // Reset to first page if current page is beyond available pages
    if (currentPage.value > totalPages && totalPages > 0) {
      currentPage.value = 1;
    }
  }

  /// Filter loans by type based on amount ranges or notes
  List<LoanModel> _filterByLoanType(List<LoanModel> loans, String type) {
    switch (type.toLowerCase()) {
      case 'personal':
        return loans
            .where((loan) =>
                loan.amount <= 100000 ||
                (loan.notes?.toLowerCase().contains('personal') ?? false))
            .toList();
      case 'business':
        return loans
            .where((loan) =>
                loan.amount > 100000 ||
                (loan.notes?.toLowerCase().contains('business') ?? false))
            .toList();
      case 'emergency':
        return loans
            .where((loan) =>
                loan.notes?.toLowerCase().contains('emergency') ?? false)
            .toList();
      case 'investment':
        return loans
            .where((loan) =>
                loan.notes?.toLowerCase().contains('investment') ?? false)
            .toList();
      default:
        return loans;
    }
  }

  /// Apply date filter to loans
  List<LoanModel> _applyDateFilter(List<LoanModel> loans) {
    switch (dateFilterType.value) {
      case 'today':
        final today = DateTime.now();
        return loans.where((loan) {
          final date = loan.requestDate;
          return date.year == today.year &&
              date.month == today.month &&
              date.day == today.day;
        }).toList();

      case 'week':
        final weekAgo = DateTime.now().subtract(const Duration(days: 7));
        return loans
            .where((loan) => loan.requestDate.isAfter(weekAgo))
            .toList();

      case 'month':
        final monthAgo = DateTime.now().subtract(const Duration(days: 30));
        return loans
            .where((loan) => loan.requestDate.isAfter(monthAgo))
            .toList();

      case 'last30days':
        final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
        return loans
            .where((loan) => loan.requestDate.isAfter(thirtyDaysAgo))
            .toList();

      case 'custom':
        return loans.where((loan) {
          final date = loan.requestDate;
          return date.isAfter(
                  selectedStartDate.value.subtract(const Duration(days: 1))) &&
              date.isBefore(selectedEndDate.value.add(const Duration(days: 1)));
        }).toList();

      case 'all':
      default:
        return loans;
    }
  }

  /// Get paginated loans for display
  List<LoanModel> get paginatedLoans => paginateList(filteredLoans);

  /// Search functionality
  void searchLoans(String query) {
    // Only update searchQuery, don't modify controller.text to avoid text selection
    searchQuery.value = query;
    _applyFilters();
  }

  /// Filter by status
  void filterByStatus(String? status) {
    selectedStatus.value = status;
    _applyFilters();
  }

  /// Filter by loan type
  void filterByLoanType(String? type) {
    selectedLoanType.value = type;
    _applyFilters();
  }

  /// Set date filter type
  void setDateFilter(String filterType) {
    dateFilterType.value = filterType;
    _applyFilters();
  }

  /// Set custom date range
  void setCustomDateRange(DateTime startDate, DateTime endDate) {
    selectedStartDate.value = startDate;
    selectedEndDate.value = endDate;
    dateFilterType.value = 'custom';
    _applyFilters();
  }

  /// Clear all filters
  void clearFilters() {
    searchQuery.value = '';
    searchController.clear();
    selectedStatus.value = 'All';
    selectedLoanType.value = 'All';
    dateFilterType.value = 'all';
    _applyFilters();
  }

  /// Format currency
  String formatCurrency(double amount) {
    final formatter = NumberFormat.currency(
      symbol: 'PKR ',
      decimalDigits: 2,
    );
    return formatter.format(amount);
  }

  /// Format date
  String formatDate(DateTime? date) {
    if (date == null) return '';
    return DateFormat('dd/MM/yyyy').format(date);
  }

  /// Get status color
  Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'approved':
        return const Color(0xFF4CAF50); // Green
      case 'pending':
        return const Color(0xFFFF9800); // Orange
      case 'rejected':
        return const Color(0xFFF44336); // Red
      case 'repaid':
        return const Color(0xFF2196F3); // Blue
      default:
        return const Color(0xFF9E9E9E); // Grey
    }
  }

  /// Calculate remaining balance for approved loans
  double getRemainingBalance(LoanModel loan) {
    if (loan.status.toLowerCase() == 'repaid') {
      return 0.0;
    }
    if (loan.status.toLowerCase() == 'approved') {
      // For now, return full amount as remaining
      // In a real app, you'd calculate based on payments made
      return loan.amount;
    }
    return 0.0;
  }

  /// Get paid amount for loans
  double getPaidAmount(LoanModel loan) {
    if (loan.status.toLowerCase() == 'repaid') {
      return loan.amount;
    }
    // For now, return 0 for non-repaid loans
    // In a real app, you'd calculate based on partial payments
    return 0.0;
  }

  /// Check if loan is overdue
  bool isLoanOverdue(LoanModel loan) {
    if (loan.status.toLowerCase() == 'repaid' ||
        loan.status.toLowerCase() == 'rejected' ||
        loan.status.toLowerCase() == 'pending') {
      return false;
    }

    return DateTime.now().isAfter(loan.dueDate) &&
        loan.status.toLowerCase() == 'approved';
  }

  /// Get display status with overdue check
  String getDisplayStatus(LoanModel loan) {
    if (isLoanOverdue(loan)) {
      return 'overdue';
    }
    return loan.status;
  }

  /// Get display status color with overdue check
  Color getDisplayStatusColor(LoanModel loan) {
    if (isLoanOverdue(loan)) {
      return const Color(0xFFD32F2F); // Dark red for overdue
    }
    return getStatusColor(loan.status);
  }
}
