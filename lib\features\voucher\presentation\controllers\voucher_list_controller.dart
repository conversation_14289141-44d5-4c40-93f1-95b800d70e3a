import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/core/utils/constants/custom_dialogs.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/core/utils/mixins/pagination_mixin.dart';
import 'package:logestics/features/company/presentation/conrollers/company_controller.dart';
import 'package:logestics/features/finance/deposits/presentation/controllers/deposit_controller.dart';
import 'package:logestics/features/finance/expenses/presentation/controllers/expense_controller.dart';

import '../../use_cases/delete_voucher_use_case.dart';

class VoucherListController extends GetxController with PaginationMixin {
  final DeleteVoucherUseCase deleteVoucherUseCase;
  VoucherListController({required this.deleteVoucherUseCase});
  // bool isActive = false;
  var companyController = Get.find<CompanyController>();
  final searchQuery = ''.obs;
  final filteredVouchers = <dynamic>[].obs;
  final isLoading = false.obs;

  ScrollController scrollController = ScrollController();

  Map<String, Color> statusColor = {
    'In Progress': const Color(0xFFffb269),
    "Completed": const Color(0xFF34d47f),
    "Not Started": const Color(0xFF0f7bf4),
    "Pending": const Color(0xFFe85542),
    // Color(0xFFffb269),
    //Color(0xFFe85542),
  };

  @override
  void onInit() {
    //log(companyController.company.invoices);
    log(companyController.company.vouchers.toString());
    super.onInit();
    loadVouchers();
  }

  Future<void> loadVouchers() async {
    isLoading.value = true;
    try {
      // Wait for company controller to load vouchers if not already loaded
      if (!companyController.hasInitialVoucherLoad.value) {
        await Future.delayed(const Duration(milliseconds: 500));
      }
      _filterVouchers();
    } finally {
      isLoading.value = false;
    }
  }

  void refreshVouchers() {
    _filterVouchers();
  }

  void _filterVouchers() {
    if (searchQuery.value.isEmpty) {
      filteredVouchers.value = companyController.company.vouchers;
    } else {
      final query = searchQuery.value.toLowerCase();
      filteredVouchers.value =
          companyController.company.vouchers.where((voucher) {
        return voucher.voucherNumber.toLowerCase().contains(query) ||
            voucher.voucherStatus.toLowerCase().contains(query);
      }).toList();
    }
    setTotalItems(filteredVouchers.length);
  }

  void setSearchQuery(String query) {
    searchQuery.value = query;
    _filterVouchers();
  }

  int selectindex = 6;

  setindexforitem(value) {
    selectindex = value;
    update();
  }

  List<dynamic> get paginatedVouchers => paginateList(filteredVouchers);

  Future<void> deleteVoucher({
    required String voucherNumber,
  }) async {
    // Show comprehensive confirmation dialog
    Get.defaultDialog(
      title: "Complete Voucher Deletion",
      middleText:
          "This will permanently delete the voucher and ALL associated financial records including bank statements, tax payments, broker fees, munshiana, and account transactions. Account balances will be automatically adjusted. This action cannot be undone!",
      textConfirm: "Delete Everything",
      textCancel: "Cancel",
      confirmTextColor: Colors.white,
      buttonColor: Colors.red,
      onConfirm: () async {
        Get.back(); // Close the dialog after confirmation

        // Show loading dialog
        Get.dialog(
          const Center(child: CircularProgressIndicator()),
          barrierDismissible: false,
        );

        try {
          // Call the use case
          final result = await deleteVoucherUseCase.call(
            voucherNumber: voucherNumber,
          );

          // Close loading dialog
          Get.back();

          // Handle result
          result.fold(
            (failure) => showErrorDialog(failure),
            (success) {
              // Remove the voucher from the filtered list
              companyController.company.vouchers.removeWhere(
                  (voucher) => voucher.voucherNumber == voucherNumber);

              // Update filtered list and total items count
              filteredVouchers.value = companyController.company.vouchers;
              setTotalItems(filteredVouchers.length);

              // Update UI
              companyController.update();

              // Notify other controllers about voucher deletion for real-time updates
              try {
                Get.find<DepositController>().forceRefresh();
                Get.find<ExpenseController>().forceRefresh();
              } catch (e) {
                // Controllers might not be initialized, ignore error
                log('Could not refresh finance controllers: $e');
              }

              // Show success message
              _showSuccessSnackbar(success);
            },
          );
        } catch (e) {
          // Close loading dialog
          Get.back();

          // Show unexpected error
          showUnexpectedErrorDialog();
        }
      },
      cancelTextColor: Colors.black,
      onCancel: () {
        Get.back(); // Close the dialog without doing anything
      },
    );

/*
    // Show loading dialog
    Get.dialog(
      const Center(child: CircularProgressIndicator()),
      barrierDismissible: false,
    );

    try {
      // Call the use case
      final result = await deleteInvoiceUseCase.call(
        tasNumber: tasNumber,
      );

      // Close loading dialog
      Get.back();

      // Handle result
      result.fold(
            (failure) => showErrorDialog(failure),
            (success) => _showSuccessSnackbar(success),
      );
    } catch (e) {
      // Close loading dialog
      Get.back();

      // Show unexpected error
      Get.defaultDialog(
        title: "Unexpected Error",
        middleText: "Something went wrong. Please try again later.",
        textConfirm: "OK",
        confirmTextColor: Colors.white,
        onConfirm: () => Get.back(),
      );
    }*/
  }

  /// Show an error popup with details.

  void showErrorSnackbar(String errorMessage) {
    if (errorMessage.isNotEmpty) {
      SnackbarUtils.showError(
        "Error", // Title of the snackbar
        errorMessage, // Error message content
      );
    }
  }

  /// Show a success snackbar.
  void _showSuccessSnackbar(SuccessObj success) {
    Get.back(); // Close any active dialogs
    displaySuccessSnackbar(success);
  }
}
