import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/widgets/my_text_field.dart';
import 'package:logestics/features/finance/brokers/presentation/controllers/broker_controller.dart';

class BrokerFormDialog extends StatelessWidget {
  final bool isEdit;

  const BrokerFormDialog({
    super.key,
    required this.isEdit,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<BrokerController>();

    return AlertDialog(
      title: Text(isEdit ? 'Edit Broker' : 'Add New Broker'),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.8,
        child: Form(
          key: controller.formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                MyTextFormField(
                  titleText: 'Broker Name',
                  labelText: 'Broker Name',
                  hintText: 'Enter broker name',
                  controller: controller.nameController,
                  validator: controller.validateName,
                ),
                const SizedBox(height: 16),
                MyTextFormField(
                  titleText: 'Description',
                  labelText: 'Description (Optional)',
                  hintText: 'Enter broker description',
                  controller: controller.descriptionController,
                ),
                const SizedBox(height: 16),
                MyTextFormField(
                  titleText: 'Phone Number',
                  labelText: 'Phone Number (Optional)',
                  hintText: 'Enter phone number',
                  controller: controller.phoneNumberController,
                  validator: controller.validatePhoneNumber,
                  keyboardType: TextInputType.phone,
                ),
                const SizedBox(height: 16),
                MyTextFormField(
                  titleText: 'Email',
                  labelText: 'Email (Optional)',
                  hintText: 'Enter email address',
                  controller: controller.emailController,
                  validator: controller.validateEmail,
                  keyboardType: TextInputType.emailAddress,
                ),
                const SizedBox(height: 16),
                MyTextFormField(
                  titleText: 'Address',
                  labelText: 'Address (Optional)',
                  hintText: 'Enter broker address',
                  controller: controller.addressController,
                  
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            controller.clearForm();
            Get.back();
          },
          child: const Text('Cancel'),
        ),
        Obx(() => ElevatedButton(
              onPressed: controller.isLoading.value
                  ? null
                  : () {
                      if (isEdit) {
                        controller.updateBroker();
                      } else {
                        controller.createBroker();
                      }
                    },
              child: controller.isLoading.value
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : Text(isEdit ? 'Update' : 'Create'),
            )),
      ],
    );
  }
}
