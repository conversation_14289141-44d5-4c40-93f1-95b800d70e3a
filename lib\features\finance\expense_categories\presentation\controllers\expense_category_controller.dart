import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/core/utils/mixins/pagination_mixin.dart';
import 'package:logestics/models/finance/expense_category_model.dart';
import 'package:logestics/features/finance/expense_categories/repositories/expense_category_repository.dart';
import 'package:uuid/uuid.dart';

class ExpenseCategoryManagementController extends GetxController
    with PaginationMixin {
  final ExpenseCategoryRepository _repository;

  final nameController = TextEditingController();
  final descriptionController = TextEditingController();
  final searchController = TextEditingController();
  final formKey = GlobalKey<FormState>();

  final _isLoading = false.obs;
  bool get isLoading => _isLoading.value;

  final _categories = <ExpenseCategoryModel>[].obs;
  List<ExpenseCategoryModel> get categories => _categories;

  final _filteredCategories = <ExpenseCategoryModel>[].obs;
  List<ExpenseCategoryModel> get filteredCategories => _filteredCategories;

  List<ExpenseCategoryModel> get paginatedCategories {
    setTotalItems(_filteredCategories.length);
    return paginateList(_filteredCategories);
  }

  final _isDialogOpen = false.obs;
  bool get isDialogOpen => _isDialogOpen.value;

  ExpenseCategoryManagementController(this._repository);

  @override
  void onInit() {
    super.onInit();
    searchController.addListener(_onSearchChanged);
    loadCategories();
  }

  @override
  void onClose() {
    nameController.dispose();
    descriptionController.dispose();
    searchController.dispose();
    super.onClose();
  }

  void _onSearchChanged() {
    _filterCategories(searchController.text);
  }

  String? validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Category name is required';
    }
    return null;
  }

  Future<void> loadCategories() async {
    _isLoading.value = true;
    try {
      final result = await _repository.getExpenseCategories();
      result.fold(
        (failure) => SnackbarUtils.showError(
          AppStrings.errorS,
          failure.message,
        ),
        (categories) {
          _categories.value = categories;
          _filteredCategories.value = categories;
        },
      );
    } catch (e) {
      SnackbarUtils.showError(
        AppStrings.errorS,
        'Failed to load categories: $e',
      );
    } finally {
      _isLoading.value = false;
    }
  }

  void _filterCategories(String query) {
    if (query.isEmpty) {
      _filteredCategories.value = _categories;
    } else {
      _filteredCategories.value = _categories.where((category) {
        return category.name.toLowerCase().contains(query.toLowerCase()) ||
            category.description.toLowerCase().contains(query.toLowerCase());
      }).toList();
    }
  }

  Future<void> addCategory() async {
    if (nameController.text.isEmpty) {
      SnackbarUtils.showError(
        AppStrings.errorS,
        'Please enter a category name',
      );
      return;
    }

    final category = ExpenseCategoryModel(
      id: const Uuid().v4(),
      name: nameController.text,
      description: descriptionController.text,
      createdAt: DateTime.now(),
    );

    _isLoading.value = true;
    try {
      final result = await _repository.createExpenseCategory(category);
      result.fold(
        (failure) => SnackbarUtils.showError(
          AppStrings.errorS,
          failure.message,
        ),
        (_) {
          _categories.add(category);
          _filteredCategories.value = _categories;
          clearForm();
          closeDialog();
          SnackbarUtils.showSuccess(
            AppStrings.success,
            'Category added successfully',
          );
          // Force refresh to ensure pagination updates immediately
          _refreshCategoryData();
        },
      );
    } catch (e) {
      SnackbarUtils.showError(
        AppStrings.error,
        'Failed to add category: $e',
      );
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> deleteCategory(String id) async {
    _isLoading.value = true;
    try {
      final result = await _repository.deleteExpenseCategory(id);
      result.fold(
        (failure) => SnackbarUtils.showError(
          AppStrings.errorS,
          failure.message,
        ),
        (_) {
          _categories.removeWhere((category) => category.id == id);
          _filteredCategories.value = _categories;
          SnackbarUtils.showSuccess(
            AppStrings.success,
            'Category deleted successfully',
          );
          // Force refresh to ensure pagination updates immediately
          _refreshCategoryData();
        },
      );
    } catch (e) {
      SnackbarUtils.showError(
        AppStrings.error,
        'Failed to delete category: $e',
      );
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> updateCategory(ExpenseCategoryModel category) async {
    _isLoading.value = true;
    try {
      final result = await _repository.updateExpenseCategory(category);
      result.fold(
        (failure) => SnackbarUtils.showError(
          AppStrings.error,
          failure.message,
        ),
        (_) {
          final index = _categories.indexWhere((c) => c.id == category.id);
          if (index != -1) {
            _categories[index] = category;
            _filteredCategories.value = _categories;
          }
          SnackbarUtils.showSuccess(
            AppStrings.success,
            'Category updated successfully',
          );
          // Force refresh to ensure pagination updates immediately
          _refreshCategoryData();
        },
      );
    } catch (e) {
      SnackbarUtils.showError(
          AppStrings.error, 'Failed to update category: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  void clearForm() {
    nameController.clear();
    descriptionController.clear();
  }

  void openDialog() {
    _isDialogOpen.value = true;
  }

  void closeDialog() {
    _isDialogOpen.value = false;
    clearForm();
  }

  void _refreshCategoryData() {
    // Immediately refresh the filtered list and pagination
    _filterCategories(searchController.text);

    // Add a small delay to ensure any Firestore listeners have processed the change
    Future.delayed(const Duration(milliseconds: 500), () {
      // Force refresh the data from Firestore
      loadCategories();
    });
  }
}
