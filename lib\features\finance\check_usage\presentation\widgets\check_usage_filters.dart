import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/features/finance/check_usage/presentation/controllers/check_usage_controller.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';

class CheckUsageFilters extends StatelessWidget {
  final CheckUsageController controller;

  const CheckUsageFilters({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    notifier = Provider.of(context, listen: true);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: notifier.getBgColor,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: notifier.getfillborder),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Filters',
            style: AppTextStyles.titleStyle.copyWith(
              color: notifier.text,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 16),

          // Search bar
          TextField(
            onChanged: (value) => controller.searchQuery.value = value,
            decoration: InputDecoration(
              hintText: 'Search by check number, payee, voucher, bank...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
          ),

          const SizedBox(height: 16),

          // Filter dropdowns and date pickers
          Wrap(
            spacing: 16,
            runSpacing: 16,
            children: [
              // Bank filter
              SizedBox(
                width: 200,
                child: Obx(() => DropdownButtonFormField<String>(
                      value: controller.selectedBankFilter.value.isEmpty
                          ? null
                          : controller.selectedBankFilter.value,
                      decoration: const InputDecoration(
                        labelText: 'Bank',
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: controller.bankOptions.map((bank) {
                        return DropdownMenuItem(
                          value: bank,
                          child: Text(bank),
                        );
                      }).toList(),
                      onChanged: (value) {
                        controller.selectedBankFilter.value = value ?? '';
                      },
                    )),
              ),

              // Status filter
              SizedBox(
                width: 150,
                child: Obx(() => DropdownButtonFormField<String>(
                      value: controller.selectedStatusFilter.value.isEmpty
                          ? null
                          : controller.selectedStatusFilter.value,
                      decoration: const InputDecoration(
                        labelText: 'Status',
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: controller.statusOptions.map((status) {
                        return DropdownMenuItem(
                          value: status,
                          child: Text(
                              status == 'All' ? status : status.toUpperCase()),
                        );
                      }).toList(),
                      onChanged: (value) {
                        controller.selectedStatusFilter.value = value ?? '';
                      },
                    )),
              ),

              // Check type filter
              SizedBox(
                width: 150,
                child: Obx(() => DropdownButtonFormField<String>(
                      value: controller.selectedCheckTypeFilter.value.isEmpty
                          ? null
                          : controller.selectedCheckTypeFilter.value,
                      decoration: const InputDecoration(
                        labelText: 'Type',
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: controller.checkTypeOptions.map((type) {
                        return DropdownMenuItem(
                          value: type,
                          child:
                              Text(type == 'All' ? type : type.toUpperCase()),
                        );
                      }).toList(),
                      onChanged: (value) {
                        controller.selectedCheckTypeFilter.value = value ?? '';
                      },
                    )),
              ),

              // Company filter
              SizedBox(
                width: 150,
                child: Obx(() {
                  final companies = controller.companyOptions;
                  return DropdownButtonFormField<String>(
                    value: controller.selectedCompanyFilter.value.isEmpty ||
                            !companies.contains(
                                controller.selectedCompanyFilter.value)
                        ? null
                        : controller.selectedCompanyFilter.value,
                    decoration: const InputDecoration(
                      labelText: 'Company',
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    items: companies.map((company) {
                      return DropdownMenuItem(
                        value: company,
                        child: Text(company),
                      );
                    }).toList(),
                    onChanged: (value) {
                      controller.selectedCompanyFilter.value = value ?? '';
                    },
                  );
                }),
              ),

              // Expiry date sort order
              SizedBox(
                width: 180,
                child: Obx(() {
                  String currentValue;
                  switch (controller.expiryDateSortOrder.value) {
                    case 'asc':
                      currentValue = 'Expiry Date (Earliest)';
                      break;
                    case 'desc':
                      currentValue = 'Expiry Date (Latest)';
                      break;
                    default:
                      currentValue = 'None';
                  }

                  return DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'Sort by Expiry',
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    value: currentValue,
                    items: controller.sortOptions.map((option) {
                      return DropdownMenuItem(
                        value: option,
                        child: Text(option),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        controller.setExpiryDateSortOrder(value);
                      }
                    },
                  );
                }),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Date Range Filter Section
          Text(
            'Date Range Filter (Usage Date)',
            style: TextStyle(
              color: notifier.text,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),

          Wrap(
            spacing: 16,
            runSpacing: 16,
            children: [
              // Start date picker
              SizedBox(
                width: 150,
                child: Obx(() => InkWell(
                      onTap: () => _selectStartDate(context),
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          labelText: 'Start Date',
                          border: OutlineInputBorder(),
                          contentPadding:
                              EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                        child: Text(
                          controller.startDateFilter.value != null
                              ? _formatDate(controller.startDateFilter.value!)
                              : 'Select date',
                          style: TextStyle(
                            color: controller.startDateFilter.value != null
                                ? notifier.text
                                : Colors.grey,
                          ),
                        ),
                      ),
                    )),
              ),

              // End date picker
              SizedBox(
                width: 150,
                child: Obx(() => InkWell(
                      onTap: () => _selectEndDate(context),
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          labelText: 'End Date',
                          border: OutlineInputBorder(),
                          contentPadding:
                              EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                        child: Text(
                          controller.endDateFilter.value != null
                              ? _formatDate(controller.endDateFilter.value!)
                              : 'Select date',
                          style: TextStyle(
                            color: controller.endDateFilter.value != null
                                ? notifier.text
                                : Colors.grey,
                          ),
                        ),
                      ),
                    )),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Quick date filter presets
          Text(
            'Quick Date Filters',
            style: TextStyle(
              color: notifier.text,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildQuickFilterButton(
                  'Today', () => _setDateRange(DateTime.now(), DateTime.now())),
              _buildQuickFilterButton('This Week',
                  () => _setDateRange(_getStartOfWeek(), DateTime.now())),
              _buildQuickFilterButton('This Month',
                  () => _setDateRange(_getStartOfMonth(), DateTime.now())),
              _buildQuickFilterButton(
                  'Last 30 Days',
                  () => _setDateRange(
                      DateTime.now().subtract(const Duration(days: 30)),
                      DateTime.now())),
              _buildQuickFilterButton('Clear Dates', () => _clearDateFilters()),
            ],
          ),

          const SizedBox(height: 16),

          // Action buttons
          Row(
            children: [
              ElevatedButton.icon(
                onPressed: () => controller.clearFilters(),
                icon: const Icon(Icons.clear),
                label: const Text('Clear Filters'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey,
                  foregroundColor: Colors.white,
                ),
              ),
              const SizedBox(width: 16),
              Obx(() => Text(
                    'Showing ${controller.filteredCheckUsages.length} of ${controller.checkUsages.length} records',
                    style: TextStyle(
                      color: notifier.text,
                      fontSize: 14,
                    ),
                  )),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _selectStartDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: controller.startDateFilter.value ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      controller.startDateFilter.value = picked;
    }
  }

  Future<void> _selectEndDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: controller.endDateFilter.value ?? DateTime.now(),
      firstDate: controller.startDateFilter.value ?? DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      controller.endDateFilter.value = picked;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Widget _buildQuickFilterButton(String label, VoidCallback onPressed) {
    return OutlinedButton(
      onPressed: onPressed,
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        minimumSize: const Size(0, 32),
      ),
      child: Text(
        label,
        style: const TextStyle(fontSize: 12),
      ),
    );
  }

  void _setDateRange(DateTime start, DateTime end) {
    controller.startDateFilter.value = start;
    controller.endDateFilter.value = end;
  }

  void _clearDateFilters() {
    controller.startDateFilter.value = null;
    controller.endDateFilter.value = null;
  }

  DateTime _getStartOfWeek() {
    final now = DateTime.now();
    final weekday = now.weekday;
    return now.subtract(Duration(days: weekday - 1));
  }

  DateTime _getStartOfMonth() {
    final now = DateTime.now();
    return DateTime(now.year, now.month, 1);
  }
}
