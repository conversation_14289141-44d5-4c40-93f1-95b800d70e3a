import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:logestics/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Chart of Accounts Integration Tests', () {
    testWidgets('Complete Chart of Accounts workflow test',
        (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 5));

      // Step 1: Navigate to Chart of Accounts
      print('Step 1: Looking for Chart of Accounts navigation...');

      // Look for drawer or navigation menu
      final drawerButton = find.byIcon(Icons.menu);
      if (drawerButton.evaluate().isNotEmpty) {
        await tester.tap(drawerButton);
        await tester.pumpAndSettle();
        print('Opened navigation drawer');
      }

      // Look for Chart of Accounts menu item
      final chartOfAccountsItem = find.text('Chart of Accounts');
      expect(chartOfAccountsItem, findsOneWidget,
          reason: 'Chart of Accounts menu item should be visible');

      await tester.tap(chartOfAccountsItem);
      await tester.pumpAndSettle(const Duration(seconds: 3));
      print('Navigated to Chart of Accounts');

      // Step 2: Verify Chart of Accounts view loads
      print('Step 2: Verifying Chart of Accounts view...');

      // Check for key elements
      expect(find.text('Chart of Accounts'), findsOneWidget,
          reason: 'Chart of Accounts title should be visible');

      final addAccountButton = find.text('Add Account');
      expect(addAccountButton, findsOneWidget,
          reason: 'Add Account button should be visible');

      print('Chart of Accounts view loaded successfully');

      // Step 3: Test Add Account button functionality
      print('Step 3: Testing Add Account button...');

      // Tap the Add Account button
      await tester.tap(addAccountButton.first);
      await tester.pumpAndSettle(const Duration(seconds: 2));

      // Check if form drawer opened
      final formDrawer = find
          .byType(Container)
          .evaluate()
          .where((element) => element.widget is Container)
          .isNotEmpty;

      if (formDrawer) {
        print('✓ Add Account form drawer opened successfully');
      } else {
        print('✗ Add Account form drawer did not open');
      }

      // Step 4: Test form interactions
      print('Step 4: Testing form interactions...');

      // Look for form fields
      final accountNameField = find.byType(TextFormField);
      if (accountNameField.evaluate().isNotEmpty) {
        print('✓ Form fields are present');

        // Test entering text
        await tester.enterText(accountNameField.first, 'Test Account');
        await tester.pumpAndSettle();
        print('✓ Text entry works');
      } else {
        print('✗ Form fields not found');
      }

      // Step 5: Test dropdown interactions
      print('Step 5: Testing dropdown interactions...');

      final dropdowns = find.byType(DropdownButtonFormField);
      if (dropdowns.evaluate().isNotEmpty) {
        print('✓ Dropdown fields are present');

        // Test opening dropdown
        await tester.tap(dropdowns.first);
        await tester.pumpAndSettle();
        print('✓ Dropdown opens');
      } else {
        print('✗ Dropdown fields not found');
      }

      // Step 6: Test form closure
      print('Step 6: Testing form closure...');

      // Look for close button or cancel button
      final closeButton = find.byIcon(Icons.close);
      final cancelButton = find.text('Cancel');

      if (closeButton.evaluate().isNotEmpty) {
        await tester.tap(closeButton.first);
        await tester.pumpAndSettle();
        print('✓ Form closed via close button');
      } else if (cancelButton.evaluate().isNotEmpty) {
        await tester.tap(cancelButton.first);
        await tester.pumpAndSettle();
        print('✓ Form closed via cancel button');
      } else {
        print('✗ No close/cancel button found');
      }

      // Step 7: Test search functionality
      print('Step 7: Testing search functionality...');

      final searchField = find.byType(TextField);
      if (searchField.evaluate().isNotEmpty) {
        await tester.enterText(searchField.first, 'test search');
        await tester.pumpAndSettle();
        print('✓ Search field works');
      } else {
        print('✗ Search field not found');
      }

      // Step 8: Test pagination controls
      print('Step 8: Testing pagination controls...');

      final paginationControls = find.text('Items per page');
      if (paginationControls.evaluate().isNotEmpty) {
        print('✓ Pagination controls are present');
      } else {
        print('✗ Pagination controls not found');
      }

      // Step 9: Test filter controls
      print('Step 9: Testing filter controls...');

      final filterDropdowns = find.byType(DropdownButton);
      if (filterDropdowns.evaluate().isNotEmpty) {
        print('✓ Filter controls are present');
      } else {
        print('✗ Filter controls not found');
      }

      // Step 10: Final verification
      print('Step 10: Final verification...');

      // Verify no error dialogs or snackbars
      final errorDialogs = find.byType(AlertDialog);
      expect(errorDialogs, findsNothing,
          reason: 'No error dialogs should be present');

      final errorSnackbars = find.byType(SnackBar);
      expect(errorSnackbars, findsNothing,
          reason: 'No error snackbars should be present');

      print('✓ No error dialogs or snackbars found');
      print('Integration test completed successfully!');
    });

    testWidgets('Chart of Accounts button responsiveness test',
        (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 5));

      // Navigate to Chart of Accounts
      final drawerButton = find.byIcon(Icons.menu);
      if (drawerButton.evaluate().isNotEmpty) {
        await tester.tap(drawerButton);
        await tester.pumpAndSettle();
      }

      final chartOfAccountsItem = find.text('Chart of Accounts');
      await tester.tap(chartOfAccountsItem);
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Test rapid button clicks
      print('Testing button responsiveness...');

      final addAccountButton = find.text('Add Account');
      expect(addAccountButton, findsOneWidget);

      // Perform multiple rapid clicks
      for (int i = 0; i < 5; i++) {
        await tester.tap(addAccountButton.first);
        await tester.pump(const Duration(milliseconds: 100));
        print('Click $i completed');
      }

      await tester.pumpAndSettle(const Duration(seconds: 2));
      print('Button responsiveness test completed');
    });

    testWidgets('Chart of Accounts UI freeze detection test',
        (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 5));

      // Navigate to Chart of Accounts
      final drawerButton = find.byIcon(Icons.menu);
      if (drawerButton.evaluate().isNotEmpty) {
        await tester.tap(drawerButton);
        await tester.pumpAndSettle();
      }

      final chartOfAccountsItem = find.text('Chart of Accounts');
      await tester.tap(chartOfAccountsItem);
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Test for UI freezing
      print('Testing for UI freezing...');

      final addAccountButton = find.text('Add Account');
      expect(addAccountButton, findsOneWidget);

      // Click button and verify UI remains responsive
      await tester.tap(addAccountButton.first);

      // Try to interact with other UI elements immediately after
      final searchField = find.byType(TextField);
      if (searchField.evaluate().isNotEmpty) {
        await tester.tap(searchField.first);
        await tester.enterText(searchField.first, 'freeze test');
        print('✓ UI remains responsive after button click');
      }

      await tester.pumpAndSettle(const Duration(seconds: 2));
      print('UI freeze detection test completed');
    });
  });
}
