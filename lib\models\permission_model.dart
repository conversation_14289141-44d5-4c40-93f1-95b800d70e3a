// /// Represents a specific system permission
// class Permission {
//   final String id;
//   final String name;
//   final String description;

//   const Permission({
//     required this.id,
//     required this.name,
//     required this.description,
//   });

//   @override
//   bool operator ==(Object other) =>
//       identical(this, other) ||
//       other is Permission && runtimeType == other.runtimeType && id == other.id;

//   @override
//   int get hashCode => id.hashCode;
// }

// /// System permissions
// class Permissions {
//   // Location permissions
//   static const viewLocations = Permission(
//     id: 'view_locations',
//     name: 'View Locations',
//     description: 'Can view locations but not edit them',
//   );

//   static const manageLocations = Permission(
//     id: 'manage_locations',
//     name: 'Manage Locations',
//     description: 'Can add, edit, and delete locations',
//   );

//   // Invoice permissions
//   static const viewInvoices = Permission(
//     id: 'view_invoices',
//     name: 'View Invoices',
//     description: 'Can view invoices but not edit them',
//   );

//   static const createInvoices = Permission(
//     id: 'create_invoices',
//     name: 'Create Invoices',
//     description: 'Can create new invoices',
//   );

//   static const editInvoices = Permission(
//     id: 'edit_invoices',
//     name: 'Edit Invoices',
//     description: 'Can edit existing invoices',
//   );

//   // Voucher permissions
//   static const viewVouchers = Permission(
//     id: 'view_vouchers',
//     name: 'View Vouchers',
//     description: 'Can view vouchers but not edit them',
//   );

//   static const createVouchers = Permission(
//     id: 'create_vouchers',
//     name: 'Create Vouchers',
//     description: 'Can create new vouchers',
//   );

//   static const approveVouchers = Permission(
//     id: 'approve_vouchers',
//     name: 'Approve Vouchers',
//     description: 'Can approve vouchers',
//   );

//   // Finance permissions
//   static const viewFinance = Permission(
//     id: 'view_finance',
//     name: 'View Finance',
//     description: 'Can view financial information',
//   );

//   static const manageFinance = Permission(
//     id: 'manage_finance',
//     name: 'Manage Finance',
//     description: 'Can manage financial settings and transactions',
//   );

//   // Loan permissions
//   static const viewLoans = Permission(
//     id: 'view_loans',
//     name: 'View Loans',
//     description: 'Can view loan information',
//   );

//   static const manageLoans = Permission(
//     id: 'manage_loans',
//     name: 'Manage Loans',
//     description: 'Can create, approve, or reject loans',
//   );

//   // User management permissions
//   static const viewUsers = Permission(
//     id: 'view_users',
//     name: 'View Users',
//     description: 'Can view system users',
//   );

//   static const manageUsers = Permission(
//     id: 'manage_users',
//     name: 'Manage Users',
//     description: 'Can add, edit, and delete users',
//   );

//   static const assignRoles = Permission(
//     id: 'assign_roles',
//     name: 'Assign Roles',
//     description: 'Can assign roles to users',
//   );

//   // Reports permissions
//   static const viewReports = Permission(
//     id: 'view_reports',
//     name: 'View Reports',
//     description: 'Can view system reports',
//   );

//   static const generateReports = Permission(
//     id: 'generate_reports',
//     name: 'Generate Reports',
//     description: 'Can generate new reports',
//   );

//   // Get all permissions
//   static List<Permission> getAll() {
//     return [
//       viewLocations,
//       manageLocations,
//       viewInvoices,
//       createInvoices,
//       editInvoices,
//       viewVouchers,
//       createVouchers,
//       approveVouchers,
//       viewFinance,
//       manageFinance,
//       viewLoans,
//       manageLoans,
//       viewUsers,
//       manageUsers,
//       assignRoles,
//       viewReports,
//       generateReports,
//     ];
//   }
// }

// /// Represents a user role with specific permissions
// class RoleModel {
//   final String id;
//   final String name;
//   final String description;
//   final List<Permission> permissions;
//   final bool isCustom;
//   final bool isActive;

//   const RoleModel({
//     required this.id,
//     required this.name,
//     required this.description,
//     required this.permissions,
//     this.isCustom = false,
//     this.isActive = true,
//   });

//   /// Check if this role has the specified permission
//   bool hasPermission(Permission permission) {
//     return permissions.contains(permission);
//   }

//   /// Check if this role has all the specified permissions
//   bool hasAllPermissions(List<Permission> requiredPermissions) {
//     return requiredPermissions.every((permission) => hasPermission(permission));
//   }

//   /// Check if this role has any of the specified permissions
//   bool hasAnyPermission(List<Permission> requiredPermissions) {
//     return requiredPermissions.any((permission) => hasPermission(permission));
//   }

//   /// Create a copy of this role with updated fields
//   RoleModel copyWith({
//     String? id,
//     String? name,
//     String? description,
//     List<Permission>? permissions,
//     bool? isCustom,
//     bool? isActive,
//   }) {
//     return RoleModel(
//       id: id ?? this.id,
//       name: name ?? this.name,
//       description: description ?? this.description,
//       permissions: permissions ?? this.permissions,
//       isCustom: isCustom ?? this.isCustom,
//       isActive: isActive ?? this.isActive,
//     );
//   }

//   /// Convert to JSON map
//   Map<String, dynamic> toJson() {
//     return {
//       'id': id,
//       'name': name,
//       'description': description,
//       'permissions': permissions.map((p) => p.id).toList(),
//       'isCustom': isCustom,
//       'isActive': isActive,
//     };
//   }

//   /// Create from JSON map
//   factory RoleModel.fromJson(Map<String, dynamic> json) {
//     final permissionIds = List<String>.from(json['permissions'] ?? []);
//     final allPermissions = Permissions.getAll();

//     return RoleModel(
//       id: json['id'] ?? '',
//       name: json['name'] ?? '',
//       description: json['description'] ?? '',
//       permissions:
//           allPermissions.where((p) => permissionIds.contains(p.id)).toList(),
//       isCustom: json['isCustom'] ?? false,
//       isActive: json['isActive'] ?? true,
//     );
//   }
// }

// /// Predefined system roles
// class Roles {
//   /// Super Admin - Has all permissions
//   static RoleModel superAdmin() {
//     return RoleModel(
//       id: 'super_admin',
//       name: 'Super Admin',
//       description: 'Has full access to all system features',
//       permissions: Permissions.getAll(),
//     );
//   }

//   /// Admin - Has most permissions except some sensitive ones
//   static RoleModel admin() {
//     final allPermissions = Permissions.getAll();
//     return RoleModel(
//       id: 'admin',
//       name: 'Admin',
//       description: 'Has access to most system features',
//       permissions: allPermissions,
//     );
//   }

//   /// Manager - Can manage most operations but with limited admin access
//   static RoleModel manager() {
//     return RoleModel(
//       id: 'manager',
//       name: 'Manager',
//       description: 'Can manage day-to-day operations',
//       permissions: [
//         Permissions.viewLocations,
//         Permissions.manageLocations,
//         Permissions.viewInvoices,
//         Permissions.createInvoices,
//         Permissions.editInvoices,
//         Permissions.viewVouchers,
//         Permissions.createVouchers,
//         Permissions.approveVouchers,
//         Permissions.viewFinance,
//         Permissions.manageFinance,
//         Permissions.viewLoans,
//         Permissions.manageLoans,
//         Permissions.viewUsers,
//         Permissions.viewReports,
//         Permissions.generateReports,
//       ],
//     );
//   }

//   /// Finance Officer - Focused on financial aspects
//   static RoleModel financeOfficer() {
//     return RoleModel(
//       id: 'finance_officer',
//       name: 'Finance Officer',
//       description: 'Manages financial operations',
//       permissions: [
//         Permissions.viewLocations,
//         Permissions.viewInvoices,
//         Permissions.viewVouchers,
//         Permissions.viewFinance,
//         Permissions.manageFinance,
//         Permissions.viewLoans,
//         Permissions.manageLoans,
//         Permissions.viewReports,
//         Permissions.generateReports,
//       ],
//     );
//   }

//   /// Logistics Coordinator - Focused on operational aspects
//   static RoleModel logisticsCoordinator() {
//     return RoleModel(
//       id: 'logistics_coordinator',
//       name: 'Logistics Coordinator',
//       description: 'Coordinates logistics operations',
//       permissions: [
//         Permissions.viewLocations,
//         Permissions.manageLocations,
//         Permissions.viewInvoices,
//         Permissions.createInvoices,
//         Permissions.viewVouchers,
//         Permissions.createVouchers,
//         Permissions.viewFinance,
//         Permissions.viewReports,
//       ],
//     );
//   }

//   /// Data Entry - Limited to creating and viewing basic information
//   static RoleModel dataEntry() {
//     return RoleModel(
//       id: 'data_entry',
//       name: 'Data Entry',
//       description: 'Can enter and view basic data',
//       permissions: [
//         Permissions.viewLocations,
//         Permissions.viewInvoices,
//         Permissions.createInvoices,
//         Permissions.viewVouchers,
//         Permissions.createVouchers,
//       ],
//     );
//   }

//   /// View Only - Can only view information, not modify anything
//   static RoleModel viewOnly() {
//     return RoleModel(
//       id: 'view_only',
//       name: 'View Only',
//       description: 'Can only view information',
//       permissions: [
//         Permissions.viewLocations,
//         Permissions.viewInvoices,
//         Permissions.viewVouchers,
//         Permissions.viewFinance,
//         Permissions.viewLoans,
//         Permissions.viewReports,
//       ],
//     );
//   }

//   /// Get all predefined roles
//   static List<RoleModel> getAll() {
//     return [
//       superAdmin(),
//       admin(),
//       manager(),
//       financeOfficer(),
//       logisticsCoordinator(),
//       dataEntry(),
//       viewOnly(),
//     ];
//   }

//   /// Get a role by ID
//   static RoleModel? getById(String id) {
//     try {
//       return getAll().firstWhere((role) => role.id == id);
//     } catch (e) {
//       return null;
//     }
//   }
// }
