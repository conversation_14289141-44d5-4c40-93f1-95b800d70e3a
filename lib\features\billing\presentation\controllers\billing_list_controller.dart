import '../../../invoices/presentation/controllers/invoice_list_controller.dart';
import '../../../invoices/use_cases/delete_invoice_use_case.dart';
import '../../../invoices/use_cases/update_invoice_status_use_case.dart';

/// BillingListController extends InvoiceListController to reuse all existing
/// invoice functionality while filtering out "Pending Billing" status invoices.
/// This ensures code reuse and maintains consistency with the invoice section.
class BillingListController extends InvoiceListController {
  BillingListController({
    required DeleteInvoiceUseCase deleteInvoiceUseCase,
    required UpdateInvoiceStatusUseCase updateInvoiceStatusUseCase,
  }) : super(
          deleteInvoiceUseCase: deleteInvoiceUseCase,
          updateInvoiceStatusUseCase: updateInvoiceStatusUseCase,
        );

  @override
  void onInit() {
    // Set the default status filter to "Received" to show only received invoices
    selectedStatus.value = 'Received';
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    // Ensure the filter is applied when the controller is ready
    // Use the public filterByStatus method to trigger filtering
    filterByStatus('Received');
  }

  /// Override the filter method to ensure "Received" is the default
  /// unless explicitly changed by the user
  @override
  void filterByStatus(String? status) {
    // Allow users to change the status filter if needed
    // This maintains flexibility while defaulting to "Received"
    selectedStatus.value = status;
    // Call the parent's public method to trigger filtering
    super.filterByStatus(status);
  }

  /// Method to reset filter back to "Received" if needed
  void resetToBillingFilter() {
    filterByStatus('Received');
  }

  /// Override onScreenActivated to ensure billing filter is maintained
  @override
  void onScreenActivated() {
    // Ensure we're still filtering for "Received" by default
    if (selectedStatus.value != 'Received') {
      selectedStatus.value = 'Received';
    }

    // Call parent implementation
    super.onScreenActivated();
  }
}
