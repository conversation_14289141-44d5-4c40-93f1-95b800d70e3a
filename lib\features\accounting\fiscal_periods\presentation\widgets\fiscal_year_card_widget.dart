import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../../models/finance/fiscal_period_model.dart';

/// Widget displaying fiscal year information in a card format
class FiscalYearCardWidget extends StatelessWidget {
  final FiscalYearModel fiscalYear;
  final bool isSelected;
  final VoidCallback onTap;
  final VoidCallback onSetActive;
  final VoidCallback onGeneratePeriods;

  const FiscalYearCardWidget({
    super.key,
    required this.fiscalYear,
    required this.isSelected,
    required this.onTap,
    required this.onSetActive,
    required this.onGeneratePeriods,
  });

  @override
  Widget build(BuildContext context) {
    final dateFormatter = DateFormat('MMM dd, yyyy');

    return Card(
      elevation: isSelected ? 8 : 2,
      color: isSelected ? Colors.blue[50] : null,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row with year name and status
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          fiscalYear.yearName,
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: isSelected
                                ? Colors.blue[700]
                                : Colors.grey[800],
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${dateFormatter.format(fiscalYear.startDate)} - ${dateFormatter.format(fiscalYear.endDate)}',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Status badges
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      if (fiscalYear.isActive)
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.green,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Text(
                            'ACTIVE',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: _getStatusColor(fiscalYear.status),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          fiscalYear.status.displayName.toUpperCase(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Details row
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildDetailItem(
                          icon: Icons.calendar_today,
                          label: 'Duration',
                          value: '${fiscalYear.durationInDays} days',
                        ),
                        const SizedBox(height: 8),
                        _buildDetailItem(
                          icon: Icons.access_time,
                          label: 'Created',
                          value: dateFormatter.format(fiscalYear.createdAt),
                        ),
                      ],
                    ),
                  ),

                  // Current status indicator
                  if (fiscalYear.isCurrent)
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.green[100],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        children: [
                          Icon(Icons.schedule,
                              color: Colors.green[700], size: 20),
                          const SizedBox(height: 4),
                          Text(
                            'CURRENT',
                            style: TextStyle(
                              color: Colors.green[700],
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),

              const SizedBox(height: 16),

              // Action buttons
              Row(
                children: [
                  if (!fiscalYear.isActive)
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: onSetActive,
                        icon: const Icon(Icons.check_circle_outline, size: 18),
                        label: const Text('Set Active'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.blue[700],
                          side: BorderSide(color: Colors.blue[700]!),
                        ),
                      ),
                    ),
                  if (!fiscalYear.isActive) const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: onGeneratePeriods,
                      icon: const Icon(Icons.auto_awesome, size: 18),
                      label: const Text('Generate Periods'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue[700],
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build detail item with icon, label, and value
  Widget _buildDetailItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 6),
        Text(
          '$label: ',
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 13,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 13,
          ),
        ),
      ],
    );
  }

  /// Get color for fiscal period status
  Color _getStatusColor(FiscalPeriodStatus status) {
    switch (status) {
      case FiscalPeriodStatus.open:
        return Colors.green;
      case FiscalPeriodStatus.closed:
        return Colors.red;
      case FiscalPeriodStatus.locked:
        return Colors.orange;
    }
  }
}
