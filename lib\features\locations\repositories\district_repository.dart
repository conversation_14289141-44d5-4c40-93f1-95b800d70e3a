import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/firebase_service/locations/district_firebase_service.dart';
import 'package:logestics/models/locations/district_model.dart';

abstract class DistrictRepository {
  /// Creates an Zone and returns either a success or error object.
  Future<Either<FailureObj, SuccessObj>> createDistrict({
    required DistrictModel district,
  });
  Future<Either<FailureObj, List<DistrictModel>>> getDistricts();

  Stream<List<DistrictModel>> listenToDistricts();

  Future<Either<FailureObj, SuccessObj>> deleteDistrict({
    required String districtId,
  });
}

class DistrictRepositoryImpl implements DistrictRepository {
  final DistrictFirebaseService firebaseService;

  DistrictRepositoryImpl(this.firebaseService);

  @override
  Future<Either<FailureObj, SuccessObj>> createDistrict({
    required DistrictModel district,
  }) async {
    try {
      final existingDistrict =
          await firebaseService.checkDistrictExists(district.districtName);
      if (existingDistrict) {
        return Left(FailureObj(
            code: 'district-exists',
            message: 'A district with the same name already exists.'));
      }

      await firebaseService.createDistrict(district);
      return Right(SuccessObj(message: 'District created successfully'));
    } catch (e) {
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, List<DistrictModel>>> getDistricts() async {
    try {
      final districts = await firebaseService.getDistricts();
      return Right(districts);
    } catch (e) {
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> deleteDistrict(
      {required String districtId}) async {
    try {
      await firebaseService.deleteDistrict(districtId);
      return Right(SuccessObj(message: 'District deleted successfully'));
    } catch (e) {
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Stream<List<DistrictModel>> listenToDistricts() {
    return firebaseService.listenToDistricts();
  }
}
