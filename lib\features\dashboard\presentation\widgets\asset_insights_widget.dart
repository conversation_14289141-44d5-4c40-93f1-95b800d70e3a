import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/features/home/<USER>/theme.dart';
import 'package:provider/provider.dart';
import '../controllers/dashboard_data_controller.dart';

class AssetInsightsWidget extends StatelessWidget {
  const AssetInsightsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<DashboardDataController>();

    return Consumer<ColorNotifier>(
      builder: (context, notifier, child) {
        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: notifier.getBgColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: notifier.getfillborder, width: 1),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Asset Management',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: notifier.text,
                    ),
                  ),
                  IconButton(
                    icon: Icon(Icons.refresh, color: notifier.text),
                    onPressed: () => controller.loadAssetData(),
                    tooltip: 'Refresh Asset Data',
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // Loading/Error States
              Obx(() {
                if (controller.isLoadingAssets.value) {
                  return const Center(
                    child: Padding(
                      padding: EdgeInsets.all(20),
                      child: CircularProgressIndicator(),
                    ),
                  );
                }

                if (controller.assetError.value.isNotEmpty) {
                  return Center(
                    child: Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        children: [
                          Icon(Icons.error_outline,
                              color: Colors.red, size: 32),
                          const SizedBox(height: 8),
                          Text(
                            'Error: ${controller.assetError.value}',
                            style: const TextStyle(color: Colors.red),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  );
                }

                return Column(
                  children: [
                    // Asset Statistics Cards
                    _buildAssetStatsRow(controller, notifier),
                    const SizedBox(height: 20),

                    // Asset Status Breakdown
                    _buildAssetStatusBreakdown(controller, notifier),
                  ],
                );
              }),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAssetStatsRow(
      DashboardDataController controller, ColorNotifier notifier) {
    return Row(
      children: [
        Expanded(
          child: _buildAssetStatCard(
            'Total Assets',
            controller.totalAssets.value.toString(),
            Icons.inventory,
            notifier.liblueColor,
            Colors.blue,
            notifier,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildAssetStatCard(
            'Total Value',
            'PKR ${_formatCurrency(controller.totalAssetValue.value)}',
            Icons.attach_money,
            notifier.ligreenColor,
            Colors.green,
            notifier,
          ),
        ),
      ],
    );
  }

  Widget _buildAssetStatCard(
    String title,
    String value,
    IconData icon,
    Color backgroundColor,
    Color iconColor,
    ColorNotifier notifier,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: backgroundColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: backgroundColor.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: iconColor, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    color: notifier.text.withOpacity(0.7),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: notifier.text,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAssetStatusBreakdown(
      DashboardDataController controller, ColorNotifier notifier) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: notifier.getBgColor,
        borderRadius: BorderRadius.circular(8),
        border:
            Border.all(color: notifier.getfillborder.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Asset Status',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: notifier.text,
            ),
          ),
          const SizedBox(height: 12),
          _buildStatusRow(
              'In Use', controller.assetsInUse.value, Colors.green, notifier),
          const SizedBox(height: 8),
          _buildStatusRow('Under Maintenance',
              controller.assetsUnderMaintenance.value, Colors.orange, notifier),
          const SizedBox(height: 8),
          _buildStatusRow(
              'Retired', controller.assetsRetired.value, Colors.grey, notifier),
          const SizedBox(height: 8),
          _buildStatusRow(
              'Sold', controller.assetsSold.value, Colors.blue, notifier),
        ],
      ),
    );
  }

  Widget _buildStatusRow(
      String status, int count, Color color, ColorNotifier notifier) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: color,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              status,
              style: TextStyle(
                fontSize: 12,
                color: notifier.text.withOpacity(0.8),
              ),
            ),
          ],
        ),
        Text(
          count.toString(),
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: notifier.text,
          ),
        ),
      ],
    );
  }

  String _formatCurrency(double amount) {
    if (amount >= 1000000) {
      return '${(amount / 1000000).toStringAsFixed(1)}M';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)}K';
    } else {
      return amount.toStringAsFixed(0);
    }
  }
}
