import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';

class SlabManagementScreenController extends GetxController {
  var slabs = <String>[].obs;

  void addSlab(String slabName) {
    if (slabName.isNotEmpty) {
      slabs.add(slabName);
    } else {
      (failure) => SnackbarUtils.showError(
            AppStrings.errorS,
            'Slab name cannot be empty!',
          );
    }
  }

  void removeSlab(int index) {
    slabs.removeAt(index);
  }
}
