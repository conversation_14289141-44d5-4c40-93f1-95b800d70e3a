import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/constants/custom_dialogs.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/core/utils/mixins/pagination_mixin.dart';
import 'package:logestics/core/utils/mixins/auto_refresh_mixin.dart';
import 'package:logestics/models/invoice_model.dart';

import '../../../company/presentation/conrollers/company_controller.dart';
import '../../../../core/shared_services/success_obj.dart';
import '../../use_cases/delete_invoice_use_case.dart';
import '../../use_cases/update_invoice_status_use_case.dart';

class InvoiceListController extends GetxController
    with PaginationMixin, AutoRefreshMixin {
  final DeleteInvoiceUseCase deleteInvoiceUseCase;
  final UpdateInvoiceStatusUseCase updateInvoiceStatusUseCase;

  InvoiceListController({
    required this.deleteInvoiceUseCase,
    required this.updateInvoiceStatusUseCase,
  });

  var companyController = Get.find<CompanyController>();
  final selectedStatus = RxnString();
  final searchQuery = ''.obs;
  final filteredInvoices = <dynamic>[].obs;
  final searchController = TextEditingController();
  final isLoading = false.obs;
  final isInitialLoading = true.obs;
  final isRefreshing = false.obs;

  // Date filtering
  final dateFilterType =
      'all'.obs; // all, today, yesterday, week, month, custom
  final selectedStartDate = DateTime.now().obs;
  final selectedEndDate = DateTime.now().obs;

  @override
  void onInit() {
    super.onInit();
    searchController.addListener(_onSearchChanged);

    // Listen to real-time changes from company controller
    ever(companyController.company.invoices, (_) {
      // When invoices are updated, we're no longer in initial loading state
      if (isInitialLoading.value) {
        isInitialLoading.value = false;
      }
      _filterInvoices();
    });

    _filterInvoices();
  }

  @override
  void onReady() {
    super.onReady();
    // Refresh data when controller is ready
    refreshData();
  }

  /// Check if any loading operation is in progress
  bool get isAnyLoading =>
      isLoading.value || isInitialLoading.value || isRefreshing.value;

  /// Implementation of AutoRefreshMixin.refreshData
  @override
  Future<void> refreshData() async {
    isRefreshing.value = true;
    isLoading.value = true;
    try {
      // Check if we have data already loaded in company controller
      if (companyController.company.invoices.isNotEmpty) {
        // If we have data, just filter and update immediately
        _filterInvoices();
        update();
        // Reset loading states immediately since we have data
        isLoading.value = false;
        isRefreshing.value = false;
        isInitialLoading.value = false;
      } else {
        // If no data, force refresh the company controller
        companyController.forceRefresh();

        // Wait a moment for any potential Firestore updates to be received
        await Future.delayed(const Duration(milliseconds: 500));

        // Explicitly trigger filtering to update the UI
        _filterInvoices();
        update();
      }
    } finally {
      // Ensure loading states are reset
      isLoading.value = false;
      isRefreshing.value = false;
    }
  }

  /// Method to handle when the invoice screen becomes active
  void onScreenActivated() {
    // Reset initial loading state if we have data
    if (companyController.company.invoices.isNotEmpty) {
      isInitialLoading.value = false;
    }

    // Trigger filtering to ensure UI is updated
    _filterInvoices();
    update();
  }

  /// Manual refresh method for pull-to-refresh functionality
  Future<void> manualRefresh() async {
    await refreshData();
  }

  void _onSearchChanged() {
    searchQuery.value = searchController.text;
    _filterInvoices();
  }

  void searchInvoices(String query) {
    // Only update searchQuery, don't modify controller.text to avoid text selection
    searchQuery.value = query;
    _filterInvoices();
  }

  void filterByStatus(String? status) {
    selectedStatus.value = status;
    _filterInvoices();
  }

  void setSelectedStatus(String? status) {
    selectedStatus.value = status;
    _filterInvoices();
  }

  void setDateFilter(String filterType) {
    dateFilterType.value = filterType;
    _filterInvoices();
  }

  void setCustomDateRange(DateTime startDate, DateTime endDate) {
    selectedStartDate.value = startDate;
    selectedEndDate.value = endDate;
    dateFilterType.value = 'custom';
    _filterInvoices();
  }

  void _filterInvoices() {
    var filtered = List<dynamic>.from(companyController.company.invoices);

    // Apply search filter
    if (searchQuery.value.isNotEmpty) {
      final query = searchQuery.value.toLowerCase();
      filtered = filtered.where((invoice) {
        return invoice.tasNumber.toString().toLowerCase().contains(query) ||
            invoice.invoiceStatus.toLowerCase().contains(query) ||
            invoice.customerName.toLowerCase().contains(query) ||
            invoice.conveyNoteNumber.toLowerCase().contains(query);
      }).toList();
    }

    // Apply status filter
    if (selectedStatus.value != null && selectedStatus.value!.isNotEmpty) {
      filtered = filtered
          .where((invoice) => invoice.invoiceStatus == selectedStatus.value)
          .toList();
    }

    // Apply date filter based on belongsToDate (or orderDate as fallback)
    switch (dateFilterType.value) {
      case 'today':
        filtered = filtered.where((invoice) {
          final date =
              invoice.belongsToDate ?? invoice.orderDate ?? invoice.createdAt;
          return date.year == DateTime.now().year &&
              date.month == DateTime.now().month &&
              date.day == DateTime.now().day;
        }).toList();
        break;
      case 'yesterday':
        final yesterday = DateTime.now().subtract(const Duration(days: 1));
        filtered = filtered.where((invoice) {
          final date =
              invoice.belongsToDate ?? invoice.orderDate ?? invoice.createdAt;
          return date.year == yesterday.year &&
              date.month == yesterday.month &&
              date.day == yesterday.day;
        }).toList();
        break;
      case 'week':
        final weekAgo = DateTime.now().subtract(const Duration(days: 7));
        filtered = filtered.where((invoice) {
          final date =
              invoice.belongsToDate ?? invoice.orderDate ?? invoice.createdAt;
          return date.isAfter(weekAgo);
        }).toList();
        break;
      case 'month':
        filtered = filtered.where((invoice) {
          final date =
              invoice.belongsToDate ?? invoice.orderDate ?? invoice.createdAt;
          return date.year == DateTime.now().year &&
              date.month == DateTime.now().month;
        }).toList();
        break;
      case 'custom':
        filtered = filtered.where((invoice) {
          final date =
              invoice.belongsToDate ?? invoice.orderDate ?? invoice.createdAt;
          return date.isAfter(
                  selectedStartDate.value.subtract(const Duration(days: 1))) &&
              date.isBefore(selectedEndDate.value.add(const Duration(days: 1)));
        }).toList();
        break;
      case 'all':
      default:
        // No date filtering
        break;
    }

    filteredInvoices.value = filtered;
    setTotalItems(filteredInvoices.length);
  }

  List<dynamic> get paginatedInvoices => paginateList(filteredInvoices);

  Map<String, Color> statusColor = {
    'In Progress': const Color(0xFFffb269),
    "Completed": const Color(0xFF34d47f),
    "Not Started": const Color(0xFF0f7bf4),
    "Pending": const Color(0xFFe85542),
  };

  Future<void> deleteInvoice({
    required String tasNumber,
  }) async {
    try {
      final uid = companyController.currentUserId;
      if (uid.isEmpty) {
        SnackbarUtils.showError(
          AppStrings.errorS,
          'Company ID not found. Please try again.',
        );
        return;
      }

      // Show loading indicator
      Get.dialog(
        const Center(
          child: CircularProgressIndicator(),
        ),
        barrierDismissible: false,
      );

      // Call the use case
      final result = await deleteInvoiceUseCase.call(
        tasNumber: tasNumber,
        uid: uid,
      );

      // Close loading indicator
      Get.back();

      // Handle result
      result.fold(
        (failure) {
          SnackbarUtils.showError(
            AppStrings.errorS,
            failure.message,
          );
        },
        (success) {
          // Remove the invoice from the company controller's list
          companyController.company.invoices
              .removeWhere((invoice) => invoice.tasNumber == tasNumber);

          // Update filtered list and total items count
          _filterInvoices();

          // Update the UI
          companyController.update();

          // Show success message
          _showSuccessSnackbar(success);
        },
      );
    } catch (e) {
      // Close loading indicator if it's showing
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      // Show unexpected error
      SnackbarUtils.showError(
        AppStrings.errorS,
        'An unexpected error occurred: ${e.toString()}',
      );
    }
  }

  void showErrorSnackbar(String errorMessage) {
    if (errorMessage.isNotEmpty) {
      SnackbarUtils.showError(
        "Error", // Title of the snackbar
        errorMessage, // Error message content
      );
    }
  }

  Future<void> updateInvoiceStatus({
    required String tasNumber,
    required String newStatus,
  }) async {
    try {
      final uid = companyController.currentUserId;
      if (uid.isEmpty) {
        SnackbarUtils.showError(
          'Error',
          'Company ID not found. Please try again.',
        );
        return;
      }

      // Get the current invoice to check payment status
      final invoice = companyController.company.invoices
          .firstWhere((inv) => inv.tasNumber == tasNumber);

      // If trying to change from "Pending Payment" to another status
      if (invoice.invoiceStatus == 'Pending Payment' &&
          newStatus != 'Pending Payment') {
        // Check if there are any pending payments
        final vouchers = companyController.company.vouchers
            .where((v) => v.invoiceTasNumberList.contains(tasNumber));

        if (vouchers.isNotEmpty) {
          final voucher = vouchers.first;
          if (voucher.pendingAmount > 0) {
            SnackbarUtils.showError(
              'Error',
              'Cannot change status: Invoice has pending payments of PKR ${voucher.pendingAmount.toStringAsFixed(2)}',
            );
            return;
          }
        }
      }

      // Call the use case
      final result = await updateInvoiceStatusUseCase.call(
        invoiceNumber: tasNumber,
        uid: uid,
        newStatus: newStatus,
      );

      // Handle result
      result.fold(
        (failure) {
          SnackbarUtils.showError(
            AppStrings.errorS,
            failure.message,
          );
        },
        (success) {
          // Update the invoice in the company controller's list
          final index = companyController.company.invoices
              .indexWhere((invoice) => invoice.tasNumber == tasNumber);
          if (index != -1) {
            final currentInvoice = companyController.company.invoices[index];
            final updatedInvoice = InvoiceModel(
              invoiceNumber: currentInvoice.invoiceNumber,
              invoiceStatus: newStatus,
              tasNumber: currentInvoice.tasNumber,
              productName: currentInvoice.productName,
              numberOfBags: currentInvoice.numberOfBags,
              weightPerBag: currentInvoice.weightPerBag,
              customerName: currentInvoice.customerName,
              truckNumber: currentInvoice.truckNumber,
              conveyNoteNumber: currentInvoice.conveyNoteNumber,
              biltyNumber: currentInvoice.biltyNumber,
              orderDate: currentInvoice.orderDate,
              consignorName: currentInvoice.consignorName,
              deliveryMode: currentInvoice.deliveryMode,
              districtId: currentInvoice.districtId,
              districtName: currentInvoice.districtName,
              stationId: currentInvoice.stationId,
              stationName: currentInvoice.stationName,
              fromPlaceId: currentInvoice.fromPlaceId,
              fromPlaceName: currentInvoice.fromPlaceName,
              distanceInKilometers: currentInvoice.distanceInKilometers,
              consignorPickUpAddress: currentInvoice.consignorPickUpAddress,
              uid: currentInvoice.uid,
            );
            companyController.company.invoices[index] = updatedInvoice;
          }

          // Update filtered list and total items count
          _filterInvoices();

          // Update the UI
          update();

          // Show success message
          SnackbarUtils.showSuccess(
            'Success',
            success.message,
          );
        },
      );
    } catch (e) {
      SnackbarUtils.showError(
        'Error',
        'Failed to update invoice status: ${e.toString()}',
      );
    }
  }

  void _showSuccessSnackbar(SuccessObj success) {
    Get.back(); // Close any active dialogs
    displaySuccessSnackbar(success);
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }
}
