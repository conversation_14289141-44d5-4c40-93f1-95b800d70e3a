class InvoiceModel {
  // Invoice Information
  final int invoiceNumber;
  final String invoiceStatus;
  final String tasNumber;

  // Product Details
  final String productName;
  final int numberOfBags;
  final double weightPerBag;

  // Customer & Transport Details
  final String customerName;
  final String truckNumber;
  final String conveyNoteNumber; // Renamed from conveyanceNumber
  final String biltyNumber; // Added bilty number field
  final DateTime? orderDate;
  final DateTime? belongsToDate; // Date this invoice belongs to (user-selected)
  final DateTime createdAt; // System creation date

  // Consignor & Delivery Details
  final String consignorName;
  String consignorPickUpAddress;

  final String deliveryMode;

  // Location & Distance Management
  final String districtId;
  final String districtName;
  final String stationId;
  final String stationName;
  final String fromPlaceId;
  final String fromPlaceName;
  final double distanceInKilometers;

  // User Identification
  final String uid; // User ID who owns this invoice

  InvoiceModel({
    required this.invoiceNumber,
    required this.invoiceStatus,
    required this.tasNumber,
    required this.productName,
    required this.numberOfBags,
    required this.weightPerBag,
    required this.customerName,
    required this.truckNumber,
    required this.conveyNoteNumber,
    required this.biltyNumber,
    this.orderDate,
    this.belongsToDate,
    DateTime? createdAt,
    required this.consignorName,
    required this.deliveryMode,
    required this.districtId,
    required this.districtName,
    required this.stationId,
    required this.stationName,
    required this.fromPlaceId,
    required this.fromPlaceName,
    required this.distanceInKilometers,
    required this.consignorPickUpAddress,
    this.uid = '', // Default empty string for backward compatibility
  }) : createdAt = createdAt ?? DateTime.now();

  factory InvoiceModel.fromJson(Map<String, dynamic> json) {
    return InvoiceModel(
      invoiceNumber: json['invoiceNumber'] is int
          ? json['invoiceNumber']
          : int.tryParse(json['invoiceNumber']?.toString() ?? '0') ?? 0,
      invoiceStatus: json['invoiceStatus']?.toString() ?? '',
      tasNumber: json['tasNumber']?.toString() ?? '',
      productName: json['productName']?.toString() ?? '',
      numberOfBags: json['numberOfBags'] is int
          ? json['numberOfBags']
          : int.tryParse(json['numberOfBags']?.toString() ?? '0') ?? 0,
      weightPerBag: json['weightPerBag'] != null
          ? (json['weightPerBag'] as num).toDouble()
          : 50.0,
      customerName: json['customerName']?.toString() ?? '',
      truckNumber: json['truckNumber']?.toString() ?? '',
      conveyNoteNumber: json['conveyNoteNumber']?.toString() ?? '',
      biltyNumber: json['biltyNumber']?.toString() ?? '',
      orderDate:
          json['orderDate'] != null ? _parseDateTime(json['orderDate']) : null,
      belongsToDate: json['belongsToDate'] != null
          ? _parseDateTime(json['belongsToDate'])
          : null,
      createdAt: json['createdAt'] != null
          ? _parseDateTime(json['createdAt']) ?? DateTime.now()
          : DateTime.now(),
      consignorName: json['consignorName']?.toString() ?? '',
      deliveryMode: json['deliveryMode']?.toString() ?? '',
      districtId: json['districtId']?.toString() ?? '',
      districtName: json['districtName']?.toString() ?? '',
      stationId: json['stationId']?.toString() ?? '',
      stationName: json['stationName']?.toString() ?? '',
      fromPlaceId: json['fromPlaceId']?.toString() ?? '',
      fromPlaceName: json['fromPlaceName']?.toString() ?? '',
      distanceInKilometers: json['distanceInKilometers'] != null
          ? (json['distanceInKilometers'] as num).toDouble()
          : 0.0,
      consignorPickUpAddress: json['consignorPickUpAddress']?.toString() ?? '',
      uid: json['uid']?.toString() ?? '', // Extract UID from JSON
    );
  }

  // Helper method to safely parse DateTime from various formats
  static DateTime? _parseDateTime(dynamic value) {
    if (value == null) return null;

    try {
      // If it's already a DateTime, return it
      if (value is DateTime) return value;

      // If it's a timestamp (int), convert it
      if (value is int) {
        return DateTime.fromMillisecondsSinceEpoch(value);
      }

      // If it's a string, try to parse it
      if (value is String) {
        // Try parsing as ISO string first
        try {
          return DateTime.parse(value);
        } catch (e) {
          // If that fails, try parsing as timestamp
          final timestamp = int.tryParse(value);
          if (timestamp != null) {
            return DateTime.fromMillisecondsSinceEpoch(timestamp);
          }
        }
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'invoiceNumber': invoiceNumber,
      'invoiceStatus': invoiceStatus,
      'tasNumber': tasNumber,
      'productName': productName,
      'numberOfBags': numberOfBags,
      'weightPerBag': weightPerBag,
      'customerName': customerName,
      'truckNumber': truckNumber,
      'conveyNoteNumber': conveyNoteNumber,
      'biltyNumber': biltyNumber,
      'orderDate': orderDate?.toIso8601String(),
      'belongsToDate': belongsToDate?.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'consignorName': consignorName,
      'deliveryMode': deliveryMode,
      'districtId': districtId,
      'districtName': districtName,
      'stationId': stationId,
      'stationName': stationName,
      'fromPlaceId': fromPlaceId,
      'fromPlaceName': fromPlaceName,
      'distanceInKilometers': distanceInKilometers,
      'consignorPickUpAddress': consignorPickUpAddress,
      'uid': uid, // Include UID in JSON
    };
  }

  @override
  String toString() {
    return 'Invoice(invoiceNumber: $invoiceNumber, invoiceStatus: $invoiceStatus, tasNumber: $tasNumber, productName: $productName, numberOfBags: $numberOfBags, weightPerBag: $weightPerBag, customerName: $customerName, truckNumber: $truckNumber, conveyNoteNumber: $conveyNoteNumber, biltyNumber: $biltyNumber, orderDate: $orderDate, belongsToDate: $belongsToDate, createdAt: $createdAt, consignorName: $consignorName, consignorPickUpAddress: $consignorPickUpAddress, deliveryMode: $deliveryMode, districtId: $districtId, districtName: $districtName, stationId: $stationId, stationName: $stationName, fromPlaceId: $fromPlaceId, fromPlaceName: $fromPlaceName, distanceInKilometers: $distanceInKilometers, uid: $uid)';
  }
}

class ListOfInvoices {
  List<InvoiceModel> invoices;

  // Constructor
  ListOfInvoices({required this.invoices});

  // Method to convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'invoices': invoices.map((invoice) => invoice.toJson()).toList(),
    };
  }

  // Factory to create an instance from JSON
  factory ListOfInvoices.fromJson(Map<String, dynamic> json) {
    return ListOfInvoices(
      invoices: (json['invoices'] as List)
          .map((invoiceJson) => InvoiceModel.fromJson(invoiceJson))
          .toList(),
    );
  }
}
