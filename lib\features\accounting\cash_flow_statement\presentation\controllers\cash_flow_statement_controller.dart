import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../../../core/utils/snackbar_utils.dart';
import '../../../../../models/finance/cash_flow_statement_model.dart';
import '../../repositories/cash_flow_statement_repository.dart';

/// Controller for Cash Flow Statement functionality
class CashFlowStatementController extends GetxController {
  late final CashFlowStatementRepository _repository;

  // Form controllers
  final formKey = GlobalKey<FormState>();
  final companyNameController = TextEditingController();
  final startDateController = TextEditingController();
  final endDateController = TextEditingController();

  // Observable properties
  final _isLoading = false.obs;
  final _currentReport = Rxn<CashFlowStatementReport>();
  final _includeInactiveAccounts = false.obs;
  final _includeZeroBalances = false.obs;
  final _useDirectMethod = false.obs;

  // Getters
  bool get isLoading => _isLoading.value;
  CashFlowStatementReport? get currentReport => _currentReport.value;
  bool get includeInactiveAccounts => _includeInactiveAccounts.value;
  bool get includeZeroBalances => _includeZeroBalances.value;
  bool get useDirectMethod => _useDirectMethod.value;

  // Computed properties
  bool get hasReportData => _currentReport.value != null;
  bool get hasOperatingData =>
      hasReportData &&
      _currentReport.value!.operatingActivities.lineItems.isNotEmpty;
  bool get hasInvestingData =>
      hasReportData &&
      _currentReport.value!.investingActivities.lineItems.isNotEmpty;
  bool get hasFinancingData =>
      hasReportData &&
      _currentReport.value!.financingActivities.lineItems.isNotEmpty;

  @override
  void onInit() {
    super.onInit();
    log('CashFlowStatementController initialized');
    _repository = Get.find<CashFlowStatementRepository>();
    _initializeForm();
  }

  @override
  void onClose() {
    companyNameController.dispose();
    startDateController.dispose();
    endDateController.dispose();
    super.onClose();
  }

  /// Initialize form with default values
  void _initializeForm() {
    // Set default company name (could be fetched from user preferences)
    companyNameController.text = 'Your Company Name';

    // Set default date range (current month)
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);

    startDateController.text = DateFormat('dd/MM/yyyy').format(startOfMonth);
    endDateController.text = DateFormat('dd/MM/yyyy').format(endOfMonth);
  }

  /// Toggle include inactive accounts
  void toggleIncludeInactiveAccounts() {
    _includeInactiveAccounts.value = !_includeInactiveAccounts.value;
  }

  /// Toggle include zero balances
  void toggleIncludeZeroBalances() {
    _includeZeroBalances.value = !_includeZeroBalances.value;
  }

  /// Toggle direct method
  void toggleDirectMethod() {
    _useDirectMethod.value = !_useDirectMethod.value;
  }

  /// Generate Cash Flow Statement report
  Future<void> generateReport() async {
    if (!formKey.currentState!.validate()) {
      SnackbarUtils.showError('Form Validation', 'Please fix form errors');
      return;
    }

    try {
      _isLoading.value = true;

      final startDate = _parseDate(startDateController.text);
      final endDate = _parseDate(endDateController.text);

      if (startDate == null || endDate == null) {
        SnackbarUtils.showError('Date Error', 'Invalid date format');
        return;
      }

      log('Generating Cash Flow Statement from $startDate to $endDate');

      final result = await _repository.generateCashFlowStatement(
        startDate: startDate,
        endDate: endDate,
        companyName: companyNameController.text.trim(),
        includeInactiveAccounts: _includeInactiveAccounts.value,
        includeZeroBalances: _includeZeroBalances.value,
        useDirectMethod: _useDirectMethod.value,
      );

      result.fold(
        (failure) {
          log('Cash Flow Statement generation failed: ${failure.message}');
          SnackbarUtils.showError('Generation Failed', failure.message);
        },
        (report) {
          log('Cash Flow Statement generated successfully');
          _currentReport.value = report;
          SnackbarUtils.showSuccess(
            'Report Generated',
            'Cash Flow Statement generated successfully',
          );
        },
      );
    } catch (e) {
      log('Error generating Cash Flow Statement: $e');
      SnackbarUtils.showError('Error', 'Failed to generate report: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Clear current report
  void clearReport() {
    _currentReport.value = null;
    log('Cash Flow Statement report cleared');
  }

  /// Parse date string to DateTime
  DateTime? _parseDate(String dateString) {
    try {
      return DateFormat('dd/MM/yyyy').parse(dateString);
    } catch (e) {
      log('Error parsing date: $dateString - $e');
      return null;
    }
  }

  /// Validate date range
  String? validateDateRange(String? value) {
    if (value == null || value.isEmpty) {
      return 'Date is required';
    }

    final date = _parseDate(value);
    if (date == null) {
      return 'Invalid date format (dd/MM/yyyy)';
    }

    if (date.isAfter(DateTime.now())) {
      return 'Date cannot be in the future';
    }

    return null;
  }

  /// Validate company name
  String? validateCompanyName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Company name is required';
    }

    if (value.trim().length < 2) {
      return 'Company name must be at least 2 characters';
    }

    return null;
  }

  /// Get formatted report period
  String get reportPeriod {
    if (!hasReportData) return '';

    final report = _currentReport.value!;
    final startDate = DateFormat('MMM dd, yyyy').format(report.startDate);
    final endDate = DateFormat('MMM dd, yyyy').format(report.endDate);

    return '$startDate - $endDate';
  }

  /// Get method description
  String get methodDescription {
    return _useDirectMethod.value
        ? 'Direct Method - Shows actual cash receipts and payments'
        : 'Indirect Method - Starts with net income and adjusts for non-cash items';
  }

  /// Export report to PDF
  Future<void> exportToPDF() async {
    if (!hasReportData) {
      SnackbarUtils.showError('Export Error', 'No report data to export');
      return;
    }

    try {
      _isLoading.value = true;

      // TODO: Implement PDF export using existing financial report export service
      log('Exporting Cash Flow Statement to PDF');

      SnackbarUtils.showSuccess(
          'Export', 'PDF export functionality coming soon');
    } catch (e) {
      log('Error exporting to PDF: $e');
      SnackbarUtils.showError('Export Error', 'Failed to export PDF: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Export report to Excel
  Future<void> exportToExcel() async {
    if (!hasReportData) {
      SnackbarUtils.showError('Export Error', 'No report data to export');
      return;
    }

    try {
      _isLoading.value = true;

      // TODO: Implement Excel export
      log('Exporting Cash Flow Statement to Excel');

      SnackbarUtils.showSuccess(
          'Export', 'Excel export functionality coming soon');
    } catch (e) {
      log('Error exporting to Excel: $e');
      SnackbarUtils.showError('Export Error', 'Failed to export Excel: $e');
    } finally {
      _isLoading.value = false;
    }
  }
}
