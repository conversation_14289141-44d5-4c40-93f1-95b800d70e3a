import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:logestics/core/utils/widgets/my_dropdown_field.dart';
import 'package:logestics/core/utils/widgets/my_text_field.dart';
import 'package:logestics/features/asset_management/presentation/controllers/asset_form_controller.dart';
import 'package:logestics/models/asset/asset_model.dart';
import 'package:logestics/features/home/<USER>/theme.dart';

class AssetFormView extends StatefulWidget {
  final AssetModel? asset; // For editing existing asset

  const AssetFormView({Key? key, this.asset}) : super(key: key);

  @override
  State<AssetFormView> createState() => _AssetFormViewState();
}

class _AssetFormViewState extends State<AssetFormView> {
  late ColorNotifier notifier;
  late AssetFormController controller;

  @override
  void initState() {
    super.initState();
    controller = Get.find<AssetFormController>();

    // Initialize form for editing if asset is provided
    if (widget.asset != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        controller.initializeForEdit(widget.asset!);
      });
    } else {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        controller.clearForm();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    notifier = Provider.of<ColorNotifier>(context, listen: true);

    return Scaffold(
      backgroundColor: notifier.getBgColor,
      appBar: AppBar(
        backgroundColor: notifier.getBgColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: notifier.text),
          onPressed: () => Get.back(),
        ),
        title: Obx(() => Text(
              controller.isEditMode.value ? 'Edit Asset' : 'Add New Asset',
              style: TextStyle(
                color: notifier.text,
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
            )),
        actions: [
          Obx(() => controller.isLoading.value
              ? const Padding(
                  padding: EdgeInsets.all(16.0),
                  child: SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                )
              : TextButton(
                  onPressed: _saveAsset,
                  child: Text(
                    'Save',
                    style: TextStyle(
                      color: const Color(0xFF0165FC),
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                )),
        ],
      ),
      body: Form(
        key: controller.formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildBasicInfoSection(),
              const SizedBox(height: 24),
              _buildFinancialInfoSection(),
              const SizedBox(height: 24),
              _buildOperationalInfoSection(),
              const SizedBox(height: 24),
              _buildDepreciationSection(),
              const SizedBox(height: 24),
              _buildAttachmentsSection(),
              const SizedBox(height: 24),
              _buildNotesSection(),
              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      color: notifier.getBgColor,
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Basic Information',
              style: TextStyle(
                color: notifier.text,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            MyTextFormField(
              controller: controller.nameController,
              titleText: 'Asset Name *',
              labelText: 'Asset Name *',
              hintText: 'Enter asset name',
              validator: (value) =>
                  controller.validateRequired(value, 'Asset name'),
            ),
            const SizedBox(height: 16),
            Obx(() => MyDropdownFormField(
                  titletext: 'Asset Type *',
                  hinttext: 'Select asset type',
                  items: AssetType.allTypes,
                  initalValue: controller.selectedType.value.isNotEmpty
                      ? controller.selectedType.value
                      : null,
                  onChanged: (value) =>
                      controller.selectedType.value = value ?? '',
                )),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: MyTextFormField(
                    controller: controller.brandController,
                    titleText: 'Brand',
                    labelText: 'Brand',
                    hintText: 'Enter brand',
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: MyTextFormField(
                    controller: controller.modelController,
                    titleText: 'Model',
                    labelText: 'Model',
                    hintText: 'Enter model',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: MyTextFormField(
                    controller: controller.registrationNumberController,
                    titleText: 'Registration Number',
                    labelText: 'Registration Number',
                    hintText: 'Enter registration number',
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: MyTextFormField(
                    controller: controller.serialNumberController,
                    titleText: 'Serial Number',
                    labelText: 'Serial Number',
                    hintText: 'Enter serial number',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialInfoSection() {
    return Card(
      color: notifier.getBgColor,
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Financial Information',
              style: TextStyle(
                color: notifier.text,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildDatePicker(),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: MyTextFormField(
                    controller: controller.purchaseCostController,
                    titleText: 'Purchase Cost *',
                    labelText: 'Purchase Cost *',
                    hintText: 'Enter purchase cost',
                    keyboardType: TextInputType.number,
                    validator: (value) => controller.validatePositiveNumber(
                        value, 'Purchase cost'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: MyTextFormField(
                    controller: controller.vendorController,
                    titleText: 'Vendor',
                    labelText: 'Vendor',
                    hintText: 'Enter vendor name',
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: MyTextFormField(
                    controller: controller.supplierController,
                    titleText: 'Supplier',
                    labelText: 'Supplier',
                    hintText: 'Enter supplier name',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOperationalInfoSection() {
    return Card(
      color: notifier.getBgColor,
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Operational Information',
              style: TextStyle(
                color: notifier.text,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: MyTextFormField(
                    controller: controller.locationController,
                    titleText: 'Location',
                    labelText: 'Location',
                    hintText: 'Enter location',
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: MyTextFormField(
                    controller: controller.departmentController,
                    titleText: 'Department',
                    labelText: 'Department',
                    hintText: 'Enter department',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Obx(() => MyDropdownFormField(
                  titletext: 'Current Status *',
                  hinttext: 'Select current status',
                  items: AssetStatus.allStatuses,
                  initalValue: controller.selectedStatus.value.isNotEmpty
                      ? controller.selectedStatus.value
                      : null,
                  onChanged: (value) =>
                      controller.selectedStatus.value = value ?? '',
                )),
          ],
        ),
      ),
    );
  }

  Widget _buildDepreciationSection() {
    return Card(
      color: notifier.getBgColor,
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Depreciation Configuration',
              style: TextStyle(
                color: notifier.text,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Obx(() => MyDropdownFormField(
                        titletext: 'Depreciation Method *',
                        hinttext: 'Select method',
                        items: DepreciationMethod.allMethods,
                        initalValue: controller
                                .selectedDepreciationMethod.value.isNotEmpty
                            ? controller.selectedDepreciationMethod.value
                            : null,
                        onChanged: (value) => controller
                            .selectedDepreciationMethod.value = value ?? '',
                      )),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: MyTextFormField(
                    controller: controller.estimatedUsefulLifeController,
                    titleText: 'Useful Life (Years) *',
                    labelText: 'Useful Life (Years) *',
                    hintText: 'Enter years',
                    keyboardType: TextInputType.number,
                    validator: (value) =>
                        controller.validateInteger(value, 'Useful life'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Obx(() => MyTextFormField(
                        controller: controller.currentValueController,
                        titleText: 'Current Value *',
                        labelText: 'Current Value *',
                        hintText: 'Current value',
                        keyboardType: TextInputType.number,
                        validator: (value) =>
                            controller.validateNumber(value, 'Current value'),
                        enabled: controller.isCurrentValueManual.value,
                      )),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Obx(() => CheckboxListTile(
                        title: Text(
                          'Manual Override',
                          style: TextStyle(color: notifier.text, fontSize: 14),
                        ),
                        value: controller.isCurrentValueManual.value,
                        onChanged: (value) => controller
                            .isCurrentValueManual.value = value ?? false,
                        activeColor: const Color(0xFF0165FC),
                        contentPadding: EdgeInsets.zero,
                      )),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAttachmentsSection() {
    return Card(
      color: notifier.getBgColor,
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Attachments',
              style: TextStyle(
                color: notifier.text,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            OutlinedButton.icon(
              icon: const Icon(Icons.attach_file),
              label: const Text('Add Files'),
              onPressed: () {
                // File picker implementation would go here
                // For now, show a placeholder message
                Get.snackbar(
                  'Coming Soon',
                  'File attachment functionality will be available soon',
                  snackPosition: SnackPosition.TOP,
                );
              },
            ),
            const SizedBox(height: 8),
            Text(
              'Supported: Images, PDFs, Documents',
              style: TextStyle(
                color: notifier.text.withOpacity(0.6),
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesSection() {
    return Card(
      color: notifier.getBgColor,
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Additional Notes',
              style: TextStyle(
                color: notifier.text,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            MyTextFormField(
              controller: controller.notesController,
              titleText: 'Notes',
              labelText: 'Notes',
              hintText: 'Enter any additional notes or comments',
              maxLength: 500,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDatePicker() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Purchase Date *',
          style: TextStyle(
            color: notifier.text,
            fontFamily: "Outfit",
          ),
        ),
        const SizedBox(height: 7),
        Obx(() => InkWell(
              onTap: _selectPurchaseDate,
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                decoration: BoxDecoration(
                  border: Border.all(color: notifier.getfillborder),
                  borderRadius: BorderRadius.circular(5),
                ),
                child: Row(
                  children: [
                    Icon(Icons.calendar_today, color: notifier.text, size: 20),
                    const SizedBox(width: 12),
                    Text(
                      controller.purchaseDate.value != null
                          ? '${controller.purchaseDate.value!.day}/${controller.purchaseDate.value!.month}/${controller.purchaseDate.value!.year}'
                          : 'Select purchase date',
                      style: TextStyle(
                        color: controller.purchaseDate.value != null
                            ? notifier.text
                            : Colors.grey,
                        fontFamily: "Outfit",
                      ),
                    ),
                  ],
                ),
              ),
            )),
      ],
    );
  }

  Future<void> _selectPurchaseDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: controller.purchaseDate.value ?? DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      controller.purchaseDate.value = picked;
    }
  }

  Future<void> _saveAsset() async {
    final success = await controller.saveAsset();
    if (success) {
      Get.back(result: true); // Return true to indicate success
    }
  }
}
