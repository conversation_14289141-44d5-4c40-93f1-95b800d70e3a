import 'package:flutter_test/flutter_test.dart';
import 'package:logestics/models/finance/account_transaction_model.dart';

void main() {
  group('PDF Preview Functionality Tests', () {
    // Helper function to create test transactions
    List<AccountTransactionModel> createTestTransactions() {
      return [
        AccountTransactionModel(
          id: '1',
          accountId: 'acc1',
          accountName: 'Test Account',
          amount: 1000.0,
          transactionDate: DateTime(2024, 1, 15, 10, 30), // Jan 15, 2024
          description: 'Transaction 1',
          type: TransactionType.deposit,
          uid: 'user1',
        ),
        AccountTransactionModel(
          id: '2',
          accountId: 'acc1',
          accountName: 'Test Account',
          amount: 500.0,
          transactionDate: DateTime(2024, 1, 20, 14, 45), // Jan 20, 2024
          description: 'Transaction 2',
          type: TransactionType.expense,
          uid: 'user1',
        ),
        AccountTransactionModel(
          id: '3',
          accountId: 'acc1',
          accountName: 'Test Account',
          amount: 750.0,
          transactionDate: DateTime(2024, 1, 25, 9, 15), // Jan 25, 2024
          description: 'Transaction 3',
          type: TransactionType.deposit,
          uid: 'user1',
        ),
        AccountTransactionModel(
          id: '4',
          accountId: 'acc1',
          accountName: 'Test Account',
          amount: 300.0,
          transactionDate: DateTime(2024, 2, 5, 16, 20), // Feb 5, 2024
          description: 'Transaction 4',
          type: TransactionType.expense,
          uid: 'user1',
        ),
      ];
    }

    // Helper function to simulate the date filtering logic
    List<AccountTransactionModel> applyDateFilter(
      List<AccountTransactionModel> transactions,
      DateTime startDate,
      DateTime endDate,
    ) {
      final startOfDay = DateTime(startDate.year, startDate.month, startDate.day, 0, 0, 0);
      final endOfDay = DateTime(endDate.year, endDate.month, endDate.day, 23, 59, 59, 999);

      return transactions.where((transaction) {
        final transactionDate = transaction.transactionDate;
        return (transactionDate.isAfter(startOfDay) || transactionDate.isAtSameMomentAs(startOfDay)) &&
               (transactionDate.isBefore(endOfDay) || transactionDate.isAtSameMomentAs(endOfDay));
      }).toList();
    }

    // Helper function to calculate transaction summary
    Map<String, double> calculateSummary(List<AccountTransactionModel> transactions) {
      double totalCredits = 0.0;
      double totalDebits = 0.0;

      for (final transaction in transactions) {
        if (transaction.type == TransactionType.deposit) {
          totalCredits += transaction.amount;
        } else {
          totalDebits += transaction.amount;
        }
      }

      return {
        'totalCredits': totalCredits,
        'totalDebits': totalDebits,
        'netBalance': totalCredits - totalDebits,
      };
    }

    test('should show correct filtered transaction count in preview', () {
      final allTransactions = createTestTransactions();
      
      // Filter for January 15-25, 2024
      final startDate = DateTime(2024, 1, 15);
      final endDate = DateTime(2024, 1, 25);
      final filteredTransactions = applyDateFilter(allTransactions, startDate, endDate);
      
      // Should show 3 of 4 transactions
      expect(filteredTransactions.length, 3);
      expect(allTransactions.length, 4);
      
      // Verify the filtering status message
      final isFiltered = filteredTransactions.length < allTransactions.length;
      expect(isFiltered, true);
      
      final expectedMessage = 'Showing ${filteredTransactions.length} of ${allTransactions.length} transactions (filtered by date range)';
      expect(expectedMessage, 'Showing 3 of 4 transactions (filtered by date range)');
    });

    test('should show all transactions message when no filtering applied', () {
      final allTransactions = createTestTransactions();
      
      // Filter for entire range that includes all transactions
      final startDate = DateTime(2024, 1, 1);
      final endDate = DateTime(2024, 2, 28);
      final filteredTransactions = applyDateFilter(allTransactions, startDate, endDate);
      
      // Should show all transactions
      expect(filteredTransactions.length, 4);
      expect(allTransactions.length, 4);
      
      // Verify the no-filtering status message
      final isFiltered = filteredTransactions.length < allTransactions.length;
      expect(isFiltered, false);
      
      final expectedMessage = 'Showing all ${filteredTransactions.length} transactions for selected period';
      expect(expectedMessage, 'Showing all 4 transactions for selected period');
    });

    test('should calculate correct summary for filtered transactions', () {
      final allTransactions = createTestTransactions();
      
      // Filter for January 15-25, 2024 (transactions 1, 2, 3)
      final startDate = DateTime(2024, 1, 15);
      final endDate = DateTime(2024, 1, 25);
      final filteredTransactions = applyDateFilter(allTransactions, startDate, endDate);
      
      final summary = calculateSummary(filteredTransactions);
      
      // Transaction 1: +1000 (deposit)
      // Transaction 2: -500 (expense)  
      // Transaction 3: +750 (deposit)
      expect(summary['totalCredits'], 1750.0); // 1000 + 750
      expect(summary['totalDebits'], 500.0);   // 500
      expect(summary['netBalance'], 1250.0);   // 1750 - 500
    });

    test('should show correct transaction preview list', () {
      final allTransactions = createTestTransactions();
      
      // Filter for January 15-25, 2024
      final startDate = DateTime(2024, 1, 15);
      final endDate = DateTime(2024, 1, 25);
      final filteredTransactions = applyDateFilter(allTransactions, startDate, endDate);
      
      // Should show first 5 transactions (or all if less than 5)
      final previewCount = filteredTransactions.length > 5 ? 5 : filteredTransactions.length;
      expect(previewCount, 3); // We have 3 filtered transactions
      
      // Verify the transactions are in the correct order
      expect(filteredTransactions[0].id, '1'); // Jan 15
      expect(filteredTransactions[1].id, '2'); // Jan 20
      expect(filteredTransactions[2].id, '3'); // Jan 25
      
      // Verify transaction types for display
      expect(filteredTransactions[0].type, TransactionType.deposit);
      expect(filteredTransactions[1].type, TransactionType.expense);
      expect(filteredTransactions[2].type, TransactionType.deposit);
    });

    test('should handle preview with more than 5 transactions', () {
      // Create more than 5 transactions
      final manyTransactions = List.generate(10, (index) => 
        AccountTransactionModel(
          id: 'tx_$index',
          accountId: 'acc1',
          accountName: 'Test Account',
          amount: 100.0 * (index + 1),
          transactionDate: DateTime(2024, 1, 15 + index), // Sequential dates
          description: 'Transaction ${index + 1}',
          type: index % 2 == 0 ? TransactionType.deposit : TransactionType.expense,
          uid: 'user1',
        ),
      );
      
      // Filter to include all transactions
      final startDate = DateTime(2024, 1, 15);
      final endDate = DateTime(2024, 1, 25);
      final filteredTransactions = applyDateFilter(manyTransactions, startDate, endDate);
      
      // Should show first 5 in preview
      final previewCount = filteredTransactions.length > 5 ? 5 : filteredTransactions.length;
      expect(previewCount, 5);
      
      // Should indicate there are more transactions
      final hasMore = filteredTransactions.length > 5;
      expect(hasMore, true);
      
      final remainingCount = filteredTransactions.length - 5;
      final moreMessage = '... and $remainingCount more transactions';
      expect(moreMessage, '... and ${filteredTransactions.length - 5} more transactions');
    });

    test('should format date range display correctly', () {
      final startDate = DateTime(2024, 1, 15);
      final endDate = DateTime(2024, 1, 25);
      
      // Simulate date range formatting (dd/MM/yyyy)
      final dateFormat = 'dd/MM/yyyy';
      final formattedStart = '15/01/2024';
      final formattedEnd = '25/01/2024';
      
      final dateRangeDisplay = '$formattedStart - $formattedEnd';
      expect(dateRangeDisplay, '15/01/2024 - 25/01/2024');
    });

    test('should handle empty filtered results', () {
      final allTransactions = createTestTransactions();
      
      // Filter for a date range with no transactions
      final startDate = DateTime(2024, 3, 1);
      final endDate = DateTime(2024, 3, 31);
      final filteredTransactions = applyDateFilter(allTransactions, startDate, endDate);
      
      // Should have no filtered transactions
      expect(filteredTransactions.length, 0);
      expect(allTransactions.length, 4);
      
      // Summary should be zero
      final summary = calculateSummary(filteredTransactions);
      expect(summary['totalCredits'], 0.0);
      expect(summary['totalDebits'], 0.0);
      expect(summary['netBalance'], 0.0);
    });

    test('should maintain consistency between preview and PDF data', () {
      final allTransactions = createTestTransactions();
      
      // Filter for January 20-25, 2024
      final startDate = DateTime(2024, 1, 20);
      final endDate = DateTime(2024, 1, 25);
      final filteredTransactions = applyDateFilter(allTransactions, startDate, endDate);
      
      // The filtered transactions should be exactly what goes into the PDF
      expect(filteredTransactions.length, 2); // Transactions 2 and 3
      expect(filteredTransactions[0].id, '2'); // Jan 20
      expect(filteredTransactions[1].id, '3'); // Jan 25
      
      // Summary should match what will be in the PDF
      final summary = calculateSummary(filteredTransactions);
      expect(summary['totalCredits'], 750.0);  // Transaction 3
      expect(summary['totalDebits'], 500.0);   // Transaction 2
      expect(summary['netBalance'], 250.0);    // 750 - 500
      
      // This data should be identical to what the PDF service receives
      final pdfData = {
        'transactions': filteredTransactions,
        'startDate': startDate,
        'endDate': endDate,
        'summary': summary,
      };
      
      expect(pdfData['transactions'], filteredTransactions);
      expect(pdfData['startDate'], startDate);
      expect(pdfData['endDate'], endDate);
    });
  });
}
