import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Tax Authority Payment Distribution Tests', () {
    test('should calculate single tax authority payment correctly', () {
      // Test data
      const companyFreight = 10000.0;
      const expectedTaxAmount = companyFreight * 0.15; // 15% = 1500.0
      final selectedAuthorities = ['SRB (Sindh Revenue Board)'];

      // Simulate single authority calculation
      final numberOfAuthorities = selectedAuthorities.length;
      expect(numberOfAuthorities, 1);

      // Single authority gets full 15% tax amount
      final singleAuthorityAmount = expectedTaxAmount;
      expect(singleAuthorityAmount, 1500.0);

      // Verify description format
      const voucherNumber = 'V-001';
      final expectedDescription = 'Tax payment to ${selectedAuthorities.first} - Voucher #$voucherNumber';
      expect(expectedDescription, 'Tax payment to SRB (Sindh Revenue Board) - Voucher #V-001');
    });

    test('should calculate dual tax authority payment split correctly', () {
      // Test data
      const companyFreight = 10000.0;
      const expectedTaxAmount = companyFreight * 0.15; // 15% = 1500.0
      final selectedAuthorities = [
        'SRB (Sindh Revenue Board)',
        'PRA (Punjab Revenue Authority)'
      ];

      // Simulate dual authority calculation
      final numberOfAuthorities = selectedAuthorities.length;
      expect(numberOfAuthorities, 2);

      // Dual authorities split the 15% tax amount equally (7.5% each)
      final splitAmount = expectedTaxAmount / 2;
      expect(splitAmount, 750.0);

      // Verify both authorities get equal amounts
      final authority1Amount = splitAmount;
      final authority2Amount = splitAmount;
      expect(authority1Amount, authority2Amount);
      expect(authority1Amount + authority2Amount, expectedTaxAmount);

      // Verify description format for split payments
      const voucherNumber = 'V-002';
      final expectedDescription1 = 'Tax payment to ${selectedAuthorities[0]} - Voucher #$voucherNumber (Split payment)';
      final expectedDescription2 = 'Tax payment to ${selectedAuthorities[1]} - Voucher #$voucherNumber (Split payment)';
      
      expect(expectedDescription1, 'Tax payment to SRB (Sindh Revenue Board) - Voucher #V-002 (Split payment)');
      expect(expectedDescription2, 'Tax payment to PRA (Punjab Revenue Authority) - Voucher #V-002 (Split payment)');
    });

    test('should handle different company freight amounts correctly', () {
      final testCases = [
        {'freight': 5000.0, 'expectedTax': 750.0},
        {'freight': 15000.0, 'expectedTax': 2250.0},
        {'freight': 25000.0, 'expectedTax': 3750.0},
        {'freight': 100000.0, 'expectedTax': 15000.0},
      ];

      for (final testCase in testCases) {
        final freight = testCase['freight'] as double;
        final expectedTax = testCase['expectedTax'] as double;
        
        // Calculate 15% tax
        final calculatedTax = freight * 0.15;
        expect(calculatedTax, expectedTax);

        // Test single authority
        final singleAmount = calculatedTax;
        expect(singleAmount, expectedTax);

        // Test dual authority split
        final splitAmount = calculatedTax / 2;
        expect(splitAmount, expectedTax / 2);
        expect(splitAmount * 2, expectedTax);
      }
    });

    test('should validate tax authority selection limits', () {
      // Test empty selection
      final emptySelection = <String>[];
      expect(emptySelection.isEmpty, true);

      // Test single selection
      final singleSelection = ['SRB (Sindh Revenue Board)'];
      expect(singleSelection.length, 1);
      expect(singleSelection.length <= 2, true);

      // Test dual selection
      final dualSelection = [
        'SRB (Sindh Revenue Board)',
        'PRA (Punjab Revenue Authority)'
      ];
      expect(dualSelection.length, 2);
      expect(dualSelection.length <= 2, true);

      // Test invalid selection (more than 2)
      final invalidSelection = [
        'SRB (Sindh Revenue Board)',
        'PRA (Punjab Revenue Authority)',
        'BRA (Balochistan Revenue Authority)'
      ];
      expect(invalidSelection.length, 3);
      expect(invalidSelection.length > 2, true);
    });

    test('should handle all available tax authorities correctly', () {
      final availableAuthorities = [
        'SRB (Sindh Revenue Board)',
        'PRA (Punjab Revenue Authority)',
        'BRA (Balochistan Revenue Authority)',
        'KRA (Khyber Revenue Authority)',
      ];

      expect(availableAuthorities.length, 4);

      // Test each authority individually
      for (final authority in availableAuthorities) {
        expect(authority.isNotEmpty, true);
        expect(authority.contains('Revenue'), true);
        
        // Verify authority name format
        final isValidFormat = authority.contains('(') && authority.contains(')');
        expect(isValidFormat, true);
      }

      // Test all possible dual combinations
      final dualCombinations = [
        [availableAuthorities[0], availableAuthorities[1]],
        [availableAuthorities[0], availableAuthorities[2]],
        [availableAuthorities[0], availableAuthorities[3]],
        [availableAuthorities[1], availableAuthorities[2]],
        [availableAuthorities[1], availableAuthorities[3]],
        [availableAuthorities[2], availableAuthorities[3]],
      ];

      expect(dualCombinations.length, 6);
      
      for (final combination in dualCombinations) {
        expect(combination.length, 2);
        expect(combination[0] != combination[1], true);
      }
    });

    test('should calculate payment distribution for edge cases', () {
      // Test very small amounts
      const smallFreight = 100.0;
      const smallTax = smallFreight * 0.15; // 15.0
      final smallSplit = smallTax / 2; // 7.5
      
      expect(smallTax, 15.0);
      expect(smallSplit, 7.5);

      // Test large amounts
      const largeFreight = 1000000.0;
      const largeTax = largeFreight * 0.15; // 150000.0
      final largeSplit = largeTax / 2; // 75000.0
      
      expect(largeTax, 150000.0);
      expect(largeSplit, 75000.0);

      // Test decimal precision
      const precisionFreight = 12345.67;
      const precisionTax = precisionFreight * 0.15; // 1851.8505
      final precisionSplit = precisionTax / 2; // 925.92525
      
      expect(precisionTax, closeTo(1851.85, 0.01));
      expect(precisionSplit, closeTo(925.93, 0.01));
    });

    test('should generate correct bank statement entries', () {
      const companyFreight = 20000.0;
      const taxAmount = companyFreight * 0.15; // 3000.0
      const voucherNumber = 'V-123';

      // Test single authority entry
      final singleAuthority = 'SRB (Sindh Revenue Board)';
      final singleEntry = {
        'categoryId': 'tax_authority_single',
        'categoryName': 'Tax Authority Payment',
        'payerName': singleAuthority,
        'amount': taxAmount,
        'notes': 'Tax payment to $singleAuthority - Voucher #$voucherNumber',
        'referenceNumber': voucherNumber,
      };

      expect(singleEntry['categoryId'], 'tax_authority_single');
      expect(singleEntry['amount'], 3000.0);
      expect(singleEntry['notes'], 'Tax payment to SRB (Sindh Revenue Board) - Voucher #V-123');

      // Test dual authority entries
      final dualAuthorities = [
        'SRB (Sindh Revenue Board)',
        'PRA (Punjab Revenue Authority)'
      ];
      final splitAmount = taxAmount / 2; // 1500.0

      final dualEntries = dualAuthorities.map((authority) => {
        'categoryId': 'tax_authority_split',
        'categoryName': 'Tax Authority Payment (Split)',
        'payerName': authority,
        'amount': splitAmount,
        'notes': 'Tax payment to $authority - Voucher #$voucherNumber (Split payment)',
        'referenceNumber': voucherNumber,
      }).toList();

      expect(dualEntries.length, 2);
      expect(dualEntries[0]['categoryId'], 'tax_authority_split');
      expect(dualEntries[0]['amount'], 1500.0);
      expect(dualEntries[1]['amount'], 1500.0);
      
      final totalSplitAmount = (dualEntries[0]['amount'] as double) + (dualEntries[1]['amount'] as double);
      expect(totalSplitAmount, taxAmount);
    });

    test('should handle voucher reference number formatting', () {
      final testVoucherNumbers = ['001', '123', 'ABC-001', '2024-001'];
      
      for (final voucherNumber in testVoucherNumbers) {
        final referenceNumber = 'V-$voucherNumber';
        final expectedFormat = RegExp(r'^V-.+$');
        
        expect(referenceNumber.startsWith('V-'), true);
        expect(expectedFormat.hasMatch(referenceNumber), true);
      }
    });
  });
}
