import 'dart:developer';
import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/firebase_service/slab/slab_firebase_service.dart';
import 'package:logestics/models/slab/slab_model.dart';
import 'slab_repository.dart';

class SlabRepositoryImpl implements SlabRepository {
  final SlabFirebaseService _firebaseService;

  SlabRepositoryImpl(this._firebaseService);

  @override
  Future<Either<FailureObj, void>> createSlab({
    required SlabModel slab,
  }) async {
    try {
      await _firebaseService.createSlab(slab: slab);
      return const Right(null);
    } catch (e) {
      log('Error creating slab: $e');
      return Left(FailureObj(code: 'create-slab-error', message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, List<SlabModel>>> getSlabs() async {
    try {
      final slabs = await _firebaseService.getSlabs();
      return Right(slabs);
    } catch (e) {
      log('Error fetching slabs: $e');
      return Left(FailureObj(code: 'get-slabs-error', message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, void>> updateSlab({
    required SlabModel slab,
  }) async {
    try {
      await _firebaseService.updateSlab(slab: slab);
      return const Right(null);
    } catch (e) {
      log('Error updating slab: $e');
      return Left(FailureObj(code: 'update-slab-error', message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, void>> deleteSlab({
    required String slabId,
  }) async {
    try {
      await _firebaseService.deleteSlab(slabId: slabId);
      return const Right(null);
    } catch (e) {
      log('Error deleting slab: $e');
      return Left(FailureObj(code: 'delete-slab-error', message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, List<SlabModel>>> getActiveSlabsForDistrict({
    required String districtId,
    required DateTime date,
  }) async {
    try {
      final slabs = await _firebaseService.getActiveSlabsForDistrict(
        districtId: districtId,
        date: date,
      );
      return Right(slabs);
    } catch (e) {
      log('Error fetching active slabs for district: $e');
      return Left(
          FailureObj(code: 'get-active-slabs-error', message: e.toString()));
    }
  }
}
