import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../../core/services/financial_mock_data_service.dart';
import '../../../../../core/utils/snackbar_utils.dart';
import '../../../../../firebase_service/accounting/chart_of_accounts_firebase_service.dart';
import '../../../../../firebase_service/accounting/journal_entry_firebase_service.dart';

class FinancialMockDataController extends GetxController {
  final FinancialMockDataService _mockDataService = Get.find();
  final ChartOfAccountsFirebaseService _accountsService = Get.find();
  final JournalEntryFirebaseService _journalService = Get.find();

  // Reactive variables
  RxBool get isGenerating => _mockDataService.isGenerating;
  RxString get currentStep => _mockDataService.currentStep;
  RxDouble get progress => _mockDataService.progress;

  /// Generate comprehensive mock data
  Future<void> generateMockData() async {
    try {
      await _mockDataService.generateComprehensiveMockData();
    } catch (e) {
      SnackbarUtils.showError('Error', 'Failed to generate mock data: $e');
    }
  }

  /// Clear all financial data (for testing purposes)
  Future<void> clearAllData() async {
    try {
      // Show confirmation dialog
      final bool? confirmed = await Get.dialog<bool>(
        AlertDialog(
          title: const Text('Clear All Financial Data'),
          content: const Text(
            'This will permanently delete all Chart of Accounts, Journal Entries, and General Ledger data. This action cannot be undone.\n\nAre you sure you want to continue?',
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () => Get.back(result: true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Delete All'),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        // Clear all financial data
        isGenerating.value = true;
        currentStep.value = 'Clearing financial data...';

        try {
          // Get all accounts first
          final accounts = await _accountsService.getAllAccounts();

          // Delete all journal entries first (to avoid foreign key constraints)
          currentStep.value = 'Clearing journal entries...';
          final journalEntries = await _journalService.getJournalEntries();
          for (final entry in journalEntries) {
            await _journalService.deleteJournalEntry(entry.id);
          }

          // Delete all accounts
          currentStep.value = 'Clearing chart of accounts...';
          for (final account in accounts) {
            try {
              await _accountsService.deleteAccount(account.id);
            } catch (e) {
              // Skip accounts that can't be deleted (may have dependencies)
              continue;
            }
          }

          SnackbarUtils.showSuccess(
              'Success', 'All financial data cleared successfully');
        } catch (e) {
          SnackbarUtils.showError('Error', 'Failed to clear some data: $e');
        } finally {
          isGenerating.value = false;
          currentStep.value = '';
        }
      }
    } catch (e) {
      SnackbarUtils.showError('Error', 'Failed to clear data: $e');
    }
  }

  /// Check if mock data already exists
  Future<bool> checkMockDataExists() async {
    try {
      // Check if we have any accounts
      final accounts = await _accountsService.getAllAccounts();
      if (accounts.isNotEmpty) {
        return true;
      }

      // Check if we have any journal entries
      final journalEntries = await _journalService.getJournalEntries();
      if (journalEntries.isNotEmpty) {
        return true;
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  /// Get data statistics
  Future<Map<String, int>> getDataStatistics() async {
    try {
      // Get accounts count
      final accounts = await _accountsService.getAllAccounts();

      // Get journal entries count
      final journalEntries = await _journalService.getJournalEntries();

      // Calculate total transaction lines
      int totalTransactionLines = 0;
      for (final entry in journalEntries) {
        totalTransactionLines += entry.lines.length;
      }

      return {
        'accounts': accounts.length,
        'journalEntries': journalEntries.length,
        'transactions': totalTransactionLines,
      };
    } catch (e) {
      return {
        'accounts': 0,
        'journalEntries': 0,
        'transactions': 0,
      };
    }
  }
}
