import 'package:flutter/material.dart';

class AppTextStyles {
  static const String outfitFontFamily = "Outfit";

  static const TextStyle titleStyle = TextStyle(
    fontFamily: outfitFontFamily,
    fontSize: 18,
    fontWeight: FontWeight.bold,
  );

  static const TextStyle titleLargeStyle = TextStyle(
    fontFamily: outfitFontFamily,
    fontSize: 20,
    fontWeight: FontWeight.bold,
  );

  static const TextStyle buttonTextStyle = TextStyle(
    color: Colors.white,
    fontFamily: outfitFontFamily,
  );

  // Region List View Styles
  static const TextStyle regionTitleStyle = TextStyle(
    fontFamily: outfitFontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w600,
  );

  static const TextStyle regionSubtitleStyle = TextStyle(
    fontFamily: outfitFontFamily,
    fontSize: 14,
    color: Colors.grey,
  );

  static const TextStyle destinationTextStyle = TextStyle(
    fontFamily: outfitFontFamily,
    fontSize: 14,
  );

  static const TextStyle emptyStateStyle = TextStyle(
    fontFamily: outfitFontFamily,
    fontSize: 16,
    color: Colors.grey,
    fontWeight: FontWeight.w500,
  );

  // Invoice List View Styles
  static const TextStyle addNewInvoiceStyle = TextStyle(
    color: Color(0xFF0f7bf4),
    fontFamily: outfitFontFamily,
  );

  static const TextStyle invoiceHeaderStyle = TextStyle(
    fontFamily: outfitFontFamily,
    fontSize: 15,
    fontWeight: FontWeight.bold,
  );

  static const TextStyle invoiceDataStyle = TextStyle(
    letterSpacing: 1,
    fontFamily: outfitFontFamily,
    fontSize: 15,
    color: Colors.grey,
    fontWeight: FontWeight.w300,
  );

  static const TextStyle searchHintStyle = TextStyle(
    color: Colors.grey,
    fontFamily: outfitFontFamily,
  );

  static const TextStyle invoiceTitleStyle = TextStyle(
    fontFamily: outfitFontFamily,
    fontSize: 20,
    fontWeight: FontWeight.bold,
  );

  // Voucher List View Styles
  static const TextStyle voucherTitleStyle = TextStyle(
    fontFamily: outfitFontFamily,
    fontSize: 20,
    fontWeight: FontWeight.bold,
  );

  static const TextStyle addNewVoucherStyle = TextStyle(
    color: Color(0xFF0f7bf4),
    fontFamily: outfitFontFamily,
  );

  static const TextStyle voucherHeaderStyle = TextStyle(
    fontFamily: outfitFontFamily,
    fontSize: 15,
    fontWeight: FontWeight.bold,
  );

  static const TextStyle voucherDataStyle = TextStyle(
    letterSpacing: 1,
    fontFamily: outfitFontFamily,
    fontSize: 15,
    color: Colors.grey,
    fontWeight: FontWeight.w300,
  );

  static const TextStyle voucherStatusStyle = TextStyle(
    fontFamily: outfitFontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w500,
  );

  // Navigation Styles
  static const TextStyle navigationTextStyle = TextStyle(
    fontFamily: outfitFontFamily,
    fontSize: 15,
    color: Colors.grey,
  );

  static const TextStyle activeNavigationTextStyle = TextStyle(
    fontFamily: outfitFontFamily,
    fontSize: 15,
  );

  static const TextStyle inputTextStyle = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    letterSpacing: 0.5,
  );

  static var subtitleStyle = TextStyle(
    fontFamily: outfitFontFamily,
    fontSize: 14,
    color: Colors.grey,
  );

  static const TextStyle searchFieldStyle = TextStyle(
    fontFamily: outfitFontFamily,
    fontSize: 14,
    color: Colors.grey,
  );

  // Additional text styles for account journal transactions
  static const TextStyle headingMedium = TextStyle(
    fontFamily: outfitFontFamily,
    fontSize: 18,
    fontWeight: FontWeight.w600,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontFamily: outfitFontFamily,
    fontSize: 14,
    fontWeight: FontWeight.normal,
  );

  static const TextStyle bodySmall = TextStyle(
    fontFamily: outfitFontFamily,
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: Colors.grey,
  );
}
