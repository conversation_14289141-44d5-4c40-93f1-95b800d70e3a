import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/widgets/pagination_widget.dart';
import 'package:logestics/features/finance/accounts/presentation/controllers/account_transaction_controller.dart';
import 'package:logestics/features/home/<USER>/theme.dart';
import 'package:logestics/models/finance/account_model.dart';
import 'package:logestics/models/finance/account_transaction_model.dart';
import 'package:provider/provider.dart';
import 'pdf_generation_dialog.dart';

class AccountTransactionsView extends StatefulWidget {
  final AccountModel account;
  final AccountTransactionController controller;

  const AccountTransactionsView({
    super.key,
    required this.account,
    required this.controller,
  });

  @override
  State<AccountTransactionsView> createState() =>
      _AccountTransactionsViewState();
}

class _AccountTransactionsViewState extends State<AccountTransactionsView> {
  final searchController = TextEditingController();
  String searchQuery = '';
  String? selectedTransactionType;
  DateTimeRange? selectedDateRange;
  // final FirebaseAdminService _adminService = FirebaseAdminService();
  // final bool _isDeleting = false;

  final List<String> transactionTypes = [
    'All',
    'Deposit',
    'Expense',
    'Broker Fees',
    'Munshiana',
    'Voucher Payment',
    'Loan',
  ];

  @override
  void initState() {
    super.initState();
    // Use paginated loading instead of loading all transactions
    widget.controller.loadTransactionsForAccountPaginated(widget.account.id);
    searchController.addListener(_onSearchChanged);
    selectedTransactionType = 'All';
  }

  void _onSearchChanged() {
    setState(() {
      searchQuery = searchController.text.toLowerCase();
    });
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }

  // Show confirmation dialog before deleting all collections
  // Future<void> _showDeleteConfirmationDialog() async {
  //   return showDialog<void>(
  //     context: context,
  //     barrierDismissible: false,
  //     builder: (BuildContext context) {
  //       return AlertDialog(
  //         title: const Text('⚠️ DELETE ALL DATA'),
  //         content: SingleChildScrollView(
  //           child: ListBody(
  //             children: <Widget>[
  //               const Text(
  //                 'WARNING: This will delete ALL your data from Firebase!',
  //                 style: TextStyle(
  //                   fontWeight: FontWeight.bold,
  //                   color: Colors.red,
  //                 ),
  //               ),
  //               const SizedBox(height: 16),
  //               const Text(
  //                 'This action is irreversible and should only be used in development.',
  //               ),
  //               const SizedBox(height: 8),
  //               const Text(
  //                 'Are you absolutely sure you want to continue?',
  //               ),
  //             ],
  //           ),
  //         ),
  //         actions: <Widget>[
  //           TextButton(
  //             child: const Text('Cancel'),
  //             onPressed: () {
  //               Navigator.of(context).pop();
  //             },
  //           ),
  //           TextButton(
  //             child: const Text(
  //               'DELETE ALL DATA',
  //               style: TextStyle(color: Colors.red),
  //             ),
  //             onPressed: () {
  //               Navigator.of(context).pop();
  //               _deleteAllCollections();
  //             },
  //           ),
  //         ],
  //       );
  //     },
  //   );
  // }

  // Delete all collections
  // Future<void> _deleteAllCollections() async {
  //   setState(() {
  //     _isDeleting = true;
  //   });

  //   try {
  //     await _adminService.deleteAllUserData();
  //     SnackbarUtils.showSuccess(
  //       'Success',
  //       'All user data has been deleted from Firebase',
  //     );
  //     // Refresh the data
  //     widget.controller.loadTransactionsForAccount(widget.account.id);
  //   } catch (e) {
  //     SnackbarUtils.showError(
  //       'Error',
  //       'Failed to delete data: $e',
  //     );
  //   } finally {
  //     setState(() {
  //       _isDeleting = false;
  //     });
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    final notifier = Provider.of<ColorNotifier>(context, listen: true);

    return Scaffold(
      backgroundColor: notifier.getBgColor,
      appBar: AppBar(
        backgroundColor: notifier.getBgColor,
        elevation: 0,
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Account Statement',
              style: AppTextStyles.titleStyle.copyWith(
                color: notifier.text,
                fontSize: 20,
              ),
            ),
            Text(
              widget.account.name,
              style: TextStyle(
                color: notifier.text.withValues(alpha: 0.7),
                fontSize: 14,
              ),
            ),
          ],
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: notifier.text),
          onPressed: () => Get.back(),
        ),
        // actions: [
        //   if (_isDeleting)
        //     const Padding(
        //       padding: EdgeInsets.all(8.0),
        //       child: Center(
        //         child: SizedBox(
        //           height: 20,
        //           width: 20,
        //           child: CircularProgressIndicator(
        //             strokeWidth: 2,
        //           ),
        //         ),
        //       ),
        //     ),
        //   PopupMenuButton<String>(
        //     icon: Icon(Icons.more_vert, color: notifier.text),
        //     onSelected: (value) {
        //       if (value == 'delete_all') {
        //         _showDeleteConfirmationDialog();
        //       }
        //     },
        //     itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
        //       const PopupMenuItem<String>(
        //         value: 'delete_all',
        //         child: Row(
        //           children: [
        //             Icon(
        //               Icons.delete_forever,
        //               color: Colors.red,
        //             ),
        //             SizedBox(width: 8),
        //             Text(
        //               'Delete All Collections (DEV)',
        //               style: TextStyle(color: Colors.red),
        //             ),
        //           ],
        //         ),
        //       ),
        //     ],
        //   ),
        // ],
      ),
      body: Column(
        children: [
          // Account Summary Card
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: notifier.getcardColor,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Account Number',
                        style: TextStyle(
                          color: notifier.text.withValues(alpha: 0.7),
                          fontSize: 12,
                        ),
                      ),
                      Text(
                        widget.account.accountNumber,
                        style: TextStyle(
                          color: notifier.text,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Branch Code',
                        style: TextStyle(
                          color: notifier.text.withValues(alpha: 0.7),
                          fontSize: 12,
                        ),
                      ),
                      Text(
                        widget.account.branchCode,
                        style: TextStyle(
                          color: notifier.text,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        'Available Balance',
                        style: TextStyle(
                          color: notifier.text.withValues(alpha: 0.7),
                          fontSize: 12,
                        ),
                      ),
                      Text(
                        'PKR ${widget.account.availableBalance.toStringAsFixed(2)}',
                        style: TextStyle(
                          color: widget.account.availableBalance >= 0
                              ? Colors.green[700]
                              : Colors.red[700],
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Filters Section
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: notifier.getcardColor,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    // Search Field
                    Expanded(
                      flex: 2,
                      child: TextField(
                        controller: searchController,
                        decoration: InputDecoration(
                          hintText: 'Search transactions...',
                          prefixIcon: Icon(Icons.search, color: notifier.text),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          filled: true,
                          fillColor: notifier.textFileColor,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),

                    // Transaction Type Filter
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: 'Transaction Type',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          filled: true,
                          fillColor: notifier.textFileColor,
                        ),
                        value: selectedTransactionType,
                        items: transactionTypes.map((type) {
                          return DropdownMenuItem<String>(
                            value: type,
                            child: Text(type),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            selectedTransactionType = value;
                          });
                        },
                      ),
                    ),
                    const SizedBox(width: 16),

                    // Date Range Filter
                    OutlinedButton.icon(
                      icon: const Icon(Icons.date_range),
                      label: Text(selectedDateRange != null
                          ? '${DateFormat('dd/MM').format(selectedDateRange!.start)} - ${DateFormat('dd/MM').format(selectedDateRange!.end)}'
                          : 'Date Range'),
                      onPressed: () => _selectDateRange(notifier),
                    ),
                    const SizedBox(width: 16),

                    // Generate PDF Button
                    ElevatedButton.icon(
                      icon: const Icon(Icons.picture_as_pdf),
                      label: const Text('Generate PDF'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 12),
                      ),
                      onPressed: () => _showPDFGenerationDialog(),
                    ),
                  ],
                ),
                if (selectedDateRange != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton.icon(
                          icon: const Icon(Icons.clear),
                          label: const Text('Clear Date Filter'),
                          onPressed: () {
                            setState(() {
                              selectedDateRange = null;
                            });
                          },
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),

          // Transactions Table
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: notifier.getcardColor,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Obx(() {
                if (widget.controller.isLoading.value) {
                  return const Center(child: CircularProgressIndicator());
                }

                // Show loading indicator for page transitions
                if (widget.controller.isLoadingPage.value) {
                  return const Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('Loading next page...'),
                      ],
                    ),
                  );
                }

                final filteredTransactions =
                    _getFilteredPaginatedTransactions();

                if (filteredTransactions.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.receipt_long_outlined,
                          size: 64,
                          color: notifier.text.withValues(alpha: 0.5),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'No transactions found',
                          style: TextStyle(
                            fontSize: 18,
                            color: notifier.text.withValues(alpha: 0.7),
                          ),
                        ),
                        if (searchQuery.isNotEmpty ||
                            selectedTransactionType != 'All' ||
                            selectedDateRange != null)
                          TextButton(
                            onPressed: () {
                              setState(() {
                                searchController.clear();
                                searchQuery = '';
                                selectedTransactionType = 'All';
                                selectedDateRange = null;
                              });
                            },
                            child: const Text('Clear all filters'),
                          ),
                      ],
                    ),
                  );
                }

                return SingleChildScrollView(
                  child: SizedBox(
                    width: double.infinity,
                    child: DataTable(
                      headingRowHeight: 56,
                      columnSpacing: 16,
                      headingTextStyle: TextStyle(
                        color: notifier.text,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                      dataTextStyle: TextStyle(
                        color: notifier.text,
                        fontSize: 13,
                      ),
                      headingRowColor: WidgetStateProperty.resolveWith<Color?>(
                        (Set<WidgetState> states) {
                          return notifier.getBgColor.withValues(alpha: 0.1);
                        },
                      ),
                      columns: [
                        DataColumn(
                          label: Text('Date'),
                        ),
                        DataColumn(
                          label: Text('Description'),
                        ),
                        DataColumn(
                          label: Text('Payee/Payer'),
                        ),
                        DataColumn(
                          label: Text('Amount'),
                          numeric: true,
                        ),
                        DataColumn(
                          label: Text('Type'),
                        ),
                        DataColumn(
                          label: Text('Payment Method'),
                        ),
                      ],
                      rows: filteredTransactions.map((transaction) {
                        final isCredit = transaction.amount > 0;
                        final amountColor =
                            isCredit ? Colors.green[700] : Colors.red[700];
                        final amountPrefix = isCredit ? '+' : '';

                        return DataRow(
                          cells: [
                            DataCell(
                              Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    DateFormat('dd MMM yyyy')
                                        .format(transaction.transactionDate),
                                    style: TextStyle(
                                      color: notifier.text,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  Text(
                                    DateFormat('hh:mm a')
                                        .format(transaction.transactionDate),
                                    style: TextStyle(
                                      color:
                                          notifier.text.withValues(alpha: 0.6),
                                      fontSize: 11,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            DataCell(
                              Text(
                                transaction.description.isNotEmpty
                                    ? transaction.description
                                    : 'Transaction',
                                style: TextStyle(
                                  color: notifier.text,
                                  fontWeight: FontWeight.w500,
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 2,
                              ),
                            ),
                            DataCell(
                              Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Show payer name for credit transactions, payee name for debit transactions
                                  if (isCredit &&
                                      (transaction.payerName?.isNotEmpty ??
                                          false))
                                    Text(
                                      transaction.payerName!,
                                      style: TextStyle(
                                        color: notifier.text,
                                        fontSize: 12,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    )
                                  else if (!isCredit &&
                                      (transaction.payeeName?.isNotEmpty ??
                                          false))
                                    Text(
                                      transaction.payeeName!,
                                      style: TextStyle(
                                        color: notifier.text,
                                        fontSize: 12,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  if (transaction.voucherNumber?.isNotEmpty ??
                                      false)
                                    Text(
                                      'V: ${transaction.voucherNumber}',
                                      style: TextStyle(
                                        color: notifier.text
                                            .withValues(alpha: 0.7),
                                        fontSize: 11,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  // Show dash only if no company name and no voucher number
                                  if ((isCredit &&
                                              (transaction.payerName?.isEmpty ??
                                                  true) ||
                                          !isCredit &&
                                              (transaction.payeeName?.isEmpty ??
                                                  true)) &&
                                      (transaction.voucherNumber?.isEmpty ??
                                          true))
                                    Text(
                                      '-',
                                      style: TextStyle(
                                        color: notifier.text
                                            .withValues(alpha: 0.5),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                            DataCell(
                              Text(
                                '$amountPrefix PKR ${transaction.amount.abs().toStringAsFixed(2)}',
                                style: TextStyle(
                                  color: amountColor,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                            DataCell(
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: isCredit
                                      ? Colors.green.withValues(alpha: 0.1)
                                      : Colors.red.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: isCredit
                                        ? Colors.green.withValues(alpha: 0.3)
                                        : Colors.red.withValues(alpha: 0.3),
                                    width: 1,
                                  ),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      isCredit
                                          ? Icons.arrow_downward
                                          : Icons.arrow_upward,
                                      color: amountColor,
                                      size: 12,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      isCredit ? 'Credit' : 'Debit',
                                      style: TextStyle(
                                        color: amountColor,
                                        fontSize: 11,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            DataCell(
                              Text(
                                _getPaymentMethodDisplay(transaction),
                                style: TextStyle(
                                  color: notifier.text,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        );
                      }).toList(),
                    ),
                  ),
                );
              }),
            ),
          ),

          // Pagination Widget
          Container(
            margin: const EdgeInsets.all(16),
            child: Obx(() => PaginationWidget(
                  currentPage: widget.controller.currentPage.value,
                  totalPages: widget.controller.totalPages,
                  itemsPerPage: widget.controller.itemsPerPage.value,
                  onPageChanged: (page) =>
                      widget.controller.setCurrentPage(page),
                  onItemsPerPageChanged: (count) =>
                      widget.controller.setItemsPerPage(count),
                  availableItemsPerPage: const [10, 25, 50, 100],
                )),
          ),
        ],
      ),
    );
  }

  String _getPaymentMethodDisplay(AccountTransactionModel transaction) {
    // Check if transaction has payment method metadata
    if (transaction.metadata != null &&
        transaction.metadata!.containsKey('paymentType')) {
      return transaction.metadata!['paymentType'] as String;
    }

    // Fallback to transaction type for older transactions
    switch (transaction.type) {
      case TransactionType.voucherPayment:
        return 'Voucher Payment';
      case TransactionType.deposit:
        return 'Deposit';
      case TransactionType.expense:
        return 'Expense';
      case TransactionType.loan:
        return 'Loan';
      default:
        return 'Transaction';
    }
  }

  List<AccountTransactionModel> _getFilteredPaginatedTransactions() {
    var transactions = widget.controller.currentPageTransactions.toList();

    // Filter by search query
    if (searchQuery.isNotEmpty) {
      transactions = transactions.where((transaction) {
        return transaction.description.toLowerCase().contains(searchQuery) ||
            (transaction.payeeName?.toLowerCase().contains(searchQuery) ??
                false) ||
            (transaction.voucherNumber?.toLowerCase().contains(searchQuery) ??
                false) ||
            transaction.amount.toString().contains(searchQuery);
      }).toList();
    }

    // Filter by transaction type
    if (selectedTransactionType != null && selectedTransactionType != 'All') {
      transactions = transactions.where((transaction) {
        switch (selectedTransactionType) {
          case 'Deposit':
            return transaction.amount > 0;
          case 'Expense':
            return transaction.amount < 0;
          case 'Broker Fees':
            return transaction.description.toLowerCase().contains('broker');
          case 'Munshiana':
            return transaction.description.toLowerCase().contains('munshiana');
          case 'Voucher Payment':
            return transaction.voucherNumber?.isNotEmpty ?? false;
          case 'Loan':
            return transaction.description.toLowerCase().contains('loan');
          default:
            return true;
        }
      }).toList();
    }

    // Filter by date range
    if (selectedDateRange != null) {
      transactions = transactions.where((transaction) {
        final transactionDate = transaction.transactionDate;
        return transactionDate.isAfter(selectedDateRange!.start) &&
            transactionDate
                .isBefore(selectedDateRange!.end.add(const Duration(days: 1)));
      }).toList();
    }

    // Sort by date (oldest first) - voucher date ordering
    transactions.sort((a, b) => a.transactionDate.compareTo(b.transactionDate));

    return transactions;
  }

  Future<void> _selectDateRange(notifier) async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: selectedDateRange,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: const Color(0xFF2B79F3),
              surface: notifier.getBgColor,
              onSurface: notifier.text,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != selectedDateRange) {
      setState(() {
        selectedDateRange = picked;
      });
    }
  }

  void _showPDFGenerationDialog() {
    showDialog(
      context: context,
      builder: (context) => PDFGenerationDialog(
        preSelectedAccount: widget.account,
        initialDateRange: selectedDateRange,
      ),
    );
  }
}
