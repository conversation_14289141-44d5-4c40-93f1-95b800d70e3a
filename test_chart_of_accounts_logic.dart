// Chart of Accounts Logic Testing Script
// Tests the business logic without UI dependencies

import 'dart:developer';
import 'lib/models/finance/chart_of_accounts_model.dart';

void main() async {
  print('🧪 CHART OF ACCOUNTS LOGIC TESTING');
  print('===================================\n');
  
  // Run all tests
  await runAllTests();
  
  print('\n✅ TESTING COMPLETED');
  print('Check the results above for any failures or issues.');
}

Future<void> runAllTests() async {
  print('🚀 Starting logic tests...\n');
  
  // Test 1: Model Validation
  await testAccountModelValidation();
  
  // Test 2: Account Categories and Types
  await testAccountCategoriesAndTypes();
  
  // Test 3: Account Number Ranges
  await testAccountNumberRanges();
  
  // Test 4: JSON Serialization
  await testJSONSerialization();
  
  // Test 5: Account Hierarchy
  await testAccountHierarchy();
  
  // Test 6: Validation Logic
  await testValidationLogic();
  
  // Test 7: Display Methods
  await testDisplayMethods();
}

Future<void> testAccountModelValidation() async {
  print('📋 Test 1: Account Model Validation');
  print('-----------------------------------');
  
  try {
    // Test valid account creation
    final validAccount = ChartOfAccountsModel(
      id: 'test-id',
      accountNumber: '1001',
      accountName: 'Test Cash Account',
      description: 'Test description',
      category: AccountCategory.assets,
      accountType: AccountType.cash,
      uid: 'test-uid',
      createdAt: DateTime.now(),
    );
    
    print('✅ Valid account model created successfully');
    print('   Account: ${validAccount.displayName}');
    print('   Category: ${validAccount.category.displayName}');
    print('   Type: ${validAccount.accountType.displayName}');
    print('   Balance: ${validAccount.balance}');
    print('   Active: ${validAccount.isActive}');
    
  } catch (e) {
    print('❌ Account model validation failed: $e');
  }
  
  print('');
}

Future<void> testAccountCategoriesAndTypes() async {
  print('📊 Test 2: Account Categories and Types');
  print('---------------------------------------');
  
  try {
    // Test all categories exist
    final categories = AccountCategory.values;
    print('✅ Found ${categories.length} account categories:');
    for (final category in categories) {
      print('   - ${category.displayName} (${category.name})');
    }
    
    // Test account types for each category
    print('\n✅ Account types by category:');
    for (final category in categories) {
      final types = AccountType.getTypesForCategory(category);
      print('   ${category.displayName}: ${types.length} types');
      for (final type in types) {
        print('     - ${type.displayName}');
      }
    }
    
  } catch (e) {
    print('❌ Account categories and types test failed: $e');
  }
  
  print('');
}

Future<void> testAccountNumberRanges() async {
  print('🔢 Test 3: Account Number Ranges');
  print('----------------------------------');
  
  try {
    // Test account number ranges
    print('✅ Account number ranges:');
    for (final category in AccountCategory.values) {
      final range = category.accountNumberRange;
      print('   ${category.displayName}: ${range.start}-${range.end}');
      
      // Validate range logic
      if (range.start < range.end && range.start > 0) {
        print('     ✅ Valid range');
      } else {
        print('     ❌ Invalid range');
      }
    }
    
    // Test specific ranges
    final assetsRange = AccountCategory.assets.accountNumberRange;
    final liabilitiesRange = AccountCategory.liabilities.accountNumberRange;
    final equityRange = AccountCategory.equity.accountNumberRange;
    final revenueRange = AccountCategory.revenue.accountNumberRange;
    final expensesRange = AccountCategory.expenses.accountNumberRange;
    
    print('\n✅ Specific range validation:');
    print('   Assets: ${assetsRange.start}-${assetsRange.end} (Expected: 1000-1999)');
    print('   Liabilities: ${liabilitiesRange.start}-${liabilitiesRange.end} (Expected: 2000-2999)');
    print('   Equity: ${equityRange.start}-${equityRange.end} (Expected: 3000-3999)');
    print('   Revenue: ${revenueRange.start}-${revenueRange.end} (Expected: 4000-4999)');
    print('   Expenses: ${expensesRange.start}-${expensesRange.end} (Expected: 5000-5999)');
    
  } catch (e) {
    print('❌ Account number ranges test failed: $e');
  }
  
  print('');
}

Future<void> testJSONSerialization() async {
  print('🔄 Test 4: JSON Serialization');
  print('------------------------------');
  
  try {
    // Create test account
    final originalAccount = ChartOfAccountsModel(
      id: 'test-json-id',
      accountNumber: '1002',
      accountName: 'Test Bank Account',
      description: 'Test JSON serialization',
      category: AccountCategory.assets,
      accountType: AccountType.bank,
      parentAccountId: 'parent-id',
      childAccountIds: ['child1', 'child2'],
      isActive: true,
      balance: 1500.50,
      uid: 'test-uid',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
    
    // Test JSON serialization
    final json = originalAccount.toJson();
    print('✅ JSON serialization successful');
    print('   Keys: ${json.keys.join(', ')}');
    
    // Test JSON deserialization
    final deserializedAccount = ChartOfAccountsModel.fromJson(json);
    print('✅ JSON deserialization successful');
    
    // Validate data integrity
    bool dataIntegrityValid = true;
    final checks = [
      deserializedAccount.id == originalAccount.id,
      deserializedAccount.accountNumber == originalAccount.accountNumber,
      deserializedAccount.accountName == originalAccount.accountName,
      deserializedAccount.description == originalAccount.description,
      deserializedAccount.category == originalAccount.category,
      deserializedAccount.accountType == originalAccount.accountType,
      deserializedAccount.parentAccountId == originalAccount.parentAccountId,
      deserializedAccount.childAccountIds.length == originalAccount.childAccountIds.length,
      deserializedAccount.isActive == originalAccount.isActive,
      deserializedAccount.balance == originalAccount.balance,
      deserializedAccount.uid == originalAccount.uid,
    ];
    
    dataIntegrityValid = checks.every((check) => check);
    
    if (dataIntegrityValid) {
      print('✅ Data integrity maintained through serialization');
    } else {
      print('❌ Data integrity lost during serialization');
    }
    
  } catch (e) {
    print('❌ JSON serialization test failed: $e');
  }
  
  print('');
}

Future<void> testAccountHierarchy() async {
  print('🌳 Test 5: Account Hierarchy');
  print('-----------------------------');
  
  try {
    // Test parent account
    final parentAccount = ChartOfAccountsModel(
      id: 'parent-id',
      accountNumber: '1000',
      accountName: 'Current Assets',
      category: AccountCategory.assets,
      accountType: AccountType.currentAssets,
      childAccountIds: ['child1', 'child2'],
      uid: 'test-uid',
      createdAt: DateTime.now(),
    );
    
    // Test child account
    final childAccount = ChartOfAccountsModel(
      id: 'child1',
      accountNumber: '1001',
      accountName: 'Cash',
      category: AccountCategory.assets,
      accountType: AccountType.cash,
      parentAccountId: 'parent-id',
      uid: 'test-uid',
      createdAt: DateTime.now(),
    );
    
    print('✅ Hierarchy structure:');
    print('   Parent: ${parentAccount.displayName}');
    print('     Has children: ${parentAccount.hasChildren}');
    print('     Child count: ${parentAccount.childAccountIds.length}');
    print('     Level: ${parentAccount.level}');
    
    print('   Child: ${childAccount.displayName}');
    print('     Has parent: ${childAccount.hasParent}');
    print('     Parent ID: ${childAccount.parentAccountId}');
    print('     Level: ${childAccount.level}');
    
    // Test hierarchy methods
    if (parentAccount.hasChildren && childAccount.hasParent) {
      print('✅ Hierarchy methods working correctly');
    } else {
      print('❌ Hierarchy methods failed');
    }
    
  } catch (e) {
    print('❌ Account hierarchy test failed: $e');
  }
  
  print('');
}

Future<void> testValidationLogic() async {
  print('✅ Test 6: Validation Logic');
  print('----------------------------');
  
  try {
    // Test account number validation
    final validNumbers = ['1001', '2500', '3999', '4000', '5999'];
    final invalidNumbers = ['', '0', '999', '6000', 'abc', '10000'];
    
    print('✅ Account number validation:');
    for (final number in validNumbers) {
      final isValid = number.isNotEmpty && 
                     int.tryParse(number) != null && 
                     int.parse(number) >= 1000 && 
                     int.parse(number) <= 5999;
      print('   $number: ${isValid ? "✅ Valid" : "❌ Invalid"}');
    }
    
    for (final number in invalidNumbers) {
      final isValid = number.isNotEmpty && 
                     int.tryParse(number) != null && 
                     int.parse(number) >= 1000 && 
                     int.parse(number) <= 5999;
      print('   $number: ${isValid ? "❌ Should be invalid" : "✅ Correctly invalid"}');
    }
    
    // Test account name validation
    final validNames = ['Cash', 'Accounts Receivable', 'Office Equipment'];
    final invalidNames = ['', '   ', 'A', 'X' * 256]; // Empty, whitespace, too short, too long
    
    print('\n✅ Account name validation:');
    for (final name in validNames) {
      final isValid = name.trim().isNotEmpty && name.trim().length >= 2 && name.trim().length <= 100;
      print('   "$name": ${isValid ? "✅ Valid" : "❌ Invalid"}');
    }
    
    for (final name in invalidNames) {
      final isValid = name.trim().isNotEmpty && name.trim().length >= 2 && name.trim().length <= 100;
      print('   "$name": ${isValid ? "❌ Should be invalid" : "✅ Correctly invalid"}');
    }
    
  } catch (e) {
    print('❌ Validation logic test failed: $e');
  }
  
  print('');
}

Future<void> testDisplayMethods() async {
  print('🎨 Test 7: Display Methods');
  print('---------------------------');
  
  try {
    final account = ChartOfAccountsModel(
      id: 'display-test',
      accountNumber: '1001',
      accountName: 'Cash Account',
      description: 'Primary cash account',
      category: AccountCategory.assets,
      accountType: AccountType.cash,
      balance: 2500.75,
      uid: 'test-uid',
      createdAt: DateTime.now(),
    );
    
    print('✅ Display methods:');
    print('   Display Name: ${account.displayName}');
    print('   Full Account Number: ${account.fullAccountNumber}');
    print('   Category Display: ${account.category.displayName}');
    print('   Type Display: ${account.accountType.displayName}');
    print('   Balance: \$${account.balance.toStringAsFixed(2)}');
    print('   Status: ${account.isActive ? "Active" : "Inactive"}');
    
    // Test toString method
    final stringRepresentation = account.toString();
    if (stringRepresentation.contains(account.accountNumber) && 
        stringRepresentation.contains(account.accountName)) {
      print('✅ toString() method working correctly');
    } else {
      print('❌ toString() method failed');
    }
    
  } catch (e) {
    print('❌ Display methods test failed: $e');
  }
  
  print('');
}
