import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/features/finance/brokers/repositories/broker_repository.dart';
import 'package:logestics/models/finance/broker_model.dart';
import 'package:uuid/uuid.dart';

class BrokerController extends GetxController {
  final BrokerRepository repository;

  BrokerController({required this.repository});

  // Observable list of brokers
  final RxList<BrokerModel> brokers = <BrokerModel>[].obs;
  final RxBool isLoading = false.obs;

  // Form controllers
  final nameController = TextEditingController();
  final descriptionController = TextEditingController();
  final phoneNumberController = TextEditingController();
  final emailController = TextEditingController();
  final addressController = TextEditingController();

  // Form key for validation
  final formKey = GlobalKey<FormState>();

  // Current broker being edited
  BrokerModel? currentBroker;

  @override
  void onInit() {
    super.onInit();
    loadBrokers();
    _listenToBrokers();
  }

  @override
  void onClose() {
    nameController.dispose();
    descriptionController.dispose();
    phoneNumberController.dispose();
    emailController.dispose();
    addressController.dispose();
    super.onClose();
  }

  void _listenToBrokers() {
    repository.listenToBrokers().listen(
      (brokerList) {
        brokers.value = brokerList;
      },
      onError: (error) {
        log('Error listening to brokers: $error');
        SnackbarUtils.showError(
          'Error',
          'Failed to sync brokers: $error',
        );
      },
    );
  }

  Future<void> loadBrokers() async {
    isLoading.value = true;
    try {
      final result = await repository.getBrokers();
      result.fold(
        (failure) {
          log('Failed to load brokers: ${failure.message}');
          SnackbarUtils.showError(
            'Error',
            'Failed to load brokers: ${failure.message}',
          );
        },
        (brokerList) {
          brokers.value = brokerList;
          log('Successfully loaded ${brokerList.length} brokers');
        },
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> createBroker() async {
    if (!formKey.currentState!.validate()) return;

    isLoading.value = true;
    try {
      final broker = BrokerModel(
        id: const Uuid().v4(),
        name: nameController.text.trim(),
        description: descriptionController.text.trim().isEmpty
            ? null
            : descriptionController.text.trim(),
        phoneNumber: phoneNumberController.text.trim().isEmpty
            ? null
            : phoneNumberController.text.trim(),
        email: emailController.text.trim().isEmpty
            ? null
            : emailController.text.trim(),
        address: addressController.text.trim().isEmpty
            ? null
            : addressController.text.trim(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final result = await repository.createBroker(broker);
      result.fold(
        (failure) {
          log('Failed to create broker: ${failure.message}');
          SnackbarUtils.showError(
            'Error',
            'Failed to create broker: ${failure.message}',
          );
        },
        (_) {
          log('Successfully created broker: ${broker.name}');
          SnackbarUtils.showSuccess(
            'Success',
            'Broker created successfully',
          );
          clearForm();
          Get.back(); // Close the form dialog
        },
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> updateBroker() async {
    if (!formKey.currentState!.validate() || currentBroker == null) return;

    isLoading.value = true;
    try {
      final updatedBroker = currentBroker!.copyWith(
        name: nameController.text.trim(),
        description: descriptionController.text.trim().isEmpty
            ? null
            : descriptionController.text.trim(),
        phoneNumber: phoneNumberController.text.trim().isEmpty
            ? null
            : phoneNumberController.text.trim(),
        email: emailController.text.trim().isEmpty
            ? null
            : emailController.text.trim(),
        address: addressController.text.trim().isEmpty
            ? null
            : addressController.text.trim(),
        updatedAt: DateTime.now(),
      );

      final result = await repository.updateBroker(updatedBroker);
      result.fold(
        (failure) {
          log('Failed to update broker: ${failure.message}');
          SnackbarUtils.showError(
            'Error',
            'Failed to update broker: ${failure.message}',
          );
        },
        (_) {
          log('Successfully updated broker: ${updatedBroker.name}');
          SnackbarUtils.showSuccess(
            'Success',
            'Broker updated successfully',
          );
          clearForm();
          Get.back(); // Close the form dialog
        },
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> deleteBroker(String brokerId) async {
    // Show confirmation dialog
    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('Delete Broker'),
        content: const Text('Are you sure you want to delete this broker?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    isLoading.value = true;
    try {
      final result = await repository.deleteBroker(brokerId);
      result.fold(
        (failure) {
          log('Failed to delete broker: ${failure.message}');
          SnackbarUtils.showError(
            'Error',
            'Failed to delete broker: ${failure.message}',
          );
        },
        (_) {
          log('Successfully deleted broker: $brokerId');
          SnackbarUtils.showSuccess(
            'Success',
            'Broker deleted successfully',
          );
        },
      );
    } finally {
      isLoading.value = false;
    }
  }

  void editBroker(BrokerModel broker) {
    currentBroker = broker;
    nameController.text = broker.name;
    descriptionController.text = broker.description ?? '';
    phoneNumberController.text = broker.phoneNumber ?? '';
    emailController.text = broker.email ?? '';
    addressController.text = broker.address ?? '';
  }

  void clearForm() {
    currentBroker = null;
    nameController.clear();
    descriptionController.clear();
    phoneNumberController.clear();
    emailController.clear();
    addressController.clear();
  }

  String? validateName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Broker name is required';
    }
    if (value.trim().length < 2) {
      return 'Broker name must be at least 2 characters';
    }
    return null;
  }

  String? validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // Email is optional
    }
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value.trim())) {
      return 'Please enter a valid email address';
    }
    return null;
  }

  String? validatePhoneNumber(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // Phone number is optional
    }
    final phoneRegex = RegExp(r'^\+?[\d\s\-\(\)]{10,}$');
    if (!phoneRegex.hasMatch(value.trim())) {
      return 'Please enter a valid phone number';
    }
    return null;
  }
}
