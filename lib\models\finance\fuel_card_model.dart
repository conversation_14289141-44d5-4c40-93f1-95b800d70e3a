import 'package:cloud_firestore/cloud_firestore.dart';

class FuelCardModel {
  final String id;
  final String cardNumber;
  final String companyName; // Raj<PERSON>, <PERSON><PERSON>, <PERSON>, etc.
  final String uid; // ID of the owning company
  final String fuelStationName; // PSO, Shell, etc.
  final String fuelStationId; // ID of the fuel station
  final double totalCapacity; // Total capacity in liters
  final double remainingCapacity; // Remaining capacity in liters
  final double currentRate; // Current fuel rate per liter
  final DateTime createdAt;
  final DateTime? updatedAt;
  final bool isActive;

  FuelCardModel({
    required this.id,
    required this.cardNumber,
    required this.companyName,
    this.uid = '',
    required this.fuelStationName,
    this.fuelStationId = '',
    required this.totalCapacity,
    required this.remainingCapacity,
    required this.currentRate,
    required this.createdAt,
    this.updatedAt,
    this.isActive = true,
  });

  // Create a FuelCardModel from a map (for Firestore)
  factory FuelCardModel.fromMap(Map<String, dynamic> map) {
    return FuelCardModel(
      id: map['id'] ?? '',
      cardNumber: map['cardNumber'] ?? '',
      companyName: map['companyName'] ?? '',
      uid: map['uid'] ?? '',
      fuelStationName: map['fuelStationName'] ?? '',
      fuelStationId: map['fuelStationId'] ?? '',
      totalCapacity: (map['totalCapacity'] ?? 0).toDouble(),
      remainingCapacity: (map['remainingCapacity'] ?? 0).toDouble(),
      currentRate: (map['currentRate'] ?? 0).toDouble(),
      createdAt: map['createdAt'] is DateTime
          ? map['createdAt'] as DateTime
          : (map['createdAt'] as Timestamp).toDate(),
      updatedAt: map['updatedAt'] != null
          ? (map['updatedAt'] is DateTime
              ? map['updatedAt'] as DateTime
              : (map['updatedAt'] as Timestamp).toDate())
          : null,
      isActive: map['isActive'] ?? true,
    );
  }

  // Convert FuelCardModel to a map (for Firestore)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'cardNumber': cardNumber,
      'companyName': companyName,
      'uid': uid,
      'fuelStationName': fuelStationName,
      'fuelStationId': fuelStationId,
      'totalCapacity': totalCapacity,
      'remainingCapacity': remainingCapacity,
      'currentRate': currentRate,
      'createdAt': createdAt,
      'updatedAt': updatedAt ?? DateTime.now(),
      'isActive': isActive,
    };
  }

  // Create a copy of the FuelCardModel with updated fields
  FuelCardModel copyWith({
    String? id,
    String? cardNumber,
    String? companyName,
    String? uid,
    String? fuelStationName,
    String? fuelStationId,
    double? totalCapacity,
    double? remainingCapacity,
    double? currentRate,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
  }) {
    return FuelCardModel(
      id: id ?? this.id,
      cardNumber: cardNumber ?? this.cardNumber,
      companyName: companyName ?? this.companyName,
      uid: uid ?? this.uid,
      fuelStationName: fuelStationName ?? this.fuelStationName,
      fuelStationId: fuelStationId ?? this.fuelStationId,
      totalCapacity: totalCapacity ?? this.totalCapacity,
      remainingCapacity: remainingCapacity ?? this.remainingCapacity,
      currentRate: currentRate ?? this.currentRate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
    );
  }
}
