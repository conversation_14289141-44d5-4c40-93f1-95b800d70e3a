import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/models/finance/bill_model.dart';
import '../repositories/bill_repository.dart';

class CreateBillUseCase {
  final BillRepository _billRepository;

  CreateBillUseCase(this._billRepository);

  /// Executes the use case to create a bill.
  Future<Either<FailureObj, SuccessObj>> call({
    required String uid,
    required BillModel bill,
    required List<String> invoiceIds,
  }) async {
    // Validate inputs before calling the repository
    if (uid.isEmpty) {
      return Left(FailureObj(
        code: 'invalid-company-id',
        message: 'Company ID cannot be empty.',
      ));
    }

    if (bill.billId.isEmpty) {
      return Left(FailureObj(
        code: 'invalid-bill',
        message: 'Bill ID must not be empty.',
      ));
    }

    if (bill.billNumber.isEmpty) {
      return Left(FailureObj(
        code: 'invalid-bill-number',
        message: 'Bill number must not be empty.',
      ));
    }

    if (invoiceIds.isEmpty) {
      return Left(FailureObj(
        code: 'invalid-invoices',
        message: 'At least one invoice must be linked to the bill.',
      ));
    }

    if (bill.totalAmount <= 0) {
      return Left(FailureObj(
        code: 'invalid-amount',
        message: 'Bill total amount must be greater than zero.',
      ));
    }

    // Call the repository
    return await _billRepository.createBill(
      uid: uid,
      bill: bill,
      invoiceIds: invoiceIds,
    );
  }
}
