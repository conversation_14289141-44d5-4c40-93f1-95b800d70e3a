import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';
import 'package:logestics/models/finance/payer_model.dart';

class PayerFirebaseService {
  late FirebaseFirestore _firestore;

  // Get current user's UID or return 'anonymous' if not authenticated
  String get _uid => FirebaseAuth.instance.currentUser?.uid ?? 'anonymous';

  PayerFirebaseService() {
    _firestore = FirebaseFirestore.instance;
  }

  Future<void> createPayer(PayerModel payer) async {
    log('Creating payer: ${payer.name}');
    try {
      final payerRef =
          _firestore.collection(AppCollection.payersCollection).doc();
      final payerId = payerRef.id;

      final payerData = payer.toJson();
      payerData['id'] = payerId;
      payerData['uid'] = _uid; // Add current user's UID

      await payerRef.set(payerData);
      log('Successfully created payer: $payerId');
    } catch (e) {
      log('Error creating payer: $e');
      rethrow;
    }
  }

  Future<List<PayerModel>> getPayers() async {
    log('Fetching payers from Firestore');
    try {
      final snapshot = await _firestore
          .collection(AppCollection.payersCollection)
          .where('uid', isEqualTo: _uid) // Filter by current user's UID
          .orderBy('createdAt', descending: true)
          .get();

      final payers =
          snapshot.docs.map((doc) => PayerModel.fromJson(doc.data())).toList();

      log('Successfully fetched ${payers.length} payers');
      return payers;
    } catch (e) {
      log('Error fetching payers: $e');
      rethrow;
    }
  }

  Future<void> deletePayer(String payerId) async {
    log('Deleting payer: $payerId');
    try {
      if (payerId.isEmpty) {
        throw ArgumentError('Payer ID cannot be empty');
      }

      // First check if the payer belongs to the current user
      final payerDoc = await _firestore
          .collection(AppCollection.payersCollection)
          .doc(payerId)
          .get();

      if (!payerDoc.exists) {
        throw Exception('Payer not found');
      }

      final payerData = payerDoc.data() as Map<String, dynamic>;
      if (payerData['uid'] != _uid) {
        throw Exception('You do not have permission to delete this payer');
      }

      await _firestore
          .collection(AppCollection.payersCollection)
          .doc(payerId)
          .delete();
      log('Successfully deleted payer: $payerId');
    } catch (e) {
      log('Error deleting payer: $e');
      rethrow;
    }
  }

  Future<void> updatePayer(PayerModel payer) async {
    log('Updating payer: ${payer.id}');
    try {
      if (payer.id.isEmpty) {
        throw ArgumentError('Payer ID cannot be empty');
      }

      // First check if the payer belongs to the current user
      final payerDoc = await _firestore
          .collection(AppCollection.payersCollection)
          .doc(payer.id)
          .get();

      if (!payerDoc.exists) {
        throw Exception('Payer not found');
      }

      final payerData = payerDoc.data() as Map<String, dynamic>;
      if (payerData['uid'] != _uid) {
        throw Exception('You do not have permission to update this payer');
      }

      // Preserve the original UID
      final updatedPayerData = payer.toJson();
      updatedPayerData['uid'] = payerData['uid'];

      await _firestore
          .collection(AppCollection.payersCollection)
          .doc(payer.id)
          .update(updatedPayerData);
      log('Successfully updated payer: ${payer.id}');
    } catch (e) {
      log('Error updating payer: $e');
      rethrow;
    }
  }

  /// Stream to listen for real-time updates to payers
  Stream<List<PayerModel>> listenToPayers() {
    try {
      return _firestore
          .collection(AppCollection.payersCollection)
          .where('uid', isEqualTo: _uid) // Filter by current user's UID
          .orderBy('createdAt', descending: true)
          .snapshots()
          .map((snapshot) => snapshot.docs
              .map((doc) => PayerModel.fromJson(doc.data()))
              .toList());
    } catch (e) {
      log('Error listening to payers: $e', error: e);
      return Stream.value([]);
    }
  }
}
