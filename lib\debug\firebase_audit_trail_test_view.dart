import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/debug/firebase_audit_trail_tester.dart';

/// Firebase Audit Trail Test View
/// UI for running Firebase connectivity and audit trail tests
class FirebaseAuditTrailTestView extends StatelessWidget {
  const FirebaseAuditTrailTestView({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(FirebaseAuditTrailTester());

    return Scaffold(
      appBar: AppBar(
        title: const Text('Firebase Audit Trail Tester'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status and Controls
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Firebase Audit Trail Connectivity Test',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Obx(() => Text(
                      'Status: ${controller.testStatus.value}',
                      style: TextStyle(
                        color: controller.isRunning.value 
                            ? Colors.orange 
                            : Colors.green,
                        fontWeight: FontWeight.bold,
                      ),
                    )),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Obx(() => ElevatedButton.icon(
                          onPressed: controller.isRunning.value 
                              ? null 
                              : controller.runTests,
                          icon: controller.isRunning.value
                              ? const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(strokeWidth: 2),
                                )
                              : const Icon(Icons.play_arrow),
                          label: Text(controller.isRunning.value 
                              ? 'Running Tests...' 
                              : 'Run Tests'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                          ),
                        )),
                        const SizedBox(width: 16),
                        ElevatedButton.icon(
                          onPressed: controller.clearResults,
                          icon: const Icon(Icons.clear),
                          label: const Text('Clear Results'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.grey,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            
            // Test Results
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Test Results',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: Obx(() {
                          if (controller.testResults.isEmpty) {
                            return const Center(
                              child: Text(
                                'No test results yet.\nClick "Run Tests" to start.',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: Colors.grey,
                                  fontSize: 16,
                                ),
                              ),
                            );
                          }
                          
                          return ListView.builder(
                            itemCount: controller.testResults.length,
                            itemBuilder: (context, index) {
                              final result = controller.testResults[index];
                              final isError = result.contains('❌');
                              final isWarning = result.contains('⚠️');
                              final isSuccess = result.contains('✅');
                              
                              Color textColor = Colors.black;
                              Color backgroundColor = Colors.transparent;
                              
                              if (isError) {
                                textColor = Colors.red;
                                backgroundColor = Colors.red.withValues(alpha: 0.1);
                              } else if (isWarning) {
                                textColor = Colors.orange;
                                backgroundColor = Colors.orange.withValues(alpha: 0.1);
                              } else if (isSuccess) {
                                textColor = Colors.green;
                                backgroundColor = Colors.green.withValues(alpha: 0.1);
                              }
                              
                              return Container(
                                margin: const EdgeInsets.only(bottom: 4),
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: backgroundColor,
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  result,
                                  style: TextStyle(
                                    color: textColor,
                                    fontFamily: 'monospace',
                                    fontSize: 12,
                                  ),
                                ),
                              );
                            },
                          );
                        }),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            
            // Instructions
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Test Coverage',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '• Firebase Firestore connection\n'
                      '• User authentication status\n'
                      '• Audit trail collection access\n'
                      '• Read/write permissions\n'
                      '• Basic and asset-specific queries\n'
                      '• Real-time stream functionality',
                      style: TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
