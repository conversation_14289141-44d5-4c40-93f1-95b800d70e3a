import 'package:flutter/material.dart';

class AppColors {
//////light theme colors for color Scheme
  static const Color primary = Color.fromARGB(255, 45, 142, 239);
  static const Color onPrimary = Color(0xFFFFFFFF);

  static const Color secondary = Color(0xFF34495E);
  static const Color onSecondary = Color(0xFFFFFFFF);
  static Color secondaryContainer = const Color(0x1A34495E);
  static Color onSecondaryContainer = const Color(0xFF34495E);

  static const Color whiteColor = Colors.white;
  static const Color tertiary = Color(0xFFF5F6FA);
  static const Color onTertiary = Color(0xFF2C3E50);
  static const Color onTertiaryFixedVariant = Color(0xFF95A5A6);

  static const Color surface = Color(0xFFFFFFFF);
  static const Color onSurface = Color(0xFF2C3E50);
  static const Color onSurfaceVariant = Color(0xFF95A5A6);

  static const Color error = Color(0x1AFF6B6B);
  static const Color onError = Color(0xFFE74C3C);
  static const Color errorContainer = Color(0x1AFF6B6B);
  static const Color onErrorContainer = Color(0xFFE74C3C);

  static const Color shadow = Color(0x296B3E3E);
//////dark theme colors for color Scheme
  static const Color darkPrimary = Color(0xFF2C3E50);
  static const Color darkOnPrimary = Color(0xFFFFFFFF);

  static const Color darkSecondary = Color(0xFF34495E);
  static const Color darkOnSecondary = Color(0xFFFFFFFF);
  static Color darkSecondaryContainer = const Color(0x1A34495E);
  static Color darkOnSecondaryContainer = const Color(0xFF34495E);

  static const Color darkTertiary = Color(0xFF2C3E50);
  static const Color darkOnTertiary = Color(0xFFFFFFFF);
  static const Color darkOnTertiaryFixedVariant = Color(0xFF95A5A6);

  static const Color darkSurface = Color(0xFF1A1A1A);
  static const Color darkOnSurface = Color(0xFFFFFFFF);
  static const Color darkOnSurfaceVariant = Color(0xFF95A5A6);
  static const Color darkSurfaceContainer = Color(0xFF2C2C2C);

  static const Color darkError = Color(0x1AFF6B6B);
  static const Color darkOnError = Color(0xFFE74C3C);
  static const Color darkErrorContainer = Color(0x1AFF6B6B);
  static const Color darkOnErrorContainer = Color(0xFFE74C3C);

  static const Color darkShadow = Color(0xFF2C2C2C);
}
