/// Aged Reports Models for Receivables and Payables
///
/// This file contains models for aged receivables and payables reports
/// following the existing financial reporting patterns

/// Enum for aging periods
enum AgingPeriod {
  current, // 0-30 days
  period30, // 31-60 days
  period60, // 61-90 days
  period90Plus // 90+ days
}

/// Extension for aging period display
extension AgingPeriodExtension on AgingPeriod {
  String get displayName {
    switch (this) {
      case AgingPeriod.current:
        return 'Current (0-30 days)';
      case AgingPeriod.period30:
        return '31-60 days';
      case AgingPeriod.period60:
        return '61-90 days';
      case AgingPeriod.period90Plus:
        return '90+ days';
    }
  }

  String get shortName {
    switch (this) {
      case AgingPeriod.current:
        return '0-30';
      case AgingPeriod.period30:
        return '31-60';
      case AgingPeriod.period60:
        return '61-90';
      case AgingPeriod.period90Plus:
        return '90+';
    }
  }

  int get minDays {
    switch (this) {
      case AgingPeriod.current:
        return 0;
      case AgingPeriod.period30:
        return 31;
      case AgingPeriod.period60:
        return 61;
      case AgingPeriod.period90Plus:
        return 91;
    }
  }

  int? get maxDays {
    switch (this) {
      case AgingPeriod.current:
        return 30;
      case AgingPeriod.period30:
        return 60;
      case AgingPeriod.period60:
        return 90;
      case AgingPeriod.period90Plus:
        return null; // No upper limit
    }
  }
}

/// Model for individual aged receivable entry
class AgedReceivableEntry {
  final String customerId;
  final String customerName;
  final String invoiceId;
  final String invoiceNumber;
  final DateTime invoiceDate;
  final DateTime? dueDate;
  final double totalAmount;
  final double paidAmount;
  final double outstandingAmount;
  final int daysOutstanding;
  final AgingPeriod agingPeriod;
  final String status;
  final String? notes;

  AgedReceivableEntry({
    required this.customerId,
    required this.customerName,
    required this.invoiceId,
    required this.invoiceNumber,
    required this.invoiceDate,
    this.dueDate,
    required this.totalAmount,
    required this.paidAmount,
    required this.outstandingAmount,
    required this.daysOutstanding,
    required this.agingPeriod,
    required this.status,
    this.notes,
  });

  /// Calculate aging period based on days outstanding
  static AgingPeriod calculateAgingPeriod(int daysOutstanding) {
    if (daysOutstanding <= 30) {
      return AgingPeriod.current;
    } else if (daysOutstanding <= 60) {
      return AgingPeriod.period30;
    } else if (daysOutstanding <= 90) {
      return AgingPeriod.period60;
    } else {
      return AgingPeriod.period90Plus;
    }
  }

  /// Check if entry is overdue
  bool get isOverdue {
    if (dueDate == null) return false;
    return DateTime.now().isAfter(dueDate!);
  }

  /// Convert to map for Firebase storage
  Map<String, dynamic> toMap() {
    return {
      'customerId': customerId,
      'customerName': customerName,
      'invoiceId': invoiceId,
      'invoiceNumber': invoiceNumber,
      'invoiceDate': invoiceDate.millisecondsSinceEpoch,
      'dueDate': dueDate?.millisecondsSinceEpoch,
      'totalAmount': totalAmount,
      'paidAmount': paidAmount,
      'outstandingAmount': outstandingAmount,
      'daysOutstanding': daysOutstanding,
      'agingPeriod': agingPeriod.name,
      'status': status,
      'notes': notes,
    };
  }

  /// Create from map (Firebase data)
  factory AgedReceivableEntry.fromMap(Map<String, dynamic> map) {
    return AgedReceivableEntry(
      customerId: map['customerId'] ?? '',
      customerName: map['customerName'] ?? '',
      invoiceId: map['invoiceId'] ?? '',
      invoiceNumber: map['invoiceNumber'] ?? '',
      invoiceDate: DateTime.fromMillisecondsSinceEpoch(map['invoiceDate'] ?? 0),
      dueDate: map['dueDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['dueDate'])
          : null,
      totalAmount: (map['totalAmount'] ?? 0.0).toDouble(),
      paidAmount: (map['paidAmount'] ?? 0.0).toDouble(),
      outstandingAmount: (map['outstandingAmount'] ?? 0.0).toDouble(),
      daysOutstanding: map['daysOutstanding'] ?? 0,
      agingPeriod: AgingPeriod.values.firstWhere(
        (e) => e.name == map['agingPeriod'],
        orElse: () => AgingPeriod.current,
      ),
      status: map['status'] ?? '',
      notes: map['notes'],
    );
  }

  @override
  String toString() {
    return 'AgedReceivableEntry(customerId: $customerId, customerName: $customerName, invoiceNumber: $invoiceNumber, outstandingAmount: $outstandingAmount, daysOutstanding: $daysOutstanding, agingPeriod: $agingPeriod)';
  }
}

/// Model for individual aged payable entry
class AgedPayableEntry {
  final String vendorId;
  final String vendorName;
  final String billId;
  final String billNumber;
  final DateTime billDate;
  final DateTime? dueDate;
  final double totalAmount;
  final double paidAmount;
  final double outstandingAmount;
  final int daysOutstanding;
  final AgingPeriod agingPeriod;
  final String status;
  final String? notes;

  AgedPayableEntry({
    required this.vendorId,
    required this.vendorName,
    required this.billId,
    required this.billNumber,
    required this.billDate,
    this.dueDate,
    required this.totalAmount,
    required this.paidAmount,
    required this.outstandingAmount,
    required this.daysOutstanding,
    required this.agingPeriod,
    required this.status,
    this.notes,
  });

  /// Check if entry is overdue
  bool get isOverdue {
    if (dueDate == null) return false;
    return DateTime.now().isAfter(dueDate!);
  }

  /// Convert to map for Firebase storage
  Map<String, dynamic> toMap() {
    return {
      'vendorId': vendorId,
      'vendorName': vendorName,
      'billId': billId,
      'billNumber': billNumber,
      'billDate': billDate.millisecondsSinceEpoch,
      'dueDate': dueDate?.millisecondsSinceEpoch,
      'totalAmount': totalAmount,
      'paidAmount': paidAmount,
      'outstandingAmount': outstandingAmount,
      'daysOutstanding': daysOutstanding,
      'agingPeriod': agingPeriod.name,
      'status': status,
      'notes': notes,
    };
  }

  /// Create from map (Firebase data)
  factory AgedPayableEntry.fromMap(Map<String, dynamic> map) {
    return AgedPayableEntry(
      vendorId: map['vendorId'] ?? '',
      vendorName: map['vendorName'] ?? '',
      billId: map['billId'] ?? '',
      billNumber: map['billNumber'] ?? '',
      billDate: DateTime.fromMillisecondsSinceEpoch(map['billDate'] ?? 0),
      dueDate: map['dueDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['dueDate'])
          : null,
      totalAmount: (map['totalAmount'] ?? 0.0).toDouble(),
      paidAmount: (map['paidAmount'] ?? 0.0).toDouble(),
      outstandingAmount: (map['outstandingAmount'] ?? 0.0).toDouble(),
      daysOutstanding: map['daysOutstanding'] ?? 0,
      agingPeriod: AgingPeriod.values.firstWhere(
        (e) => e.name == map['agingPeriod'],
        orElse: () => AgingPeriod.current,
      ),
      status: map['status'] ?? '',
      notes: map['notes'],
    );
  }

  @override
  String toString() {
    return 'AgedPayableEntry(vendorId: $vendorId, vendorName: $vendorName, billNumber: $billNumber, outstandingAmount: $outstandingAmount, daysOutstanding: $daysOutstanding, agingPeriod: $agingPeriod)';
  }
}

/// Summary model for aged receivables by customer
class AgedReceivableCustomerSummary {
  final String customerId;
  final String customerName;
  final double currentAmount;
  final double period30Amount;
  final double period60Amount;
  final double period90PlusAmount;
  final double totalOutstanding;
  final int totalInvoices;
  final DateTime? oldestInvoiceDate;

  AgedReceivableCustomerSummary({
    required this.customerId,
    required this.customerName,
    required this.currentAmount,
    required this.period30Amount,
    required this.period60Amount,
    required this.period90PlusAmount,
    required this.totalOutstanding,
    required this.totalInvoices,
    this.oldestInvoiceDate,
  });

  /// Create summary from list of entries
  factory AgedReceivableCustomerSummary.fromEntries(
    String customerId,
    String customerName,
    List<AgedReceivableEntry> entries,
  ) {
    double currentAmount = 0;
    double period30Amount = 0;
    double period60Amount = 0;
    double period90PlusAmount = 0;
    DateTime? oldestDate;

    for (final entry in entries) {
      switch (entry.agingPeriod) {
        case AgingPeriod.current:
          currentAmount += entry.outstandingAmount;
          break;
        case AgingPeriod.period30:
          period30Amount += entry.outstandingAmount;
          break;
        case AgingPeriod.period60:
          period60Amount += entry.outstandingAmount;
          break;
        case AgingPeriod.period90Plus:
          period90PlusAmount += entry.outstandingAmount;
          break;
      }

      if (oldestDate == null || entry.invoiceDate.isBefore(oldestDate)) {
        oldestDate = entry.invoiceDate;
      }
    }

    return AgedReceivableCustomerSummary(
      customerId: customerId,
      customerName: customerName,
      currentAmount: currentAmount,
      period30Amount: period30Amount,
      period60Amount: period60Amount,
      period90PlusAmount: period90PlusAmount,
      totalOutstanding:
          currentAmount + period30Amount + period60Amount + period90PlusAmount,
      totalInvoices: entries.length,
      oldestInvoiceDate: oldestDate,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'customerId': customerId,
      'customerName': customerName,
      'currentAmount': currentAmount,
      'period30Amount': period30Amount,
      'period60Amount': period60Amount,
      'period90PlusAmount': period90PlusAmount,
      'totalOutstanding': totalOutstanding,
      'totalInvoices': totalInvoices,
      'oldestInvoiceDate': oldestInvoiceDate?.millisecondsSinceEpoch,
    };
  }

  factory AgedReceivableCustomerSummary.fromMap(Map<String, dynamic> map) {
    return AgedReceivableCustomerSummary(
      customerId: map['customerId'] ?? '',
      customerName: map['customerName'] ?? '',
      currentAmount: (map['currentAmount'] ?? 0.0).toDouble(),
      period30Amount: (map['period30Amount'] ?? 0.0).toDouble(),
      period60Amount: (map['period60Amount'] ?? 0.0).toDouble(),
      period90PlusAmount: (map['period90PlusAmount'] ?? 0.0).toDouble(),
      totalOutstanding: (map['totalOutstanding'] ?? 0.0).toDouble(),
      totalInvoices: map['totalInvoices'] ?? 0,
      oldestInvoiceDate: map['oldestInvoiceDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['oldestInvoiceDate'])
          : null,
    );
  }
}

/// Summary model for aged payables by vendor
class AgedPayableVendorSummary {
  final String vendorId;
  final String vendorName;
  final double currentAmount;
  final double period30Amount;
  final double period60Amount;
  final double period90PlusAmount;
  final double totalOutstanding;
  final int totalBills;
  final DateTime? oldestBillDate;

  AgedPayableVendorSummary({
    required this.vendorId,
    required this.vendorName,
    required this.currentAmount,
    required this.period30Amount,
    required this.period60Amount,
    required this.period90PlusAmount,
    required this.totalOutstanding,
    required this.totalBills,
    this.oldestBillDate,
  });

  /// Create summary from list of entries
  factory AgedPayableVendorSummary.fromEntries(
    String vendorId,
    String vendorName,
    List<AgedPayableEntry> entries,
  ) {
    double currentAmount = 0;
    double period30Amount = 0;
    double period60Amount = 0;
    double period90PlusAmount = 0;
    DateTime? oldestDate;

    for (final entry in entries) {
      switch (entry.agingPeriod) {
        case AgingPeriod.current:
          currentAmount += entry.outstandingAmount;
          break;
        case AgingPeriod.period30:
          period30Amount += entry.outstandingAmount;
          break;
        case AgingPeriod.period60:
          period60Amount += entry.outstandingAmount;
          break;
        case AgingPeriod.period90Plus:
          period90PlusAmount += entry.outstandingAmount;
          break;
      }

      if (oldestDate == null || entry.billDate.isBefore(oldestDate)) {
        oldestDate = entry.billDate;
      }
    }

    return AgedPayableVendorSummary(
      vendorId: vendorId,
      vendorName: vendorName,
      currentAmount: currentAmount,
      period30Amount: period30Amount,
      period60Amount: period60Amount,
      period90PlusAmount: period90PlusAmount,
      totalOutstanding:
          currentAmount + period30Amount + period60Amount + period90PlusAmount,
      totalBills: entries.length,
      oldestBillDate: oldestDate,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'vendorId': vendorId,
      'vendorName': vendorName,
      'currentAmount': currentAmount,
      'period30Amount': period30Amount,
      'period60Amount': period60Amount,
      'period90PlusAmount': period90PlusAmount,
      'totalOutstanding': totalOutstanding,
      'totalBills': totalBills,
      'oldestBillDate': oldestBillDate?.millisecondsSinceEpoch,
    };
  }

  factory AgedPayableVendorSummary.fromMap(Map<String, dynamic> map) {
    return AgedPayableVendorSummary(
      vendorId: map['vendorId'] ?? '',
      vendorName: map['vendorName'] ?? '',
      currentAmount: (map['currentAmount'] ?? 0.0).toDouble(),
      period30Amount: (map['period30Amount'] ?? 0.0).toDouble(),
      period60Amount: (map['period60Amount'] ?? 0.0).toDouble(),
      period90PlusAmount: (map['period90PlusAmount'] ?? 0.0).toDouble(),
      totalOutstanding: (map['totalOutstanding'] ?? 0.0).toDouble(),
      totalBills: map['totalBills'] ?? 0,
      oldestBillDate: map['oldestBillDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['oldestBillDate'])
          : null,
    );
  }
}

/// Complete Aged Receivables Report
class AgedReceivablesReport {
  final String reportId;
  final DateTime asOfDate;
  final DateTime generatedAt;
  final String companyName;
  final String companyUid;
  final List<AgedReceivableEntry> entries;
  final List<AgedReceivableCustomerSummary> customerSummaries;
  final double totalCurrentAmount;
  final double totalPeriod30Amount;
  final double totalPeriod60Amount;
  final double totalPeriod90PlusAmount;
  final double grandTotalOutstanding;
  final int totalCustomers;
  final int totalInvoices;

  AgedReceivablesReport({
    required this.reportId,
    required this.asOfDate,
    required this.generatedAt,
    required this.companyName,
    required this.companyUid,
    required this.entries,
    required this.customerSummaries,
    required this.totalCurrentAmount,
    required this.totalPeriod30Amount,
    required this.totalPeriod60Amount,
    required this.totalPeriod90PlusAmount,
    required this.grandTotalOutstanding,
    required this.totalCustomers,
    required this.totalInvoices,
  });

  /// Create report from entries
  factory AgedReceivablesReport.fromEntries({
    required String reportId,
    required DateTime asOfDate,
    required String companyName,
    required String companyUid,
    required List<AgedReceivableEntry> entries,
  }) {
    // Group entries by customer
    final Map<String, List<AgedReceivableEntry>> customerGroups = {};
    for (final entry in entries) {
      final key = '${entry.customerId}_${entry.customerName}';
      customerGroups.putIfAbsent(key, () => []).add(entry);
    }

    // Create customer summaries
    final customerSummaries = customerGroups.entries.map((group) {
      final parts = group.key.split('_');
      final customerId = parts[0];
      final customerName =
          parts.length > 1 ? parts.sublist(1).join('_') : parts[0];
      return AgedReceivableCustomerSummary.fromEntries(
        customerId,
        customerName,
        group.value,
      );
    }).toList();

    // Calculate totals
    double totalCurrent = 0;
    double totalPeriod30 = 0;
    double totalPeriod60 = 0;
    double totalPeriod90Plus = 0;

    for (final summary in customerSummaries) {
      totalCurrent += summary.currentAmount;
      totalPeriod30 += summary.period30Amount;
      totalPeriod60 += summary.period60Amount;
      totalPeriod90Plus += summary.period90PlusAmount;
    }

    return AgedReceivablesReport(
      reportId: reportId,
      asOfDate: asOfDate,
      generatedAt: DateTime.now(),
      companyName: companyName,
      companyUid: companyUid,
      entries: entries,
      customerSummaries: customerSummaries,
      totalCurrentAmount: totalCurrent,
      totalPeriod30Amount: totalPeriod30,
      totalPeriod60Amount: totalPeriod60,
      totalPeriod90PlusAmount: totalPeriod90Plus,
      grandTotalOutstanding:
          totalCurrent + totalPeriod30 + totalPeriod60 + totalPeriod90Plus,
      totalCustomers: customerSummaries.length,
      totalInvoices: entries.length,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'reportId': reportId,
      'asOfDate': asOfDate.millisecondsSinceEpoch,
      'generatedAt': generatedAt.millisecondsSinceEpoch,
      'companyName': companyName,
      'companyUid': companyUid,
      'entries': entries.map((e) => e.toMap()).toList(),
      'customerSummaries': customerSummaries.map((s) => s.toMap()).toList(),
      'totalCurrentAmount': totalCurrentAmount,
      'totalPeriod30Amount': totalPeriod30Amount,
      'totalPeriod60Amount': totalPeriod60Amount,
      'totalPeriod90PlusAmount': totalPeriod90PlusAmount,
      'grandTotalOutstanding': grandTotalOutstanding,
      'totalCustomers': totalCustomers,
      'totalInvoices': totalInvoices,
    };
  }

  factory AgedReceivablesReport.fromMap(Map<String, dynamic> map) {
    return AgedReceivablesReport(
      reportId: map['reportId'] ?? '',
      asOfDate: DateTime.fromMillisecondsSinceEpoch(map['asOfDate'] ?? 0),
      generatedAt: DateTime.fromMillisecondsSinceEpoch(map['generatedAt'] ?? 0),
      companyName: map['companyName'] ?? '',
      companyUid: map['companyUid'] ?? '',
      entries: (map['entries'] as List<dynamic>? ?? [])
          .map((e) => AgedReceivableEntry.fromMap(e as Map<String, dynamic>))
          .toList(),
      customerSummaries: (map['customerSummaries'] as List<dynamic>? ?? [])
          .map((s) =>
              AgedReceivableCustomerSummary.fromMap(s as Map<String, dynamic>))
          .toList(),
      totalCurrentAmount: (map['totalCurrentAmount'] ?? 0.0).toDouble(),
      totalPeriod30Amount: (map['totalPeriod30Amount'] ?? 0.0).toDouble(),
      totalPeriod60Amount: (map['totalPeriod60Amount'] ?? 0.0).toDouble(),
      totalPeriod90PlusAmount:
          (map['totalPeriod90PlusAmount'] ?? 0.0).toDouble(),
      grandTotalOutstanding: (map['grandTotalOutstanding'] ?? 0.0).toDouble(),
      totalCustomers: map['totalCustomers'] ?? 0,
      totalInvoices: map['totalInvoices'] ?? 0,
    );
  }
}

/// Complete Aged Payables Report
class AgedPayablesReport {
  final String reportId;
  final DateTime asOfDate;
  final DateTime generatedAt;
  final String companyName;
  final String companyUid;
  final List<AgedPayableEntry> entries;
  final List<AgedPayableVendorSummary> vendorSummaries;
  final double totalCurrentAmount;
  final double totalPeriod30Amount;
  final double totalPeriod60Amount;
  final double totalPeriod90PlusAmount;
  final double grandTotalOutstanding;
  final int totalVendors;
  final int totalBills;

  AgedPayablesReport({
    required this.reportId,
    required this.asOfDate,
    required this.generatedAt,
    required this.companyName,
    required this.companyUid,
    required this.entries,
    required this.vendorSummaries,
    required this.totalCurrentAmount,
    required this.totalPeriod30Amount,
    required this.totalPeriod60Amount,
    required this.totalPeriod90PlusAmount,
    required this.grandTotalOutstanding,
    required this.totalVendors,
    required this.totalBills,
  });

  /// Create report from entries
  factory AgedPayablesReport.fromEntries({
    required String reportId,
    required DateTime asOfDate,
    required String companyName,
    required String companyUid,
    required List<AgedPayableEntry> entries,
  }) {
    // Group entries by vendor
    final Map<String, List<AgedPayableEntry>> vendorGroups = {};
    for (final entry in entries) {
      final key = '${entry.vendorId}_${entry.vendorName}';
      vendorGroups.putIfAbsent(key, () => []).add(entry);
    }

    // Create vendor summaries
    final vendorSummaries = vendorGroups.entries.map((group) {
      final parts = group.key.split('_');
      final vendorId = parts[0];
      final vendorName =
          parts.length > 1 ? parts.sublist(1).join('_') : parts[0];
      return AgedPayableVendorSummary.fromEntries(
        vendorId,
        vendorName,
        group.value,
      );
    }).toList();

    // Calculate totals
    double totalCurrent = 0;
    double totalPeriod30 = 0;
    double totalPeriod60 = 0;
    double totalPeriod90Plus = 0;

    for (final summary in vendorSummaries) {
      totalCurrent += summary.currentAmount;
      totalPeriod30 += summary.period30Amount;
      totalPeriod60 += summary.period60Amount;
      totalPeriod90Plus += summary.period90PlusAmount;
    }

    return AgedPayablesReport(
      reportId: reportId,
      asOfDate: asOfDate,
      generatedAt: DateTime.now(),
      companyName: companyName,
      companyUid: companyUid,
      entries: entries,
      vendorSummaries: vendorSummaries,
      totalCurrentAmount: totalCurrent,
      totalPeriod30Amount: totalPeriod30,
      totalPeriod60Amount: totalPeriod60,
      totalPeriod90PlusAmount: totalPeriod90Plus,
      grandTotalOutstanding:
          totalCurrent + totalPeriod30 + totalPeriod60 + totalPeriod90Plus,
      totalVendors: vendorSummaries.length,
      totalBills: entries.length,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'reportId': reportId,
      'asOfDate': asOfDate.millisecondsSinceEpoch,
      'generatedAt': generatedAt.millisecondsSinceEpoch,
      'companyName': companyName,
      'companyUid': companyUid,
      'entries': entries.map((e) => e.toMap()).toList(),
      'vendorSummaries': vendorSummaries.map((s) => s.toMap()).toList(),
      'totalCurrentAmount': totalCurrentAmount,
      'totalPeriod30Amount': totalPeriod30Amount,
      'totalPeriod60Amount': totalPeriod60Amount,
      'totalPeriod90PlusAmount': totalPeriod90PlusAmount,
      'grandTotalOutstanding': grandTotalOutstanding,
      'totalVendors': totalVendors,
      'totalBills': totalBills,
    };
  }

  factory AgedPayablesReport.fromMap(Map<String, dynamic> map) {
    return AgedPayablesReport(
      reportId: map['reportId'] ?? '',
      asOfDate: DateTime.fromMillisecondsSinceEpoch(map['asOfDate'] ?? 0),
      generatedAt: DateTime.fromMillisecondsSinceEpoch(map['generatedAt'] ?? 0),
      companyName: map['companyName'] ?? '',
      companyUid: map['companyUid'] ?? '',
      entries: (map['entries'] as List<dynamic>? ?? [])
          .map((e) => AgedPayableEntry.fromMap(e as Map<String, dynamic>))
          .toList(),
      vendorSummaries: (map['vendorSummaries'] as List<dynamic>? ?? [])
          .map((s) =>
              AgedPayableVendorSummary.fromMap(s as Map<String, dynamic>))
          .toList(),
      totalCurrentAmount: (map['totalCurrentAmount'] ?? 0.0).toDouble(),
      totalPeriod30Amount: (map['totalPeriod30Amount'] ?? 0.0).toDouble(),
      totalPeriod60Amount: (map['totalPeriod60Amount'] ?? 0.0).toDouble(),
      totalPeriod90PlusAmount:
          (map['totalPeriod90PlusAmount'] ?? 0.0).toDouble(),
      grandTotalOutstanding: (map['grandTotalOutstanding'] ?? 0.0).toDouble(),
      totalVendors: map['totalVendors'] ?? 0,
      totalBills: map['totalBills'] ?? 0,
    );
  }
}
