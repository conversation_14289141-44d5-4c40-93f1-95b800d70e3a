import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logistics/firebase_service/accounting/journal_entry_firebase_service.dart';
import 'package:logistics/models/finance/journal_entry_model.dart';
import 'package:logistics/models/finance/chart_of_accounts_model.dart';
import 'package:logistics/controllers/auth_controller.dart';

/// Test script to verify Journal Entry data format consistency
/// 
/// This script tests the complete CRUD cycle:
/// 1. Create new journal entry (saves with Timestamp format)
/// 2. Post journal entry (updates with Timestamp format)
/// 3. Load journal entries (reads with Timestamp format)
/// 4. Verify no data type mismatches occur
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  print('🧪 Testing Journal Entry Data Format Consistency...\n');
  
  try {
    // Initialize auth controller
    final authController = Get.put(AuthController());
    await Future.delayed(const Duration(seconds: 2));
    
    if (authController.currentUser.value == null) {
      print('❌ Error: User not authenticated. Please login first.');
      return;
    }
    
    print('✅ User authenticated: ${authController.currentUser.value!.email}');
    print('🏢 Company UID: ${authController.currentUser.value!.uid}\n');
    
    final journalService = JournalEntryFirebaseService();
    
    // Test 1: Load existing entries to verify current state
    print('📋 Test 1: Loading existing journal entries...');
    try {
      final existingEntries = await journalService.getJournalEntries();
      print('✅ Successfully loaded ${existingEntries.length} existing entries');
      
      if (existingEntries.isNotEmpty) {
        final recentEntry = existingEntries.first;
        print('📄 Most recent entry: ${recentEntry.entryNumber}');
        print('📅 Entry date: ${recentEntry.entryDate}');
        print('🕐 Created at: ${recentEntry.createdAt}');
        print('🔄 Updated at: ${recentEntry.updatedAt}');
        print('📊 Status: ${recentEntry.status.displayName}');
      }
    } catch (e) {
      print('❌ Error loading existing entries: $e');
      return;
    }
    
    print('\n' + '='*60 + '\n');
    
    // Test 2: Create a new journal entry
    print('📝 Test 2: Creating new journal entry...');
    try {
      final entryNumber = await journalService.getNextJournalEntryNumber();
      print('🔢 Generated entry number: $entryNumber');
      
      // Create sample journal entry with proper structure
      final newEntry = JournalEntryModel(
        id: '', // Will be auto-generated
        entryNumber: entryNumber,
        entryDate: DateTime.now(),
        description: 'Test Entry - Data Consistency Check',
        entryType: JournalEntryType.manual,
        status: JournalEntryStatus.draft,
        lines: [
          JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: 'test-account-1',
            accountNumber: '1000',
            accountName: 'Test Cash Account',
            debitAmount: 1000.00,
            creditAmount: 0.00,
            description: 'Test debit entry',
            createdAt: DateTime.now(),
          ),
          JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: 'test-account-2',
            accountNumber: '4000',
            accountName: 'Test Revenue Account',
            debitAmount: 0.00,
            creditAmount: 1000.00,
            description: 'Test credit entry',
            createdAt: DateTime.now(),
          ),
        ],
        totalDebits: 1000.00,
        totalCredits: 1000.00,
        createdAt: DateTime.now(),
        createdBy: authController.currentUser.value!.uid,
        uid: authController.currentUser.value!.uid,
      );
      
      await journalService.createJournalEntry(newEntry);
      print('✅ Successfully created new journal entry: $entryNumber');
      
      // Wait a moment for the data to be written
      await Future.delayed(const Duration(seconds: 2));
      
    } catch (e) {
      print('❌ Error creating journal entry: $e');
      return;
    }
    
    print('\n' + '='*60 + '\n');
    
    // Test 3: Load entries again to verify the new entry appears
    print('🔄 Test 3: Loading entries after creation...');
    try {
      final entriesAfterCreate = await journalService.getJournalEntries();
      print('✅ Successfully loaded ${entriesAfterCreate.length} entries after creation');
      
      // Find our test entry
      final testEntry = entriesAfterCreate.firstWhereOrNull(
        (entry) => entry.description.contains('Test Entry - Data Consistency Check')
      );
      
      if (testEntry != null) {
        print('✅ Found our test entry: ${testEntry.entryNumber}');
        print('📅 Entry date: ${testEntry.entryDate}');
        print('🕐 Created at: ${testEntry.createdAt}');
        print('📊 Status: ${testEntry.status.displayName}');
        print('💰 Total debits: \$${testEntry.totalDebits.toStringAsFixed(2)}');
        print('💰 Total credits: \$${testEntry.totalCredits.toStringAsFixed(2)}');
        print('📋 Lines count: ${testEntry.lines.length}');
        
        // Test 4: Post the journal entry (status update)
        print('\n📮 Test 4: Posting journal entry (status update)...');
        try {
          await journalService.postJournalEntry(testEntry.id);
          print('✅ Successfully posted journal entry');
          
          // Wait for the update to be processed
          await Future.delayed(const Duration(seconds: 2));
          
        } catch (e) {
          print('❌ Error posting journal entry: $e');
          return;
        }
        
        print('\n' + '='*60 + '\n');
        
        // Test 5: Load entries after posting to verify status update
        print('🔄 Test 5: Loading entries after posting...');
        try {
          final entriesAfterPost = await journalService.getJournalEntries();
          print('✅ Successfully loaded ${entriesAfterPost.length} entries after posting');
          
          // Find our test entry again
          final postedEntry = entriesAfterPost.firstWhereOrNull(
            (entry) => entry.id == testEntry.id
          );
          
          if (postedEntry != null) {
            print('✅ Found posted entry: ${postedEntry.entryNumber}');
            print('📊 Status: ${postedEntry.status.displayName}');
            print('🕐 Created at: ${postedEntry.createdAt}');
            print('🔄 Updated at: ${postedEntry.updatedAt}');
            
            // Verify status changed to posted
            if (postedEntry.status == JournalEntryStatus.posted) {
              print('✅ Status correctly updated to POSTED');
            } else {
              print('⚠️ Status not updated correctly: ${postedEntry.status.displayName}');
            }
            
            // Verify updatedAt is set
            if (postedEntry.updatedAt != null) {
              print('✅ UpdatedAt timestamp correctly set');
            } else {
              print('⚠️ UpdatedAt timestamp not set');
            }
            
          } else {
            print('❌ Could not find posted entry');
          }
          
        } catch (e) {
          print('❌ Error loading entries after posting: $e');
          print('🔍 This is the critical error we were trying to fix!');
          return;
        }
        
      } else {
        print('❌ Could not find our test entry');
        return;
      }
      
    } catch (e) {
      print('❌ Error loading entries after creation: $e');
      return;
    }
    
    print('\n' + '='*60 + '\n');
    
    // Test 6: Test date range queries
    print('📅 Test 6: Testing date range queries...');
    try {
      final endDate = DateTime.now();
      final startDate = endDate.subtract(const Duration(days: 1));
      
      final entriesInRange = await journalService.getJournalEntriesByDateRange(
        startDate, 
        endDate
      );
      
      print('✅ Successfully loaded ${entriesInRange.length} entries in date range');
      
      // Verify our test entry is in the range
      final testEntryInRange = entriesInRange.firstWhereOrNull(
        (entry) => entry.description.contains('Test Entry - Data Consistency Check')
      );
      
      if (testEntryInRange != null) {
        print('✅ Test entry found in date range query');
      } else {
        print('⚠️ Test entry not found in date range query');
      }
      
    } catch (e) {
      print('❌ Error with date range query: $e');
    }
    
    print('\n' + '='*60 + '\n');
    print('🎉 Journal Entry Data Consistency Test Complete!');
    print('');
    print('📋 Summary:');
    print('✅ Journal entry creation with Timestamp format');
    print('✅ Journal entry status updates with Timestamp format');
    print('✅ Journal entry loading with Timestamp parsing');
    print('✅ Date range queries with Timestamp comparisons');
    print('✅ No data type mismatches between save and load operations');
    print('');
    print('🚀 The data format consistency issue has been resolved!');
    print('');
    print('🔧 Technical Details:');
    print('- All save operations now use toFirestore() → Timestamp objects');
    print('- All load operations now use fromFirestore() → Timestamp parsing');
    print('- Status updates use Timestamp.fromDate() instead of millisecondsSinceEpoch');
    print('- Date range queries use Timestamp.fromDate() for comparisons');
    print('- Consistent data format throughout the entire CRUD cycle');
    
  } catch (e) {
    print('❌ Fatal error in test: $e');
  }
}
