class UserModel {
  final String uid;
  final String email;
  final String companyName;
  final String phoneNumber;
  // final String roleName;
  // final List<String>? permissionIds;
  // final DateTime createdAt;
  // final DateTime? lastLogin;
  // final bool isActive;

  UserModel({
    required this.uid,
    required this.email,
    required this.companyName,
    required this.phoneNumber,
    // required this.roleName,
    // this.permissionIds,
    // required this.createdAt,
    // this.lastLogin,
    // this.isActive = true,
  });

  // Role and permission related methods commented out
  /*
  // RoleModel? get role => Roles.getById(phoneNumber);

  bool hasPermission(Permission permission) {
    if (permissionIds != null && permissionIds!.contains(permission.id)) {
      return true;
    }
    final userRole = role;
    return userRole?.hasPermission(permission) ?? false;
  }

  bool hasAllPermissions(List<Permission> permissions) {
    return permissions.every((permission) => hasPermission(permission));
  }

  bool hasAnyPermission(List<Permission> permissions) {
    return permissions.any((permission) => hasPermission(permission));
  }
  */

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      uid: json['uid'] ?? '',
      email: json['email'] ?? '',
      companyName: json['companyName'] ?? '',
      phoneNumber: json['phoneNumber'] ?? '',
      // roleName: json['roleName'] ?? '',
      // permissionIds: json['permissionIds'] != null
      //     ? List<String>.from(json['permissionIds'])
      //     : null,
      // createdAt: json['createdAt'] is DateTime
      //     ? (json['createdAt'] as DateTime)
      //     : DateTime.parse(json['createdAt'].toString()),
      // lastLogin: json['lastLogin'] != null
      //     ? (json['lastLogin'] is DateTime
      //         ? (json['lastLogin'] as DateTime)
      //         : DateTime.parse(json['lastLogin'].toString()))
      //     : null,
      // isActive: json['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'uid': uid,
      'email': email,
      'companyName': companyName,
      'phoneNumber': phoneNumber,
      // 'roleName': roleName,
      // 'permissionIds': permissionIds,
      // 'createdAt': createdAt,
      // 'lastLogin': lastLogin,
      // 'isActive': isActive,
    };
  }

  UserModel copyWith({
    String? uid,
    String? email,
    String? companyName,
    String? phoneNumber,
    // String? roleName,
    // List<String>? permissionIds,
    DateTime? createdAt,
    DateTime? lastLogin,
    // bool? isActive,
  }) {
    return UserModel(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      companyName: companyName ?? this.companyName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      // roleName: roleName ?? this.roleName,
      // permissionIds: permissionIds ?? this.permissionIds,
      // createdAt: createdAt ?? this.createdAt,
      // lastLogin: lastLogin ?? this.lastLogin,
      // isActive: isActive ?? this.isActive,
    );
  }
}
