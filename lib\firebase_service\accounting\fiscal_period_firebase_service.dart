import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../core/utils/app_constants/firebase/collection_names.dart';
import '../../models/finance/fiscal_period_model.dart';

class FiscalPeriodFirebaseService {
  late FirebaseFirestore _firestore;

  // Get current user's UID or return 'anonymous' if not authenticated
  String get _uid => FirebaseAuth.instance.currentUser?.uid ?? 'anonymous';

  FiscalPeriodFirebaseService() {
    _firestore = FirebaseFirestore.instance;
  }

  /// Create a new fiscal year
  Future<void> createFiscalYear(FiscalYearModel fiscalYear) async {
    log('Creating fiscal year: ${fiscalYear.yearName}');
    try {
      final yearRef = _firestore
          .collection(AppCollection.fiscalYearsCollection)
          .doc();
      final yearId = yearRef.id;

      final yearData = fiscalYear.toJson();
      yearData['id'] = yearId;
      yearData['uid'] = _uid;

      await yearRef.set(yearData);
      log('Successfully created fiscal year: $yearId');
    } catch (e) {
      log('Error creating fiscal year: $e');
      rethrow;
    }
  }

  /// Get all fiscal years for the current user
  Future<List<FiscalYearModel>> getFiscalYears() async {
    log('Fetching fiscal years from Firestore');
    try {
      final snapshot = await _firestore
          .collection(AppCollection.fiscalYearsCollection)
          .where('uid', isEqualTo: _uid)
          .orderBy('startDate', descending: true)
          .get();

      final years = snapshot.docs
          .map((doc) => FiscalYearModel.fromJson(doc.data()))
          .toList();

      log('Successfully fetched ${years.length} fiscal years');
      return years;
    } catch (e) {
      log('Error fetching fiscal years: $e');
      rethrow;
    }
  }

  /// Get active fiscal year
  Future<FiscalYearModel?> getActiveFiscalYear() async {
    log('Fetching active fiscal year');
    try {
      final snapshot = await _firestore
          .collection(AppCollection.fiscalYearsCollection)
          .where('uid', isEqualTo: _uid)
          .where('isActive', isEqualTo: true)
          .limit(1)
          .get();

      if (snapshot.docs.isEmpty) {
        log('No active fiscal year found');
        return null;
      }

      final year = FiscalYearModel.fromJson(snapshot.docs.first.data());
      log('Successfully fetched active fiscal year: ${year.yearName}');
      return year;
    } catch (e) {
      log('Error fetching active fiscal year: $e');
      rethrow;
    }
  }

  /// Update fiscal year
  Future<void> updateFiscalYear(FiscalYearModel fiscalYear) async {
    log('Updating fiscal year: ${fiscalYear.yearName}');
    try {
      final yearData = fiscalYear.copyWith(
        updatedAt: DateTime.now(),
        uid: _uid,
      ).toJson();

      await _firestore
          .collection(AppCollection.fiscalYearsCollection)
          .doc(fiscalYear.id)
          .update(yearData);

      log('Successfully updated fiscal year: ${fiscalYear.id}');
    } catch (e) {
      log('Error updating fiscal year: $e');
      rethrow;
    }
  }

  /// Set active fiscal year (deactivates others)
  Future<void> setActiveFiscalYear(String fiscalYearId) async {
    log('Setting active fiscal year: $fiscalYearId');
    try {
      final batch = _firestore.batch();

      // Deactivate all fiscal years for this user
      final allYearsSnapshot = await _firestore
          .collection(AppCollection.fiscalYearsCollection)
          .where('uid', isEqualTo: _uid)
          .get();

      for (final doc in allYearsSnapshot.docs) {
        batch.update(doc.reference, {
          'isActive': false,
          'updatedAt': DateTime.now().millisecondsSinceEpoch,
        });
      }

      // Activate the selected fiscal year
      final selectedYearRef = _firestore
          .collection(AppCollection.fiscalYearsCollection)
          .doc(fiscalYearId);

      batch.update(selectedYearRef, {
        'isActive': true,
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      });

      await batch.commit();
      log('Successfully set active fiscal year: $fiscalYearId');
    } catch (e) {
      log('Error setting active fiscal year: $e');
      rethrow;
    }
  }

  /// Create a new fiscal period
  Future<void> createFiscalPeriod(FiscalPeriodModel fiscalPeriod) async {
    log('Creating fiscal period: ${fiscalPeriod.periodName}');
    try {
      final periodRef = _firestore
          .collection(AppCollection.fiscalPeriodsCollection)
          .doc();
      final periodId = periodRef.id;

      final periodData = fiscalPeriod.toJson();
      periodData['id'] = periodId;
      periodData['uid'] = _uid;

      await periodRef.set(periodData);
      log('Successfully created fiscal period: $periodId');
    } catch (e) {
      log('Error creating fiscal period: $e');
      rethrow;
    }
  }

  /// Get fiscal periods for a fiscal year
  Future<List<FiscalPeriodModel>> getFiscalPeriods(String fiscalYearId) async {
    log('Fetching fiscal periods for year: $fiscalYearId');
    try {
      final snapshot = await _firestore
          .collection(AppCollection.fiscalPeriodsCollection)
          .where('uid', isEqualTo: _uid)
          .where('fiscalYearId', isEqualTo: fiscalYearId)
          .orderBy('startDate')
          .get();

      final periods = snapshot.docs
          .map((doc) => FiscalPeriodModel.fromJson(doc.data()))
          .toList();

      log('Successfully fetched ${periods.length} fiscal periods');
      return periods;
    } catch (e) {
      log('Error fetching fiscal periods: $e');
      rethrow;
    }
  }

  /// Get current fiscal period
  Future<FiscalPeriodModel?> getCurrentFiscalPeriod() async {
    log('Fetching current fiscal period');
    try {
      final now = DateTime.now();
      final snapshot = await _firestore
          .collection(AppCollection.fiscalPeriodsCollection)
          .where('uid', isEqualTo: _uid)
          .where('startDate', isLessThanOrEqualTo: now.millisecondsSinceEpoch)
          .where('endDate', isGreaterThanOrEqualTo: now.millisecondsSinceEpoch)
          .where('status', isEqualTo: FiscalPeriodStatus.open.name)
          .limit(1)
          .get();

      if (snapshot.docs.isEmpty) {
        log('No current fiscal period found');
        return null;
      }

      final period = FiscalPeriodModel.fromJson(snapshot.docs.first.data());
      log('Successfully fetched current fiscal period: ${period.periodName}');
      return period;
    } catch (e) {
      log('Error fetching current fiscal period: $e');
      rethrow;
    }
  }

  /// Update fiscal period
  Future<void> updateFiscalPeriod(FiscalPeriodModel fiscalPeriod) async {
    log('Updating fiscal period: ${fiscalPeriod.periodName}');
    try {
      final periodData = fiscalPeriod.copyWith(
        updatedAt: DateTime.now(),
        uid: _uid,
      ).toJson();

      await _firestore
          .collection(AppCollection.fiscalPeriodsCollection)
          .doc(fiscalPeriod.id)
          .update(periodData);

      log('Successfully updated fiscal period: ${fiscalPeriod.id}');
    } catch (e) {
      log('Error updating fiscal period: $e');
      rethrow;
    }
  }

  /// Close fiscal period
  Future<void> closeFiscalPeriod(String periodId, String closedBy) async {
    log('Closing fiscal period: $periodId');
    try {
      await _firestore
          .collection(AppCollection.fiscalPeriodsCollection)
          .doc(periodId)
          .update({
        'status': FiscalPeriodStatus.closed.name,
        'closedDate': DateTime.now().millisecondsSinceEpoch,
        'closedBy': closedBy,
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      });

      log('Successfully closed fiscal period: $periodId');
    } catch (e) {
      log('Error closing fiscal period: $e');
      rethrow;
    }
  }

  /// Reopen fiscal period
  Future<void> reopenFiscalPeriod(String periodId) async {
    log('Reopening fiscal period: $periodId');
    try {
      await _firestore
          .collection(AppCollection.fiscalPeriodsCollection)
          .doc(periodId)
          .update({
        'status': FiscalPeriodStatus.open.name,
        'closedDate': null,
        'closedBy': null,
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      });

      log('Successfully reopened fiscal period: $periodId');
    } catch (e) {
      log('Error reopening fiscal period: $e');
      rethrow;
    }
  }

  /// Stream to listen for real-time updates to fiscal years
  Stream<List<FiscalYearModel>> listenToFiscalYears() {
    try {
      return _firestore
          .collection(AppCollection.fiscalYearsCollection)
          .where('uid', isEqualTo: _uid)
          .orderBy('startDate', descending: true)
          .snapshots()
          .map((snapshot) => snapshot.docs
              .map((doc) => FiscalYearModel.fromJson(doc.data()))
              .toList());
    } catch (e) {
      log('Error listening to fiscal years: $e', error: e);
      return Stream.value([]);
    }
  }

  /// Stream to listen for real-time updates to fiscal periods
  Stream<List<FiscalPeriodModel>> listenToFiscalPeriods(String fiscalYearId) {
    try {
      return _firestore
          .collection(AppCollection.fiscalPeriodsCollection)
          .where('uid', isEqualTo: _uid)
          .where('fiscalYearId', isEqualTo: fiscalYearId)
          .orderBy('startDate')
          .snapshots()
          .map((snapshot) => snapshot.docs
              .map((doc) => FiscalPeriodModel.fromJson(doc.data()))
              .toList());
    } catch (e) {
      log('Error listening to fiscal periods: $e', error: e);
      return Stream.value([]);
    }
  }
}
