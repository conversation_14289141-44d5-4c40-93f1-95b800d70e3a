import 'dart:async';
import 'dart:developer';
import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:intl/intl.dart';
import '../../repositories/financial_dashboard_repository.dart';
import '../../../../../models/accounting/financial_dashboard_models.dart';
import '../../../../../core/utils/snackbar_utils.dart';
import '../../../../../features/home/<USER>/drawer_controllers.dart';
import '../../../../../features/accounting/chart_of_accounts/presentation/views/chart_of_accounts_view.dart';

/// GetX Controller for Financial Dashboard management
class FinancialDashboardController extends GetxController {
  final FinancialDashboardRepository _repository =
      FinancialDashboardRepository();

  // Current user
  String get uid => FirebaseAuth.instance.currentUser?.uid ?? '';

  // Loading states
  var isLoading = false.obs;
  var isRefreshing = false.obs;

  // Dashboard data
  var financialSummary = Rxn<FinancialSummaryModel>();
  var accountBalances = <AccountBalanceModel>[].obs;
  var recentTransactions = <DashboardTransactionModel>[].obs;
  var cashFlowData = <CashFlowDataModel>[].obs;
  var expenseBreakdown = <ExpenseBreakdownModel>[].obs;

  // Settings
  var autoRefresh = false.obs;
  var showInactiveAccounts = false.obs;
  var startDate = DateTime.now().subtract(const Duration(days: 30)).obs;
  var endDate = DateTime.now().obs;

  // Auto refresh timer
  Timer? _autoRefreshTimer;

  // Computed properties
  String get dateRangeText {
    final formatter = DateFormat('MMM dd, yyyy');
    return '${formatter.format(startDate.value)} - ${formatter.format(endDate.value)}';
  }

  @override
  void onInit() {
    super.onInit();
    log('FinancialDashboardController initialized');
    loadDashboardData();
  }

  @override
  void onClose() {
    _autoRefreshTimer?.cancel();
    super.onClose();
  }

  /// Load all dashboard data
  Future<void> loadDashboardData() async {
    if (uid.isEmpty) {
      log('No user authenticated');
      return;
    }

    try {
      isLoading.value = true;
      log('Loading dashboard data for user: $uid');

      // Load all data concurrently
      await Future.wait([
        _loadFinancialSummary(),
        _loadAccountBalances(),
        _loadRecentTransactions(),
        _loadCashFlowData(),
        _loadExpenseBreakdown(),
      ]);

      log('Dashboard data loaded successfully');
    } catch (e) {
      log('Error loading dashboard data: $e');
      SnackbarUtils.showError('Failed to load dashboard data', e.toString());
    } finally {
      isLoading.value = false;
    }
  }

  /// Refresh dashboard data
  Future<void> refreshDashboard() async {
    if (uid.isEmpty) return;

    try {
      isRefreshing.value = true;
      log('Refreshing dashboard data');
      await loadDashboardData();
      SnackbarUtils.showSuccess(
          'Dashboard refreshed', 'Data updated successfully');
    } catch (e) {
      log('Error refreshing dashboard: $e');
      SnackbarUtils.showError('Refresh failed', e.toString());
    } finally {
      isRefreshing.value = false;
    }
  }

  /// Load financial summary
  Future<void> _loadFinancialSummary() async {
    final result = await _repository.getFinancialSummary(
      uid,
      startDate.value,
      endDate.value,
    );

    result.fold(
      (failure) {
        log('Failed to load financial summary: ${failure.message}');
      },
      (summary) {
        financialSummary.value = summary;
        log('Financial summary loaded: ${summary.totalRevenue}');
      },
    );
  }

  /// Load account balances
  Future<void> _loadAccountBalances() async {
    final result = await _repository.getAccountBalances(
      uid,
      showInactiveAccounts.value,
    );

    result.fold(
      (failure) {
        log('Failed to load account balances: ${failure.message}');
      },
      (balances) {
        accountBalances.value = balances;
        log('Account balances loaded: ${balances.length} accounts');
      },
    );
  }

  /// Load recent transactions
  Future<void> _loadRecentTransactions() async {
    final result = await _repository.getRecentTransactions(uid, 10);

    result.fold(
      (failure) {
        log('Failed to load recent transactions: ${failure.message}');
      },
      (transactions) {
        recentTransactions.value = transactions;
        log('Recent transactions loaded: ${transactions.length} transactions');
      },
    );
  }

  /// Load cash flow data
  Future<void> _loadCashFlowData() async {
    final result = await _repository.getCashFlowData(
      uid,
      startDate.value,
      endDate.value,
    );

    result.fold(
      (failure) {
        log('Failed to load cash flow data: ${failure.message}');
      },
      (data) {
        cashFlowData.value = data;
        log('Cash flow data loaded: ${data.length} data points');
      },
    );
  }

  /// Load expense breakdown
  Future<void> _loadExpenseBreakdown() async {
    final result = await _repository.getExpenseBreakdown(
      uid,
      startDate.value,
      endDate.value,
    );

    result.fold(
      (failure) {
        log('Failed to load expense breakdown: ${failure.message}');
      },
      (breakdown) {
        expenseBreakdown.value = breakdown;
        log('Expense breakdown loaded: ${breakdown.length} categories');
      },
    );
  }

  /// Toggle auto refresh
  void toggleAutoRefresh(bool value) {
    autoRefresh.value = value;

    if (value) {
      _startAutoRefresh();
    } else {
      _stopAutoRefresh();
    }

    log('Auto refresh ${value ? 'enabled' : 'disabled'}');
  }

  /// Toggle show inactive accounts
  void toggleShowInactiveAccounts(bool value) {
    showInactiveAccounts.value = value;
    _loadAccountBalances(); // Reload balances with new setting
    log('Show inactive accounts: $value');
  }

  /// Update date range
  void updateDateRange(DateTime start, DateTime end) {
    startDate.value = start;
    endDate.value = end;

    // Reload data with new date range
    _loadFinancialSummary();
    _loadCashFlowData();
    _loadExpenseBreakdown();

    log('Date range updated: $dateRangeText');
  }

  /// Start auto refresh timer
  void _startAutoRefresh() {
    _autoRefreshTimer?.cancel();
    _autoRefreshTimer = Timer.periodic(
      const Duration(minutes: 5),
      (_) => refreshDashboard(),
    );
  }

  /// Stop auto refresh timer
  void _stopAutoRefresh() {
    _autoRefreshTimer?.cancel();
    _autoRefreshTimer = null;
  }

  /// Navigate to specific financial report
  void navigateToReport(String reportType) {
    switch (reportType.toLowerCase()) {
      case 'trial_balance':
        Get.toNamed('/accounting/trial-balance');
        break;
      case 'profit_loss':
        Get.toNamed('/accounting/profit-loss');
        break;
      case 'balance_sheet':
        Get.toNamed('/accounting/balance-sheet');
        break;
      case 'cash_flow_statement':
        Get.toNamed('/accounting/cash-flow-statement');
        break;
      case 'journal_entries':
        Get.toNamed('/accounting/journal-entries');
        break;
      case 'chart_of_accounts':
        // Use drawer navigation instead of route navigation
        _navigateToChartOfAccounts();
        break;
      case 'fiscal_periods':
        Get.toNamed('/accounting/fiscal-periods');
        break;
      default:
        log('Unknown report type: $reportType');
    }
  }

  /// Navigate to Chart of Accounts using drawer system
  void _navigateToChartOfAccounts() {
    try {
      // Get the main drawer controller
      final mainDrawerController = Get.find<MainDrawerController>();

      // Navigate to Chart of Accounts using the drawer system
      mainDrawerController.updateSelectedScreen(
        const ChartOfAccountsView(),
        'finance/chart_of_accounts',
      );

      log('Navigated to Chart of Accounts via drawer system');
    } catch (e) {
      log('Error navigating to Chart of Accounts: $e');
      // Show error message instead of fallback route since route no longer exists
      SnackbarUtils.showError(
        'Navigation Error',
        'Unable to navigate to Chart of Accounts. Please try again.',
      );
    }
  }

  /// Navigate to account details
  void navigateToAccountDetails(String accountId) {
    Get.toNamed('/accounting/account-details/$accountId');
  }

  /// Navigate to transaction details
  void navigateToTransactionDetails(String transactionId) {
    Get.toNamed('/accounting/transaction-details/$transactionId');
  }
}
