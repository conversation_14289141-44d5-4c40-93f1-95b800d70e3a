import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/models/finance/fuel_card_model.dart';
import 'package:logestics/features/finance/fuel_cards/repositories/fuel_card_repository.dart';

class GetFuelCardsUseCase {
  final FuelCardRepository repository;

  GetFuelCardsUseCase(this.repository);

  Future<Either<FailureObj, List<FuelCardModel>>> call() async {
    try {
      return await repository.getFuelCards();
    } catch (e) {
      return Left(
        FailureObj(
          code: 'unexpected-error',
          message: 'An unexpected error occurred: $e',
        ),
      );
    }
  }
}
