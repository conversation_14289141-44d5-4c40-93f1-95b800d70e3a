enum RoleType {
  admin('admin'),
  manager('manager'),
  standard('standard'),
  viewer('viewer');

  final String value;
  const RoleType(this.value);
}

class RoleModel {
  final String id;
  final String name;
  final String description;
  final Map<String, bool> permissions;

  RoleModel({
    required this.id,
    required this.name,
    required this.description,
    required this.permissions,
  });

  factory RoleModel.fromJson(Map<String, dynamic> json) {
    return RoleModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      permissions: Map<String, bool>.from(json['permissions'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'permissions': permissions,
    };
  }

  // Get predefined roles
  static RoleModel getAdminRole(String id) {
    return RoleModel(
      id: id,
      name: RoleType.admin.value,
      description: 'Full access to all features',
      permissions: {
        'locations_read': true,
        'locations_write': true,
        'invoices_read': true,
        'invoices_write': true,
        'vouchers_read': true,
        'vouchers_write': true,
        'finance_read': true,
        'finance_write': true,
        'users_read': true,
        'users_write': true,
        'loans_read': true,
        'loans_write': true,
      },
    );
  }

  static RoleModel getManagerRole(String id) {
    return RoleModel(
      id: id,
      name: RoleType.manager.value,
      description: 'Can manage most areas except user administration',
      permissions: {
        'locations_read': true,
        'locations_write': true,
        'invoices_read': true,
        'invoices_write': true,
        'vouchers_read': true,
        'vouchers_write': true,
        'finance_read': true,
        'finance_write': true,
        'users_read': true,
        'users_write': false,
        'loans_read': true,
        'loans_write': true,
      },
    );
  }

  static RoleModel getStandardRole(String id) {
    return RoleModel(
      id: id,
      name: RoleType.standard.value,
      description: 'Standard access for daily operations',
      permissions: {
        'locations_read': true,
        'locations_write': true,
        'invoices_read': true,
        'invoices_write': true,
        'vouchers_read': true,
        'vouchers_write': true,
        'finance_read': true,
        'finance_write': false,
        'users_read': false,
        'users_write': false,
        'loans_read': true,
        'loans_write': false,
      },
    );
  }

  static RoleModel getViewerRole(String id) {
    return RoleModel(
      id: id,
      name: RoleType.viewer.value,
      description: 'Read-only access to all areas',
      permissions: {
        'locations_read': true,
        'locations_write': false,
        'invoices_read': true,
        'invoices_write': false,
        'vouchers_read': true,
        'vouchers_write': false,
        'finance_read': true,
        'finance_write': false,
        'users_read': false,
        'users_write': false,
        'loans_read': true,
        'loans_write': false,
      },
    );
  }

  bool hasPermission(String permission) {
    return permissions[permission] ?? false;
  }

  // Create a copy with updated fields
  RoleModel copyWith({
    String? id,
    String? name,
    String? description,
    Map<String, bool>? permissions,
  }) {
    return RoleModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      permissions: permissions ?? Map.from(this.permissions),
    );
  }
}
