import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';
import 'package:logestics/core/utils/app_constants/colors/app_colors.dart';
import '../controllers/dashboard_data_controller.dart';

class TotalInvoicesWidget extends StatelessWidget {
  const TotalInvoicesWidget({super.key});

  @override
  Widget build(BuildContext context) {
    notifier = Provider.of(context, listen: true);

    log('TotalInvoicesWidget: Building widget');

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: notifier.getBgColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total Invoices',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: notifier.text,
                ),
              ),
              Icon(
                Icons.receipt_long,
                color: notifier.text.withValues(alpha: 0.6),
                size: 24,
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Total Count
          GetBuilder<DashboardDataController>(
            builder: (controller) {
              log('TotalInvoicesWidget: GetBuilder rebuilding');
              log('TotalInvoicesWidget: Loading state: ${controller.isLoadingInvoices.value}');
              log('TotalInvoicesWidget: Error state: ${controller.invoiceError.value}');
              log('TotalInvoicesWidget: Total invoices: ${controller.totalInvoices.value}');

              if (controller.isLoadingInvoices.value) {
                return const Center(
                  child: CircularProgressIndicator(),
                );
              }

              if (controller.invoiceError.value.isNotEmpty) {
                return Center(
                  child: Text(
                    'Error: ${controller.invoiceError.value}',
                    style: TextStyle(
                      color: Colors.red,
                      fontSize: 14,
                    ),
                  ),
                );
              }

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Total number
                  Text(
                    '${controller.totalInvoices.value}',
                    style: TextStyle(
                      fontSize: 36,
                      fontWeight: FontWeight.bold,
                      color: notifier.text,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Total Invoices',
                    style: TextStyle(
                      fontSize: 14,
                      color: notifier.text.withValues(alpha: 0.7),
                    ),
                  ),
                  const SizedBox(height: 20),

                  // Status breakdown
                  _buildStatusBreakdown(controller),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildStatusBreakdown(DashboardDataController controller) {
    return Column(
      children: [
        // Paid invoices
        _buildStatusItem(
          controller,
          'Paid',
          controller.paidInvoices.value,
          notifier.ligreenColor,
          Colors.green,
          () => _navigateToInvoices('Completed'),
        ),
        const SizedBox(height: 12),

        // Unpaid invoices
        _buildStatusItem(
          controller,
          'Unpaid',
          controller.unpaidInvoices.value,
          notifier.liyellowColor,
          Colors.orange,
          () => _navigateToInvoices('In Progress'),
        ),
        const SizedBox(height: 12),

        // Overdue invoices
        _buildStatusItem(
          controller,
          'Overdue',
          controller.overdueInvoices.value,
          notifier.liredColor,
          Colors.red,
          () => _navigateToInvoices('Overdue'),
        ),
      ],
    );
  }

  Widget _buildStatusItem(
    DashboardDataController controller,
    String label,
    int count,
    Color backgroundColor,
    Color textColor,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: textColor.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: textColor,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: textColor,
                  ),
                ),
              ],
            ),
            Row(
              children: [
                Text(
                  '$count',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: textColor,
                  ),
                ),
                const SizedBox(width: 8),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 12,
                  color: textColor.withValues(alpha: 0.7),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToInvoices(String status) {
    // Navigate to the main home page where invoices are managed
    // The application uses a drawer-based navigation system
    Get.offAllNamed('/home');

    // Show a snackbar to inform user about the status filter
    Get.snackbar(
      'Invoice Filter',
      'Showing $status invoices',
      backgroundColor: AppColors.primary,
      colorText: Colors.white,
      duration: const Duration(seconds: 2),
    );
  }
}
