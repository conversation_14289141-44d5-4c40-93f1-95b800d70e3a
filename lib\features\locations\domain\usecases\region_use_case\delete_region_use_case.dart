import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/features/locations/repositories/region_repository.dart';

class DeleteRegionUseCase {
  final RegionRepository _regionRepository;

  DeleteRegionUseCase(this._regionRepository);

  /// Executes the use case to delete an Region.
  Future<Either<FailureObj, SuccessObj>> call({
    required String regionId,
  }) async {
    // Validate inputs before calling the repository

    if (regionId.isEmpty) {
      return Left(FailureObj(
        code: 'invalid-Region',
        message: 'Region "regionId" must not be empty.',
      ));
    }

    // Call the repository
    return await _regionRepository.deleteRegion(
      regionId: regionId,
    );
  }
}
