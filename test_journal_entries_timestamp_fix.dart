import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logistics/firebase_service/accounting/journal_entry_firebase_service.dart';
import 'package:logistics/models/finance/journal_entry_model.dart';
import 'package:logistics/controllers/auth_controller.dart';

/// Test script to verify Journal Entries Timestamp parsing fix
/// 
/// This script tests:
/// 1. Journal Entries loading without Timestamp parsing errors
/// 2. Fallback query functionality with proper Timestamp handling
/// 3. Date range queries with Timestamp conversion
/// 4. Verification that entries display correctly in UI
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  print('🧪 Testing Journal Entries Timestamp Fix...\n');
  
  try {
    // Initialize auth controller (needed for uid)
    final authController = Get.put(AuthController());
    
    // Wait for auth initialization
    await Future.delayed(const Duration(seconds: 2));
    
    if (authController.currentUser.value == null) {
      print('❌ Error: User not authenticated. Please login first.');
      return;
    }
    
    print('✅ User authenticated: ${authController.currentUser.value!.email}');
    print('🏢 Company UID: ${authController.currentUser.value!.uid}\n');
    
    // Initialize Journal Entry Firebase Service
    final journalService = JournalEntryFirebaseService();
    
    // Test 1: Basic Journal Entries Loading
    print('📋 Test 1: Loading Journal Entries (with fallback)...');
    try {
      final entries = await journalService.getJournalEntries();
      print('✅ Successfully loaded ${entries.length} journal entries');
      
      if (entries.isNotEmpty) {
        final firstEntry = entries.first;
        print('📄 First entry: ${firstEntry.entryNumber}');
        print('📅 Entry date: ${firstEntry.entryDate}');
        print('📝 Description: ${firstEntry.description}');
        print('💰 Total debits: \$${firstEntry.totalDebits.toStringAsFixed(2)}');
        print('💰 Total credits: \$${firstEntry.totalCredits.toStringAsFixed(2)}');
        print('📊 Lines count: ${firstEntry.lines.length}');
        
        if (firstEntry.lines.isNotEmpty) {
          final firstLine = firstEntry.lines.first;
          print('   📌 First line: ${firstLine.accountName} - \$${firstLine.amount.toStringAsFixed(2)}');
        }
      } else {
        print('⚠️ No journal entries found in database');
      }
    } catch (e) {
      print('❌ Error loading journal entries: $e');
    }
    
    print('\n' + '='*60 + '\n');
    
    // Test 2: Date Range Query
    print('📅 Test 2: Date Range Query...');
    try {
      final endDate = DateTime.now();
      final startDate = endDate.subtract(const Duration(days: 30));
      
      print('🗓️ Date range: ${startDate.toLocal().toString().split(' ')[0]} to ${endDate.toLocal().toString().split(' ')[0]}');
      
      final entriesInRange = await journalService.getJournalEntriesByDateRange(
        startDate, 
        endDate
      );
      
      print('✅ Successfully loaded ${entriesInRange.length} entries in date range');
      
      if (entriesInRange.isNotEmpty) {
        print('📊 Entries by status:');
        final statusCounts = <JournalEntryStatus, int>{};
        for (final entry in entriesInRange) {
          statusCounts[entry.status] = (statusCounts[entry.status] ?? 0) + 1;
        }
        
        for (final status in statusCounts.keys) {
          print('   ${status.displayName}: ${statusCounts[status]} entries');
        }
      }
    } catch (e) {
      print('❌ Error with date range query: $e');
    }
    
    print('\n' + '='*60 + '\n');
    
    // Test 3: Stream Listening
    print('🔄 Test 3: Real-time Stream Listening...');
    try {
      print('📡 Setting up real-time listener...');
      
      final streamSubscription = journalService.listenToJournalEntries().listen(
        (entries) {
          print('🔄 Stream update: ${entries.length} entries received');
          if (entries.isNotEmpty) {
            final latestEntry = entries.first;
            print('   📄 Latest: ${latestEntry.entryNumber} - ${latestEntry.description}');
          }
        },
        onError: (error) {
          print('❌ Stream error: $error');
        },
      );
      
      // Listen for 5 seconds
      await Future.delayed(const Duration(seconds: 5));
      
      await streamSubscription.cancel();
      print('✅ Stream test completed');
    } catch (e) {
      print('❌ Error with stream listening: $e');
    }
    
    print('\n' + '='*60 + '\n');
    
    // Test 4: Timestamp Parsing Verification
    print('🕐 Test 4: Timestamp Parsing Verification...');
    try {
      final entries = await journalService.getJournalEntries();
      
      if (entries.isNotEmpty) {
        print('✅ All entries parsed successfully without Timestamp errors');
        
        // Check for proper date parsing
        var validDates = 0;
        var invalidDates = 0;
        
        for (final entry in entries) {
          if (entry.entryDate.year > 2020 && entry.entryDate.year < 2030) {
            validDates++;
          } else {
            invalidDates++;
            print('⚠️ Invalid date found: ${entry.entryDate} in entry ${entry.entryNumber}');
          }
          
          // Check line dates
          for (final line in entry.lines) {
            if (line.createdAt.year < 2020 || line.createdAt.year > 2030) {
              print('⚠️ Invalid line date: ${line.createdAt} in entry ${entry.entryNumber}');
            }
          }
        }
        
        print('📊 Date validation: $validDates valid, $invalidDates invalid');
      }
    } catch (e) {
      print('❌ Error in timestamp verification: $e');
    }
    
    print('\n' + '='*60 + '\n');
    print('🎉 Journal Entries Timestamp Fix Test Complete!');
    print('');
    print('📋 Summary:');
    print('✅ Timestamp parsing errors should be resolved');
    print('✅ Fallback queries should work properly');
    print('✅ Date range filtering should function correctly');
    print('✅ Real-time streams should work without parsing errors');
    print('');
    print('🚀 Next Steps:');
    print('1. Test Journal Entries screen in the app');
    print('2. Create new journal entries to verify they save correctly');
    print('3. Create Firebase indexes using FIREBASE_INDEX_CREATION_CHECKLIST.md');
    print('4. Run comprehensive accounting system tests');
    
  } catch (e) {
    print('❌ Fatal error in test: $e');
  }
}
