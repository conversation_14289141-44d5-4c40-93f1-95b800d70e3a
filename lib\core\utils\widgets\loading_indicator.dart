import 'package:flutter/material.dart';
import 'package:logestics/core/utils/app_constants/colors/app_colors.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';

/// A reusable loading indicator widget that follows Material Design guidelines
/// and can be used across the application for various loading states.
class LoadingIndicator extends StatelessWidget {
  /// The type of loading indicator to display
  final LoadingIndicatorType type;

  /// Custom message to display below the loading indicator
  final String? message;

  /// Size of the loading indicator
  final LoadingIndicatorSize size;

  /// Color of the loading indicator (defaults to primary color)
  final Color? color;

  /// Whether to show the loading indicator with a semi-transparent overlay
  final bool withOverlay;

  /// Background color for overlay (when withOverlay is true)
  final Color? overlayColor;

  /// Whether to center the loading indicator
  final bool centered;

  const LoadingIndicator({
    super.key,
    this.type = LoadingIndicatorType.circular,
    this.message,
    this.size = LoadingIndicatorSize.medium,
    this.color,
    this.withOverlay = false,
    this.overlayColor,
    this.centered = true,
  });

  /// Factory constructor for a simple circular loading indicator
  const LoadingIndicator.circular({
    super.key,
    this.message,
    this.size = LoadingIndicatorSize.medium,
    this.color,
    this.centered = true,
  })  : type = LoadingIndicatorType.circular,
        withOverlay = false,
        overlayColor = null;

  /// Factory constructor for a linear loading indicator
  const LoadingIndicator.linear({
    super.key,
    this.message,
    this.size = LoadingIndicatorSize.medium,
    this.color,
    this.centered = true,
  })  : type = LoadingIndicatorType.linear,
        withOverlay = false,
        overlayColor = null;

  /// Factory constructor for a loading indicator with overlay
  const LoadingIndicator.overlay({
    super.key,
    this.type = LoadingIndicatorType.circular,
    this.message,
    this.size = LoadingIndicatorSize.medium,
    this.color,
    this.overlayColor,
  })  : withOverlay = true,
        centered = true;

  @override
  Widget build(BuildContext context) {
    final indicatorColor = color ?? AppColors.primary;
    final Widget indicator = _buildIndicator(indicatorColor);

    Widget content = Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        indicator,
        if (message != null) ...[
          const SizedBox(height: 16),
          Text(
            message!,
            style: AppTextStyles.subtitleStyle.copyWith(
              color: indicatorColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );

    if (centered) {
      content = Center(child: content);
    }

    if (withOverlay) {
      return Container(
        color: overlayColor ?? Colors.black.withValues(alpha: 0.3),
        child: content,
      );
    }

    return content;
  }

  Widget _buildIndicator(Color indicatorColor) {
    final double indicatorSize = _getIndicatorSize();

    switch (type) {
      case LoadingIndicatorType.circular:
        return SizedBox(
          width: indicatorSize,
          height: indicatorSize,
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(indicatorColor),
            strokeWidth: _getStrokeWidth(),
          ),
        );

      case LoadingIndicatorType.linear:
        return SizedBox(
          width: indicatorSize * 2, // Linear indicators are typically wider
          child: LinearProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(indicatorColor),
            backgroundColor: indicatorColor.withValues(alpha: 0.2),
          ),
        );
    }
  }

  double _getIndicatorSize() {
    switch (size) {
      case LoadingIndicatorSize.small:
        return 20.0;
      case LoadingIndicatorSize.medium:
        return 32.0;
      case LoadingIndicatorSize.large:
        return 48.0;
    }
  }

  double _getStrokeWidth() {
    switch (size) {
      case LoadingIndicatorSize.small:
        return 2.0;
      case LoadingIndicatorSize.medium:
        return 3.0;
      case LoadingIndicatorSize.large:
        return 4.0;
    }
  }
}

/// Types of loading indicators available
enum LoadingIndicatorType {
  /// Circular progress indicator (default)
  circular,

  /// Linear progress indicator
  linear,
}

/// Sizes available for loading indicators
enum LoadingIndicatorSize {
  /// Small size (20px)
  small,

  /// Medium size (32px) - default
  medium,

  /// Large size (48px)
  large,
}

/// A specialized loading indicator for data tables and lists
class DataLoadingIndicator extends StatelessWidget {
  final String? message;
  final double? height;

  const DataLoadingIndicator({
    super.key,
    this.message,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height ?? 200,
      padding: const EdgeInsets.all(24),
      child: LoadingIndicator.circular(
        message: message ?? 'Loading data...',
        size: LoadingIndicatorSize.medium,
      ),
    );
  }
}

/// A loading indicator specifically designed for invoice operations
class InvoiceLoadingIndicator extends StatelessWidget {
  final String? message;
  final bool isRefreshing;

  const InvoiceLoadingIndicator({
    super.key,
    this.message,
    this.isRefreshing = false,
  });

  @override
  Widget build(BuildContext context) {
    final defaultMessage =
        isRefreshing ? 'Refreshing invoices...' : 'Loading invoices...';

    return DataLoadingIndicator(
      message: message ?? defaultMessage,
      height: 150,
    );
  }
}
