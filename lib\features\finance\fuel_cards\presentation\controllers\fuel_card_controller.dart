import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/mixins/auto_refresh_mixin.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/features/finance/fuel_cards/repositories/fuel_card_repository.dart';
import 'package:logestics/features/finance/fuel_cards/usecases/add_fuel_rate_use_case.dart';
import 'package:logestics/features/finance/fuel_cards/usecases/create_fuel_card_use_case.dart';
import 'package:logestics/features/finance/fuel_cards/usecases/get_fuel_cards_use_case.dart';
import 'package:logestics/features/finance/fuel_cards/usecases/get_fuel_rate_history_use_case.dart';
import 'package:logestics/features/finance/fuel_cards/usecases/get_latest_fuel_rate_use_case.dart';
import 'package:logestics/models/finance/fuel_card_model.dart';
import 'package:logestics/models/finance/fuel_rate_model.dart';
import 'package:uuid/uuid.dart';

class FuelCardController extends GetxController with AutoRefreshMixin {
  final FuelCardRepository repository;
  final CreateFuelCardUseCase createFuelCardUseCase;
  final GetFuelCardsUseCase getFuelCardsUseCase;
  final AddFuelRateUseCase addFuelRateUseCase;
  final GetLatestFuelRateUseCase getLatestFuelRateUseCase;
  final GetFuelRateHistoryUseCase getFuelRateHistoryUseCase;

  FuelCardController({
    required this.repository,
    required this.createFuelCardUseCase,
    required this.getFuelCardsUseCase,
    required this.addFuelRateUseCase,
    required this.getLatestFuelRateUseCase,
    required this.getFuelRateHistoryUseCase,
  });

  // Observable lists of fuel cards and rates
  final fuelCards = <FuelCardModel>[].obs;
  final fuelRates = <FuelRateModel>[].obs;
  final rateHistory = <FuelRateModel>[].obs;

  // Loading state
  final isLoading = false.obs;
  final isRateHistoryLoading = false.obs;
  final error = ''.obs;

  // Form controllers for adding a new fuel card
  final cardNumberController = TextEditingController();
  final companyNameController = TextEditingController();
  final totalCapacityController = TextEditingController();
  final currentRateController = TextEditingController();

  // Form keys
  final fuelCardFormKey = GlobalKey<FormState>();
  final fuelRateFormKey = GlobalKey<FormState>();

  // Selected fuel card for operations
  final selectedFuelCard = Rxn<FuelCardModel>();

  /// Implementation of AutoRefreshMixin.refreshData
  @override
  Future<void> refreshData() async {
    await loadFuelCards();
  }

  @override
  void onClose() {
    cardNumberController.dispose();
    companyNameController.dispose();
    totalCapacityController.dispose();
    currentRateController.dispose();
    super.onClose();
  }

  // Load all fuel cards
  Future<void> loadFuelCards() async {
    isLoading.value = true;
    error.value = '';

    try {
      final result = await getFuelCardsUseCase();
      result.fold(
        (failure) {
          error.value = failure.message;
          log('Error loading fuel cards: ${failure.message}');
        },
        (cards) {
          fuelCards.value = cards;
          log('Loaded ${cards.length} fuel cards');

          // Extract unique company names and load the latest rates for each
          final companies =
              cards.map((card) => card.companyName).toSet().toList();
          loadLatestRates(companies);
        },
      );
    } catch (e) {
      error.value = 'An unexpected error occurred: $e';
      log('Unexpected error loading fuel cards: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Load latest rates for all companies
  Future<void> loadLatestRates(List<String> companies) async {
    try {
      final rates = <FuelRateModel>[];
      for (final company in companies) {
        final result = await getLatestFuelRateUseCase(company);
        result.fold(
          (failure) =>
              log('Error loading rate for $company: ${failure.message}'),
          (rate) => rates.add(rate),
        );
      }
      fuelRates.value = rates;
    } catch (e) {
      log('Error loading latest rates: $e');
    }
  }

  // Create a new fuel card
  Future<void> createFuelCard() async {
    if (!fuelCardFormKey.currentState!.validate()) {
      return;
    }

    isLoading.value = true;
    error.value = '';

    try {
      final double totalCapacity =
          double.tryParse(totalCapacityController.text) ?? 0;
      final double currentRate =
          double.tryParse(currentRateController.text) ?? 0;

      final fuelCard = FuelCardModel(
        id: const Uuid().v4(),
        cardNumber: cardNumberController.text,
        companyName: companyNameController.text,
        fuelStationName: 'Shell',
        totalCapacity: totalCapacity,
        remainingCapacity: totalCapacity, // Initially full
        currentRate: currentRate,
        createdAt: DateTime.now(),
      );

      final result = await createFuelCardUseCase(fuelCard);
      result.fold(
        (failure) {
          error.value = failure.message;
          SnackbarUtils.showError(AppStrings.error, failure.message);
        },
        (success) {
          // Add to local list and clear form
          fuelCards.add(fuelCard);

          // Add initial rate for this card
          final initialRate = FuelRateModel(
            id: const Uuid().v4(),
            companyName: fuelCard.companyName,
            rate: currentRate,
            effectiveDate: DateTime.now(),
            createdAt: DateTime.now(),
          );

          addFuelRateUseCase(initialRate);

          // Reload all fuel cards to ensure consistent UI state
          loadFuelCards();

          clearFuelCardForm();
          Get.back(); // Close dialog
          SnackbarUtils.showSuccess(AppStrings.success, success.message);
        },
      );
    } catch (e) {
      error.value = 'An unexpected error occurred: $e';
      SnackbarUtils.showError(
          AppStrings.error, 'An unexpected error occurred: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Add a new fuel rate
  Future<void> addFuelRate(String companyName, double rate) async {
    isLoading.value = true;
    error.value = '';

    try {
      final fuelRate = FuelRateModel(
        id: const Uuid().v4(),
        companyName: companyName,
        rate: rate,
        effectiveDate: DateTime.now(),
        createdAt: DateTime.now(),
      );

      final result = await addFuelRateUseCase(fuelRate);
      result.fold(
        (failure) {
          log(failure.message.toString());
          error.value = failure.message;
          SnackbarUtils.showError(AppStrings.error, failure.message);
        },
        (success) {
          // Update existing fuel cards with this company name
          for (var i = 0; i < fuelCards.length; i++) {
            if (fuelCards[i].companyName == companyName) {
              fuelCards[i] = fuelCards[i].copyWith(
                currentRate: rate,
                updatedAt: DateTime.now(),
              );
            }
          }

          // Update the fuel cards in Firebase to persist the new rate
          _updateFuelCardsInFirebase(companyName, rate);

          // Make sure the rates section is also updated
          final companies =
              fuelCards.map((card) => card.companyName).toSet().toList();
          loadLatestRates(companies);

          // Force refresh to ensure UI shows updated rates everywhere
          refreshData();

          SnackbarUtils.showSuccess(AppStrings.success, success.message);
        },
      );
    } catch (e) {
      error.value = 'An unexpected error occurred: $e';
      SnackbarUtils.showError(
          AppStrings.error, 'An unexpected error occurred: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Get the latest fuel rate for a company
  Future<double> getLatestFuelRate(String companyName) async {
    try {
      final result = await getLatestFuelRateUseCase(companyName);
      return result.fold(
        (failure) => 0.0, // Default value on failure
        (fuelRate) => fuelRate.rate,
      );
    } catch (e) {
      log('Error getting latest fuel rate: $e');
      return 0.0;
    }
  }

  // Get fuel rate history for a company
  Future<void> getFuelRateHistory(String companyName) async {
    try {
      isRateHistoryLoading.value = true;
      final result = await getFuelRateHistoryUseCase(companyName);

      result.fold(
        (failure) {
          log(failure.message.toString());
          error.value = failure.message;
          SnackbarUtils.showError(AppStrings.loadFailed,
              'Failed to load rate history: ${failure.message}');
          rateHistory.clear();
        },
        (rates) {
          // Sort rates by date (newest first)
          rates.sort((a, b) => b.effectiveDate.compareTo(a.effectiveDate));
          rateHistory.value = rates;
        },
      );
    } catch (e) {
      error.value = 'An unexpected error occurred: $e';
      SnackbarUtils.showError(AppStrings.error,
          'An unexpected error occurred while loading rate history');
      rateHistory.clear();
    } finally {
      isRateHistoryLoading.value = false;
    }
  }

  // Update a fuel card's remaining capacity
  Future<bool> updateFuelCardCapacity(String cardId, double usedLiters) async {
    try {
      // Find the card
      final index = fuelCards.indexWhere((card) => card.id == cardId);
      if (index == -1) {
        error.value = 'Fuel card not found';
        return false;
      }

      final card = fuelCards[index];

      // Check if there's enough capacity
      if (card.remainingCapacity < usedLiters) {
        error.value = 'Not enough fuel available on this card';
        return false;
      }

      // Update the card
      final updatedCard = card.copyWith(
        remainingCapacity: card.remainingCapacity - usedLiters,
        updatedAt: DateTime.now(),
      );

      // Update in database
      final result = await repository.updateFuelCard(updatedCard);

      return result.fold(
        (failure) {
          error.value = failure.message;
          return false;
        },
        (success) {
          // Update in local list
          fuelCards[index] = updatedCard;
          return true;
        },
      );
    } catch (e) {
      error.value = 'Failed to update fuel card: $e';
      return false;
    }
  }

  // Clear fuel card form
  void clearFuelCardForm() {
    cardNumberController.clear();
    companyNameController.clear();
    totalCapacityController.clear();
    currentRateController.clear();
  }

  // Form validation
  String? validateCardNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'Card number is required';
    }
    return null;
  }

  String? validateCompanyName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Company name is required';
    }
    return null;
  }

  String? validateTotalCapacity(String? value) {
    if (value == null || value.isEmpty) {
      return 'Total capacity is required';
    }
    if (double.tryParse(value) == null) {
      return 'Please enter a valid number';
    }
    if (double.parse(value) <= 0) {
      return 'Capacity must be greater than 0';
    }
    return null;
  }

  String? validateCurrentRate(String? value) {
    if (value == null || value.isEmpty) {
      return 'Current rate is required';
    }
    if (double.tryParse(value) == null) {
      return 'Please enter a valid number';
    }
    if (double.parse(value) <= 0) {
      return 'Rate must be greater than 0';
    }
    return null;
  }

  // Update a specific fuel card in the list
  void updateFuelCardInList(FuelCardModel updatedCard) {
    final index = fuelCards.indexWhere((card) => card.id == updatedCard.id);
    if (index != -1) {
      fuelCards[index] = updatedCard;
      update();
    } else {
      log('Warning: Tried to update a fuel card that was not in the list: ${updatedCard.id}');
    }
  }

  // Helper method to update fuel cards in Firebase with new rate
  Future<void> _updateFuelCardsInFirebase(
      String companyName, double newRate) async {
    try {
      for (var card in fuelCards) {
        if (card.companyName == companyName) {
          final updatedCard = card.copyWith(
            currentRate: newRate,
            updatedAt: DateTime.now(),
          );
          await repository.updateFuelCard(updatedCard);
        }
      }
      log('Updated fuel cards in Firebase with new rate for $companyName');
    } catch (e) {
      log('Error updating fuel cards in Firebase: $e');
    }
  }
}
