import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:either_dart/either.dart';
import '../../core/shared_services/failure_obj.dart';
import '../../core/shared_services/success_obj.dart';
import '../../core/utils/app_constants/firebase/collection_names.dart';
import '../../models/finance/chart_of_accounts_model.dart';
import '../../models/finance/journal_entry_model.dart';

/// Firebase service for General Ledger operations
class GeneralLedgerFirebaseService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Get current user's UID
  String get _uid => _auth.currentUser?.uid ?? 'anonymous';

  /// Get account balance for a specific account
  Future<Either<FailureObj, double>> getAccountBalance(String accountId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return Left(FailureObj(
          code: 'user-not-authenticated',
          message: 'User not authenticated',
        ));
      }

      log('Getting account balance for account: $accountId');

      // Get all journal entries for this account
      final journalEntriesQuery = await _firestore
          .collection(AppCollection.journalEntriesCollection)
          .where('uid', isEqualTo: user.uid)
          .where('status', isEqualTo: 'posted')
          .get();

      double balance = 0.0;

      for (final doc in journalEntriesQuery.docs) {
        final journalEntry = JournalEntryModel.fromFirestore(doc);

        // Calculate balance from debit and credit entries
        for (final entry in journalEntry.lines) {
          if (entry.accountId == accountId) {
            balance += entry.debitAmount - entry.creditAmount;
          }
        }
      }

      log('Account balance calculated: $balance');
      return Right(balance);
    } catch (e) {
      log('Error getting account balance: $e');
      return Left(FailureObj(
        code: 'account-balance-error',
        message: 'Failed to get account balance: $e',
      ));
    }
  }

  /// Get account transactions for a specific account
  Future<Either<FailureObj, List<JournalEntryModel>>> getAccountTransactions(
    String accountId, {
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return Left(FailureObj(
          code: 'user-not-authenticated',
          message: 'User not authenticated',
        ));
      }

      log('Getting account transactions for account: $accountId');

      Query query = _firestore
          .collection(AppCollection.journalEntriesCollection)
          .where('uid', isEqualTo: user.uid)
          .where('status', isEqualTo: 'posted');

      if (startDate != null) {
        query = query.where('entryDate',
            isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
      }

      if (endDate != null) {
        query = query.where('entryDate',
            isLessThanOrEqualTo: Timestamp.fromDate(endDate));
      }

      query = query.orderBy('entryDate', descending: true);

      if (limit != null) {
        query = query.limit(limit);
      }

      final snapshot = await query.get();
      final List<JournalEntryModel> accountTransactions = [];

      for (final doc in snapshot.docs) {
        final journalEntry = JournalEntryModel.fromFirestore(doc);

        // Check if this journal entry affects the specified account
        final hasAccountEntry =
            journalEntry.lines.any((entry) => entry.accountId == accountId);

        if (hasAccountEntry) {
          accountTransactions.add(journalEntry);
        }
      }

      log('Found ${accountTransactions.length} transactions for account');
      return Right(accountTransactions);
    } catch (e) {
      log('Error getting account transactions: $e');
      return Left(FailureObj(
        code: 'account-transactions-error',
        message: 'Failed to get account transactions: $e',
      ));
    }
  }

  /// Update account balance after journal entry posting
  Future<Either<FailureObj, SuccessObj>> updateAccountBalance(
    String accountId,
    double amount,
    bool isDebit,
  ) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return Left(FailureObj(
          code: 'user-not-authenticated',
          message: 'User not authenticated',
        ));
      }

      log('Updating account balance for account: $accountId, amount: $amount, isDebit: $isDebit');

      // Get current account
      final accountDoc = await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .where('uid', isEqualTo: user.uid)
          .where('id', isEqualTo: accountId)
          .limit(1)
          .get();

      if (accountDoc.docs.isEmpty) {
        return Left(FailureObj(
          code: 'account-not-found',
          message: 'Account not found',
        ));
      }

      final accountData = accountDoc.docs.first.data();
      final account = ChartOfAccountsModel.fromJson(accountData);

      // Calculate new balance based on account type and transaction type
      double newBalance = account.balance;

      // For asset, expense accounts: debit increases, credit decreases
      // For liability, equity, revenue accounts: credit increases, debit decreases
      if (_isDebitAccount(account.accountType)) {
        newBalance += isDebit ? amount : -amount;
      } else {
        newBalance += isDebit ? -amount : amount;
      }

      // Update the account balance
      await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .doc(accountDoc.docs.first.id)
          .update({'balance': newBalance});

      log('Account balance updated successfully');
      return Right(SuccessObj(message: 'Account balance updated successfully'));
    } catch (e) {
      log('Error updating account balance: $e');
      return Left(FailureObj(
        code: 'account-balance-update-error',
        message: 'Failed to update account balance: $e',
      ));
    }
  }

  /// Check if account type is a debit account (assets, expenses)
  bool _isDebitAccount(AccountType accountType) {
    switch (accountType.category) {
      case AccountCategory.assets:
      case AccountCategory.expenses:
        return true;
      case AccountCategory.liabilities:
      case AccountCategory.equity:
      case AccountCategory.revenue:
        return false;
    }
  }

  /// Get general ledger summary for all accounts
  Future<Either<FailureObj, Map<String, dynamic>>> getGeneralLedgerSummary({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return Left(FailureObj(
          code: 'user-not-authenticated',
          message: 'User not authenticated',
        ));
      }

      log('Getting general ledger summary');

      // Get all accounts
      final accountsSnapshot = await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .where('uid', isEqualTo: user.uid)
          .where('isActive', isEqualTo: true)
          .orderBy('accountNumber')
          .get();

      final Map<String, dynamic> summary = {
        'accounts': <Map<String, dynamic>>[],
        'totalDebits': 0.0,
        'totalCredits': 0.0,
        'totalAssets': 0.0,
        'totalLiabilities': 0.0,
        'totalEquity': 0.0,
        'totalRevenue': 0.0,
        'totalExpenses': 0.0,
      };

      for (final doc in accountsSnapshot.docs) {
        final account = ChartOfAccountsModel.fromJson(doc.data());

        // Get account balance
        final balanceResult = await getAccountBalance(account.id);
        final balance = balanceResult.fold(
          (failure) => account.balance, // Fallback to stored balance
          (balance) => balance,
        );

        final accountSummary = {
          'account': account,
          'balance': balance,
        };

        summary['accounts'].add(accountSummary);

        // Add to category totals
        switch (account.accountType.category) {
          case AccountCategory.assets:
            summary['totalAssets'] += balance;
            break;
          case AccountCategory.liabilities:
            summary['totalLiabilities'] += balance;
            break;
          case AccountCategory.equity:
            summary['totalEquity'] += balance;
            break;
          case AccountCategory.revenue:
            summary['totalRevenue'] += balance;
            break;
          case AccountCategory.expenses:
            summary['totalExpenses'] += balance;
            break;
        }
      }

      log('General ledger summary generated successfully');
      return Right(summary);
    } catch (e) {
      log('Error getting general ledger summary: $e');
      return Left(FailureObj(
        code: 'general-ledger-summary-error',
        message: 'Failed to get general ledger summary: $e',
      ));
    }
  }

  /// Create a journal entry (for integration services)
  Future<Either<FailureObj, String>> createJournalEntry(
      JournalEntryModel journalEntry) async {
    try {
      log('Creating journal entry: ${journalEntry.entryNumber}');

      final docRef = _firestore
          .collection(AppCollection.journalEntriesCollection)
          .doc(journalEntry.id);

      await docRef.set(journalEntry.toFirestore());

      log('Journal entry created successfully: ${journalEntry.id}');
      return Right(journalEntry.id);
    } catch (e) {
      log('Error creating journal entry: $e');
      return Left(FailureObj(
        code: 'journal-entry-creation-error',
        message: 'Failed to create journal entry',
      ));
    }
  }

  /// Get journal entries for a specific reference
  Future<Either<FailureObj, List<JournalEntryModel>>> getJournalEntries(
      String uid, String? referenceId, String? referenceType) async {
    try {
      log('Getting journal entries for reference: $referenceId');

      Query query = _firestore
          .collection(AppCollection.journalEntriesCollection)
          .where('uid', isEqualTo: uid);

      if (referenceId != null) {
        query = query.where('sourceTransactionId', isEqualTo: referenceId);
      }

      if (referenceType != null) {
        query = query.where('sourceTransactionType', isEqualTo: referenceType);
      }

      final snapshot = await query.get();
      final journalEntries = snapshot.docs
          .map((doc) => JournalEntryModel.fromFirestore(doc))
          .toList();

      log('Found ${journalEntries.length} journal entries');
      return Right(journalEntries);
    } catch (e) {
      log('Error getting journal entries: $e');
      return Left(FailureObj(
        code: 'journal-entries-fetch-error',
        message: 'Failed to get journal entries',
      ));
    }
  }

  /// Reverse a journal entry
  Future<Either<FailureObj, String>> reverseJournalEntry(
      String journalEntryId, String reversalReason, String createdBy) async {
    try {
      log('Reversing journal entry: $journalEntryId');

      // Get the original journal entry
      final docRef = _firestore
          .collection(AppCollection.journalEntriesCollection)
          .doc(journalEntryId);

      final doc = await docRef.get();
      if (!doc.exists) {
        return Left(FailureObj(
          code: 'journal-entry-not-found',
          message: 'Journal entry not found',
        ));
      }

      final originalEntry = JournalEntryModel.fromFirestore(doc);

      // Create reversal entry with opposite amounts
      final reversalLines = originalEntry.lines
          .map((line) => JournalEntryLineModel(
                id: _firestore.collection('temp').doc().id,
                journalEntryId: '',
                accountId: line.accountId,
                accountNumber: line.accountNumber,
                accountName: line.accountName,
                debitAmount: line.creditAmount, // Swap debit and credit
                creditAmount: line.debitAmount,
                description: 'Reversal: ${line.description}',
                referenceId: line.referenceId,
                referenceType: line.referenceType,
                createdAt: DateTime.now(),
              ))
          .toList();

      final reversalEntry = JournalEntryModel(
        id: _firestore.collection('temp').doc().id,
        entryNumber: 'REV-${originalEntry.entryNumber}',
        entryDate: DateTime.now(),
        description: 'Reversal: $reversalReason',
        entryType: JournalEntryType.reversal,
        status: JournalEntryStatus.posted,
        lines: reversalLines,
        totalDebits: originalEntry.totalCredits,
        totalCredits: originalEntry.totalDebits,
        referenceNumber: originalEntry.referenceNumber,
        sourceTransactionId: originalEntry.sourceTransactionId,
        sourceTransactionType: originalEntry.sourceTransactionType,
        reversalOfEntryId: originalEntry.id,
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: originalEntry.uid,
      );

      // Create the reversal entry
      final createResult = await createJournalEntry(reversalEntry);
      if (createResult.isLeft) {
        return createResult;
      }

      // Update original entry status to reversed
      await docRef.update({
        'status': JournalEntryStatus.reversed.name,
        'updatedAt': Timestamp.now(),
      });

      log('Journal entry reversed successfully');
      return Right(reversalEntry.id);
    } catch (e) {
      log('Error reversing journal entry: $e');
      return Left(FailureObj(
        code: 'journal-entry-reversal-error',
        message: 'Failed to reverse journal entry',
      ));
    }
  }
}
