import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/firebase_service/asset/asset_audit_firebase_service.dart';
import 'package:logestics/models/asset/asset_audit_model.dart';
import 'package:logestics/models/asset/asset_model.dart';

abstract class AssetAuditRepository {
  Future<Either<FailureObj, SuccessObj>> createAuditEntry(AssetAuditModel auditEntry);
  Future<Either<FailureObj, SuccessObj>> logAssetCreated(AssetModel asset, {String notes = ''});
  Future<Either<FailureObj, SuccessObj>> logAssetUpdated(AssetModel oldAsset, AssetModel newAsset, {String notes = ''});
  Future<Either<FailureObj, SuccessObj>> logAssetDeleted(AssetModel asset, {String notes = ''});
  Future<Either<FailureObj, SuccessObj>> logMaintenanceAdded(String assetId, String assetName, String maintenanceId, {String notes = ''});
  Future<Either<FailureObj, SuccessObj>> logFileUploaded(String assetId, String assetName, String fileName, {String notes = ''});
  Future<Either<FailureObj, SuccessObj>> logFileDeleted(String assetId, String assetName, String fileName, {String notes = ''});
  Future<Either<FailureObj, List<AssetAuditModel>>> getAssetAuditTrail(String assetId);
  Future<Either<FailureObj, List<AssetAuditModel>>> getAuditTrail({
    int limit = 25,
    DocumentSnapshot? startAfter,
    DateTime? startDate,
    DateTime? endDate,
    String? userId,
    String? action,
    String? assetId,
  });
  Stream<List<AssetAuditModel>> listenToAuditTrail({
    int limit = 25,
    DateTime? startDate,
    DateTime? endDate,
    String? userId,
    String? action,
    String? assetId,
  });
}

class AssetAuditRepositoryImpl implements AssetAuditRepository {
  final AssetAuditFirebaseService _firebaseService;

  AssetAuditRepositoryImpl(this._firebaseService);

  @override
  Future<Either<FailureObj, SuccessObj>> createAuditEntry(AssetAuditModel auditEntry) async {
    try {
      log('Creating audit entry in repository: ${auditEntry.action} for asset ${auditEntry.assetId}');
      await _firebaseService.createAuditEntry(auditEntry);
      return Right(SuccessObj(message: 'Audit entry created successfully'));
    } catch (e) {
      log('Error creating audit entry: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> logAssetCreated(AssetModel asset, {String notes = ''}) async {
    try {
      log('Logging asset creation in repository: ${asset.name}');
      await _firebaseService.logAssetCreated(asset, notes: notes);
      return Right(SuccessObj(message: 'Asset creation logged successfully'));
    } catch (e) {
      log('Error logging asset creation: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> logAssetUpdated(AssetModel oldAsset, AssetModel newAsset, {String notes = ''}) async {
    try {
      log('Logging asset update in repository: ${newAsset.name}');
      await _firebaseService.logAssetUpdated(oldAsset, newAsset, notes: notes);
      return Right(SuccessObj(message: 'Asset update logged successfully'));
    } catch (e) {
      log('Error logging asset update: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> logAssetDeleted(AssetModel asset, {String notes = ''}) async {
    try {
      log('Logging asset deletion in repository: ${asset.name}');
      await _firebaseService.logAssetDeleted(asset, notes: notes);
      return Right(SuccessObj(message: 'Asset deletion logged successfully'));
    } catch (e) {
      log('Error logging asset deletion: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> logMaintenanceAdded(String assetId, String assetName, String maintenanceId, {String notes = ''}) async {
    try {
      log('Logging maintenance addition in repository for asset: $assetId');
      await _firebaseService.logMaintenanceAdded(assetId, assetName, maintenanceId, notes: notes);
      return Right(SuccessObj(message: 'Maintenance addition logged successfully'));
    } catch (e) {
      log('Error logging maintenance addition: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> logFileUploaded(String assetId, String assetName, String fileName, {String notes = ''}) async {
    try {
      log('Logging file upload in repository for asset: $assetId');
      await _firebaseService.logFileUploaded(assetId, assetName, fileName, notes: notes);
      return Right(SuccessObj(message: 'File upload logged successfully'));
    } catch (e) {
      log('Error logging file upload: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> logFileDeleted(String assetId, String assetName, String fileName, {String notes = ''}) async {
    try {
      log('Logging file deletion in repository for asset: $assetId');
      await _firebaseService.logFileDeleted(assetId, assetName, fileName, notes: notes);
      return Right(SuccessObj(message: 'File deletion logged successfully'));
    } catch (e) {
      log('Error logging file deletion: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, List<AssetAuditModel>>> getAssetAuditTrail(String assetId) async {
    try {
      log('Fetching asset audit trail in repository for asset: $assetId');
      final auditTrail = await _firebaseService.getAssetAuditTrail(assetId);
      return Right(auditTrail);
    } catch (e) {
      log('Error fetching asset audit trail: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, List<AssetAuditModel>>> getAuditTrail({
    int limit = 25,
    DocumentSnapshot? startAfter,
    DateTime? startDate,
    DateTime? endDate,
    String? userId,
    String? action,
    String? assetId,
  }) async {
    try {
      log('Fetching audit trail in repository with filters');
      final auditTrail = await _firebaseService.getAuditTrail(
        limit: limit,
        startAfter: startAfter,
        startDate: startDate,
        endDate: endDate,
        userId: userId,
        action: action,
        assetId: assetId,
      );
      return Right(auditTrail);
    } catch (e) {
      log('Error fetching audit trail: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Stream<List<AssetAuditModel>> listenToAuditTrail({
    int limit = 25,
    DateTime? startDate,
    DateTime? endDate,
    String? userId,
    String? action,
    String? assetId,
  }) {
    try {
      log('Setting up audit trail stream in repository');
      return _firebaseService.listenToAuditTrail(
        limit: limit,
        startDate: startDate,
        endDate: endDate,
        userId: userId,
        action: action,
        assetId: assetId,
      );
    } catch (e) {
      log('Error setting up audit trail stream: $e');
      return Stream.value(<AssetAuditModel>[]);
    }
  }
}
