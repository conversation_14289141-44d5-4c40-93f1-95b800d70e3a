import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/core/utils/constants/constants.dart';
import 'package:logestics/core/utils/constants/custom_dialogs.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/features/locations/domain/usecases/region_use_case/create_region_use_case.dart';
import 'package:logestics/features/locations/presentation/regions/controller/region_list_controller.dart';
import 'package:logestics/firebase_service/locations/zone_firebase_service.dart';
import 'package:logestics/models/locations/region_model.dart';

import 'dart:developer';

import 'package:logestics/models/locations/zone_model.dart';

class AddRegionController extends GetxController {
  final String? currentRegionName;
  final CreateRegionUseCase createRegionUseCase;
  var isLoading = false.obs;

  AddRegionController({
    this.currentRegionName,
    required this.createRegionUseCase,
  });

  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  late TextEditingController regionNameController;
  late TextEditingController regionCodeController;

  // For zone selection
  final zones = <ZoneModel>[].obs;
  final selectedZone = Rxn<ZoneModel>();
  final isLoadingZones = false.obs;

  var editMode = false;

  @override
  void onInit() {
    super.onInit();
    regionNameController = TextEditingController(text: currentRegionName ?? '');
    regionCodeController = TextEditingController();
    currentRegionName == null ? editMode = false : editMode = true;
    fetchZones();
  }

  @override
  void onClose() {
    regionNameController.dispose();
    regionCodeController.dispose();
    super.onClose();
  }

  Future<void> fetchZones() async {
    isLoadingZones.value = true;
    try {
      final zoneFirebaseService = ZoneFirebaseService();
      final fetchedZones = await zoneFirebaseService.getZones();
      zones.value = fetchedZones;
    } catch (e) {
      log('Error fetching zones: $e');
    } finally {
      isLoadingZones.value = false;
    }
  }

  void setSelectedZone(ZoneModel zone) {
    selectedZone.value = zone;
  }

  String? validateRegionName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Region name is required.';
    }
    return null;
  }

  String? validateRegionCode(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Region code is required.';
    }
    return null;
  }

  String? validateZone(String? value) {
    if (selectedZone.value == null) {
      return 'Zone selection is required.';
    }
    return null;
  }

  Future<void> saveRegion() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    if (selectedZone.value == null) {
      showErrorSnackbar('Please select a zone.');
      return;
    }

    var regionName = regionNameController.text.trim();
    var regionCode = regionCodeController.text.trim();

    await createRegion(
      region: RegionModel(
        regionId: '',
        regionName: regionName.capitalizeFirstLetter(),
        zoneId: selectedZone.value!.zoneId,
        zoneName: selectedZone.value!.zoneName.capitalizeFirstLetter(),
        regionCode: regionCode,
        createdAt: DateTime.now(),
      ),
    );
  }

  Future<void> createRegion({required RegionModel region}) async {
    if (region.regionName.isEmpty) {
      showErrorSnackbar('Region name must not be empty.');
      return;
    }

    isLoading.value = true;
    try {
      final result = await createRegionUseCase.call(region: region);
      result.fold(
        (failure) => showErrorDialog(failure),
        (success) => _showSuccessSnackbar(success, region),
      );
    } catch (e) {
      showUnexpectedErrorDialog();
    } finally {
      isLoading.value = false;
    }
  }

  // Future<void> updateRegion({required String RegionName}) async {
  //   Get.dialog(
  //     const Center(child: CircularProgressIndicator()),
  //     barrierDismissible: false,
  //   );

  //   try {
  //     final result = await firebaseServices.updateRegion(RegionName: RegionName);
  //     Get.back();
  //     result.fold(
  //       (failure) => showErrorDialog(failure),
  //       (success) => _showSuccessSnackbar(success),
  //     );
  //   } catch (e) {
  //     Get.back();
  //     Get.defaultDialog(
  //       title: "Unexpected Error",
  //       middleText: "Something went wrong. Please try again later.",
  //       textConfirm: "OK",
  //       confirmTextColor: Colors.white,
  //       onConfirm: () => Get.back(),
  //     );
  //   }
  // }

  void showErrorSnackbar(String errorMessage) {
    if (errorMessage.isNotEmpty) {
      (failure) => SnackbarUtils.showError(
            "Error",
            errorMessage,
          );
    }
  }

  void _showSuccessSnackbar(SuccessObj success, RegionModel region) {
    try {
      // Get the region list controller
      final regionListController = Get.find<RegionListController>();
      // Add the new region to the list
      regionListController.addRegionToList(region);
    } catch (e) {
      log('RegionListController not found: $e');
      // Fallback to full refresh if controller not found
    } finally {
      clearControllers();
      Get.back();
      displaySuccessSnackbar(success);
    }
  }

  void clearControllers() {
    regionNameController.clear();
    regionCodeController.clear();
  }

  // Call this method to refresh zones when returning to the view
  void refreshZones() {
    fetchZones();
  }
}
