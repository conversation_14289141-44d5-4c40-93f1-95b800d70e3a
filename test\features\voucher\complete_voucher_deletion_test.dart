import 'package:flutter_test/flutter_test.dart';
import 'package:logestics/models/finance/deposit_model.dart';
import 'package:logestics/models/finance/expense_model.dart';

void main() {
  group('Complete Voucher Deletion Tests', () {
    const voucherNumber = 'V-001';
    const referenceNumber = 'V-V-001';

    // Helper function to create test deposit entries
    List<DepositModel> createTestDepositEntries() {
      return [
        DepositModel(
          id: 'dep1',
          accountId: 'acc1',
          accountName: 'Broker Account',
          amount: 5000.0,
          createdAt: DateTime.now(),
          categoryId: 'broker_fees',
          categoryName: 'Broker Fees',
          payerId: '',
          payerName: 'Own Broker',
          referenceNumber: referenceNumber,
          notes: 'Broker Fee - Voucher #$voucherNumber',
        ),
        DepositModel(
          id: 'dep2',
          accountId: 'acc2',
          accountName: 'Munshiana Account',
          amount: 2000.0,
          createdAt: DateTime.now(),
          categoryId: 'munshiana',
          categoryName: 'Munshiana',
          payerId: '',
          payerName: 'Munshiana Payment',
          referenceNumber: referenceNumber,
          notes: 'Munshiana - Voucher #$voucherNumber',
        ),
        DepositModel(
          id: 'dep3',
          accountId: 'acc3',
          accountName: 'Company Freight Account',
          amount: 45000.0,
          createdAt: DateTime.now(),
          categoryId: 'company_freight',
          categoryName: 'Company Freight',
          payerId: '',
          payerName: 'NLC Amount',
          referenceNumber: referenceNumber,
          notes: 'Company Freight - Voucher #$voucherNumber',
        ),
        DepositModel(
          id: 'dep4',
          accountId: 'acc4',
          accountName: 'Sales Tax Account',
          amount: 2070.0,
          createdAt: DateTime.now(),
          categoryId: 'sales_tax',
          categoryName: 'Sales Tax',
          payerId: '',
          payerName: 'Tax Collection',
          referenceNumber: referenceNumber,
          notes: 'Sales Tax (4.6%) - Voucher #$voucherNumber',
        ),
        DepositModel(
          id: 'dep5',
          accountId: 'acc5',
          accountName: 'Freight Tax Account',
          amount: 6750.0,
          createdAt: DateTime.now(),
          categoryId: 'tax_authority_single',
          categoryName: 'Tax Authority Payment',
          payerId: '',
          payerName: 'SRB (Sindh Revenue Board)',
          referenceNumber: referenceNumber,
          notes:
              'Tax payment to SRB (Sindh Revenue Board) - Voucher #$voucherNumber',
        ),
        DepositModel(
          id: 'dep6',
          accountId: 'acc6',
          accountName: 'Profit Account',
          amount: 15000.0,
          createdAt: DateTime.now(),
          categoryId: 'net_profit',
          categoryName: 'Net Profit',
          payerId: '',
          payerName: 'Profit',
          referenceNumber: referenceNumber,
          notes: 'Net Profit - Voucher #$voucherNumber',
        ),
      ];
    }

    // Helper function to create test expense entries
    List<ExpenseModel> createTestExpenseEntries() {
      return [
        ExpenseModel(
          id: 'exp1',
          title: 'Broker Fee - Voucher #$voucherNumber',
          accountId: 'acc1',
          accountName: 'Broker Account',
          amount: 3000.0,
          createdAt: DateTime.now(),
          categoryId: 'broker_fees',
          categoryName: 'Broker Fees',
          payeeId: '',
          payeeName: 'Outsource Broker',
          referenceNumber: referenceNumber,
          notes: 'Broker Fee - Voucher #$voucherNumber',
        ),
        ExpenseModel(
          id: 'exp2',
          title: 'Net Loss - Voucher #$voucherNumber',
          accountId: 'acc6',
          accountName: 'Profit Account',
          amount: 5000.0,
          createdAt: DateTime.now(),
          categoryId: 'net_loss',
          categoryName: 'Net Loss',
          payeeId: '',
          payeeName: 'Loss',
          referenceNumber: referenceNumber,
          notes: 'Net Loss - Voucher #$voucherNumber',
        ),
      ];
    }

    // Helper function to filter entries by reference number
    List<T> filterByReferenceNumber<T>(List<T> entries, String refNumber) {
      return entries.where((entry) {
        if (entry is DepositModel) {
          return entry.referenceNumber == refNumber;
        } else if (entry is ExpenseModel) {
          return entry.referenceNumber == refNumber;
        }
        return false;
      }).toList();
    }

    test('should identify all deposit entries for voucher deletion', () {
      final allDeposits = createTestDepositEntries();
      final voucherDeposits =
          filterByReferenceNumber(allDeposits, referenceNumber);

      // Should find all 6 deposit entries for this voucher
      expect(voucherDeposits.length, 6);

      // Verify all entries have correct reference number
      for (final deposit in voucherDeposits) {
        expect((deposit as DepositModel).referenceNumber, referenceNumber);
      }

      // Verify all expected categories are present
      final categories =
          voucherDeposits.map((d) => (d as DepositModel).categoryId).toSet();
      expect(categories.contains('broker_fees'), true);
      expect(categories.contains('munshiana'), true);
      expect(categories.contains('company_freight'), true);
      expect(categories.contains('sales_tax'), true);
      expect(categories.contains('tax_authority_single'), true);
      expect(categories.contains('net_profit'), true);
    });

    test('should identify all expense entries for voucher deletion', () {
      final allExpenses = createTestExpenseEntries();
      final voucherExpenses =
          filterByReferenceNumber(allExpenses, referenceNumber);

      // Should find all 2 expense entries for this voucher
      expect(voucherExpenses.length, 2);

      // Verify all entries have correct reference number
      for (final expense in voucherExpenses) {
        expect((expense as ExpenseModel).referenceNumber, referenceNumber);
      }

      // Verify all expected categories are present
      final categories =
          voucherExpenses.map((e) => (e as ExpenseModel).categoryId).toSet();
      expect(categories.contains('broker_fees'), true);
      expect(categories.contains('net_loss'), true);
    });

    test('should calculate total financial impact of voucher deletion', () {
      final deposits = createTestDepositEntries();
      final expenses = createTestExpenseEntries();

      final voucherDeposits =
          filterByReferenceNumber(deposits, referenceNumber);
      final voucherExpenses =
          filterByReferenceNumber(expenses, referenceNumber);

      // Calculate total amounts
      final totalDepositAmount = voucherDeposits
          .map((d) => (d as DepositModel).amount)
          .fold(0.0, (sum, amount) => sum + amount);

      final totalExpenseAmount = voucherExpenses
          .map((e) => (e as ExpenseModel).amount)
          .fold(0.0, (sum, amount) => sum + amount);

      // Verify expected totals
      expect(totalDepositAmount, 75820.0); // Sum of all deposit amounts
      expect(totalExpenseAmount, 8000.0); // Sum of all expense amounts

      // Net financial impact (deposits - expenses)
      final netImpact = totalDepositAmount - totalExpenseAmount;
      expect(netImpact, 67820.0);
    });

    test('should verify voucher deletion affects multiple account types', () {
      final deposits = createTestDepositEntries();
      final expenses = createTestExpenseEntries();

      final voucherDeposits =
          filterByReferenceNumber(deposits, referenceNumber);
      final voucherExpenses =
          filterByReferenceNumber(expenses, referenceNumber);

      // Get all affected account IDs
      final depositAccountIds =
          voucherDeposits.map((d) => (d as DepositModel).accountId).toSet();
      final expenseAccountIds =
          voucherExpenses.map((e) => (e as ExpenseModel).accountId).toSet();
      final allAffectedAccounts = {...depositAccountIds, ...expenseAccountIds};

      // Should affect multiple accounts
      expect(allAffectedAccounts.length, 6);
      expect(allAffectedAccounts.contains('acc1'), true); // Broker Account
      expect(allAffectedAccounts.contains('acc2'), true); // Munshiana Account
      expect(allAffectedAccounts.contains('acc3'),
          true); // Company Freight Account
      expect(allAffectedAccounts.contains('acc4'), true); // Sales Tax Account
      expect(allAffectedAccounts.contains('acc5'), true); // Freight Tax Account
      expect(allAffectedAccounts.contains('acc6'), true); // Profit Account
    });

    test('should verify comprehensive cleanup categories', () {
      final deposits = createTestDepositEntries();
      final expenses = createTestExpenseEntries();

      final voucherDeposits =
          filterByReferenceNumber(deposits, referenceNumber);
      final voucherExpenses =
          filterByReferenceNumber(expenses, referenceNumber);

      // Get all categories that will be cleaned up
      final depositCategories =
          voucherDeposits.map((d) => (d as DepositModel).categoryId).toSet();
      final expenseCategories =
          voucherExpenses.map((e) => (e as ExpenseModel).categoryId).toSet();
      final allCategories = {...depositCategories, ...expenseCategories};

      // Verify all expected financial categories are covered
      final expectedCategories = {
        'broker_fees',
        'munshiana',
        'company_freight',
        'sales_tax',
        'tax_authority_single',
        'net_profit',
        'net_loss',
      };

      expect(allCategories, expectedCategories);
    });

    test('should handle split tax authority payments correctly', () {
      // Create additional split tax authority payment
      final splitTaxDeposit = DepositModel(
        id: 'dep7',
        accountId: 'acc5',
        accountName: 'Freight Tax Account',
        amount: 3375.0, // Half of 15% tax (split payment)
        createdAt: DateTime.now(),
        categoryId: 'tax_authority_split',
        categoryName: 'Tax Authority Payment (Split)',
        payerId: '',
        payerName: 'PRA (Punjab Revenue Authority)',
        referenceNumber: referenceNumber,
        notes:
            'Tax payment to PRA (Punjab Revenue Authority) - Voucher #$voucherNumber (Split payment)',
      );

      final deposits = createTestDepositEntries();
      deposits.add(splitTaxDeposit);

      final voucherDeposits =
          filterByReferenceNumber(deposits, referenceNumber);

      // Should now include both single and split tax authority payments
      final taxAuthorityDeposits = voucherDeposits
          .where(
              (d) => (d as DepositModel).categoryId.startsWith('tax_authority'))
          .toList();

      expect(taxAuthorityDeposits.length, 2);

      // Verify both single and split categories are present
      final taxCategories = taxAuthorityDeposits
          .map((d) => (d as DepositModel).categoryId)
          .toSet();
      expect(taxCategories.contains('tax_authority_single'), true);
      expect(taxCategories.contains('tax_authority_split'), true);
    });

    test('should verify deletion order and dependencies', () {
      // Test that financial entries are cleaned up before voucher deletion
      final cleanupSteps = [
        'Clean up deposit entries',
        'Clean up expense entries',
        'Clean up fuel card usage',
        'Clean up check usage',
        'Delete voucher document',
      ];

      // Verify cleanup order is logical
      expect(cleanupSteps[0], 'Clean up deposit entries');
      expect(cleanupSteps[1], 'Clean up expense entries');
      expect(cleanupSteps[4], 'Delete voucher document'); // Should be last

      // Financial cleanup should happen before voucher deletion
      final financialCleanupSteps = cleanupSteps.sublist(0, 4);
      final voucherDeletionStep = cleanupSteps[4];

      expect(financialCleanupSteps.length, 4);
      expect(voucherDeletionStep, 'Delete voucher document');
    });

    test('should handle empty voucher deletion gracefully', () {
      // Test voucher with no associated financial entries
      final emptyVoucherRef = 'V-EMPTY-001';
      final allDeposits = createTestDepositEntries();
      final allExpenses = createTestExpenseEntries();

      final emptyVoucherDeposits =
          filterByReferenceNumber(allDeposits, emptyVoucherRef);
      final emptyVoucherExpenses =
          filterByReferenceNumber(allExpenses, emptyVoucherRef);

      // Should find no entries for empty voucher
      expect(emptyVoucherDeposits.length, 0);
      expect(emptyVoucherExpenses.length, 0);

      // Deletion should still proceed (only voucher document deleted)
      expect(emptyVoucherRef.isNotEmpty, true);
    });

    test('should verify standardized description format in all entries', () {
      final deposits = createTestDepositEntries();
      final expenses = createTestExpenseEntries();

      final voucherDeposits =
          filterByReferenceNumber(deposits, referenceNumber);
      final voucherExpenses =
          filterByReferenceNumber(expenses, referenceNumber);

      // Verify all deposit descriptions follow standard format
      for (final deposit in voucherDeposits) {
        final d = deposit as DepositModel;
        if (!d.notes.startsWith('Tax payment to')) {
          // Standard format: [Transaction Type] - Voucher #[VoucherNumber]
          expect(d.notes.contains(' - Voucher #$voucherNumber'), true);
        } else {
          // Tax authority format: Tax payment to [Authority] - Voucher #[VoucherNumber]
          expect(d.notes.contains(' - Voucher #$voucherNumber'), true);
        }
      }

      // Verify all expense descriptions follow standard format
      for (final expense in voucherExpenses) {
        final e = expense as ExpenseModel;
        expect(e.title.contains(' - Voucher #$voucherNumber'), true);
        expect(e.notes.contains(' - Voucher #$voucherNumber'), true);
      }
    });

    test('should verify comprehensive logging during deletion process', () {
      const voucherNum = 'V-001';

      // Test logging messages for different phases
      final expectedLogMessages = [
        '🗑️ Starting voucher deletion process for voucher: $voucherNum',
        '🧹 Phase 1: Starting financial cleanup...',
        '🚀 Starting comprehensive financial cleanup for voucher: $voucherNum',
        '🔍 Using reference number for cleanup: V-$voucherNum',
        '🏗️ Initializing financial repositories...',
        '✅ Repositories initialized successfully',
        '📥 Step 1: Cleaning up deposit entries...',
        '🧹 Starting deposit cleanup for reference: V-$voucherNum',
        '📤 Step 2: Cleaning up expense entries...',
        '🧹 Starting expense cleanup for reference: V-$voucherNum',
        '⛽ Step 3: Cleaning up fuel card usage records...',
        '💳 Step 4: Cleaning up check usage records...',
        '🎉 Successfully completed comprehensive financial cleanup for voucher: $voucherNum',
        '✅ Phase 1 completed: Financial cleanup successful',
        '📄 Phase 2: Deleting voucher document...',
        '✅ Phase 2 completed: Voucher document deleted successfully',
        '🎉 Voucher deletion completed successfully for voucher: $voucherNum',
      ];

      // Verify all expected log messages are present
      expect(expectedLogMessages.length, 17);

      // Verify log message format consistency
      final phaseMessages =
          expectedLogMessages.where((msg) => msg.contains('Phase')).toList();
      expect(phaseMessages.length, 4); // 2 phase starts + 2 phase completions

      // Verify step messages
      final stepMessages =
          expectedLogMessages.where((msg) => msg.contains('Step')).toList();
      expect(stepMessages.length, 4); // 4 cleanup steps

      // Verify emoji usage for visual clarity
      final emojiMessages = expectedLogMessages
          .where((msg) => RegExp(r'[🗑️🧹🚀🔍🏗️✅📥📤⛽💳🎉📄]').hasMatch(msg))
          .toList();
      expect(emojiMessages.length,
          expectedLogMessages.length); // All messages should have emojis
    });

    test('should verify error handling and rollback scenarios', () {
      const voucherNum = 'V-001';

      // Test error scenarios
      final errorScenarios = [
        'Network error during voucher deletion',
        'Argument error during voucher deletion',
        'Firebase error during voucher deletion',
        'Unexpected error during voucher deletion',
        'Failed to fetch deposits for cleanup',
        'Failed to fetch expenses for cleanup',
        'Failed to delete deposit',
        'Failed to delete expense',
        'Failed to delete check usage',
      ];

      // Verify error handling coverage
      expect(errorScenarios.length, 9);

      // Verify error message format
      for (final scenario in errorScenarios) {
        expect(scenario.contains('error') || scenario.contains('Failed'), true);
      }

      // Test specific error codes
      final errorCodes = [
        'no-internet',
        'invalid-argument',
        'unexpected-error',
        'check-usage-fetch-error',
        'check-usage-delete-error',
      ];

      expect(errorCodes.length, 5);

      // Verify all error codes follow kebab-case format
      for (final code in errorCodes) {
        expect(RegExp(r'^[a-z-]+$').hasMatch(code), true);
      }
    });

    test('should verify voucher number format consistency throughout deletion',
        () {
      const inputVoucherNumber = '001'; // Input format (numeric)
      const expectedReferenceNumber = 'V-001'; // Reference format (prefixed)

      // Test voucher number to reference number conversion
      final referenceNumber = 'V-$inputVoucherNumber';
      expect(referenceNumber, expectedReferenceNumber);

      // Test that both formats are used correctly
      final usageScenarios = {
        'Input parameter': inputVoucherNumber,
        'Reference number for deposits': expectedReferenceNumber,
        'Reference number for expenses': expectedReferenceNumber,
        'Check usage voucher number': inputVoucherNumber,
        'Fuel card usage voucher number': inputVoucherNumber,
        'Log messages': inputVoucherNumber,
      };

      // Verify format consistency
      expect(usageScenarios['Input parameter'], '001');
      expect(usageScenarios['Reference number for deposits'], 'V-001');
      expect(usageScenarios['Reference number for expenses'], 'V-001');
      expect(usageScenarios['Check usage voucher number'], '001');
      expect(usageScenarios['Fuel card usage voucher number'], '001');
      expect(usageScenarios['Log messages'], '001');
    });

    test('should verify deletion process order and dependencies', () {
      // Define the correct deletion order
      final deletionSteps = [
        'Initialize repositories',
        'Clean up deposit entries',
        'Clean up expense entries',
        'Clean up fuel card usage',
        'Clean up check usage',
        'Delete voucher document',
      ];

      // Verify step order
      expect(deletionSteps[0], 'Initialize repositories');
      expect(deletionSteps[1], 'Clean up deposit entries');
      expect(deletionSteps[2], 'Clean up expense entries');
      expect(deletionSteps[3], 'Clean up fuel card usage');
      expect(deletionSteps[4], 'Clean up check usage');
      expect(deletionSteps[5], 'Delete voucher document');

      // Verify financial cleanup happens before voucher deletion
      final financialSteps = deletionSteps.sublist(1, 5);
      final voucherDeletionStep = deletionSteps[5];

      expect(financialSteps.length, 4);
      expect(voucherDeletionStep, 'Delete voucher document');

      // Verify dependencies
      final dependencies = {
        'Repositories must be initialized first': deletionSteps[0],
        'Financial cleanup must complete before voucher deletion':
            financialSteps,
        'Voucher deletion must be last': voucherDeletionStep,
      };

      expect(dependencies['Repositories must be initialized first'],
          'Initialize repositories');
      expect(
          dependencies[
              'Financial cleanup must complete before voucher deletion'],
          financialSteps);
      expect(dependencies['Voucher deletion must be last'],
          'Delete voucher document');
    });
  });
}
