import 'dart:io';
import 'dart:developer';

import 'package:dartz/dartz.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logestics/firebase_service/invoices/invoice_crud_firebase_service.dart';
import 'package:logestics/models/invoice_model.dart';

import '../../../core/shared_services/failure_obj.dart';
import '../../../core/shared_services/success_obj.dart';

abstract class InvoiceRepository {
  /// Creates an invoice and returns either a success or error object.
  Future<Either<FailureObj, SuccessObj>> createInvoice({
    required String uid,
    required InvoiceModel invoice,
  });

  Future<Either<FailureObj, SuccessObj>> updateInvoice({
    required String uid,
    required InvoiceModel invoice,
  });

  Stream<List<DocumentChange>> listenToInvoices({required String uid});

  Future<List<InvoiceModel>> getInvoicesForCompany(String uid);

  Future<Either<FailureObj, SuccessObj>> deleteInvoice({
    required String uid,
    required String tasNumber,
  });

  Future<Either<FailureObj, int>> getHighestInvoiceNumber(
      {required String uid});

  Future<Either<FailureObj, SuccessObj>> updateInvoiceStatus(
      {required String uid,
      required String invoiceNumber,
      required String newStatus});
}

class InvoiceRepositoryImpl implements InvoiceRepository {
  final InvoiceCrudFirebaseService _invoiceCrudFirebaseService;

  InvoiceRepositoryImpl(this._invoiceCrudFirebaseService);

  @override
  Future<Either<FailureObj, SuccessObj>> createInvoice({
    required String uid,
    required InvoiceModel invoice,
  }) async {
    try {
      await _invoiceCrudFirebaseService.createInvoiceToFirebase(
        uid: uid,
        invoice: invoice,
      );
      return Right(SuccessObj(message: 'Invoice created successfully.'));
    } on SocketException {
      return Left(FailureObj(
        code: 'no-internet',
        message: 'No internet connection. Please check your network.',
      ));
    } on ArgumentError catch (e) {
      return Left(FailureObj(
        code: 'invalid-argument',
        message: e.message ?? 'Invalid argument provided.',
      ));
    } on FirebaseException catch (e) {
      return Left(FailureObj(
        code: e.code,
        message: e.message ?? 'Firebase error occurred.',
      ));
    } catch (e) {
      return Left(FailureObj(
        code: 'unexpected-error',
        message: 'An unexpected error occurred: $e',
      ));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> updateInvoice({
    required String uid,
    required InvoiceModel invoice,
  }) async {
    try {
      await _invoiceCrudFirebaseService.updateInvoiceToFirebase(
        uid: uid,
        invoice: invoice,
      );
      return Right(SuccessObj(message: 'Invoice updated successfully.'));
    } on SocketException {
      return Left(FailureObj(
        code: 'no-internet',
        message: 'No internet connection. Please check your network.',
      ));
    } on ArgumentError catch (e) {
      return Left(FailureObj(
        code: 'invalid-argument',
        message: e.message ?? 'Invalid argument provided.',
      ));
    } on FirebaseException catch (e) {
      return Left(FailureObj(
        code: e.code,
        message: e.message ?? 'Firebase error occurred.',
      ));
    } catch (e) {
      return Left(FailureObj(
        code: 'unexpected-error',
        message: 'An unexpected error occurred: $e',
      ));
    }
  }

  @override
  Stream<List<DocumentChange>> listenToInvoices({required String uid}) {
    return _invoiceCrudFirebaseService.listenToInvoices(uid: uid);
  }

  @override
  Future<List<InvoiceModel>> getInvoicesForCompany(String uid) async {
    try {
      return await _invoiceCrudFirebaseService.getInvoicesForCompany(uid);
    } catch (e) {
      log('Error getting invoices for company: $e');
      rethrow;
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> deleteInvoice({
    required String uid,
    required String tasNumber,
  }) async {
    try {
      await _invoiceCrudFirebaseService.deleteInvoiceFromFirebase(
        uid: uid,
        tasNumber: tasNumber,
      );
      return Right(SuccessObj(message: 'Invoice deleted successfully.'));
    } on SocketException {
      return Left(FailureObj(
        code: 'no-internet',
        message: 'No internet connection. Please check your network.',
      ));
    } on ArgumentError catch (e) {
      return Left(FailureObj(
        code: 'invalid-argument',
        message: e.message ?? 'Invalid argument provided.',
      ));
    } on FirebaseException catch (e) {
      return Left(FailureObj(
        code: e.code,
        message: e.message ?? 'Firebase error occurred.',
      ));
    } catch (e) {
      return Left(FailureObj(
        code: 'unexpected-error',
        message: 'An unexpected error occurred: $e',
      ));
    }
  }

  @override
  Future<Either<FailureObj, int>> getHighestInvoiceNumber(
      {required String uid}) async {
    try {
      final highestNumber =
          await _invoiceCrudFirebaseService.getHighestInvoiceNumber(uid: uid);
      return Right(highestNumber);
    } catch (e) {
      return Left(FailureObj(
        code: 'error-getting-highest-number',
        message: 'Failed to get highest invoice number: $e',
      ));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> updateInvoiceStatus({
    required String uid,
    required String invoiceNumber,
    required String newStatus,
  }) async {
    try {
      await _invoiceCrudFirebaseService.updateInvoiceStatus(
        invoiceNumber: invoiceNumber,
        uid: uid,
        newStatus: newStatus,
      );
      return Right(SuccessObj(message: 'Invoice status updated successfully.'));
    } on SocketException {
      return Left(FailureObj(
        code: 'no-internet',
        message: 'No internet connection. Please check your network.',
      ));
    } on ArgumentError catch (e) {
      return Left(FailureObj(
        code: 'invalid-argument',
        message: e.message ?? 'Invalid argument provided.',
      ));
    } on FirebaseException catch (e) {
      return Left(FailureObj(
        code: e.code,
        message: e.message ?? 'Firebase error occurred.',
      ));
    } catch (e) {
      return Left(FailureObj(
        code: 'unexpected-error',
        message: 'An unexpected error occurred: $e',
      ));
    }
  }
}
