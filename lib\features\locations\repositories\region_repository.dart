import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/firebase_service/locations/region_firebase_service.dart';
import 'package:logestics/models/locations/region_model.dart';

abstract class RegionRepository {
  /// Creates an Zone and returns either a success or error object.
  Future<Either<FailureObj, SuccessObj>> createRegion({
    required RegionModel region,
  });
  Future<Either<FailureObj, List<RegionModel>>> getRegions();

  Stream<List<RegionModel>> listenToRegions();

  Future<Either<FailureObj, SuccessObj>> deleteRegion({
    required String regionId,
  });
}

class RegionRepositoryImpl implements RegionRepository {
  final RegionFirebaseService firebaseService;

  RegionRepositoryImpl(this.firebaseService);

  @override
  Future<Either<FailureObj, SuccessObj>> createRegion({
    required RegionModel region,
  }) async {
    try {
      // Optimize duplicate check by using a direct query instead of fetching all regions
      final existingRegion =
          await firebaseService.checkRegionExists(region.regionName);
      if (existingRegion) {
        return Left(FailureObj(
            code: 'region-exists',
            message: 'A region with the same name already exists.'));
      }

      await firebaseService.createRegion(region);
      return Right(SuccessObj(message: 'Region created successfully'));
    } catch (e) {
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, List<RegionModel>>> getRegions() async {
    try {
      final regions = await firebaseService.getRegions();
      return Right(regions);
    } catch (e) {
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> deleteRegion(
      {required String regionId}) async {
    try {
      await firebaseService.deleteRegion(regionId);
      return Right(SuccessObj(message: 'Region deleted successfully'));
    } catch (e) {
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Stream<List<RegionModel>> listenToRegions() {
    return firebaseService.listenToRegions();
  }
}
