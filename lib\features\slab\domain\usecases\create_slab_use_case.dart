import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/features/slab/repositories/slab_repository.dart';
import 'package:logestics/models/slab/slab_model.dart';

class CreateSlabUseCase {
  final SlabRepository repository;

  CreateSlabUseCase(this.repository);

  Future<Either<FailureObj, void>> call({
    required SlabModel slab,
  }) async {
    return await repository.createSlab(slab: slab);
  }
}
