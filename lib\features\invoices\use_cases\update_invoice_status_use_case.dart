import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import '../repositories/invoice_repository.dart';

class UpdateInvoiceStatusUseCase {
  final InvoiceRepositoryImpl repository;

  UpdateInvoiceStatusUseCase(this.repository);

  Future<Either<FailureObj, SuccessObj>> call({
    required String invoiceNumber,
    required String uid,
    required String newStatus,
  }) async {
    try {
      // Validate inputs
      if (uid.isEmpty) {
        return Left(FailureObj(
          code: 'invalid-company-id',
          message: 'Company ID cannot be empty.',
        ));
      }

      if (invoiceNumber.isEmpty) {
        return Left(FailureObj(
          code: 'invalid-invoice-number',
          message: 'Invoice number must not be empty.',
        ));
      }

      if (newStatus.isEmpty) {
        return Left(FailureObj(
          code: 'invalid-status',
          message: 'New status must not be empty.',
        ));
      }

      // Update the invoice status
      await repository.updateInvoiceStatus(
        invoiceNumber: invoiceNumber,
        uid: uid,
        newStatus: newStatus,
      );

      return Right(SuccessObj(message: 'Invoice status updated successfully.'));
    } catch (e) {
      return Left(FailureObj(
        code: 'update-status-error',
        message: 'Failed to update invoice status: $e',
      ));
    }
  }
}
