import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:logestics/features/accounting/aged_reports/repositories/aged_reports_repository.dart';
import 'package:logestics/models/finance/aged_report_model.dart';

/// GetX Controller for Aged Reports (Receivables and Payables)
/// Follows the existing controller patterns used in other financial modules
class AgedReportsController extends GetxController {
  final AgedReportsRepository _repository;

  AgedReportsController({AgedReportsRepository? repository})
      : _repository = repository ?? AgedReportsRepository();

  // Observable state variables
  final RxBool isLoading = false.obs;
  final RxBool isGeneratingReceivables = false.obs;
  final RxBool isGeneratingPayables = false.obs;
  final RxBool isExporting = false.obs;
  final RxString errorMessage = ''.obs;
  final RxString successMessage = ''.obs;

  // Current reports
  final Rx<AgedReceivablesReport?> currentReceivablesReport =
      Rx<AgedReceivablesReport?>(null);
  final Rx<AgedPayablesReport?> currentPayablesReport =
      Rx<AgedPayablesReport?>(null);

  // Saved reports
  final RxList<AgedReceivablesReport> savedReceivablesReports =
      <AgedReceivablesReport>[].obs;
  final RxList<AgedPayablesReport> savedPayablesReports =
      <AgedPayablesReport>[].obs;

  // Filter options
  final RxList<String> availableCustomers = <String>[].obs;
  final RxList<String> availableVendors = <String>[].obs;
  final RxList<String> selectedCustomers = <String>[].obs;
  final RxList<String> selectedVendors = <String>[].obs;

  // Form controllers
  final TextEditingController asOfDateController = TextEditingController();
  final RxBool includeZeroBalances = false.obs;

  // Pagination
  final RxInt currentPage = 1.obs;
  final RxInt itemsPerPage = 25.obs;
  final RxInt totalItems = 0.obs;

  // Selected report type
  final RxString selectedReportType =
      'receivables'.obs; // 'receivables' or 'payables'

  @override
  void onInit() {
    super.onInit();
    log('AgedReportsController initialized');
    _initializeController();
  }

  @override
  void onClose() {
    asOfDateController.dispose();
    super.onClose();
  }

  /// Initialize controller with default values
  void _initializeController() {
    // Set default as of date to today
    asOfDateController.text = DateTime.now().toString().split(' ')[0];

    // Load saved reports and filter options
    loadSavedReports();
    loadFilterOptions();
  }

  /// Clear all messages
  void clearMessages() {
    errorMessage.value = '';
    successMessage.value = '';
  }

  /// Set error message
  void setError(String message) {
    errorMessage.value = message;
    successMessage.value = '';
    log('Error: $message');
  }

  /// Set success message
  void setSuccess(String message) {
    successMessage.value = message;
    errorMessage.value = '';
    log('Success: $message');
  }

  /// Generate Aged Receivables Report
  Future<void> generateAgedReceivablesReport() async {
    try {
      clearMessages();
      isGeneratingReceivables.value = true;

      // Parse as of date
      final asOfDate = DateTime.tryParse(asOfDateController.text);
      if (asOfDate == null) {
        setError('Please enter a valid as of date');
        return;
      }

      log('Generating Aged Receivables Report for date: ${asOfDate.toIso8601String()}');

      final result = await _repository.generateAgedReceivablesReport(
        asOfDate: asOfDate,
        companyName: 'Current Company', // This would come from user context
        includeZeroBalances: includeZeroBalances.value,
        customerIds: selectedCustomers.isNotEmpty ? selectedCustomers : null,
      );

      result.fold(
        (failure) => setError(failure.message),
        (report) {
          currentReceivablesReport.value = report;
          setSuccess('Aged Receivables Report generated successfully');
          log('Generated report with ${report.totalCustomers} customers and total outstanding: ${report.grandTotalOutstanding}');
        },
      );
    } catch (e) {
      setError('Failed to generate aged receivables report: $e');
      log('Error generating aged receivables report: $e');
    } finally {
      isGeneratingReceivables.value = false;
    }
  }

  /// Generate Aged Payables Report
  Future<void> generateAgedPayablesReport() async {
    try {
      clearMessages();
      isGeneratingPayables.value = true;

      // Parse as of date
      final asOfDate = DateTime.tryParse(asOfDateController.text);
      if (asOfDate == null) {
        setError('Please enter a valid as of date');
        return;
      }

      log('Generating Aged Payables Report for date: ${asOfDate.toIso8601String()}');

      final result = await _repository.generateAgedPayablesReport(
        asOfDate: asOfDate,
        companyName: 'Current Company', // This would come from user context
        includeZeroBalances: includeZeroBalances.value,
        vendorIds: selectedVendors.isNotEmpty ? selectedVendors : null,
      );

      result.fold(
        (failure) => setError(failure.message),
        (report) {
          currentPayablesReport.value = report;
          setSuccess('Aged Payables Report generated successfully');
          log('Generated report with ${report.totalVendors} vendors and total outstanding: ${report.grandTotalOutstanding}');
        },
      );
    } catch (e) {
      setError('Failed to generate aged payables report: $e');
      log('Error generating aged payables report: $e');
    } finally {
      isGeneratingPayables.value = false;
    }
  }

  /// Save current Aged Receivables Report
  Future<void> saveAgedReceivablesReport() async {
    try {
      if (currentReceivablesReport.value == null) {
        setError('No receivables report to save');
        return;
      }

      clearMessages();
      isLoading.value = true;

      final result = await _repository.saveAgedReceivablesReport(
        currentReceivablesReport.value!,
      );

      result.fold(
        (failure) => setError(failure.message),
        (message) {
          setSuccess(message);
          loadSavedReports(); // Refresh saved reports list
        },
      );
    } catch (e) {
      setError('Failed to save aged receivables report: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// Save current Aged Payables Report
  Future<void> saveAgedPayablesReport() async {
    try {
      if (currentPayablesReport.value == null) {
        setError('No payables report to save');
        return;
      }

      clearMessages();
      isLoading.value = true;

      final result = await _repository.saveAgedPayablesReport(
        currentPayablesReport.value!,
      );

      result.fold(
        (failure) => setError(failure.message),
        (message) {
          setSuccess(message);
          loadSavedReports(); // Refresh saved reports list
        },
      );
    } catch (e) {
      setError('Failed to save aged payables report: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// Load saved reports
  Future<void> loadSavedReports() async {
    try {
      isLoading.value = true;

      // Load saved receivables reports
      final receivablesResult =
          await _repository.getSavedAgedReceivablesReports();
      receivablesResult.fold(
        (failure) =>
            log('Failed to load saved receivables reports: ${failure.message}'),
        (reports) => savedReceivablesReports.value = reports,
      );

      // Load saved payables reports
      final payablesResult = await _repository.getSavedAgedPayablesReports();
      payablesResult.fold(
        (failure) =>
            log('Failed to load saved payables reports: ${failure.message}'),
        (reports) => savedPayablesReports.value = reports,
      );
    } catch (e) {
      log('Error loading saved reports: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// Load filter options (customers and vendors)
  Future<void> loadFilterOptions() async {
    try {
      // Load customers
      final customersResult = await _repository.getCustomerList();
      customersResult.fold(
        (failure) => log('Failed to load customers: ${failure.message}'),
        (customers) => availableCustomers.value = customers,
      );

      // Load vendors
      final vendorsResult = await _repository.getVendorList();
      vendorsResult.fold(
        (failure) => log('Failed to load vendors: ${failure.message}'),
        (vendors) => availableVendors.value = vendors,
      );
    } catch (e) {
      log('Error loading filter options: $e');
    }
  }

  /// Export current report to Excel
  Future<void> exportToExcel() async {
    try {
      clearMessages();
      isExporting.value = true;

      if (selectedReportType.value == 'receivables') {
        if (currentReceivablesReport.value == null) {
          setError('No receivables report to export');
          return;
        }

        final result = await _repository.exportAgedReceivablesToExcel(
          currentReceivablesReport.value!,
        );

        result.fold(
          (failure) => setError(failure.message),
          (fileName) =>
              setSuccess('Receivables report exported to Excel: $fileName'),
        );
      } else {
        if (currentPayablesReport.value == null) {
          setError('No payables report to export');
          return;
        }

        final result = await _repository.exportAgedPayablesToExcel(
          currentPayablesReport.value!,
        );

        result.fold(
          (failure) => setError(failure.message),
          (fileName) =>
              setSuccess('Payables report exported to Excel: $fileName'),
        );
      }
    } catch (e) {
      setError('Failed to export report to Excel: $e');
    } finally {
      isExporting.value = false;
    }
  }

  /// Generate PDF for current report
  Future<void> generatePDF() async {
    try {
      clearMessages();
      isExporting.value = true;

      if (selectedReportType.value == 'receivables') {
        if (currentReceivablesReport.value == null) {
          setError('No receivables report to generate PDF');
          return;
        }

        final result = await _repository.generateAgedReceivablesPDF(
          currentReceivablesReport.value!,
        );

        result.fold(
          (failure) => setError(failure.message),
          (fileName) =>
              setSuccess('Receivables report PDF generated: $fileName'),
        );
      } else {
        if (currentPayablesReport.value == null) {
          setError('No payables report to generate PDF');
          return;
        }

        final result = await _repository.generateAgedPayablesPDF(
          currentPayablesReport.value!,
        );

        result.fold(
          (failure) => setError(failure.message),
          (fileName) => setSuccess('Payables report PDF generated: $fileName'),
        );
      }
    } catch (e) {
      setError('Failed to generate PDF: $e');
    } finally {
      isExporting.value = false;
    }
  }

  /// Switch between report types
  void switchReportType(String type) {
    selectedReportType.value = type;
    clearMessages();
  }

  /// Toggle customer selection
  void toggleCustomerSelection(String customer) {
    if (selectedCustomers.contains(customer)) {
      selectedCustomers.remove(customer);
    } else {
      selectedCustomers.add(customer);
    }
  }

  /// Toggle vendor selection
  void toggleVendorSelection(String vendor) {
    if (selectedVendors.contains(vendor)) {
      selectedVendors.remove(vendor);
    } else {
      selectedVendors.add(vendor);
    }
  }

  /// Clear all filters
  void clearFilters() {
    selectedCustomers.clear();
    selectedVendors.clear();
    includeZeroBalances.value = false;
    asOfDateController.text = DateTime.now().toString().split(' ')[0];
  }

  /// Refresh all data
  Future<void> refreshData() async {
    await loadSavedReports();
    await loadFilterOptions();
  }
}
