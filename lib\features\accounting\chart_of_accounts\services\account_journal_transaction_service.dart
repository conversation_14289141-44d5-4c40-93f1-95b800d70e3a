import 'dart:developer' as dev;
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../../../models/finance/journal_entry_model.dart';
import '../../../../../models/finance/chart_of_accounts_model.dart';
import '../../../../../features/accounting/journal_entries/repositories/journal_entry_repository.dart';

/// Model for account-specific transaction view
class AccountJournalTransaction {
  final String id;
  final String journalEntryId;
  final String entryNumber;
  final DateTime entryDate;
  final String description;
  final double debitAmount;
  final double creditAmount;
  final double runningBalance;
  final String referenceType;
  final String? referenceId;
  final JournalEntryStatus status;

  AccountJournalTransaction({
    required this.id,
    required this.journalEntryId,
    required this.entryNumber,
    required this.entryDate,
    required this.description,
    required this.debitAmount,
    required this.creditAmount,
    required this.runningBalance,
    required this.referenceType,
    this.referenceId,
    required this.status,
  });

  /// Get the transaction amount (positive for debits, negative for credits)
  double get amount => debitAmount > 0 ? debitAmount : -creditAmount;

  /// Check if this is a debit transaction
  bool get isDebit => debitAmount > 0;

  /// Check if this is a credit transaction
  bool get isCredit => creditAmount > 0;
}

/// Result model for paginated account transactions
class PaginatedAccountTransactionResult {
  final List<AccountJournalTransaction> transactions;
  final bool hasNextPage;
  final QueryDocumentSnapshot? nextPageCursor;
  final int totalCount;

  PaginatedAccountTransactionResult({
    required this.transactions,
    required this.hasNextPage,
    this.nextPageCursor,
    required this.totalCount,
  });
}

/// Service for retrieving account-specific journal entry transactions
class AccountJournalTransactionService {
  final JournalEntryRepository _journalRepository;

  AccountJournalTransactionService(this._journalRepository);

  /// Get paginated transactions for a specific account
  Future<PaginatedAccountTransactionResult> getAccountTransactionsPaginated({
    required String accountId,
    required String uid,
    int limit = 25,
    QueryDocumentSnapshot? lastDocument,
    DateTime? startDate,
    DateTime? endDate,
    List<JournalEntryStatus>? statusFilter,
  }) async {
    try {
      dev.log('Loading transactions for account: $accountId');

      // Get journal entries that contain transactions for this account
      final journalEntriesResult = await _journalRepository.getJournalEntriesForAccount(
        accountId: accountId,
        uid: uid,
        limit: limit,
        lastDocument: lastDocument,
        startDate: startDate,
        endDate: endDate,
        statusFilter: statusFilter,
      );

      return journalEntriesResult.fold(
        (failure) {
          dev.log('Failed to load journal entries: ${failure.message}');
          return PaginatedAccountTransactionResult(
            transactions: [],
            hasNextPage: false,
            totalCount: 0,
          );
        },
        (journalEntries) {
          // Extract account-specific transactions and calculate running balances
          final transactions = _extractAccountTransactions(
            journalEntries,
            accountId,
          );

          return PaginatedAccountTransactionResult(
            transactions: transactions,
            hasNextPage: journalEntries.length == limit,
            nextPageCursor: journalEntries.isNotEmpty ? null : null, // TODO: Implement cursor
            totalCount: transactions.length,
          );
        },
      );
    } catch (e) {
      dev.log('Error loading account transactions: $e');
      return PaginatedAccountTransactionResult(
        transactions: [],
        hasNextPage: false,
        totalCount: 0,
      );
    }
  }

  /// Extract transactions for a specific account from journal entries
  List<AccountJournalTransaction> _extractAccountTransactions(
    List<JournalEntryModel> journalEntries,
    String accountId,
  ) {
    final transactions = <AccountJournalTransaction>[];
    double runningBalance = 0.0;

    // Sort journal entries by date (oldest first) for proper running balance calculation
    journalEntries.sort((a, b) => a.entryDate.compareTo(b.entryDate));

    for (final entry in journalEntries) {
      // Find lines that affect this account
      final accountLines = entry.lines.where((line) => line.accountId == accountId);

      for (final line in accountLines) {
        // Update running balance based on account type
        // For now, we'll use a simple approach: debits increase balance, credits decrease
        // In a real system, this would depend on the account type (asset, liability, etc.)
        if (line.isDebit) {
          runningBalance += line.debitAmount;
        } else {
          runningBalance -= line.creditAmount;
        }

        transactions.add(AccountJournalTransaction(
          id: line.id,
          journalEntryId: entry.id,
          entryNumber: entry.entryNumber,
          entryDate: entry.entryDate,
          description: line.description,
          debitAmount: line.debitAmount,
          creditAmount: line.creditAmount,
          runningBalance: runningBalance,
          referenceType: line.referenceType ?? 'journal_entry',
          referenceId: line.referenceId,
          status: entry.status,
        ));
      }
    }

    // Sort by date (newest first) for display
    transactions.sort((a, b) => b.entryDate.compareTo(a.entryDate));

    return transactions;
  }

  /// Calculate running balance for account based on account type
  double _calculateRunningBalance(
    double currentBalance,
    double debitAmount,
    double creditAmount,
    AccountCategory accountCategory,
  ) {
    // Account types that increase with debits
    final debitAccounts = [
      AccountCategory.assets,
      AccountCategory.expenses,
    ];

    // Account types that increase with credits
    final creditAccounts = [
      AccountCategory.liabilities,
      AccountCategory.equity,
      AccountCategory.revenue,
    ];

    if (debitAccounts.contains(accountCategory)) {
      // For debit accounts: debits increase, credits decrease
      return currentBalance + debitAmount - creditAmount;
    } else if (creditAccounts.contains(accountCategory)) {
      // For credit accounts: credits increase, debits decrease
      return currentBalance + creditAmount - debitAmount;
    } else {
      // Default behavior
      return currentBalance + debitAmount - creditAmount;
    }
  }

  /// Get transaction count for an account
  Future<int> getAccountTransactionCount({
    required String accountId,
    required String uid,
    DateTime? startDate,
    DateTime? endDate,
    List<JournalEntryStatus>? statusFilter,
  }) async {
    try {
      final result = await _journalRepository.getJournalEntriesForAccount(
        accountId: accountId,
        uid: uid,
        limit: 1000, // Large limit to get count
        startDate: startDate,
        endDate: endDate,
        statusFilter: statusFilter,
      );

      return result.fold(
        (failure) => 0,
        (journalEntries) {
          int count = 0;
          for (final entry in journalEntries) {
            count += entry.lines.where((line) => line.accountId == accountId).length;
          }
          return count;
        },
      );
    } catch (e) {
      dev.log('Error getting transaction count: $e');
      return 0;
    }
  }

  /// Get account balance as of a specific date
  Future<double> getAccountBalanceAsOfDate({
    required String accountId,
    required String uid,
    required DateTime asOfDate,
    required AccountCategory accountCategory,
  }) async {
    try {
      final result = await _journalRepository.getJournalEntriesForAccount(
        accountId: accountId,
        uid: uid,
        endDate: asOfDate,
        limit: 1000, // Large limit to get all transactions
      );

      return result.fold(
        (failure) => 0.0,
        (journalEntries) {
          double balance = 0.0;
          
          for (final entry in journalEntries) {
            final accountLines = entry.lines.where((line) => line.accountId == accountId);
            
            for (final line in accountLines) {
              balance = _calculateRunningBalance(
                balance,
                line.debitAmount,
                line.creditAmount,
                accountCategory,
              );
            }
          }
          
          return balance;
        },
      );
    } catch (e) {
      dev.log('Error calculating account balance: $e');
      return 0.0;
    }
  }
}
