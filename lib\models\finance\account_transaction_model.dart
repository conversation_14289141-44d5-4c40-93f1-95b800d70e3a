import 'package:uuid/uuid.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

enum TransactionType {
  deposit, // Money coming into the account
  expense, // Money going out from the account
  voucherPayment, // Payment related to a voucher
  brokerFees, // Broker fees payment
  munshiana, // Munshiana payment
  loan, // Loan transactions (both incoming and outgoing)
  other // Any other type of transaction
}

class AccountTransactionModel {
  final String id;
  final String accountId;
  final String accountName;
  final double amount;
  final DateTime transactionDate;
  final TransactionType type;
  final String description;

  // Reference information
  final String? referenceId; // ID of related entity (voucher, deposit, etc.)
  final String? referenceName; // Name of related entity

  // Participant information
  final String? payerId; // ID of the payer (if applicable)
  final String? payerName; // Name of the payer
  final String? payeeId; // ID of the payee (if applicable)
  final String? payeeName; // Name of the payee

  // Additional tracking information
  final String? voucherId; // ID of related voucher (if applicable)
  final String? voucherNumber; // Voucher number for reference
  final String? category; // Category of transaction
  final Map<String, dynamic>? metadata; // Additional data
  final String uid; // User ID who owns this transaction

  AccountTransactionModel({
    required this.id,
    required this.accountId,
    required this.accountName,
    required this.amount,
    required this.transactionDate,
    required this.type,
    required this.description,
    this.referenceId,
    this.referenceName,
    this.payerId,
    this.payerName,
    this.payeeId,
    this.payeeName,
    this.voucherId,
    this.voucherNumber,
    this.category,
    this.metadata,
    this.uid = '', // Default empty string for backward compatibility
  });

  // Helper method to parse DateTime from various formats
  static DateTime _parseDateTime(dynamic value) {
    if (value == null) {
      return DateTime.now(); // Default to current time if null
    }

    if (value is DateTime) {
      return value;
    }

    if (value is Timestamp) {
      return value.toDate();
    }

    if (value is String) {
      try {
        return DateTime.parse(value);
      } catch (e) {
        return DateTime.now(); // Fallback to current time if parsing fails
      }
    }

    return DateTime.now(); // Default fallback
  }

  // Factory constructor to create a transaction from a map
  factory AccountTransactionModel.fromJson(Map<String, dynamic> json) {
    return AccountTransactionModel(
      id: json['id'] ?? const Uuid().v4(),
      accountId: json['accountId'] ?? '',
      accountName: json['accountName'] ?? '',
      amount: (json['amount'] as num?)?.toDouble() ?? 0.0,
      transactionDate: _parseDateTime(json['transactionDate']),
      type: _parseTransactionType(json['type']),
      description: json['description'] ?? '',
      referenceId: json['referenceId'],
      referenceName: json['referenceName'],
      payerId: json['payerId'],
      payerName: json['payerName'],
      payeeId: json['payeeId'],
      payeeName: json['payeeName'],
      voucherId: json['voucherId'],
      voucherNumber: json['voucherNumber'],
      category: json['category'],
      metadata: json['metadata'],
      uid: json['uid'] as String? ?? '', // Extract UID from JSON
    );
  }

  // Helper method to parse transaction type from string or int
  static TransactionType _parseTransactionType(dynamic type) {
    if (type is int && type >= 0 && type < TransactionType.values.length) {
      return TransactionType.values[type];
    } else if (type is String) {
      try {
        return TransactionType.values.firstWhere(
          (e) =>
              e.toString().split('.').last.toLowerCase() == type.toLowerCase(),
        );
      } catch (_) {
        return TransactionType.other;
      }
    }
    return TransactionType.other;
  }

  // Convert transaction to a map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'accountId': accountId,
      'accountName': accountName,
      'amount': amount,
      'transactionDate':
          transactionDate.toIso8601String(), // Always use ISO8601 string format
      'type': type.toString().split('.').last.toLowerCase(),
      'description': description,
      'referenceId': referenceId,
      'referenceName': referenceName,
      'payerId': payerId,
      'payerName': payerName,
      'payeeId': payeeId,
      'payeeName': payeeName,
      'voucherId': voucherId,
      'voucherNumber': voucherNumber,
      'category': category,
      'metadata': metadata,
      'uid': uid, // Include UID in JSON
    };
  }

  // Create a copy with modified properties
  AccountTransactionModel copyWith({
    String? id,
    String? accountId,
    String? accountName,
    double? amount,
    DateTime? transactionDate,
    TransactionType? type,
    String? description,
    String? referenceId,
    String? referenceName,
    String? payerId,
    String? payerName,
    String? payeeId,
    String? payeeName,
    String? voucherId,
    String? voucherNumber,
    String? category,
    Map<String, dynamic>? metadata,
    String? uid,
  }) {
    return AccountTransactionModel(
      id: id ?? this.id,
      accountId: accountId ?? this.accountId,
      accountName: accountName ?? this.accountName,
      amount: amount ?? this.amount,
      transactionDate: transactionDate ?? this.transactionDate,
      type: type ?? this.type,
      description: description ?? this.description,
      referenceId: referenceId ?? this.referenceId,
      referenceName: referenceName ?? this.referenceName,
      payerId: payerId ?? this.payerId,
      payerName: payerName ?? this.payerName,
      payeeId: payeeId ?? this.payeeId,
      payeeName: payeeName ?? this.payeeName,
      voucherId: voucherId ?? this.voucherId,
      voucherNumber: voucherNumber ?? this.voucherNumber,
      category: category ?? this.category,
      metadata: metadata ?? this.metadata,
      uid: uid ?? this.uid,
    );
  }
}
