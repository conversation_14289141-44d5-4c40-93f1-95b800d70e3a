import 'dart:developer';

import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:logestics/models/invoice_model.dart';
import 'package:logestics/models/voucher_model.dart';

class Company {
  final String uid;
  RxList<InvoiceModel> invoices = <InvoiceModel>[].obs;

  Company({required this.uid});

  void updateInvoice(InvoiceModel updatedInvoice) {
    int index = invoices
        .indexWhere((invoice) => invoice.tasNumber == updatedInvoice.tasNumber);
    if (index != -1) {
      invoices[index] = updatedInvoice; // Update the specific invoice
    }
  }

  void removeInvoice(String tasNumber) {
    invoices.removeWhere(
        (invoice) => invoice.tasNumber == tasNumber); // Remove an invoice
  }

  void addInvoice(InvoiceModel newInvoice) {
    // Check if invoice already exists to prevent duplicates
    bool exists =
        invoices.any((invoice) => invoice.tasNumber == newInvoice.tasNumber);
    if (!exists) {
      invoices.add(newInvoice); // Add a new invoice only if it doesn't exist
    }
  }

  RxList<VoucherModel> vouchers = <VoucherModel>[].obs;

  void updateVoucher(VoucherModel updatedVoucher) {
    int index = vouchers.indexWhere(
        (voucher) => voucher.voucherNumber == updatedVoucher.voucherNumber);
    if (index != -1) {
      vouchers[index] = updatedVoucher; // Update the specific invoice
    }
  }

  void removeVoucher(String voucherNumber) {
    vouchers.removeWhere((voucher) =>
        voucher.voucherNumber == voucherNumber); // Remove an invoice
  }

  void addVoucher(VoucherModel newVoucher) {
    // Check if voucher already exists to prevent duplicates
    bool exists = vouchers
        .any((voucher) => voucher.voucherNumber == newVoucher.voucherNumber);
    if (!exists) {
      vouchers.add(newVoucher); // Add a new voucher only if it doesn't exist
    }
  }

  // Debug methods
  int get invoiceCount => invoices.length;
  int get voucherCount => vouchers.length;

  void printDebugInfo() {
    log('Company Debug Info:');
    log('  Total Invoices: ${invoices.length}');
    log('  Total Vouchers: ${vouchers.length}');
    if (invoices.isNotEmpty) {
      log('  Latest Invoice: ${invoices.last.tasNumber}');
    }
    if (vouchers.isNotEmpty) {
      log('  Latest Voucher: ${vouchers.last.voucherNumber}');
    }
  }
}
