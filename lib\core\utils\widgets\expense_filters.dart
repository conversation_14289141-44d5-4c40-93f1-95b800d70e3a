import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/features/finance/expenses/presentation/controllers/expense_controller.dart';
import 'package:intl/intl.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';

class ExpenseFilters extends StatelessWidget {
  final ExpenseController controller;

  const ExpenseFilters({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    notifier = Provider.of(context, listen: true);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: notifier.getcardColor,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Filter Expenses',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                  color: notifier.text,
                ),
              ),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: controller.toggleFilter,
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildDateFilter(context),
              ),
            ],
          ),
          if (controller.dateFilterType.value == 'custom') ...[
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildDateRangePicker(context),
                ),
              ],
            ),
          ],
          const SizedBox(height: 16),
          Align(
            alignment: Alignment.centerRight,
            child: ElevatedButton.icon(
              icon: const Icon(Icons.refresh),
              label: const Text('Reset Filters'),
              onPressed: () {
                controller.dateFilterType.value = 'all';
                controller.selectedStartDate.value =
                    DateTime.now().subtract(const Duration(days: 30));
                controller.selectedEndDate.value = DateTime.now();
                controller.filterExpenses();
              },
              style: ElevatedButton.styleFrom(
                foregroundColor: Colors.white,
                backgroundColor: Colors.blue,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateFilter(BuildContext context) {
    return Obx(() {
      return Container(
        decoration: BoxDecoration(
          color: notifier.textFileColor,
          borderRadius: BorderRadius.circular(8),
        ),
        child: DropdownButtonFormField<String>(
          decoration: InputDecoration(
            labelText: 'Date Filter',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          ),
          value: controller.dateFilterType.value,
          items: const [
            DropdownMenuItem(value: 'all', child: Text('All Time')),
            DropdownMenuItem(value: 'today', child: Text('Today')),
            DropdownMenuItem(value: 'yesterday', child: Text('Yesterday')),
            DropdownMenuItem(value: 'week', child: Text('Last 7 Days')),
            DropdownMenuItem(value: 'month', child: Text('This Month')),
            DropdownMenuItem(value: 'custom', child: Text('Custom Range')),
          ],
          onChanged: (value) {
            if (value != null) {
              controller.setDateFilterType(value);
            }
          },
        ),
      );
    });
  }

  Widget _buildDateRangePicker(BuildContext context) {
    final dateFormat = DateFormat('dd/MM/yyyy');

    return Obx(() {
      return Row(
        children: [
          Expanded(
            child: InkWell(
              onTap: () => controller.selectStartDate(context),
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                  color: notifier.textFileColor,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'From: ${dateFormat.format(controller.selectedStartDate.value)}',
                      style: TextStyle(color: notifier.text),
                    ),
                    Icon(Icons.calendar_today, color: notifier.text, size: 16),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: InkWell(
              onTap: () => controller.selectEndDate(context),
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                  color: notifier.textFileColor,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'To: ${dateFormat.format(controller.selectedEndDate.value)}',
                      style: TextStyle(color: notifier.text),
                    ),
                    Icon(Icons.calendar_today, color: notifier.text, size: 16),
                  ],
                ),
              ),
            ),
          ),
        ],
      );
    });
  }
}
