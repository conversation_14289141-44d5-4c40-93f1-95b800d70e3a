import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';
import 'package:logestics/models/finance/check_usage_model.dart';

class CheckUsageFirebaseService {
  late FirebaseFirestore _firestore;

  // Get current user's UID or return 'anonymous' if not authenticated
  String get _uid => FirebaseAuth.instance.currentUser?.uid ?? 'anonymous';

  CheckUsageFirebaseService() {
    _firestore = FirebaseFirestore.instance;
  }

  Future<void> createCheckUsage(CheckUsageModel checkUsage) async {
    log('Creating check usage record: ${checkUsage.checkNumber}');
    try {
      final checkUsageRef =
          _firestore.collection(AppCollection.checkUsageCollection).doc();
      final checkUsageId = checkUsageRef.id;

      final checkUsageData = checkUsage.toJson();
      checkUsageData['id'] = checkUsageId;
      checkUsageData['uid'] = _uid; // Add current user's UID

      await checkUsageRef.set(checkUsageData);
      log('Successfully created check usage record: $checkUsageId');
    } catch (e) {
      log('Error creating check usage record: $e');
      rethrow;
    }
  }

  Future<List<CheckUsageModel>> getCheckUsages() async {
    log('Fetching check usage records from Firestore');
    try {
      final snapshot = await _firestore
          .collection(AppCollection.checkUsageCollection)
          .where('uid', isEqualTo: _uid) // Filter by current user's UID
          .orderBy('usageDate', descending: true)
          .get();

      final checkUsages = snapshot.docs
          .map((doc) => CheckUsageModel.fromJson(doc.data()))
          .toList();

      log('Successfully fetched ${checkUsages.length} check usage records');
      return checkUsages;
    } catch (e) {
      log('Error fetching check usage records: $e');
      rethrow;
    }
  }

  Future<List<CheckUsageModel>> getActiveChecks() async {
    log('Fetching active check records from Firestore');
    try {
      final snapshot = await _firestore
          .collection(AppCollection.checkUsageCollection)
          .where('uid', isEqualTo: _uid)
          .where('status', whereIn: ['issued', 'cleared'])
          .orderBy('usageDate', descending: true)
          .get();

      final activeChecks = snapshot.docs
          .map((doc) => CheckUsageModel.fromJson(doc.data()))
          .toList();

      log('Successfully fetched ${activeChecks.length} active check records');
      return activeChecks;
    } catch (e) {
      log('Error fetching active check records: $e');
      rethrow;
    }
  }

  Future<void> updateCheckStatus(String checkUsageId, String newStatus) async {
    log('Updating check status: $checkUsageId to $newStatus');
    try {
      if (checkUsageId.isEmpty) {
        throw ArgumentError('Check usage ID cannot be empty');
      }

      // First check if the check usage belongs to the current user
      final checkUsageDoc = await _firestore
          .collection(AppCollection.checkUsageCollection)
          .doc(checkUsageId)
          .get();

      if (!checkUsageDoc.exists) {
        throw Exception('Check usage record not found');
      }

      final checkUsageData = checkUsageDoc.data() as Map<String, dynamic>;
      if (checkUsageData['uid'] != _uid) {
        throw Exception('You do not have permission to update this check usage record');
      }

      await _firestore
          .collection(AppCollection.checkUsageCollection)
          .doc(checkUsageId)
          .update({'status': newStatus});
      log('Successfully updated check status: $checkUsageId');
    } catch (e) {
      log('Error updating check status: $e');
      rethrow;
    }
  }

  Future<void> deleteCheckUsage(String checkUsageId) async {
    log('Deleting check usage record: $checkUsageId');
    try {
      if (checkUsageId.isEmpty) {
        throw ArgumentError('Check usage ID cannot be empty');
      }

      // First check if the check usage belongs to the current user
      final checkUsageDoc = await _firestore
          .collection(AppCollection.checkUsageCollection)
          .doc(checkUsageId)
          .get();

      if (!checkUsageDoc.exists) {
        throw Exception('Check usage record not found');
      }

      final checkUsageData = checkUsageDoc.data() as Map<String, dynamic>;
      if (checkUsageData['uid'] != _uid) {
        throw Exception('You do not have permission to delete this check usage record');
      }

      await _firestore
          .collection(AppCollection.checkUsageCollection)
          .doc(checkUsageId)
          .delete();
      log('Successfully deleted check usage record: $checkUsageId');
    } catch (e) {
      log('Error deleting check usage record: $e');
      rethrow;
    }
  }

  /// Stream to listen for real-time updates to check usage records
  Stream<List<CheckUsageModel>> listenToCheckUsages() {
    try {
      return _firestore
          .collection(AppCollection.checkUsageCollection)
          .where('uid', isEqualTo: _uid) // Filter by current user's UID
          .orderBy('usageDate', descending: true)
          .snapshots()
          .map((snapshot) => snapshot.docs
              .map((doc) => CheckUsageModel.fromJson(doc.data()))
              .toList());
    } catch (e) {
      log('Error listening to check usage records: $e', error: e);
      return Stream.value([]);
    }
  }

  /// Stream to listen for active checks only
  Stream<List<CheckUsageModel>> listenToActiveChecks() {
    try {
      return _firestore
          .collection(AppCollection.checkUsageCollection)
          .where('uid', isEqualTo: _uid)
          .where('status', whereIn: ['issued', 'cleared'])
          .orderBy('usageDate', descending: true)
          .snapshots()
          .map((snapshot) => snapshot.docs
              .map((doc) => CheckUsageModel.fromJson(doc.data()))
              .toList());
    } catch (e) {
      log('Error listening to active checks: $e', error: e);
      return Stream.value([]);
    }
  }
}
