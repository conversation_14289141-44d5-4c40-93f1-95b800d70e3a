class AssetModel {
  final String id;
  final String name;
  final String type; // Vehicle, Machinery, Tools, Equipment, Other
  final String brand;
  final String model;
  final String registrationNumber;
  final String serialNumber;
  final DateTime purchaseDate;
  final double purchaseCost;
  final String vendor;
  final String supplier;
  final String location;
  final String department;
  final String status; // In Use, Under Maintenance, Retired, Sold
  final String depreciationMethod; // Straight-line, Declining Balance
  final int estimatedUsefulLife; // in years
  final double currentValue;
  final bool isCurrentValueManual; // true if manually overridden
  final List<String> attachmentUrls; // Firebase Storage URLs
  final String notes;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String uid; // User ID who owns this asset

  AssetModel({
    required this.id,
    required this.name,
    required this.type,
    required this.brand,
    required this.model,
    required this.registrationNumber,
    required this.serialNumber,
    required this.purchaseDate,
    required this.purchaseCost,
    required this.vendor,
    required this.supplier,
    required this.location,
    required this.department,
    required this.status,
    required this.depreciationMethod,
    required this.estimatedUsefulLife,
    required this.currentValue,
    this.isCurrentValueManual = false,
    this.attachmentUrls = const [],
    this.notes = '',
    required this.createdAt,
    required this.updatedAt,
    this.uid = '',
  });

  /// Calculate depreciated value based on depreciation method
  double calculateDepreciatedValue() {
    if (isCurrentValueManual) {
      return currentValue;
    }

    final now = DateTime.now();
    final yearsElapsed = now.difference(purchaseDate).inDays / 365.25;
    
    if (yearsElapsed >= estimatedUsefulLife) {
      return 0.0; // Fully depreciated
    }

    switch (depreciationMethod) {
      case 'Straight-line':
        final annualDepreciation = purchaseCost / estimatedUsefulLife;
        return purchaseCost - (annualDepreciation * yearsElapsed);
      
      case 'Declining Balance':
        // Using double declining balance method (2 / useful life)
        final depreciationRate = 2.0 / estimatedUsefulLife;
        double value = purchaseCost;
        
        for (int year = 0; year < yearsElapsed.floor(); year++) {
          value = value * (1 - depreciationRate);
        }
        
        // Handle partial year
        final partialYear = yearsElapsed - yearsElapsed.floor();
        if (partialYear > 0) {
          value = value * (1 - (depreciationRate * partialYear));
        }
        
        return value;
      
      default:
        return currentValue;
    }
  }

  /// Calculate accumulated depreciation
  double get accumulatedDepreciation {
    return purchaseCost - calculateDepreciatedValue();
  }

  /// Calculate annual depreciation amount
  double get annualDepreciation {
    switch (depreciationMethod) {
      case 'Straight-line':
        return purchaseCost / estimatedUsefulLife;
      case 'Declining Balance':
        return calculateDepreciatedValue() * (2.0 / estimatedUsefulLife);
      default:
        return 0.0;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'brand': brand,
      'model': model,
      'registrationNumber': registrationNumber,
      'serialNumber': serialNumber,
      'purchaseDate': purchaseDate.millisecondsSinceEpoch,
      'purchaseCost': purchaseCost,
      'vendor': vendor,
      'supplier': supplier,
      'location': location,
      'department': department,
      'status': status,
      'depreciationMethod': depreciationMethod,
      'estimatedUsefulLife': estimatedUsefulLife,
      'currentValue': currentValue,
      'isCurrentValueManual': isCurrentValueManual,
      'attachmentUrls': attachmentUrls,
      'notes': notes,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
      'uid': uid,
    };
  }

  factory AssetModel.fromJson(Map<String, dynamic> json) {
    return AssetModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      type: json['type'] ?? '',
      brand: json['brand'] ?? '',
      model: json['model'] ?? '',
      registrationNumber: json['registrationNumber'] ?? '',
      serialNumber: json['serialNumber'] ?? '',
      purchaseDate: DateTime.fromMillisecondsSinceEpoch(json['purchaseDate'] ?? 0),
      purchaseCost: (json['purchaseCost'] ?? 0).toDouble(),
      vendor: json['vendor'] ?? '',
      supplier: json['supplier'] ?? '',
      location: json['location'] ?? '',
      department: json['department'] ?? '',
      status: json['status'] ?? '',
      depreciationMethod: json['depreciationMethod'] ?? '',
      estimatedUsefulLife: json['estimatedUsefulLife'] ?? 0,
      currentValue: (json['currentValue'] ?? 0).toDouble(),
      isCurrentValueManual: json['isCurrentValueManual'] ?? false,
      attachmentUrls: List<String>.from(json['attachmentUrls'] ?? []),
      notes: json['notes'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt'] ?? 0),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(json['updatedAt'] ?? 0),
      uid: json['uid'] ?? '',
    );
  }

  AssetModel copyWith({
    String? id,
    String? name,
    String? type,
    String? brand,
    String? model,
    String? registrationNumber,
    String? serialNumber,
    DateTime? purchaseDate,
    double? purchaseCost,
    String? vendor,
    String? supplier,
    String? location,
    String? department,
    String? status,
    String? depreciationMethod,
    int? estimatedUsefulLife,
    double? currentValue,
    bool? isCurrentValueManual,
    List<String>? attachmentUrls,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? uid,
  }) {
    return AssetModel(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      brand: brand ?? this.brand,
      model: model ?? this.model,
      registrationNumber: registrationNumber ?? this.registrationNumber,
      serialNumber: serialNumber ?? this.serialNumber,
      purchaseDate: purchaseDate ?? this.purchaseDate,
      purchaseCost: purchaseCost ?? this.purchaseCost,
      vendor: vendor ?? this.vendor,
      supplier: supplier ?? this.supplier,
      location: location ?? this.location,
      department: department ?? this.department,
      status: status ?? this.status,
      depreciationMethod: depreciationMethod ?? this.depreciationMethod,
      estimatedUsefulLife: estimatedUsefulLife ?? this.estimatedUsefulLife,
      currentValue: currentValue ?? this.currentValue,
      isCurrentValueManual: isCurrentValueManual ?? this.isCurrentValueManual,
      attachmentUrls: attachmentUrls ?? this.attachmentUrls,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      uid: uid ?? this.uid,
    );
  }

  @override
  String toString() {
    return 'AssetModel(id: $id, name: $name, type: $type, status: $status, currentValue: $currentValue)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AssetModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Asset type constants
class AssetType {
  static const String vehicle = 'Vehicle';
  static const String machinery = 'Machinery';
  static const String tools = 'Tools';
  static const String equipment = 'Equipment';
  static const String other = 'Other';

  static List<String> get allTypes => [
    vehicle,
    machinery,
    tools,
    equipment,
    other,
  ];
}

/// Asset status constants
class AssetStatus {
  static const String inUse = 'In Use';
  static const String underMaintenance = 'Under Maintenance';
  static const String retired = 'Retired';
  static const String sold = 'Sold';

  static List<String> get allStatuses => [
    inUse,
    underMaintenance,
    retired,
    sold,
  ];
}

/// Depreciation method constants
class DepreciationMethod {
  static const String straightLine = 'Straight-line';
  static const String decliningBalance = 'Declining Balance';

  static List<String> get allMethods => [
    straightLine,
    decliningBalance,
  ];
}
