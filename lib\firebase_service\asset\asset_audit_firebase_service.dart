import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';
import 'package:logestics/models/asset/asset_audit_model.dart';
import 'package:logestics/models/asset/asset_model.dart';

class AssetAuditFirebaseService {
  late FirebaseFirestore _firestore;

  // Get current user's UID or return 'anonymous' if not authenticated
  String get _uid => FirebaseAuth.instance.currentUser?.uid ?? 'anonymous';

  // Get current user's display name
  String get _userName =>
      FirebaseAuth.instance.currentUser?.displayName ??
      FirebaseAuth.instance.currentUser?.email ??
      'Unknown User';

  AssetAuditFirebaseService() {
    _firestore = FirebaseFirestore.instance;
  }

  /// Create an audit trail entry
  Future<void> createAuditEntry(AssetAuditModel auditEntry) async {
    log('Creating audit entry for asset: ${auditEntry.assetId}, action: ${auditEntry.action}');
    try {
      final auditRef =
          _firestore.collection(AppCollection.assetAuditTrailCollection).doc();
      final auditId = auditRef.id;

      final auditData = auditEntry
          .copyWith(
            id: auditId,
            timestamp: DateTime.now(),
            uid: _uid,
          )
          .toJson();

      await auditRef.set(auditData);
      log('Successfully created audit entry: $auditId');
    } catch (e) {
      log('Error creating audit entry: $e');
      rethrow;
    }
  }

  /// Log asset creation
  Future<void> logAssetCreated(AssetModel asset, {String notes = ''}) async {
    final auditEntry = AssetAuditModel(
      id: '',
      assetId: asset.id,
      assetName: asset.name,
      userId: _uid,
      userName: _userName,
      action: AssetAuditAction.created,
      fieldChanges: _buildCreationFieldChanges(asset),
      timestamp: DateTime.now(),
      notes: notes,
      uid: _uid,
    );

    await createAuditEntry(auditEntry);
  }

  /// Log asset update with field comparison
  Future<void> logAssetUpdated(AssetModel oldAsset, AssetModel newAsset,
      {String notes = ''}) async {
    final fieldChanges = _compareAssetFields(oldAsset, newAsset);

    if (fieldChanges.isEmpty) {
      log('No field changes detected for asset update: ${newAsset.id}');
      return;
    }

    final auditEntry = AssetAuditModel(
      id: '',
      assetId: newAsset.id,
      assetName: newAsset.name,
      userId: _uid,
      userName: _userName,
      action: AssetAuditAction.updated,
      fieldChanges: fieldChanges,
      timestamp: DateTime.now(),
      notes: notes,
      uid: _uid,
    );

    await createAuditEntry(auditEntry);

    // Log specific status change if status was modified
    if (fieldChanges.containsKey('status')) {
      await _logStatusChange(newAsset, fieldChanges['status']);
    }
  }

  /// Log asset deletion
  Future<void> logAssetDeleted(AssetModel asset, {String notes = ''}) async {
    final auditEntry = AssetAuditModel(
      id: '',
      assetId: asset.id,
      assetName: asset.name,
      userId: _uid,
      userName: _userName,
      action: AssetAuditAction.deleted,
      fieldChanges: _buildDeletionFieldChanges(asset),
      timestamp: DateTime.now(),
      notes: notes,
      uid: _uid,
    );

    await createAuditEntry(auditEntry);
  }

  /// Log maintenance record addition
  Future<void> logMaintenanceAdded(
      String assetId, String assetName, String maintenanceId,
      {String notes = ''}) async {
    final auditEntry = AssetAuditModel(
      id: '',
      assetId: assetId,
      assetName: assetName,
      userId: _uid,
      userName: _userName,
      action: AssetAuditAction.maintenanceAdded,
      fieldChanges: {
        'maintenanceId': {'old': null, 'new': maintenanceId}
      },
      timestamp: DateTime.now(),
      notes: notes,
      uid: _uid,
    );

    await createAuditEntry(auditEntry);
  }

  /// Log file upload
  Future<void> logFileUploaded(
      String assetId, String assetName, String fileName,
      {String notes = ''}) async {
    final auditEntry = AssetAuditModel(
      id: '',
      assetId: assetId,
      assetName: assetName,
      userId: _uid,
      userName: _userName,
      action: AssetAuditAction.fileUploaded,
      fieldChanges: {
        'fileName': {'old': null, 'new': fileName}
      },
      timestamp: DateTime.now(),
      notes: notes,
      uid: _uid,
    );

    await createAuditEntry(auditEntry);
  }

  /// Log file deletion
  Future<void> logFileDeleted(String assetId, String assetName, String fileName,
      {String notes = ''}) async {
    final auditEntry = AssetAuditModel(
      id: '',
      assetId: assetId,
      assetName: assetName,
      userId: _uid,
      userName: _userName,
      action: AssetAuditAction.fileDeleted,
      fieldChanges: {
        'fileName': {'old': fileName, 'new': null}
      },
      timestamp: DateTime.now(),
      notes: notes,
      uid: _uid,
    );

    await createAuditEntry(auditEntry);
  }

  /// Get audit trail for a specific asset
  Future<List<AssetAuditModel>> getAssetAuditTrail(String assetId) async {
    log('Fetching audit trail for asset: $assetId');
    try {
      final snapshot = await _firestore
          .collection(AppCollection.assetAuditTrailCollection)
          .where('uid', isEqualTo: _uid)
          .where('assetId', isEqualTo: assetId)
          .orderBy('timestamp', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => AssetAuditModel.fromJson(doc.data()))
          .toList();
    } catch (e) {
      log('Error fetching asset audit trail: $e');
      return [];
    }
  }

  /// Get all audit trail entries with pagination
  Future<List<AssetAuditModel>> getAuditTrail({
    int limit = 25,
    DocumentSnapshot? startAfter,
    DateTime? startDate,
    DateTime? endDate,
    String? userId,
    String? action,
    String? assetId,
  }) async {
    log('Fetching audit trail with filters');
    try {
      Query query = _firestore
          .collection(AppCollection.assetAuditTrailCollection)
          .where('uid', isEqualTo: _uid);

      // Apply filters
      if (startDate != null) {
        query = query.where('timestamp',
            isGreaterThanOrEqualTo: startDate.millisecondsSinceEpoch);
      }
      if (endDate != null) {
        query = query.where('timestamp',
            isLessThanOrEqualTo: endDate.millisecondsSinceEpoch);
      }
      if (userId != null && userId.isNotEmpty) {
        query = query.where('userId', isEqualTo: userId);
      }
      if (action != null && action.isNotEmpty) {
        query = query.where('action', isEqualTo: action);
      }
      if (assetId != null && assetId.isNotEmpty) {
        query = query.where('assetId', isEqualTo: assetId);
      }

      query = query.orderBy('timestamp', descending: true).limit(limit);

      if (startAfter != null) {
        query = query.startAfterDocument(startAfter);
      }

      final snapshot = await query.get();
      return snapshot.docs
          .map((doc) =>
              AssetAuditModel.fromJson(doc.data() as Map<String, dynamic>))
          .toList();
    } catch (e) {
      log('Error fetching audit trail: $e');
      return [];
    }
  }

  /// Listen to audit trail changes in real-time
  Stream<List<AssetAuditModel>> listenToAuditTrail({
    int limit = 25,
    DateTime? startDate,
    DateTime? endDate,
    String? userId,
    String? action,
    String? assetId,
  }) {
    log('Setting up audit trail stream listener');
    try {
      Query query = _firestore
          .collection(AppCollection.assetAuditTrailCollection)
          .where('uid', isEqualTo: _uid);

      // Apply filters
      if (startDate != null) {
        query = query.where('timestamp',
            isGreaterThanOrEqualTo: startDate.millisecondsSinceEpoch);
      }
      if (endDate != null) {
        query = query.where('timestamp',
            isLessThanOrEqualTo: endDate.millisecondsSinceEpoch);
      }
      if (userId != null && userId.isNotEmpty) {
        query = query.where('userId', isEqualTo: userId);
      }
      if (action != null && action.isNotEmpty) {
        query = query.where('action', isEqualTo: action);
      }
      if (assetId != null && assetId.isNotEmpty) {
        query = query.where('assetId', isEqualTo: assetId);
      }

      return query
          .orderBy('timestamp', descending: true)
          .limit(limit)
          .snapshots()
          .map<List<AssetAuditModel>>((snapshot) {
        return snapshot.docs
            .map((doc) =>
                AssetAuditModel.fromJson(doc.data() as Map<String, dynamic>))
            .toList();
      });
    } catch (e) {
      log('Error setting up audit trail stream: $e');
      return Stream.value(<AssetAuditModel>[]);
    }
  }

  /// Private helper methods

  Map<String, dynamic> _buildCreationFieldChanges(AssetModel asset) {
    return {
      'name': {'old': null, 'new': asset.name},
      'type': {'old': null, 'new': asset.type},
      'status': {'old': null, 'new': asset.status},
      'purchaseCost': {'old': null, 'new': asset.purchaseCost},
      'location': {'old': null, 'new': asset.location},
    };
  }

  Map<String, dynamic> _buildDeletionFieldChanges(AssetModel asset) {
    return {
      'name': {'old': asset.name, 'new': null},
      'type': {'old': asset.type, 'new': null},
      'status': {'old': asset.status, 'new': null},
      'purchaseCost': {'old': asset.purchaseCost, 'new': null},
      'location': {'old': asset.location, 'new': null},
    };
  }

  Map<String, dynamic> _compareAssetFields(
      AssetModel oldAsset, AssetModel newAsset) {
    final changes = <String, dynamic>{};

    // Compare critical fields
    if (oldAsset.name != newAsset.name) {
      changes['name'] = {'old': oldAsset.name, 'new': newAsset.name};
    }
    if (oldAsset.type != newAsset.type) {
      changes['type'] = {'old': oldAsset.type, 'new': newAsset.type};
    }
    if (oldAsset.status != newAsset.status) {
      changes['status'] = {'old': oldAsset.status, 'new': newAsset.status};
    }
    if (oldAsset.location != newAsset.location) {
      changes['location'] = {
        'old': oldAsset.location,
        'new': newAsset.location
      };
    }
    if (oldAsset.department != newAsset.department) {
      changes['department'] = {
        'old': oldAsset.department,
        'new': newAsset.department
      };
    }
    if (oldAsset.purchaseCost != newAsset.purchaseCost) {
      changes['purchaseCost'] = {
        'old': oldAsset.purchaseCost,
        'new': newAsset.purchaseCost
      };
    }
    if (oldAsset.currentValue != newAsset.currentValue) {
      changes['currentValue'] = {
        'old': oldAsset.currentValue,
        'new': newAsset.currentValue
      };
    }
    if (oldAsset.vendor != newAsset.vendor) {
      changes['vendor'] = {'old': oldAsset.vendor, 'new': newAsset.vendor};
    }
    if (oldAsset.supplier != newAsset.supplier) {
      changes['supplier'] = {
        'old': oldAsset.supplier,
        'new': newAsset.supplier
      };
    }

    return changes;
  }

  Future<void> _logStatusChange(
      AssetModel asset, Map<String, dynamic> statusChange) async {
    final auditEntry = AssetAuditModel(
      id: '',
      assetId: asset.id,
      assetName: asset.name,
      userId: _uid,
      userName: _userName,
      action: AssetAuditAction.statusChanged,
      fieldChanges: {'status': statusChange},
      timestamp: DateTime.now(),
      notes:
          'Status changed from ${statusChange['old']} to ${statusChange['new']}',
      uid: _uid,
    );

    await createAuditEntry(auditEntry);
  }
}
