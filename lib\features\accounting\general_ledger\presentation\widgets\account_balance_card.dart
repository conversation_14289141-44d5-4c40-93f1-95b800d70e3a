import 'package:flutter/material.dart';
import '../../../../../models/finance/chart_of_accounts_model.dart';

class AccountBalanceCard extends StatelessWidget {
  final ChartOfAccountsModel account;
  final double balance;
  final VoidCallback? onTap;
  final bool isLoading;

  const AccountBalanceCard({
    Key? key,
    required this.account,
    required this.balance,
    this.onTap,
    this.isLoading = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Row
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          account.accountNumber,
                          style:
                              Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: Theme.of(context).primaryColor,
                                  ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          account.accountName,
                          style:
                              Theme.of(context).textTheme.titleSmall?.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      _buildAccountTypeChip(context),
                      const SizedBox(height: 4),
                      _buildStatusChip(context),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Description
              if (account.description?.isNotEmpty == true) ...[
                Text(
                  account.description!,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 12),
              ],

              // Balance Section
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Current Balance',
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Colors.grey[600],
                                    fontWeight: FontWeight.w500,
                                  ),
                        ),
                        const SizedBox(height: 4),
                        if (isLoading)
                          const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        else
                          Text(
                            '\$${balance.toStringAsFixed(2)}',
                            style: Theme.of(context)
                                .textTheme
                                .headlineSmall
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: _getBalanceColor(balance),
                                ),
                          ),
                      ],
                    ),
                  ),
                  _buildBalanceIndicator(context, balance),
                ],
              ),

              const SizedBox(height: 12),

              // Additional Info Row
              Row(
                children: [
                  Icon(
                    Icons.account_tree,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Parent: ${account.parentAccountId?.isNotEmpty == true ? account.parentAccountId : 'None'}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                  ),
                  const Spacer(),
                  if (account.parentAccountId != null) ...[
                    Icon(
                      Icons.subdirectory_arrow_right,
                      size: 16,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Sub Account',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                            fontStyle: FontStyle.italic,
                          ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAccountTypeChip(BuildContext context) {
    Color backgroundColor;
    Color textColor;
    IconData icon;

    switch (account.accountType) {
      // Asset Types
      case AccountType.cash:
      case AccountType.bank:
      case AccountType.accountsReceivable:
      case AccountType.inventory:
      case AccountType.fixedAssets:
      case AccountType.currentAssets:
        backgroundColor = Colors.green.withValues(alpha: 0.1);
        textColor = Colors.green[700]!;
        icon = Icons.account_balance_wallet;
        break;
      // Liability Types
      case AccountType.accountsPayable:
      case AccountType.loansPayable:
      case AccountType.currentLiabilities:
      case AccountType.longTermLiabilities:
        backgroundColor = Colors.red.withValues(alpha: 0.1);
        textColor = Colors.red[700]!;
        icon = Icons.credit_card;
        break;
      // Equity Types
      case AccountType.ownersEquity:
      case AccountType.retainedEarnings:
        backgroundColor = Colors.blue.withValues(alpha: 0.1);
        textColor = Colors.blue[700]!;
        icon = Icons.account_balance;
        break;
      // Revenue Types
      case AccountType.salesRevenue:
      case AccountType.serviceRevenue:
      case AccountType.otherRevenue:
        backgroundColor = Colors.purple.withValues(alpha: 0.1);
        textColor = Colors.purple[700]!;
        icon = Icons.trending_up;
        break;
      // Expense Types
      case AccountType.operatingExpenses:
      case AccountType.administrativeExpenses:
      case AccountType.interestExpense:
      case AccountType.taxExpense:
        backgroundColor = Colors.orange.withValues(alpha: 0.1);
        textColor = Colors.orange[700]!;
        icon = Icons.trending_down;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: textColor),
          const SizedBox(width: 4),
          Text(
            account.accountType.displayName,
            style: TextStyle(
              color: textColor,
              fontSize: 11,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(BuildContext context) {
    final isActive = account.isActive;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isActive
            ? Colors.green.withOpacity(0.1)
            : Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isActive ? Icons.check_circle : Icons.pause_circle,
            size: 12,
            color: isActive ? Colors.green[700] : Colors.grey[600],
          ),
          const SizedBox(width: 4),
          Text(
            account.isActive ? 'Active' : 'Inactive',
            style: TextStyle(
              color: isActive ? Colors.green[700] : Colors.grey[600],
              fontSize: 11,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBalanceIndicator(BuildContext context, double balance) {
    IconData icon;
    Color color;
    String label;

    if (balance > 0) {
      icon = Icons.trending_up;
      color = Colors.green;
      label = 'Positive';
    } else if (balance < 0) {
      icon = Icons.trending_down;
      color = Colors.red;
      label = 'Negative';
    } else {
      icon = Icons.remove;
      color = Colors.grey;
      label = 'Zero';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 11,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Color _getBalanceColor(double balance) {
    if (balance > 0) {
      return Colors.green[700]!;
    } else if (balance < 0) {
      return Colors.red[700]!;
    } else {
      return Colors.grey[600]!;
    }
  }
}
