import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:logestics/features/finance/fuel_cards/presentation/controllers/fuel_card_usage_controller.dart';

import 'package:logestics/core/utils/widgets/data_table_cell.dart';
import 'package:logestics/main.dart';

class FuelUsageHistoryView extends StatelessWidget {
  final String? fuelCardId;
  final String? fuelCardNumber;

  const FuelUsageHistoryView({
    super.key,
    this.fuelCardId,
    this.fuelCardNumber,
  });

  @override
  Widget build(BuildContext context) {
    notifier = Provider.of(context, listen: false);
    final controller = Get.put(FuelCardUsageController());

    // If specific fuel card is provided, load its usage
    if (fuelCardId != null && fuelCardNumber != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        controller.loadUsageForFuelCard(fuelCardId!, fuelCardNumber!);
      });
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(
          fuelCardNumber != null
              ? 'Usage History - $fuelCardNumber'
              : 'Fuel Card Usage History',
          style: TextStyle(color: notifier.text),
        ),
        iconTheme: IconThemeData(color: notifier.text),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => controller.refresh(),
            tooltip: 'Refresh',
          ),
          if (fuelCardId != null)
            IconButton(
              icon: const Icon(Icons.clear),
              onPressed: () => controller.clearFilters(),
              tooltip: 'Show All Records',
            ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Table
            Expanded(
              child: SizedBox(
                width: Get.width,
                child: Obx(() {
                  if (controller.isLoading.value) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  if (controller.error.isNotEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: 64,
                            color: Colors.red.shade300,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Error loading usage history',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: notifier.text,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            controller.error.value,
                            style: TextStyle(color: Colors.red.shade300),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () => controller.refresh(),
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    );
                  }

                  if (controller.filteredUsageRecords.isEmpty) {
                    return Center(
                      child: Text(
                        fuelCardNumber != null
                            ? 'No fuel usage recorded for this card yet'
                            : 'No fuel card usage recorded yet',
                        style: TextStyle(
                          fontSize: 16,
                          color: notifier.text,
                        ),
                      ),
                    );
                  }

                  return ListView(
                    shrinkWrap: true,
                    children: [
                      Table(
                        columnWidths: const {
                          0: FlexColumnWidth(0.5), // S.No
                          1: FlexColumnWidth(1.2), // Voucher
                          2: FlexColumnWidth(1.0), // Card Number
                          3: FlexColumnWidth(0.8), // Liters
                          4: FlexColumnWidth(0.8), // Rate
                          5: FlexColumnWidth(1.0), // Amount
                          6: FlexColumnWidth(1.0), // Date
                          7: FlexColumnWidth(1.2), // Driver
                        },
                        border: TableBorder(
                          horizontalInside: BorderSide(
                            color: notifier.getfillborder,
                          ),
                        ),
                        children: [
                          // Header row
                          TableRow(
                            decoration: BoxDecoration(
                              color: notifier.getHoverColor,
                            ),
                            children: [
                              DataTableCell(
                                text: 'S.No',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: notifier.text,
                                ),
                              ),
                              DataTableCell(
                                text: 'Voucher',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: notifier.text,
                                ),
                              ),
                              DataTableCell(
                                text: 'Card Number',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: notifier.text,
                                ),
                              ),
                              DataTableCell(
                                text: 'Liters',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: notifier.text,
                                ),
                              ),
                              DataTableCell(
                                text: 'Rate',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: notifier.text,
                                ),
                              ),
                              DataTableCell(
                                text: 'Amount',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: notifier.text,
                                ),
                              ),
                              DataTableCell(
                                text: 'Date',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: notifier.text,
                                ),
                              ),
                              DataTableCell(
                                text: 'Driver',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: notifier.text,
                                ),
                              ),
                            ],
                          ),
                          // Data rows
                          ...controller.filteredUsageRecords
                              .asMap()
                              .entries
                              .map((entry) {
                            final i = entry.key;
                            final usage = entry.value;
                            return TableRow(
                              children: [
                                DataTableCell(
                                  text: '${i + 1}',
                                  style: TextStyle(color: notifier.text),
                                ),
                                DataTableCell(
                                  text: usage.voucherNumber,
                                  style: TextStyle(
                                    color: notifier.text,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                DataTableCell(
                                  text: usage.fuelCardNumber,
                                  style: TextStyle(color: notifier.text),
                                ),
                                DataTableCell(
                                  text:
                                      '${usage.litersUsed.toStringAsFixed(2)} L',
                                  style: TextStyle(color: notifier.text),
                                ),
                                DataTableCell(
                                  text:
                                      'PKR ${usage.rateAtTime.toStringAsFixed(2)}',
                                  style: TextStyle(color: notifier.text),
                                ),
                                DataTableCell(
                                  text:
                                      'PKR ${NumberFormat('#,##0.00').format(usage.totalAmount)}',
                                  style: TextStyle(
                                    color: Colors.green[700],
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                DataTableCell(
                                  text: DateFormat('MM/dd/yyyy')
                                      .format(usage.usageDate),
                                  style: TextStyle(color: notifier.text),
                                ),
                                DataTableCell(
                                  text: usage.driverName.isNotEmpty
                                      ? usage.driverName
                                      : 'N/A',
                                  style: TextStyle(color: notifier.text),
                                ),
                              ],
                            );
                          }),
                        ],
                      ),
                    ],
                  );
                }),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
