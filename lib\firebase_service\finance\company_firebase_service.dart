import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dartz/dartz.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';
import 'package:logestics/models/user_model.dart';

class CompanyFirebaseService {
  final FirebaseFirestore _firestore;

  // Get current user's UID or return 'anonymous' if not authenticated
  String get _uid => FirebaseAuth.instance.currentUser?.uid ?? 'anonymous';

  CompanyFirebaseService({FirebaseFirestore? firestore})
      : _firestore = firestore ?? FirebaseFirestore.instance;

  Future<Either<FailureObj, List<UserModel>>> getAllUsers() async {
    try {
      // First try with isActive filter
      QuerySnapshot<Map<String, dynamic>> querySnapshot;

      try {
        querySnapshot = await _firestore
            .collection(AppCollection.usersCollection)
            .where('uid', isNotEqualTo: _uid) // Exclude current user
            .orderBy('companyName')
            .get();
      } catch (e) {
        // If isActive field doesn't exist, fall back to all users
        querySnapshot = await _firestore
            .collection(AppCollection.usersCollection)
            .where('uid', isNotEqualTo: _uid) // Exclude current user
            .orderBy('companyName')
            .get();
      }

      if (querySnapshot.docs.isEmpty) {
        // Try without any filters to see if users exist at all
        final allUsersSnapshot = await _firestore
            .collection(AppCollection.usersCollection)
            .limit(1)
            .get();

        if (allUsersSnapshot.docs.isEmpty) {
          return Left(FailureObj(
            code: 'no-users-found',
            message:
                'No users found in the system. Please check the users collection.',
          ));
        }

        return Left(FailureObj(
          code: 'no-other-users',
          message: 'No other users found in the system.',
        ));
      }

      final users = <UserModel>[];

      for (final doc in querySnapshot.docs) {
        try {
          final data = doc.data();

          // Validate essential fields exist
          if (data['companyName'] != null && data['email'] != null) {
            final user = UserModel.fromJson({
              ...data,
              'uid': doc.id, // Use document ID as UID
              // 'isActive':
              //     data['isActive'] ?? true, // Default to true if field missing
            });
            users.add(user);
          }
        } catch (e) {
          // Skip invalid user documents but continue processing others
          log('Warning: Skipping invalid user document ${doc.id}: $e');
        }
      }

      if (users.isEmpty) {
        return Left(FailureObj(
          code: 'no-valid-users',
          message: 'No valid users found. Please check user data format.',
        ));
      }

      return Right(users);
    } catch (e) {
      return Left(FailureObj(
        code: 'firebase-error',
        message: 'Failed to fetch users: $e',
      ));
    }
  }

  Future<Either<FailureObj, UserModel>> getUserById(String userId) async {
    try {
      final doc = await _firestore
          .collection(AppCollection.usersCollection)
          .doc(userId)
          .get();

      if (!doc.exists) {
        return Left(FailureObj(
          code: 'user-not-found',
          message: 'User not found',
        ));
      }

      final data = doc.data() as Map<String, dynamic>;
      return Right(UserModel.fromJson({
        ...data,
        'uid': doc.id, // Use document ID as UID
      }));
    } catch (e) {
      return Left(FailureObj(
        code: 'firebase-error',
        message: 'Failed to fetch user: $e',
      ));
    }
  }

  Future<Either<FailureObj, List<UserModel>>> searchUsers(String query) async {
    try {
      final querySnapshot = await _firestore
          .collection(AppCollection.usersCollection)
          .where('uid', isNotEqualTo: _uid) // Exclude current user
          .orderBy('companyName')
          .startAt([query]).endAt(['$query\uf8ff']).get();

      final users = querySnapshot.docs.map((doc) {
        final data = doc.data();
        return UserModel.fromJson({
          ...data,
          'uid': doc.id, // Use document ID as UID
        });
      }).toList();

      return Right(users);
    } catch (e) {
      return Left(FailureObj(
        code: 'firebase-error',
        message: 'Failed to search users: $e',
      ));
    }
  }
}
