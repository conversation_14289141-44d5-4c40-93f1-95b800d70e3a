import 'package:flutter/material.dart';
import 'package:logestics/core/utils/app_constants/colors/app_colors.dart';
import '../app_constants/styles/app_text_styles.dart';

class CustomTextButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;

  final Color? textColor;
  final Size minimumSize;
  final double borderRadius;
  final TextStyle? textStyle;
  final bool isLoading;
  final bool isDisabled;
  final EdgeInsetsGeometry? padding;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final double? elevation;
  final Border? border;

  const CustomTextButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.textColor,
    this.minimumSize = const Size(130, 60),
    this.borderRadius = 5.0,
    this.textStyle,
    this.isLoading = false,
    this.isDisabled = false,
    this.padding,
    this.prefixIcon,
    this.suffixIcon,
    this.elevation,
    this.border,
  });

  @override
  Widget build(BuildContext context) {
    return TextButton(
      onPressed: isDisabled ? null : onPressed,
      style: TextButton.styleFrom(
        backgroundColor: Colors.transparent,
        padding: padding,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
      ),
      child: isLoading
          ? const SizedBox(
              width: 24,
              height: 24,
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                strokeWidth: 2.0,
              ),
            )
          : Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (prefixIcon != null) ...[
                  prefixIcon!,
                  const SizedBox(width: 8),
                ],
                Text(
                  text,
                  style: textStyle ??
                      AppTextStyles.buttonTextStyle.copyWith(
                        color: textColor ?? AppColors.primary,
                      ),
                ),
                if (suffixIcon != null) ...[
                  const SizedBox(width: 8),
                  suffixIcon!,
                ],
              ],
            ),
    );
  }

  // Predefined button styles
  static CustomTextButton primary({
    required String text,
    required VoidCallback onPressed,
    Size minimumSize = const Size(130, 60),
    bool isLoading = false,
    bool isDisabled = false,
    Widget? prefixIcon,
    Widget? suffixIcon,
    double? elevation,
  }) {
    return CustomTextButton(
      text: text,
      onPressed: onPressed,
      minimumSize: minimumSize,
      isLoading: isLoading,
      isDisabled: isDisabled,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      elevation: elevation,
    );
  }

  static CustomTextButton danger({
    required String text,
    required VoidCallback onPressed,
    Size minimumSize = const Size(130, 60),
    bool isLoading = false,
    bool isDisabled = false,
    Widget? prefixIcon,
    Widget? suffixIcon,
    double? elevation,
  }) {
    return CustomTextButton(
      text: text,
      onPressed: onPressed,
      minimumSize: minimumSize,
      isLoading: isLoading,
      isDisabled: isDisabled,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      elevation: elevation,
    );
  }

  static CustomTextButton secondary({
    required String text,
    required VoidCallback onPressed,
    Size minimumSize = const Size(130, 60),
    bool isLoading = false,
    bool isDisabled = false,
    Widget? prefixIcon,
    Widget? suffixIcon,
    double? elevation,
  }) {
    return CustomTextButton(
      text: text,
      onPressed: onPressed,
      textColor: const Color(0xFF0f79f3),
      minimumSize: minimumSize,
      isLoading: isLoading,
      isDisabled: isDisabled,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      elevation: elevation,
      border: Border.all(
        color: const Color(0xFF0f79f3),
        width: 1.0,
      ),
    );
  }

  static CustomTextButton success({
    required String text,
    required VoidCallback onPressed,
    Size minimumSize = const Size(130, 60),
    bool isLoading = false,
    bool isDisabled = false,
    Widget? prefixIcon,
    Widget? suffixIcon,
    double? elevation,
  }) {
    return CustomTextButton(
      text: text,
      onPressed: onPressed,
      minimumSize: minimumSize,
      isLoading: isLoading,
      isDisabled: isDisabled,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      elevation: elevation,
    );
  }
}
