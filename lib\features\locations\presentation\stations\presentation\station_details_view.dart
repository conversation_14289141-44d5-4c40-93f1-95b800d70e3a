import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/bindings/app_bindings.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/widgets/my_text_field.dart';
import 'package:logestics/core/utils/widgets/data_table_cell.dart';
import 'package:logestics/models/locations/station_model.dart';
import 'package:logestics/features/locations/presentation/stations/presentation/add_station_view.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';

class StationDetailsView extends StatelessWidget {
  final StationModel station;

  const StationDetailsView({
    super.key,
    required this.station,
  });

  @override
  Widget build(BuildContext context) {
    notifier = Provider.of(context, listen: true);
    final size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: notifier.getBgColor,
      body: Dialog(
        backgroundColor: notifier.getBgColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        child: Container(
          width: size.width * 0.9,
          height: size.height * 0.9,
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with station name and actions
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      station.stationName,
                      style: AppTextStyles.titleStyle.copyWith(
                        color: notifier.text,
                      ),
                    ),
                  ),
                  Row(
                    children: [
                      IconButton(
                        onPressed: () {
                          Get.to(
                            () => AddStationView(existingStation: station),
                            binding: AppBindings(),
                          );
                        },
                        icon: Icon(Icons.edit, color: notifier.text),
                        tooltip: 'Edit Station',
                      ),
                      IconButton(
                        onPressed: () => Get.back(),
                        icon: Icon(Icons.close, color: notifier.text),
                      ),
                    ],
                  ),
                ],
              ),
              Divider(thickness: 2),

              // Station Details Section
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Basic Information Card
                      Card(
                        color: notifier.getcardColor,
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "Station Information",
                                style: AppTextStyles.subtitleStyle.copyWith(
                                  color: notifier.text,
                                ),
                              ),
                              SizedBox(height: 16),
                              Row(
                                children: [
                                  Expanded(
                                    child: MyTextFormField(
                                      titleText: AppStrings.zone,
                                      labelText: AppStrings.zone,
                                      hintText: AppStrings.enterZoneName,
                                      controller: TextEditingController(
                                        text: station.zoneName,
                                      ),
                                      enabled: false,
                                    ),
                                  ),
                                  SizedBox(width: 16),
                                  Expanded(
                                    child: MyTextFormField(
                                      titleText: AppStrings.region,
                                      labelText: AppStrings.region,
                                      hintText: AppStrings.enterRegionName,
                                      controller: TextEditingController(
                                        text: station.regionName,
                                      ),
                                      enabled: false,
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 16),
                              MyTextFormField(
                                titleText: "District",
                                controller: TextEditingController(
                                  text: station.districtName,
                                ),
                                enabled: false,
                                labelText: AppStrings.district,
                                hintText: AppStrings.selectADistrict,
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(height: 24),

                      // Places Table Card
                      Card(
                        color: notifier.getcardColor,
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    "Places (${station.places.length})",
                                    style: AppTextStyles.subtitleStyle.copyWith(
                                      color: notifier.text,
                                    ),
                                  ),
                                  ElevatedButton.icon(
                                    onPressed: () {
                                      Get.to(
                                        () => AddStationView(
                                          existingStation: station,
                                        ),
                                        binding: AppBindings(),
                                      );
                                    },
                                    icon: Icon(Icons.add),
                                    label: Text("Add More Places"),
                                  ),
                                ],
                              ),
                              SizedBox(height: 16),
                              if (station.places.isEmpty)
                                Center(
                                  child: Text(
                                    'No places added yet',
                                    style: TextStyle(color: notifier.text),
                                  ),
                                )
                              else
                                Table(
                                  border: TableBorder.all(
                                    color: notifier.getfillborder,
                                    width: 1,
                                  ),
                                  columnWidths: const {
                                    0: FlexColumnWidth(1),
                                    1: FlexColumnWidth(2),
                                    2: FlexColumnWidth(1),
                                  },
                                  children: [
                                    TableRow(
                                      decoration: BoxDecoration(
                                        color: notifier.getHoverColor,
                                      ),
                                      children: [
                                        DataTableHeaderCell(
                                          text: "Sr No.",
                                          textColor: notifier.text,
                                        ),
                                        DataTableHeaderCell(
                                          text: "Place Name",
                                          textColor: notifier.text,
                                        ),
                                        DataTableHeaderCell(
                                          text: "Kilometers",
                                          textColor: notifier.text,
                                        ),
                                      ],
                                    ),
                                    ...station.places.asMap().entries.map(
                                          (entry) => TableRow(
                                            children: [
                                              DataTableCell(
                                                text: "${entry.key + 1}",
                                              ),
                                              DataTableCell(
                                                text: entry.value.fromPlace,
                                              ),
                                              DataTableCell(
                                                text:
                                                    "${entry.value.kilometers} km",
                                              ),
                                            ],
                                          ),
                                        ),
                                  ],
                                ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
