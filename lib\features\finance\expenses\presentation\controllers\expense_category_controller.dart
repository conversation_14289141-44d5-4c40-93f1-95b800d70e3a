import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/features/finance/expense_categories/repositories/expense_category_repository.dart';
import 'package:logestics/models/finance/expense_category_model.dart';
import 'package:uuid/uuid.dart';

class ExpensesCategoryController extends GetxController {
  final ExpenseCategoryRepository repository;

  // Form controllers
  final nameController = TextEditingController();
  final descriptionController = TextEditingController();

  // Observable variables
  final categories = <ExpenseCategoryModel>[].obs;
  final isLoading = false.obs;
  final isDeleting = false.obs;
  final isDrawerOpen = false.obs;
  final searchQuery = ''.obs;
  final filteredCategories = <ExpenseCategoryModel>[].obs;

  final searchController = TextEditingController();
  final formKey = GlobalKey<FormState>();

  // Currently editing category (null if adding new)
  final editingCategory = Rxn<ExpenseCategoryModel>();

  ExpensesCategoryController({required this.repository});

  @override
  void onInit() {
    super.onInit();
    searchController.addListener(_onSearchChanged);
    loadCategories();
  }

  Future<void> loadCategories() async {
    try {
      isLoading.value = true;
      final result = await repository.getExpenseCategories();
      result.fold(
        (failure) => {
          log('Failed to fetch expense categories: ${failure.message}'),
          SnackbarUtils.showError(AppStrings.errorS,
              'Failed to load categories: ${failure.message}'),
        },
        (categoriesList) {
          categories.value = categoriesList;
          _filterCategories();
        },
      );
    } catch (e) {
      log('Error loading expense categories: $e');
      SnackbarUtils.showError(AppStrings.errorS, 'Failed to load categories');
    } finally {
      isLoading.value = false;
    }
  }

  void _onSearchChanged() {
    searchQuery.value = searchController.text;
    _filterCategories();
  }

  void _filterCategories() {
    if (searchQuery.value.isEmpty) {
      filteredCategories.value = categories;
      return;
    }

    final query = searchQuery.value.toLowerCase();
    filteredCategories.value = categories.where((category) {
      return category.name.toLowerCase().contains(query) ||
          category.description.toLowerCase().contains(query);
    }).toList();
  }

  void openDrawer({ExpenseCategoryModel? category}) {
    if (category != null) {
      // Edit mode
      editingCategory.value = category;
      nameController.text = category.name;
      descriptionController.text = category.description;
    } else {
      // Add mode
      editingCategory.value = null;
      clearForm();
    }
    isDrawerOpen.value = true;
  }

  void closeDrawer() {
    isDrawerOpen.value = false;
    editingCategory.value = null;
  }

  void clearForm() {
    nameController.clear();
    descriptionController.clear();
  }

  String? validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Name is required';
    }
    return null;
  }

  Future<void> saveCategory() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    if (isLoading.value) {
      return;
    }

    isLoading.value = true;

    try {
      if (editingCategory.value == null) {
        // Add new category
        final newCategory = ExpenseCategoryModel(
          id: const Uuid().v4(),
          name: nameController.text,
          description: descriptionController.text,
          createdAt: DateTime.now(),
        );

        final result = await repository.createExpenseCategory(newCategory);
        result.fold(
          (failure) => {
            log('Failed to create category: ${failure.message}'),
            SnackbarUtils.showError(AppStrings.error, failure.message),
          },
          (success) {
            categories.add(newCategory);
            _filterCategories();
            clearForm();
            closeDrawer();
            SnackbarUtils.showSuccess(AppStrings.success, success.message);
          },
        );
      } else {
        // Update existing category
        final updatedCategory = ExpenseCategoryModel(
          id: editingCategory.value!.id,
          name: nameController.text,
          description: descriptionController.text,
          createdAt: editingCategory.value!.createdAt,
          uid: editingCategory.value!.uid,
        );

        final result = await repository.updateExpenseCategory(updatedCategory);
        result.fold(
          (failure) => {
            log('Failed to update category: ${failure.message}'),
            SnackbarUtils.showError(AppStrings.error, failure.message),
          },
          (success) {
            final index =
                categories.indexWhere((cat) => cat.id == updatedCategory.id);
            if (index != -1) {
              categories[index] = updatedCategory;
              _filterCategories();
            }
            clearForm();
            closeDrawer();
            SnackbarUtils.showSuccess(AppStrings.success, success.message);
          },
        );
      }
    } catch (e) {
      log('Error saving category: $e');
      SnackbarUtils.showError(AppStrings.error, 'Failed to save category: $e');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> deleteCategory(String categoryId) async {
    if (isDeleting.value) {
      return;
    }

    isDeleting.value = true;

    try {
      final result = await repository.deleteExpenseCategory(categoryId);
      result.fold(
        (failure) => {
          log('Failed to delete category: ${failure.message}'),
          SnackbarUtils.showError(AppStrings.errorS, failure.message),
        },
        (success) {
          categories.removeWhere((category) => category.id == categoryId);
          _filterCategories();
          SnackbarUtils.showSuccess(AppStrings.success, success.message);
        },
      );
    } catch (e) {
      log('Error deleting category: $e');
      SnackbarUtils.showError(
          AppStrings.error, 'Failed to delete category: $e');
    } finally {
      isDeleting.value = false;
    }
  }

  @override
  void onClose() {
    nameController.dispose();
    descriptionController.dispose();
    searchController.dispose();
    super.onClose();
  }
}
