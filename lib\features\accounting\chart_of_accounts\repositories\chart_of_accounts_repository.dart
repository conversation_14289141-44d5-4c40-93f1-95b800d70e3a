import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dartz/dartz.dart';
import '../../../../core/shared_services/failure_obj.dart';
import '../../../../core/shared_services/success_obj.dart';
import '../../../../core/utils/app_constants/texts/app_strings.dart';
import '../../../../firebase_service/accounting/chart_of_accounts_firebase_service.dart';
import '../../../../models/finance/chart_of_accounts_model.dart';

abstract class ChartOfAccountsRepository {
  Future<Either<FailureObj, List<ChartOfAccountsModel>>> getAccounts();
  Future<Either<FailureObj, List<ChartOfAccountsModel>>> getAllAccounts();
  Future<Either<FailureObj, PaginatedAccountsResult>> getAccountsPaginated({
    int limit = 25,
    QueryDocumentSnapshot? lastDocument,
    bool includeInactive = false,
    AccountCategory? category,
    String? searchQuery,
  });
  Future<Either<FailureObj, List<ChartOfAccountsModel>>> getAccountsByCategory(
      AccountCategory category);
  Future<Either<FailureObj, List<ChartOfAccountsModel>>> getAccountsByType(
      AccountType accountType);
  Future<Either<FailureObj, List<ChartOfAccountsModel>>> getChildAccounts(
      String parentAccountId);
  Future<Either<FailureObj, ChartOfAccountsModel?>> getAccountById(
      String accountId);
  Future<Either<FailureObj, SuccessObj>> createAccount(
      ChartOfAccountsModel account);
  Future<Either<FailureObj, SuccessObj>> updateAccount(
      ChartOfAccountsModel account);
  Future<Either<FailureObj, SuccessObj>> deactivateAccount(String accountId);
  Future<Either<FailureObj, SuccessObj>> reactivateAccount(String accountId);
  Future<Either<FailureObj, SuccessObj>> deleteAccount(String accountId);
  Future<Either<FailureObj, bool>> isAccountNumberExists(String accountNumber);
  Future<Either<FailureObj, String>> getNextAccountNumber(
      AccountCategory category);
  Future<Either<FailureObj, bool>> hasTransactions(String accountId);
  Future<Either<FailureObj, bool>> hasChildAccounts(String accountId);
  Future<Either<FailureObj, Map<String, dynamic>>> validateAccountDeletion(
      String accountId);
  Stream<List<ChartOfAccountsModel>> listenToAccounts();
}

class ChartOfAccountsRepositoryImpl implements ChartOfAccountsRepository {
  final ChartOfAccountsFirebaseService _firebaseService;

  ChartOfAccountsRepositoryImpl(this._firebaseService);

  @override
  Future<Either<FailureObj, SuccessObj>> createAccount(
      ChartOfAccountsModel account) async {
    try {
      log('Creating chart of accounts entry in repository: ${account.accountName}');
      await _firebaseService.createAccount(account);
      return Right(
          SuccessObj(message: 'Chart of accounts entry created successfully'));
    } catch (e) {
      log('Error creating chart of accounts entry: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, List<ChartOfAccountsModel>>> getAccounts() async {
    try {
      log('Fetching chart of accounts entries from repository');
      final accounts = await _firebaseService.getAccounts();
      return Right(accounts);
    } catch (e) {
      log('Error fetching chart of accounts entries: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, List<ChartOfAccountsModel>>>
      getAllAccounts() async {
    try {
      log('Fetching all chart of accounts entries from repository');
      final accounts = await _firebaseService.getAllAccounts();
      return Right(accounts);
    } catch (e) {
      log('Error fetching all chart of accounts entries: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, PaginatedAccountsResult>> getAccountsPaginated({
    int limit = 25,
    QueryDocumentSnapshot? lastDocument,
    bool includeInactive = false,
    AccountCategory? category,
    String? searchQuery,
  }) async {
    try {
      log('Fetching paginated chart of accounts entries from repository (limit: $limit)');
      final result = await _firebaseService.getAccountsPaginated(
        limit: limit,
        lastDocument: lastDocument,
        includeInactive: includeInactive,
        category: category,
        searchQuery: searchQuery,
      );
      return Right(result);
    } catch (e) {
      log('Error fetching paginated chart of accounts entries: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, List<ChartOfAccountsModel>>> getAccountsByCategory(
      AccountCategory category) async {
    try {
      log('Fetching chart of accounts entries by category from repository: ${category.displayName}');
      final accounts = await _firebaseService.getAccountsByCategory(category);
      return Right(accounts);
    } catch (e) {
      log('Error fetching chart of accounts entries by category: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, List<ChartOfAccountsModel>>> getAccountsByType(
      AccountType accountType) async {
    try {
      log('Fetching chart of accounts entries by type from repository: ${accountType.displayName}');
      final accounts = await _firebaseService.getAccountsByType(accountType);
      return Right(accounts);
    } catch (e) {
      log('Error fetching chart of accounts entries by type: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, List<ChartOfAccountsModel>>> getChildAccounts(
      String parentAccountId) async {
    try {
      log('Fetching child accounts from repository for parent: $parentAccountId');
      final accounts = await _firebaseService.getChildAccounts(parentAccountId);
      return Right(accounts);
    } catch (e) {
      log('Error fetching child accounts: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, ChartOfAccountsModel?>> getAccountById(
      String accountId) async {
    try {
      log('Fetching chart of accounts entry by ID from repository: $accountId');
      final account = await _firebaseService.getAccountById(accountId);
      return Right(account);
    } catch (e) {
      log('Error fetching chart of accounts entry by ID: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> updateAccount(
      ChartOfAccountsModel account) async {
    try {
      log('Updating chart of accounts entry in repository: ${account.accountName}');
      await _firebaseService.updateAccount(account);
      return Right(
          SuccessObj(message: 'Chart of accounts entry updated successfully'));
    } catch (e) {
      log('Error updating chart of accounts entry: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> deactivateAccount(
      String accountId) async {
    try {
      log('Deactivating chart of accounts entry in repository: $accountId');
      await _firebaseService.deactivateAccount(accountId);
      return Right(SuccessObj(
          message: 'Chart of accounts entry deactivated successfully'));
    } catch (e) {
      log('Error deactivating chart of accounts entry: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> reactivateAccount(
      String accountId) async {
    try {
      log('Reactivating chart of accounts entry in repository: $accountId');
      await _firebaseService.reactivateAccount(accountId);
      return Right(SuccessObj(
          message: 'Chart of accounts entry reactivated successfully'));
    } catch (e) {
      log('Error reactivating chart of accounts entry: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> deleteAccount(String accountId) async {
    try {
      log('Deleting chart of accounts entry in repository: $accountId');
      await _firebaseService.deleteAccount(accountId);
      return Right(
          SuccessObj(message: 'Chart of accounts entry deleted successfully'));
    } catch (e) {
      log('Error deleting chart of accounts entry: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, bool>> isAccountNumberExists(
      String accountNumber) async {
    try {
      log('Checking if account number exists in repository: $accountNumber');
      final exists =
          await _firebaseService.isAccountNumberExists(accountNumber);
      return Right(exists);
    } catch (e) {
      log('Error checking account number existence: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, String>> getNextAccountNumber(
      AccountCategory category) async {
    try {
      log('Getting next account number in repository for category: ${category.displayName}');
      final nextNumber = await _firebaseService.getNextAccountNumber(category);
      return Right(nextNumber);
    } catch (e) {
      log('Error getting next account number: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, bool>> hasTransactions(String accountId) async {
    try {
      log('Checking if account has transactions in repository: $accountId');
      final hasTransactions = await _firebaseService.hasTransactions(accountId);
      return Right(hasTransactions);
    } catch (e) {
      log('Error checking account transactions: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, bool>> hasChildAccounts(String accountId) async {
    try {
      log('Checking if account has child accounts in repository: $accountId');
      final hasChildren = await _firebaseService.hasChildAccounts(accountId);
      return Right(hasChildren);
    } catch (e) {
      log('Error checking child accounts: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, Map<String, dynamic>>> validateAccountDeletion(
      String accountId) async {
    try {
      log('Validating account deletion in repository: $accountId');
      final validation =
          await _firebaseService.validateAccountDeletion(accountId);
      return Right(validation);
    } catch (e) {
      log('Error validating account deletion: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Stream<List<ChartOfAccountsModel>> listenToAccounts() {
    try {
      log('Setting up real-time listener for chart of accounts entries');
      return _firebaseService.listenToAccounts();
    } catch (e) {
      log('Error setting up chart of accounts listener: $e');
      return Stream.value([]);
    }
  }
}
