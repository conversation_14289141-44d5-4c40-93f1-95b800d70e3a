import 'dart:developer';

import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/firebase_service/finance/payee_firebase_service.dart';
import 'package:logestics/models/finance/payee_model.dart';

abstract class PayeeRepository {
  Future<Either<FailureObj, List<PayeeModel>>> getPayees();
  Future<Either<FailureObj, SuccessObj>> createPayee(PayeeModel payee);
  Future<Either<FailureObj, SuccessObj>> updatePayee(PayeeModel payee);
  Future<Either<FailureObj, SuccessObj>> deletePayee(String payeeId);
}

class PayeeRepositoryImpl implements PayeeRepository {
  final PayeeFirebaseService _firebaseService;

  PayeeRepositoryImpl(this._firebaseService);

  @override
  Future<Either<FailureObj, SuccessObj>> createPayee(PayeeModel payee) async {
    try {
      log('Creating payee in repository: ${payee.name}');
      await _firebaseService.createPayee(payee);
      return Right(SuccessObj(message: 'Payee created successfully'));
    } catch (e) {
      log('Error creating payee: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, List<PayeeModel>>> getPayees() async {
    try {
      log('Fetching all payees');
      final payees = await _firebaseService.getPayees();
      return Right(payees);
    } catch (e) {
      log('Error fetching payees: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> deletePayee(String payeeId) async {
    try {
      log('Deleting payee: $payeeId');
      await _firebaseService.deletePayee(payeeId);
      return Right(SuccessObj(message: 'Payee deleted successfully'));
    } catch (e) {
      log('Error deleting payee: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> updatePayee(PayeeModel payee) async {
    try {
      log('Updating payee: ${payee.id}');
      await _firebaseService.updatePayee(payee);
      return Right(SuccessObj(message: 'Payee updated successfully'));
    } catch (e) {
      log('Error updating payee: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }
}
