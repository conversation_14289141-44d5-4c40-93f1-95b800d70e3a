import 'dart:developer';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../../core/utils/snackbar_utils.dart';
import '../../../../../models/accounting/financial_report_models.dart';
import '../../../../../services/financial_report_export_service.dart';
import '../../repositories/balance_sheet_repository.dart';

/// GetX Controller for Balance Sheet report management
class BalanceSheetController extends GetxController {
  final BalanceSheetRepository _repository = BalanceSheetRepository();
  final FinancialReportExportService exportService =
      FinancialReportExportService();

  // Form controllers
  final asOfDateController = TextEditingController();
  final companyNameController = TextEditingController();

  // Reactive variables
  final _isLoading = false.obs;
  final _currentReport = Rxn<BalanceSheetReport>();
  final _savedReports = <BalanceSheetReport>[].obs;
  final _includeInactiveAccounts = false.obs;
  final _includeZeroBalances = false.obs;

  // Form validation
  final formKey = GlobalKey<FormState>();

  // Getters
  bool get isLoading => _isLoading.value;
  BalanceSheetReport? get currentReport => _currentReport.value;
  List<BalanceSheetReport> get savedReports => _savedReports;
  bool get includeInactiveAccounts => _includeInactiveAccounts.value;
  bool get includeZeroBalances => _includeZeroBalances.value;

  // Computed properties
  bool get hasReport => _currentReport.value != null;
  bool get hasAssetData =>
      _currentReport.value?.assetGroups.isNotEmpty ?? false;
  bool get hasLiabilityData =>
      _currentReport.value?.liabilityGroups.isNotEmpty ?? false;
  bool get hasEquityData =>
      _currentReport.value?.equityGroups.isNotEmpty ?? false;
  bool get isBalanced => _currentReport.value?.isBalanced ?? false;
  double get totalAssets => _currentReport.value?.totalAssets ?? 0.0;
  double get totalLiabilities => _currentReport.value?.totalLiabilities ?? 0.0;
  double get totalEquity => _currentReport.value?.totalEquity ?? 0.0;
  double get totalLiabilitiesAndEquity =>
      _currentReport.value?.totalLiabilitiesAndEquity ?? 0.0;

  @override
  void onInit() {
    super.onInit();
    _initializeForm();
    loadSavedReports();
  }

  @override
  void onClose() {
    asOfDateController.dispose();
    companyNameController.dispose();
    super.onClose();
  }

  /// Initialize form with default values
  void _initializeForm() {
    final now = DateTime.now();
    asOfDateController.text = _formatDate(now);
    companyNameController.text =
        'Your Company Name'; // TODO: Get from user settings
  }

  /// Format date for display
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  /// Parse date from string
  DateTime? _parseDate(String dateStr) {
    try {
      final parts = dateStr.split('/');
      if (parts.length == 3) {
        final day = int.parse(parts[0]);
        final month = int.parse(parts[1]);
        final year = int.parse(parts[2]);
        return DateTime(year, month, day);
      }
    } catch (e) {
      log('Error parsing date: $e');
    }
    return null;
  }

  /// Toggle include inactive accounts
  void toggleIncludeInactiveAccounts() {
    _includeInactiveAccounts.value = !_includeInactiveAccounts.value;
  }

  /// Toggle include zero balances
  void toggleIncludeZeroBalances() {
    _includeZeroBalances.value = !_includeZeroBalances.value;
  }

  /// Generate Balance Sheet report
  Future<void> generateReport() async {
    if (!formKey.currentState!.validate()) {
      SnackbarUtils.showError('Form Validation', 'Please fix form errors');
      return;
    }

    try {
      _isLoading.value = true;

      final asOfDate = _parseDate(asOfDateController.text);

      if (asOfDate == null) {
        SnackbarUtils.showError('Date Error', 'Invalid date format');
        return;
      }

      log('Generating Balance Sheet as of $asOfDate');

      final result = await _repository.generateBalanceSheet(
        asOfDate: asOfDate,
        companyName: companyNameController.text.trim(),
        includeInactiveAccounts: _includeInactiveAccounts.value,
        includeZeroBalances: _includeZeroBalances.value,
      );

      result.fold(
        (failure) {
          log('Balance Sheet generation failed: ${failure.message}');
          SnackbarUtils.showError('Generation Failed', failure.message);
        },
        (report) {
          log('Balance Sheet generated successfully');
          _currentReport.value = report;
          SnackbarUtils.showSuccess(
              'Success', 'Balance Sheet generated successfully');
        },
      );
    } catch (e) {
      log('Error generating Balance Sheet: $e');
      SnackbarUtils.showError(
          'Generation Error', 'Failed to generate Balance Sheet');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Save current report
  Future<void> saveReport() async {
    if (_currentReport.value == null) {
      SnackbarUtils.showError('Save Error', 'No report to save');
      return;
    }

    try {
      _isLoading.value = true;

      final result =
          await _repository.saveBalanceSheetReport(_currentReport.value!);

      result.fold(
        (failure) {
          log('Balance Sheet save failed: ${failure.message}');
          SnackbarUtils.showError('Save Failed', failure.message);
        },
        (success) {
          log('Balance Sheet saved successfully');
          SnackbarUtils.showSuccess(
              'Success', 'Balance Sheet saved successfully');
          loadSavedReports(); // Refresh saved reports list
        },
      );
    } catch (e) {
      log('Error saving Balance Sheet: $e');
      SnackbarUtils.showError('Save Error', 'Failed to save Balance Sheet');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Load saved reports
  Future<void> loadSavedReports() async {
    try {
      final result = await _repository.getSavedBalanceSheetReports();

      result.fold(
        (failure) {
          log('Failed to load saved Balance Sheet reports: ${failure.message}');
        },
        (reports) {
          log('Loaded ${reports.length} saved Balance Sheet reports');
          _savedReports.value = reports;
        },
      );
    } catch (e) {
      log('Error loading saved Balance Sheet reports: $e');
    }
  }

  /// Load specific report
  Future<void> loadReport(String reportId) async {
    try {
      _isLoading.value = true;

      final result = await _repository.getBalanceSheetReport(reportId);

      result.fold(
        (failure) {
          log('Failed to load Balance Sheet report: ${failure.message}');
          SnackbarUtils.showError('Load Failed', failure.message);
        },
        (report) {
          log('Balance Sheet report loaded successfully');
          _currentReport.value = report;

          // Update form with loaded report data
          asOfDateController.text = _formatDate(report.endDate);
          companyNameController.text = report.companyName;

          SnackbarUtils.showSuccess(
              'Success', 'Balance Sheet loaded successfully');
        },
      );
    } catch (e) {
      log('Error loading Balance Sheet report: $e');
      SnackbarUtils.showError(
          'Load Error', 'Failed to load Balance Sheet report');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Delete report
  Future<void> deleteReport(String reportId) async {
    try {
      _isLoading.value = true;

      final result = await _repository.deleteBalanceSheetReport(reportId);

      result.fold(
        (failure) {
          log('Failed to delete Balance Sheet report: ${failure.message}');
          SnackbarUtils.showError('Delete Failed', failure.message);
        },
        (success) {
          log('Balance Sheet report deleted successfully');
          SnackbarUtils.showSuccess(
              'Success', 'Balance Sheet deleted successfully');
          loadSavedReports(); // Refresh saved reports list

          // Clear current report if it was deleted
          if (_currentReport.value?.reportId == reportId) {
            _currentReport.value = null;
          }
        },
      );
    } catch (e) {
      log('Error deleting Balance Sheet report: $e');
      SnackbarUtils.showError(
          'Delete Error', 'Failed to delete Balance Sheet report');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Clear current report
  void clearReport() {
    _currentReport.value = null;
    _initializeForm();
  }

  /// Set quick date filters
  void setToday() {
    asOfDateController.text = _formatDate(DateTime.now());
  }

  void setEndOfMonth() {
    final now = DateTime.now();
    final endOfMonth = DateTime(now.year, now.month + 1, 0);
    asOfDateController.text = _formatDate(endOfMonth);
  }

  void setEndOfQuarter() {
    final now = DateTime.now();
    final quarter = ((now.month - 1) ~/ 3) + 1;
    final endOfQuarter = DateTime(now.year, quarter * 3 + 1, 0);
    asOfDateController.text = _formatDate(endOfQuarter);
  }

  void setEndOfYear() {
    final now = DateTime.now();
    final endOfYear = DateTime(now.year, 12, 31);
    asOfDateController.text = _formatDate(endOfYear);
  }

  /// Validate date field
  String? validateDate(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Date is required';
    }

    final date = _parseDate(value);
    if (date == null) {
      return 'Invalid date format (DD/MM/YYYY)';
    }

    if (date.isAfter(DateTime.now())) {
      return 'Date cannot be in the future';
    }

    return null;
  }

  /// Validate company name
  String? validateCompanyName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Company name is required';
    }

    if (value.trim().length < 2) {
      return 'Company name must be at least 2 characters';
    }

    return null;
  }

  /// Set loading state
  void setLoading(bool loading) {
    _isLoading.value = loading;
  }

  /// Export current report to PDF
  Future<Uint8List> exportToPDF() async {
    if (_currentReport.value == null) {
      throw Exception('No report available to export');
    }

    return await exportService.generateBalanceSheetPDF(_currentReport.value!);
  }

  /// Export current report to Excel
  Future<void> exportToExcel() async {
    if (_currentReport.value == null) {
      throw Exception('No report available to export');
    }

    await exportService.generateBalanceSheetExcel(_currentReport.value!);
  }
}
