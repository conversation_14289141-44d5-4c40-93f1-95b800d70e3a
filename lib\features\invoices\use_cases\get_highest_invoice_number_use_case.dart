import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/features/invoices/repositories/invoice_repository.dart';

class GetHighestInvoiceNumberUseCase {
  final InvoiceRepository repository;

  GetHighestInvoiceNumberUseCase(this.repository);

  Future<Either<FailureObj, int>> call(String uid) async {
    return await repository.getHighestInvoiceNumber(uid: uid);
  }
}
