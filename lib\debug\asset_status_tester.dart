import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';
import 'package:logestics/models/asset/asset_model.dart';

/// Specialized debug helper for testing asset status changes and synchronization
class AssetStatusTester {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Test asset status change workflow
  static Future<void> testAssetStatusChangeWorkflow() async {
    log('=== ASSET STATUS CHANGE WORKFLOW TEST ===');

    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        log('❌ Cannot test status change - no authenticated user');
        return;
      }

      final uid = currentUser.uid;
      log('Testing with UID: $uid');

      // Step 1: Get an existing asset
      final snapshot = await _firestore
          .collection(AppCollection.assetsCollection)
          .where('uid', isEqualTo: uid)
          .limit(1)
          .get();

      if (snapshot.docs.isEmpty) {
        log('❌ No assets found for testing');
        return;
      }

      final doc = snapshot.docs.first;
      final originalData = doc.data();
      final assetId = doc.id;
      final originalStatus = originalData['status'] ?? 'Unknown';
      final originalUid = originalData['uid'] ?? 'Unknown';

      log('✅ Found test asset: $assetId');
      log('Original status: $originalStatus');
      log('Original name: ${originalData['name']}');
      log('Original UID: $originalUid');
      log('Original createdAt: ${originalData['createdAt']}');
      log('Original updatedAt: ${originalData['updatedAt']}');

      // Step 2: Test a simple status change
      final testStatus =
          originalStatus == 'In Use' ? 'Under Maintenance' : 'In Use';

      log('--- Testing status change to: $testStatus ---');

      try {
        // Update the status
        await _firestore
            .collection(AppCollection.assetsCollection)
            .doc(assetId)
            .update({
          'status': testStatus,
          'updatedAt': DateTime.now().millisecondsSinceEpoch,
        });

        log('✅ Status update successful');

        // Wait a moment for propagation
        await Future.delayed(const Duration(milliseconds: 1000));

        // Read back the asset
        final updatedDoc = await _firestore
            .collection(AppCollection.assetsCollection)
            .doc(assetId)
            .get();

        if (updatedDoc.exists) {
          final updatedData = updatedDoc.data()!;
          final newStatus = updatedData['status'];
          final newUid = updatedData['uid'];
          final newUpdatedAt = updatedData['updatedAt'];

          log('✅ Read back successful');
          log('New status: $newStatus');
          log('New UID: $newUid');
          log('New updatedAt: $newUpdatedAt');

          if (newStatus == testStatus) {
            log('✅ Status change verified successfully');
          } else {
            log('❌ Status mismatch! Expected: $testStatus, Got: $newStatus');
          }

          if (newUid != uid) {
            log('❌ UID CHANGED! Original: $uid, New: $newUid');
          } else {
            log('✅ UID preserved correctly');
          }
        } else {
          log('❌ Asset not found after update');
        }

        // Test query with new status
        final statusQuery = await _firestore
            .collection(AppCollection.assetsCollection)
            .where('uid', isEqualTo: uid)
            .where('status', isEqualTo: testStatus)
            .get();

        log('✅ Status query found ${statusQuery.docs.length} assets with status: $testStatus');

        // Test basic uid query to see if asset is still visible
        final uidQuery = await _firestore
            .collection(AppCollection.assetsCollection)
            .where('uid', isEqualTo: uid)
            .orderBy('createdAt', descending: true)
            .get();

        log('✅ UID query found ${uidQuery.docs.length} total assets for user');

        // Check if our test asset is in the results
        final testAssetInResults =
            uidQuery.docs.any((doc) => doc.id == assetId);
        if (testAssetInResults) {
          log('✅ Test asset found in UID query results');
        } else {
          log('❌ Test asset NOT found in UID query results');
        }
      } catch (e) {
        log('❌ Status change to $testStatus failed: $e');
      }

      // Step 3: Restore original status
      try {
        await _firestore
            .collection(AppCollection.assetsCollection)
            .doc(assetId)
            .update({
          'status': originalStatus,
          'updatedAt': DateTime.now().millisecondsSinceEpoch,
        });
        log('✅ Restored original status: $originalStatus');
      } catch (e) {
        log('❌ Failed to restore original status: $e');
      }
    } catch (e) {
      log('❌ Asset status change workflow test failed: $e');
    }
  }

  /// Test real-time synchronization
  static Future<void> testRealTimeSynchronization() async {
    log('=== REAL-TIME SYNCHRONIZATION TEST ===');

    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        log('❌ Cannot test real-time sync - no authenticated user');
        return;
      }

      final uid = currentUser.uid;

      // Set up stream listener
      log('Setting up real-time listener...');

      final streamSubscription = _firestore
          .collection(AppCollection.assetsCollection)
          .where('uid', isEqualTo: uid)
          .orderBy('createdAt', descending: true)
          .snapshots()
          .listen(
        (snapshot) {
          log('🔄 Real-time update: ${snapshot.docs.length} assets');

          for (var doc in snapshot.docs) {
            final data = doc.data();
            log('Asset: ${doc.id} - ${data['name']} (${data['status']}) - Updated: ${data['updatedAt']}');
          }
        },
        onError: (error) {
          log('❌ Stream error: $error');
        },
      );

      // Let it run for a few seconds to capture any updates
      await Future.delayed(const Duration(seconds: 5));

      // Cancel subscription
      await streamSubscription.cancel();
      log('✅ Real-time synchronization test completed');
    } catch (e) {
      log('❌ Real-time synchronization test failed: $e');
    }
  }

  /// Test asset filtering by status
  static Future<void> testAssetStatusFiltering() async {
    log('=== ASSET STATUS FILTERING TEST ===');

    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        log('❌ Cannot test filtering - no authenticated user');
        return;
      }

      final uid = currentUser.uid;

      // Test each status filter
      final statuses = ['In Use', 'Under Maintenance', 'Retired', 'Sold'];

      for (final status in statuses) {
        try {
          final snapshot = await _firestore
              .collection(AppCollection.assetsCollection)
              .where('uid', isEqualTo: uid)
              .where('status', isEqualTo: status)
              .orderBy('createdAt', descending: true)
              .get();

          log('✅ Status filter "$status": ${snapshot.docs.length} assets found');

          // Log first few assets for verification
          for (var i = 0; i < snapshot.docs.length && i < 3; i++) {
            final doc = snapshot.docs[i];
            final data = doc.data();
            log('  - ${data['name']} (${data['status']})');
          }
        } catch (e) {
          log('❌ Status filter "$status" failed: $e');
        }
      }

      // Test getting all assets (no status filter)
      try {
        final allSnapshot = await _firestore
            .collection(AppCollection.assetsCollection)
            .where('uid', isEqualTo: uid)
            .orderBy('createdAt', descending: true)
            .get();

        log('✅ All assets query: ${allSnapshot.docs.length} total assets');

        // Count by status
        final statusCounts = <String, int>{};
        for (var doc in allSnapshot.docs) {
          final status = doc.data()['status'] ?? 'Unknown';
          statusCounts[status] = (statusCounts[status] ?? 0) + 1;
        }

        log('Status breakdown:');
        statusCounts.forEach((status, count) {
          log('  - $status: $count assets');
        });
      } catch (e) {
        log('❌ All assets query failed: $e');
      }
    } catch (e) {
      log('❌ Asset status filtering test failed: $e');
    }
  }

  /// Test stream behavior during asset updates
  static Future<void> testStreamDuringUpdates() async {
    log('=== STREAM BEHAVIOR DURING UPDATES TEST ===');

    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        log('❌ Cannot test stream - no authenticated user');
        return;
      }

      final uid = currentUser.uid;
      log('Setting up stream listener for UID: $uid');

      // Set up stream listener
      int streamUpdateCount = 0;
      List<String> streamAssetIds = [];

      final streamSubscription = _firestore
          .collection(AppCollection.assetsCollection)
          .where('uid', isEqualTo: uid)
          .orderBy('createdAt', descending: true)
          .snapshots()
          .listen(
        (snapshot) {
          streamUpdateCount++;
          streamAssetIds = snapshot.docs.map((doc) => doc.id).toList();
          log('🔄 Stream update #$streamUpdateCount: ${snapshot.docs.length} assets');
          log('Asset IDs in stream: ${streamAssetIds.take(3).join(', ')}${streamAssetIds.length > 3 ? '...' : ''}');
        },
        onError: (error) {
          log('❌ Stream error: $error');
        },
      );

      // Wait for initial stream data
      await Future.delayed(const Duration(seconds: 2));
      log('Initial stream updates: $streamUpdateCount');

      // Get an asset to test with
      final snapshot = await _firestore
          .collection(AppCollection.assetsCollection)
          .where('uid', isEqualTo: uid)
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        final doc = snapshot.docs.first;
        final assetId = doc.id;
        final originalData = doc.data();
        final originalStatus = originalData['status'] ?? 'Unknown';

        log('Testing with asset: $assetId (status: $originalStatus)');

        final beforeUpdateCount = streamUpdateCount;

        // Perform an update
        final newStatus =
            originalStatus == 'In Use' ? 'Under Maintenance' : 'In Use';
        await _firestore
            .collection(AppCollection.assetsCollection)
            .doc(assetId)
            .update({
          'status': newStatus,
          'updatedAt': DateTime.now().millisecondsSinceEpoch,
        });

        log('Asset updated, waiting for stream response...');

        // Wait for stream to update
        await Future.delayed(const Duration(seconds: 3));

        final afterUpdateCount = streamUpdateCount;
        log('Stream updates after asset change: ${afterUpdateCount - beforeUpdateCount}');

        // Check if asset is still in stream
        if (streamAssetIds.contains(assetId)) {
          log('✅ Asset still visible in stream after update');
        } else {
          log('❌ Asset DISAPPEARED from stream after update');
        }

        // Restore original status
        await _firestore
            .collection(AppCollection.assetsCollection)
            .doc(assetId)
            .update({
          'status': originalStatus,
          'updatedAt': DateTime.now().millisecondsSinceEpoch,
        });
      }

      // Cancel subscription
      await streamSubscription.cancel();
      log('✅ Stream behavior test completed');
    } catch (e) {
      log('❌ Stream behavior test failed: $e');
    }
  }

  /// Run all asset status tests
  static Future<void> runAllAssetStatusTests() async {
    log('🔍 Starting Asset Status Tests...');

    await testAssetStatusChangeWorkflow();
    await testStreamDuringUpdates();
    await testRealTimeSynchronization();
    await testAssetStatusFiltering();

    log('🔍 Asset Status Tests Complete');
  }
}
