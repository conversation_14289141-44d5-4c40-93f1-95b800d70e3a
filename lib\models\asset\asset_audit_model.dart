class AssetAuditModel {
  final String id;
  final String assetId;
  final String assetName; // For easier display without additional queries
  final String userId;
  final String userName;
  final String
      action; // created, updated, deleted, status_changed, maintenance_added, file_uploaded, file_deleted
  final Map<String, dynamic> fieldChanges; // {field: {old: value, new: value}}
  final DateTime timestamp;
  final String notes;
  final String ipAddress;
  final String deviceInfo;
  final String sessionId;
  final String uid; // Company/User UID for data isolation

  AssetAuditModel({
    required this.id,
    required this.assetId,
    required this.assetName,
    required this.userId,
    required this.userName,
    required this.action,
    required this.fieldChanges,
    required this.timestamp,
    this.notes = '',
    this.ipAddress = '',
    this.deviceInfo = '',
    this.sessionId = '',
    this.uid = '',
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'assetId': assetId,
      'assetName': assetName,
      'userId': userId,
      'userName': userName,
      'action': action,
      'fieldChanges': fieldChanges,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'notes': notes,
      'ipAddress': ipAddress,
      'deviceInfo': deviceInfo,
      'sessionId': sessionId,
      'uid': uid,
    };
  }

  factory AssetAuditModel.fromJson(Map<String, dynamic> json) {
    return AssetAuditModel(
      id: json['id'] ?? '',
      assetId: json['assetId'] ?? '',
      assetName: json['assetName'] ?? '',
      userId: json['userId'] ?? '',
      userName: json['userName'] ?? '',
      action: json['action'] ?? '',
      fieldChanges: Map<String, dynamic>.from(json['fieldChanges'] ?? {}),
      timestamp: DateTime.fromMillisecondsSinceEpoch(json['timestamp'] ?? 0),
      notes: json['notes'] ?? '',
      ipAddress: json['ipAddress'] ?? '',
      deviceInfo: json['deviceInfo'] ?? '',
      sessionId: json['sessionId'] ?? '',
      uid: json['uid'] ?? '',
    );
  }

  AssetAuditModel copyWith({
    String? id,
    String? assetId,
    String? assetName,
    String? userId,
    String? userName,
    String? action,
    Map<String, dynamic>? fieldChanges,
    DateTime? timestamp,
    String? notes,
    String? ipAddress,
    String? deviceInfo,
    String? sessionId,
    String? uid,
  }) {
    return AssetAuditModel(
      id: id ?? this.id,
      assetId: assetId ?? this.assetId,
      assetName: assetName ?? this.assetName,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      action: action ?? this.action,
      fieldChanges: fieldChanges ?? this.fieldChanges,
      timestamp: timestamp ?? this.timestamp,
      notes: notes ?? this.notes,
      ipAddress: ipAddress ?? this.ipAddress,
      deviceInfo: deviceInfo ?? this.deviceInfo,
      sessionId: sessionId ?? this.sessionId,
      uid: uid ?? this.uid,
    );
  }

  @override
  String toString() {
    return 'AssetAuditModel(id: $id, assetId: $assetId, action: $action, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AssetAuditModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  /// Get formatted action display text
  String get actionDisplayText {
    switch (action) {
      case 'created':
        return 'Asset Created';
      case 'updated':
        return 'Asset Updated';
      case 'deleted':
        return 'Asset Deleted';
      case 'status_changed':
        return 'Status Changed';
      case 'maintenance_added':
        return 'Maintenance Added';
      case 'maintenance_updated':
        return 'Maintenance Updated';
      case 'file_uploaded':
        return 'File Uploaded';
      case 'file_deleted':
        return 'File Deleted';
      default:
        return action.replaceAll('_', ' ').toUpperCase();
    }
  }

  /// Get action color for UI display
  String get actionColor {
    switch (action) {
      case 'created':
        return 'green';
      case 'updated':
        return 'blue';
      case 'deleted':
        return 'red';
      case 'status_changed':
        return 'yellow';
      case 'maintenance_added':
      case 'maintenance_updated':
        return 'purple';
      case 'file_uploaded':
      case 'file_deleted':
        return 'orange';
      default:
        return 'grey';
    }
  }

  /// Get formatted field changes for display
  String get formattedFieldChanges {
    if (fieldChanges.isEmpty) return 'No field changes recorded';

    final changes = <String>[];
    fieldChanges.forEach((field, change) {
      if (change is Map<String, dynamic>) {
        final oldValue = change['old']?.toString() ?? 'null';
        final newValue = change['new']?.toString() ?? 'null';
        changes.add('$field: $oldValue → $newValue');
      }
    });

    return changes.join(', ');
  }

  /// Check if this audit record has specific field changes
  bool hasFieldChange(String fieldName) {
    return fieldChanges.containsKey(fieldName);
  }

  /// Get old value for a specific field
  dynamic getOldValue(String fieldName) {
    final change = fieldChanges[fieldName];
    if (change is Map<String, dynamic>) {
      return change['old'];
    }
    return null;
  }

  /// Get new value for a specific field
  dynamic getNewValue(String fieldName) {
    final change = fieldChanges[fieldName];
    if (change is Map<String, dynamic>) {
      return change['new'];
    }
    return null;
  }

  /// Static method to get action display text
  static String getActionDisplayText(String action) {
    switch (action) {
      case 'created':
        return 'Asset Created';
      case 'updated':
        return 'Asset Updated';
      case 'deleted':
        return 'Asset Deleted';
      case 'status_changed':
        return 'Status Changed';
      case 'maintenance_added':
        return 'Maintenance Added';
      case 'maintenance_updated':
        return 'Maintenance Updated';
      case 'file_uploaded':
        return 'File Uploaded';
      case 'file_deleted':
        return 'File Deleted';
      default:
        return action.replaceAll('_', ' ').toUpperCase();
    }
  }

  /// Static method to get action color
  static String getActionColor(String action) {
    switch (action) {
      case 'created':
        return 'green';
      case 'updated':
        return 'blue';
      case 'deleted':
        return 'red';
      case 'status_changed':
        return 'yellow';
      case 'maintenance_added':
      case 'maintenance_updated':
        return 'purple';
      case 'file_uploaded':
      case 'file_deleted':
        return 'orange';
      default:
        return 'grey';
    }
  }
}

/// Asset audit action constants
class AssetAuditAction {
  static const String created = 'created';
  static const String updated = 'updated';
  static const String deleted = 'deleted';
  static const String statusChanged = 'status_changed';
  static const String maintenanceAdded = 'maintenance_added';
  static const String maintenanceUpdated = 'maintenance_updated';
  static const String fileUploaded = 'file_uploaded';
  static const String fileDeleted = 'file_deleted';

  static List<String> get allActions => [
        created,
        updated,
        deleted,
        statusChanged,
        maintenanceAdded,
        maintenanceUpdated,
        fileUploaded,
        fileDeleted,
      ];

  static List<String> get displayActions => [
        'Asset Created',
        'Asset Updated',
        'Asset Deleted',
        'Status Changed',
        'Maintenance Added',
        'Maintenance Updated',
        'File Uploaded',
        'File Deleted',
      ];
}
