import 'dart:developer';
import 'dart:typed_data';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:intl/intl.dart';
import 'package:flutter/foundation.dart';
import 'package:excel/excel.dart' as excel;
import 'dart:html' as html;
import '../models/accounting/financial_report_models.dart';
import '../models/finance/chart_of_accounts_model.dart';

/// Service for exporting financial reports to PDF and Excel
class FinancialReportExportService {
  final DateFormat _dateFormat = DateFormat('dd/MM/yyyy');
  final NumberFormat _currencyFormat =
      NumberFormat.currency(symbol: 'PKR ', decimalDigits: 2);

  /// Generate Trial Balance PDF
  Future<Uint8List> generateTrialBalancePDF(TrialBalanceReport report) async {
    try {
      log('Generating Trial Balance PDF');

      final pdf = pw.Document();

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(32),
          header: (context) => _buildTrialBalanceHeader(context, report),
          build: (context) => [
            _buildTrialBalanceTable(report),
            pw.SizedBox(height: 20),
            _buildTrialBalanceSummary(report),
          ],
          footer: (context) => _buildFooter(context),
        ),
      );

      return await pdf.save();
    } catch (e) {
      log('Error generating Trial Balance PDF: $e');
      rethrow;
    }
  }

  /// Generate Profit & Loss PDF
  Future<Uint8List> generateProfitLossPDF(ProfitLossReport report) async {
    try {
      log('Generating Profit & Loss PDF');

      final pdf = pw.Document();

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(32),
          header: (context) => _buildProfitLossHeader(context, report),
          build: (context) => [
            _buildProfitLossTable(report),
            pw.SizedBox(height: 20),
            _buildProfitLossSummary(report),
          ],
          footer: (context) => _buildFooter(context),
        ),
      );

      return await pdf.save();
    } catch (e) {
      log('Error generating Profit & Loss PDF: $e');
      rethrow;
    }
  }

  /// Generate Balance Sheet PDF
  Future<Uint8List> generateBalanceSheetPDF(BalanceSheetReport report) async {
    try {
      log('Generating Balance Sheet PDF');

      final pdf = pw.Document();

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(32),
          header: (context) => _buildBalanceSheetHeader(context, report),
          build: (context) => [
            _buildBalanceSheetTable(report),
            pw.SizedBox(height: 20),
            _buildBalanceSheetSummary(report),
          ],
          footer: (context) => _buildFooter(context),
        ),
      );

      return await pdf.save();
    } catch (e) {
      log('Error generating Balance Sheet PDF: $e');
      rethrow;
    }
  }

  /// Generate Trial Balance Excel
  Future<void> generateTrialBalanceExcel(TrialBalanceReport report) async {
    try {
      log('Generating Trial Balance Excel');

      final excelFile = excel.Excel.createExcel();
      final sheet = excelFile['Trial Balance'];

      int currentRow = 0;

      // Title
      sheet
          .cell(excel.CellIndex.indexByColumnRow(
              columnIndex: 0, rowIndex: currentRow))
          .value = excel.TextCellValue('TRIAL BALANCE');
      currentRow += 2;

      // Company and date info
      sheet
          .cell(excel.CellIndex.indexByColumnRow(
              columnIndex: 0, rowIndex: currentRow))
          .value = excel.TextCellValue('Company: ${report.companyName}');
      currentRow++;
      sheet
              .cell(excel.CellIndex.indexByColumnRow(
                  columnIndex: 0, rowIndex: currentRow))
              .value =
          excel.TextCellValue(
              'Period: ${_dateFormat.format(report.startDate)} to ${_dateFormat.format(report.endDate)}');
      currentRow += 2;

      // Headers
      final headers = ['Account Number', 'Account Name', 'Debit', 'Credit'];
      for (int i = 0; i < headers.length; i++) {
        sheet
            .cell(excel.CellIndex.indexByColumnRow(
                columnIndex: i, rowIndex: currentRow))
            .value = excel.TextCellValue(headers[i]);
      }
      currentRow++;

      // Data
      for (final account in report.accounts) {
        sheet
            .cell(excel.CellIndex.indexByColumnRow(
                columnIndex: 0, rowIndex: currentRow))
            .value = excel.TextCellValue(account.accountNumber);
        sheet
            .cell(excel.CellIndex.indexByColumnRow(
                columnIndex: 1, rowIndex: currentRow))
            .value = excel.TextCellValue(account.accountName);
        sheet
            .cell(excel.CellIndex.indexByColumnRow(
                columnIndex: 2, rowIndex: currentRow))
            .value = excel.DoubleCellValue(account.debitBalance);
        sheet
            .cell(excel.CellIndex.indexByColumnRow(
                columnIndex: 3, rowIndex: currentRow))
            .value = excel.DoubleCellValue(account.creditBalance);
        currentRow++;
      }

      // Totals
      currentRow++;
      sheet
          .cell(excel.CellIndex.indexByColumnRow(
              columnIndex: 1, rowIndex: currentRow))
          .value = excel.TextCellValue('TOTALS');
      sheet
          .cell(excel.CellIndex.indexByColumnRow(
              columnIndex: 2, rowIndex: currentRow))
          .value = excel.DoubleCellValue(report.totalDebits);
      sheet
          .cell(excel.CellIndex.indexByColumnRow(
              columnIndex: 3, rowIndex: currentRow))
          .value = excel.DoubleCellValue(report.totalCredits);

      await _downloadExcel(
          excelFile, 'trial_balance_${_getDateString(report.endDate)}.xlsx');
    } catch (e) {
      log('Error generating Trial Balance Excel: $e');
      rethrow;
    }
  }

  /// Generate Profit & Loss Excel
  Future<void> generateProfitLossExcel(ProfitLossReport report) async {
    try {
      log('Generating Profit & Loss Excel');

      final excelFile = excel.Excel.createExcel();
      final sheet = excelFile['Profit & Loss'];

      int currentRow = 0;

      // Title
      sheet
          .cell(excel.CellIndex.indexByColumnRow(
              columnIndex: 0, rowIndex: currentRow))
          .value = excel.TextCellValue('PROFIT & LOSS STATEMENT');
      currentRow += 2;

      // Company and date info
      sheet
          .cell(excel.CellIndex.indexByColumnRow(
              columnIndex: 0, rowIndex: currentRow))
          .value = excel.TextCellValue('Company: ${report.companyName}');
      currentRow++;
      sheet
              .cell(excel.CellIndex.indexByColumnRow(
                  columnIndex: 0, rowIndex: currentRow))
              .value =
          excel.TextCellValue(
              'Period: ${_dateFormat.format(report.startDate)} to ${_dateFormat.format(report.endDate)}');
      currentRow += 2;

      // Revenue Section
      sheet
          .cell(excel.CellIndex.indexByColumnRow(
              columnIndex: 0, rowIndex: currentRow))
          .value = excel.TextCellValue('REVENUE');
      currentRow++;

      for (final group in report.revenueGroups) {
        sheet
            .cell(excel.CellIndex.indexByColumnRow(
                columnIndex: 0, rowIndex: currentRow))
            .value = excel.TextCellValue(group.groupName);
        sheet
            .cell(excel.CellIndex.indexByColumnRow(
                columnIndex: 1, rowIndex: currentRow))
            .value = excel.DoubleCellValue(group.groupTotal);
        currentRow++;

        for (final account in group.accounts) {
          sheet
              .cell(excel.CellIndex.indexByColumnRow(
                  columnIndex: 0, rowIndex: currentRow))
              .value = excel.TextCellValue('  ${account.accountName}');
          sheet
              .cell(excel.CellIndex.indexByColumnRow(
                  columnIndex: 1, rowIndex: currentRow))
              .value = excel.DoubleCellValue(account.amount);
          currentRow++;
        }
      }

      // Total Revenue
      sheet
          .cell(excel.CellIndex.indexByColumnRow(
              columnIndex: 0, rowIndex: currentRow))
          .value = excel.TextCellValue('Total Revenue');
      sheet
          .cell(excel.CellIndex.indexByColumnRow(
              columnIndex: 1, rowIndex: currentRow))
          .value = excel.DoubleCellValue(report.totalRevenue);
      currentRow += 2;

      // Expenses Section
      sheet
          .cell(excel.CellIndex.indexByColumnRow(
              columnIndex: 0, rowIndex: currentRow))
          .value = excel.TextCellValue('EXPENSES');
      currentRow++;

      for (final group in report.expenseGroups) {
        sheet
            .cell(excel.CellIndex.indexByColumnRow(
                columnIndex: 0, rowIndex: currentRow))
            .value = excel.TextCellValue(group.groupName);
        sheet
            .cell(excel.CellIndex.indexByColumnRow(
                columnIndex: 1, rowIndex: currentRow))
            .value = excel.DoubleCellValue(group.groupTotal);
        currentRow++;

        for (final account in group.accounts) {
          sheet
              .cell(excel.CellIndex.indexByColumnRow(
                  columnIndex: 0, rowIndex: currentRow))
              .value = excel.TextCellValue('  ${account.accountName}');
          sheet
              .cell(excel.CellIndex.indexByColumnRow(
                  columnIndex: 1, rowIndex: currentRow))
              .value = excel.DoubleCellValue(account.amount);
          currentRow++;
        }
      }

      // Total Expenses
      sheet
          .cell(excel.CellIndex.indexByColumnRow(
              columnIndex: 0, rowIndex: currentRow))
          .value = excel.TextCellValue('Total Expenses');
      sheet
          .cell(excel.CellIndex.indexByColumnRow(
              columnIndex: 1, rowIndex: currentRow))
          .value = excel.DoubleCellValue(report.totalExpenses);
      currentRow += 2;

      // Net Income
      sheet
          .cell(excel.CellIndex.indexByColumnRow(
              columnIndex: 0, rowIndex: currentRow))
          .value = excel.TextCellValue('NET INCOME');
      sheet
          .cell(excel.CellIndex.indexByColumnRow(
              columnIndex: 1, rowIndex: currentRow))
          .value = excel.DoubleCellValue(report.netIncome);

      await _downloadExcel(
          excelFile, 'profit_loss_${_getDateString(report.endDate)}.xlsx');
    } catch (e) {
      log('Error generating Profit & Loss Excel: $e');
      rethrow;
    }
  }

  /// Generate Balance Sheet Excel
  Future<void> generateBalanceSheetExcel(BalanceSheetReport report) async {
    try {
      log('Generating Balance Sheet Excel');

      final excelFile = excel.Excel.createExcel();
      final sheet = excelFile['Balance Sheet'];

      int currentRow = 0;

      // Title
      sheet
          .cell(excel.CellIndex.indexByColumnRow(
              columnIndex: 0, rowIndex: currentRow))
          .value = excel.TextCellValue('BALANCE SHEET');
      currentRow += 2;

      // Company and date info
      sheet
          .cell(excel.CellIndex.indexByColumnRow(
              columnIndex: 0, rowIndex: currentRow))
          .value = excel.TextCellValue('Company: ${report.companyName}');
      currentRow++;
      sheet
              .cell(excel.CellIndex.indexByColumnRow(
                  columnIndex: 0, rowIndex: currentRow))
              .value =
          excel.TextCellValue('As of: ${_dateFormat.format(report.endDate)}');
      currentRow += 2;

      // Assets Section
      sheet
          .cell(excel.CellIndex.indexByColumnRow(
              columnIndex: 0, rowIndex: currentRow))
          .value = excel.TextCellValue('ASSETS');
      currentRow++;

      for (final group in report.assetGroups) {
        sheet
            .cell(excel.CellIndex.indexByColumnRow(
                columnIndex: 0, rowIndex: currentRow))
            .value = excel.TextCellValue(group.groupName);
        sheet
            .cell(excel.CellIndex.indexByColumnRow(
                columnIndex: 1, rowIndex: currentRow))
            .value = excel.DoubleCellValue(group.groupTotal);
        currentRow++;

        for (final account in group.accounts) {
          sheet
              .cell(excel.CellIndex.indexByColumnRow(
                  columnIndex: 0, rowIndex: currentRow))
              .value = excel.TextCellValue('  ${account.accountName}');
          sheet
              .cell(excel.CellIndex.indexByColumnRow(
                  columnIndex: 1, rowIndex: currentRow))
              .value = excel.DoubleCellValue(account.amount);
          currentRow++;
        }
      }

      // Total Assets
      sheet
          .cell(excel.CellIndex.indexByColumnRow(
              columnIndex: 0, rowIndex: currentRow))
          .value = excel.TextCellValue('Total Assets');
      sheet
          .cell(excel.CellIndex.indexByColumnRow(
              columnIndex: 1, rowIndex: currentRow))
          .value = excel.DoubleCellValue(report.totalAssets);
      currentRow += 2;

      // Liabilities Section
      sheet
          .cell(excel.CellIndex.indexByColumnRow(
              columnIndex: 0, rowIndex: currentRow))
          .value = excel.TextCellValue('LIABILITIES');
      currentRow++;

      for (final group in report.liabilityGroups) {
        sheet
            .cell(excel.CellIndex.indexByColumnRow(
                columnIndex: 0, rowIndex: currentRow))
            .value = excel.TextCellValue(group.groupName);
        sheet
            .cell(excel.CellIndex.indexByColumnRow(
                columnIndex: 1, rowIndex: currentRow))
            .value = excel.DoubleCellValue(group.groupTotal);
        currentRow++;

        for (final account in group.accounts) {
          sheet
              .cell(excel.CellIndex.indexByColumnRow(
                  columnIndex: 0, rowIndex: currentRow))
              .value = excel.TextCellValue('  ${account.accountName}');
          sheet
              .cell(excel.CellIndex.indexByColumnRow(
                  columnIndex: 1, rowIndex: currentRow))
              .value = excel.DoubleCellValue(account.amount);
          currentRow++;
        }
      }

      // Total Liabilities
      sheet
          .cell(excel.CellIndex.indexByColumnRow(
              columnIndex: 0, rowIndex: currentRow))
          .value = excel.TextCellValue('Total Liabilities');
      sheet
          .cell(excel.CellIndex.indexByColumnRow(
              columnIndex: 1, rowIndex: currentRow))
          .value = excel.DoubleCellValue(report.totalLiabilities);
      currentRow += 2;

      // Equity Section
      sheet
          .cell(excel.CellIndex.indexByColumnRow(
              columnIndex: 0, rowIndex: currentRow))
          .value = excel.TextCellValue('EQUITY');
      currentRow++;

      for (final group in report.equityGroups) {
        sheet
            .cell(excel.CellIndex.indexByColumnRow(
                columnIndex: 0, rowIndex: currentRow))
            .value = excel.TextCellValue(group.groupName);
        sheet
            .cell(excel.CellIndex.indexByColumnRow(
                columnIndex: 1, rowIndex: currentRow))
            .value = excel.DoubleCellValue(group.groupTotal);
        currentRow++;

        for (final account in group.accounts) {
          sheet
              .cell(excel.CellIndex.indexByColumnRow(
                  columnIndex: 0, rowIndex: currentRow))
              .value = excel.TextCellValue('  ${account.accountName}');
          sheet
              .cell(excel.CellIndex.indexByColumnRow(
                  columnIndex: 1, rowIndex: currentRow))
              .value = excel.DoubleCellValue(account.amount);
          currentRow++;
        }
      }

      // Total Equity
      sheet
          .cell(excel.CellIndex.indexByColumnRow(
              columnIndex: 0, rowIndex: currentRow))
          .value = excel.TextCellValue('Total Equity');
      sheet
          .cell(excel.CellIndex.indexByColumnRow(
              columnIndex: 1, rowIndex: currentRow))
          .value = excel.DoubleCellValue(report.totalEquity);
      currentRow += 2;

      // Balance Verification
      sheet
              .cell(excel.CellIndex.indexByColumnRow(
                  columnIndex: 0, rowIndex: currentRow))
              .value =
          excel.TextCellValue(
              'Balance Status: ${report.isBalanced ? "BALANCED" : "UNBALANCED"}');

      await _downloadExcel(
          excelFile, 'balance_sheet_${_getDateString(report.endDate)}.xlsx');
    } catch (e) {
      log('Error generating Balance Sheet Excel: $e');
      rethrow;
    }
  }

  /// Share PDF with web-specific handling
  Future<void> sharePDF(Uint8List pdfBytes, String fileName) async {
    try {
      if (kIsWeb) {
        await _downloadPDFWeb(pdfBytes, fileName);
      } else {
        await Printing.sharePdf(bytes: pdfBytes, filename: fileName);
      }
    } catch (e) {
      log('Error sharing PDF: $e');
      if (kIsWeb) {
        await _downloadPDFWeb(pdfBytes, fileName);
      } else {
        rethrow;
      }
    }
  }

  /// Download PDF for web
  Future<void> _downloadPDFWeb(Uint8List pdfBytes, String fileName) async {
    final blob = html.Blob([pdfBytes], 'application/pdf');
    final url = html.Url.createObjectUrlFromBlob(blob);
    html.AnchorElement(href: url)
      ..setAttribute('download', fileName)
      ..click();
    html.Url.revokeObjectUrl(url);
  }

  /// Download Excel file
  Future<void> _downloadExcel(excel.Excel excelFile, String fileName) async {
    final excelBytes = excelFile.encode();
    if (excelBytes == null) {
      throw Exception('Failed to generate Excel file');
    }

    if (kIsWeb) {
      await _downloadExcelWeb(Uint8List.fromList(excelBytes), fileName);
    } else {
      throw UnimplementedError('Mobile/Desktop Excel download not implemented');
    }
  }

  /// Download Excel for web
  Future<void> _downloadExcelWeb(Uint8List excelBytes, String fileName) async {
    final blob = html.Blob([excelBytes]);
    final url = html.Url.createObjectUrlFromBlob(blob);
    html.AnchorElement(href: url)
      ..setAttribute('download', fileName)
      ..click();
    html.Url.revokeObjectUrl(url);
  }

  /// Get date string for file naming
  String _getDateString(DateTime date) {
    return DateFormat('yyyyMMdd').format(date);
  }

  /// Build Trial Balance header
  pw.Widget _buildTrialBalanceHeader(
      pw.Context context, TrialBalanceReport report) {
    return pw.Container(
      padding: const pw.EdgeInsets.only(bottom: 20),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'TRIAL BALANCE',
            style: pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 10),
          pw.Text('Company: ${report.companyName}',
              style: const pw.TextStyle(fontSize: 14)),
          pw.Text(
            'Period: ${_dateFormat.format(report.startDate)} to ${_dateFormat.format(report.endDate)}',
            style: const pw.TextStyle(fontSize: 14),
          ),
          pw.Text(
            'Generated: ${_dateFormat.format(report.generatedAt)}',
            style: const pw.TextStyle(fontSize: 12),
          ),
        ],
      ),
    );
  }

  /// Build Trial Balance table
  pw.Widget _buildTrialBalanceTable(TrialBalanceReport report) {
    return pw.Table(
      border: pw.TableBorder.all(),
      columnWidths: {
        0: const pw.FixedColumnWidth(80),
        1: const pw.FlexColumnWidth(3),
        2: const pw.FixedColumnWidth(100),
        3: const pw.FixedColumnWidth(100),
      },
      children: [
        // Header
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey300),
          children: [
            _buildTableCell('Account #', isHeader: true),
            _buildTableCell('Account Name', isHeader: true),
            _buildTableCell('Debit', isHeader: true),
            _buildTableCell('Credit', isHeader: true),
          ],
        ),
        // Data rows
        ...report.accounts.map((account) => pw.TableRow(
              children: [
                _buildTableCell(account.accountNumber),
                _buildTableCell(account.accountName),
                _buildTableCell(_currencyFormat.format(account.debitBalance)),
                _buildTableCell(_currencyFormat.format(account.creditBalance)),
              ],
            )),
        // Totals row
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey200),
          children: [
            _buildTableCell(''),
            _buildTableCell('TOTALS', isHeader: true),
            _buildTableCell(_currencyFormat.format(report.totalDebits),
                isHeader: true),
            _buildTableCell(_currencyFormat.format(report.totalCredits),
                isHeader: true),
          ],
        ),
      ],
    );
  }

  /// Build Trial Balance summary
  pw.Widget _buildTrialBalanceSummary(TrialBalanceReport report) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(),
        color: report.isBalanced ? PdfColors.green100 : PdfColors.red100,
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'TRIAL BALANCE SUMMARY',
            style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 10),
          pw.Text(
              'Total Debits: ${_currencyFormat.format(report.totalDebits)}'),
          pw.Text(
              'Total Credits: ${_currencyFormat.format(report.totalCredits)}'),
          pw.Text(
              'Difference: ${_currencyFormat.format((report.totalDebits - report.totalCredits).abs())}'),
          pw.Text(
            'Status: ${report.isBalanced ? "BALANCED" : "UNBALANCED"}',
            style: pw.TextStyle(
              fontWeight: pw.FontWeight.bold,
              color: report.isBalanced ? PdfColors.green : PdfColors.red,
            ),
          ),
        ],
      ),
    );
  }

  /// Build Profit & Loss header
  pw.Widget _buildProfitLossHeader(
      pw.Context context, ProfitLossReport report) {
    return pw.Container(
      padding: const pw.EdgeInsets.only(bottom: 20),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'PROFIT & LOSS STATEMENT',
            style: pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 10),
          pw.Text('Company: ${report.companyName}',
              style: const pw.TextStyle(fontSize: 14)),
          pw.Text(
            'Period: ${_dateFormat.format(report.startDate)} to ${_dateFormat.format(report.endDate)}',
            style: const pw.TextStyle(fontSize: 14),
          ),
          pw.Text(
            'Generated: ${_dateFormat.format(report.generatedAt)}',
            style: const pw.TextStyle(fontSize: 12),
          ),
        ],
      ),
    );
  }

  /// Build Profit & Loss table
  pw.Widget _buildProfitLossTable(ProfitLossReport report) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // Revenue Section
        pw.Text(
          'REVENUE',
          style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 10),
        ...report.revenueGroups.map((group) => _buildPLGroupSection(group)),
        _buildPLTotalRow('Total Revenue', report.totalRevenue, PdfColors.blue),

        pw.SizedBox(height: 20),

        // Expenses Section
        pw.Text(
          'EXPENSES',
          style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 10),
        ...report.expenseGroups.map((group) => _buildPLGroupSection(group)),
        _buildPLTotalRow('Total Expenses', report.totalExpenses, PdfColors.red),

        pw.SizedBox(height: 20),

        // Net Income
        _buildPLTotalRow('NET INCOME', report.netIncome,
            report.netIncome >= 0 ? PdfColors.green : PdfColors.red),
      ],
    );
  }

  /// Build Profit & Loss summary
  pw.Widget _buildProfitLossSummary(ProfitLossReport report) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(),
        color: report.netIncome >= 0 ? PdfColors.green100 : PdfColors.red100,
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'FINANCIAL SUMMARY',
            style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 10),
          pw.Text(
              'Total Revenue: ${_currencyFormat.format(report.totalRevenue)}'),
          pw.Text(
              'Total Expenses: ${_currencyFormat.format(report.totalExpenses)}'),
          pw.Text(
            'Net Income: ${_currencyFormat.format(report.netIncome)}',
            style: pw.TextStyle(
              fontWeight: pw.FontWeight.bold,
              color: report.netIncome >= 0 ? PdfColors.green : PdfColors.red,
            ),
          ),
          if (report.totalRevenue > 0)
            pw.Text(
                'Profit Margin: ${((report.netIncome / report.totalRevenue) * 100).toStringAsFixed(2)}%'),
        ],
      ),
    );
  }

  /// Build Balance Sheet header
  pw.Widget _buildBalanceSheetHeader(
      pw.Context context, BalanceSheetReport report) {
    return pw.Container(
      padding: const pw.EdgeInsets.only(bottom: 20),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'BALANCE SHEET',
            style: pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 10),
          pw.Text('Company: ${report.companyName}',
              style: const pw.TextStyle(fontSize: 14)),
          pw.Text(
            'As of: ${_dateFormat.format(report.endDate)}',
            style: const pw.TextStyle(fontSize: 14),
          ),
          pw.Text(
            'Generated: ${_dateFormat.format(report.generatedAt)}',
            style: const pw.TextStyle(fontSize: 12),
          ),
        ],
      ),
    );
  }

  /// Build Balance Sheet table
  pw.Widget _buildBalanceSheetTable(BalanceSheetReport report) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // Assets Section
        pw.Text(
          'ASSETS',
          style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 10),
        ...report.assetGroups.map((group) => _buildBSGroupSection(group)),
        _buildBSTotalRow('Total Assets', report.totalAssets, PdfColors.blue),

        pw.SizedBox(height: 20),

        // Liabilities Section
        pw.Text(
          'LIABILITIES',
          style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 10),
        ...report.liabilityGroups.map((group) => _buildBSGroupSection(group)),
        _buildBSTotalRow(
            'Total Liabilities', report.totalLiabilities, PdfColors.red),

        pw.SizedBox(height: 20),

        // Equity Section
        pw.Text(
          'EQUITY',
          style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 10),
        ...report.equityGroups.map((group) => _buildBSGroupSection(group)),
        _buildBSTotalRow('Total Equity', report.totalEquity, PdfColors.green),
      ],
    );
  }

  /// Build Balance Sheet summary
  pw.Widget _buildBalanceSheetSummary(BalanceSheetReport report) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(),
        color: report.isBalanced ? PdfColors.green100 : PdfColors.red100,
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'BALANCE SHEET SUMMARY',
            style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 10),
          pw.Text(
              'Total Assets: ${_currencyFormat.format(report.totalAssets)}'),
          pw.Text(
              'Total Liabilities: ${_currencyFormat.format(report.totalLiabilities)}'),
          pw.Text(
              'Total Equity: ${_currencyFormat.format(report.totalEquity)}'),
          pw.Text(
            'Balance Status: ${report.isBalanced ? "BALANCED" : "UNBALANCED"}',
            style: pw.TextStyle(
              fontWeight: pw.FontWeight.bold,
              color: report.isBalanced ? PdfColors.green : PdfColors.red,
            ),
          ),
          if (report.totalAssets > 0) ...[
            pw.SizedBox(height: 5),
            pw.Text(
                'Debt-to-Equity Ratio: ${(report.totalLiabilities / report.totalEquity).toStringAsFixed(2)}'),
            pw.Text(
                'Equity Percentage: ${((report.totalEquity / report.totalAssets) * 100).toStringAsFixed(2)}%'),
          ],
        ],
      ),
    );
  }

  /// Build table cell
  pw.Widget _buildTableCell(String text, {bool isHeader = false}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: isHeader ? 12 : 10,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
      ),
    );
  }

  /// Build P&L group section
  pw.Widget _buildPLGroupSection(PLAccountGroup group) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Container(
          padding: const pw.EdgeInsets.symmetric(vertical: 4),
          child: pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text(
                group.groupName,
                style:
                    pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
              ),
              pw.Text(
                _currencyFormat.format(group.groupTotal),
                style:
                    pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
              ),
            ],
          ),
        ),
        ...group.accounts.map((account) => pw.Container(
              padding: const pw.EdgeInsets.only(left: 20, top: 2, bottom: 2),
              child: pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text(account.accountName,
                      style: const pw.TextStyle(fontSize: 12)),
                  pw.Text(_currencyFormat.format(account.amount),
                      style: const pw.TextStyle(fontSize: 12)),
                ],
              ),
            )),
        pw.SizedBox(height: 8),
      ],
    );
  }

  /// Build P&L total row
  pw.Widget _buildPLTotalRow(String label, double amount, PdfColor color) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(),
        color: color.shade(0.1),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            label,
            style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
          ),
          pw.Text(
            _currencyFormat.format(amount),
            style: pw.TextStyle(
                fontSize: 16, fontWeight: pw.FontWeight.bold, color: color),
          ),
        ],
      ),
    );
  }

  /// Build BS group section
  pw.Widget _buildBSGroupSection(BSAccountGroup group) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Container(
          padding: const pw.EdgeInsets.symmetric(vertical: 4),
          child: pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text(
                group.groupName,
                style:
                    pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
              ),
              pw.Text(
                _currencyFormat.format(group.groupTotal),
                style:
                    pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
              ),
            ],
          ),
        ),
        ...group.accounts.map((account) => pw.Container(
              padding: const pw.EdgeInsets.only(left: 20, top: 2, bottom: 2),
              child: pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text(account.accountName,
                      style: const pw.TextStyle(fontSize: 12)),
                  pw.Text(_currencyFormat.format(account.amount),
                      style: const pw.TextStyle(fontSize: 12)),
                ],
              ),
            )),
        pw.SizedBox(height: 8),
      ],
    );
  }

  /// Build BS total row
  pw.Widget _buildBSTotalRow(String label, double amount, PdfColor color) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(),
        color: color.shade(0.1),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            label,
            style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
          ),
          pw.Text(
            _currencyFormat.format(amount),
            style: pw.TextStyle(
                fontSize: 16, fontWeight: pw.FontWeight.bold, color: color),
          ),
        ],
      ),
    );
  }

  /// Build footer
  pw.Widget _buildFooter(pw.Context context) {
    return pw.Container(
      alignment: pw.Alignment.centerRight,
      margin: const pw.EdgeInsets.only(top: 20),
      child: pw.Text(
        'Page ${context.pageNumber} of ${context.pagesCount}',
        style: const pw.TextStyle(fontSize: 10),
      ),
    );
  }
}
