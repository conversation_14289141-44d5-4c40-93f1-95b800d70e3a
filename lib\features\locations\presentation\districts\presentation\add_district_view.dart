import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/widgets/custom_button.dart';
import 'package:logestics/core/utils/widgets/my_text_field.dart';
import 'package:logestics/core/utils/widgets/my_dropdown_field.dart';
import 'package:logestics/features/locations/domain/usecases/district_use_case/create_district_use_case.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';

import '../controller/add_district_controller.dart';

class AddDistrictView extends StatefulWidget {
  const AddDistrictView({super.key});

  @override
  State<AddDistrictView> createState() => _AddDistrictViewState();
}

class _AddDistrictViewState extends State<AddDistrictView> {
  late AddDistrictController controller;

  @override
  void initState() {
    super.initState();
    controller = Get.put(AddDistrictController(
        createDistrictUseCase: CreateDistrictUseCase(Get.find())));

    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.refreshRegions();
    });
  }

  @override
  Widget build(BuildContext context) {
    notifier = Provider.of(context, listen: false);
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 600;

    return Form(
      key: controller.formKey,
      child: Scaffold(
        backgroundColor: notifier.getBgColor,
        body: Dialog(
          backgroundColor: notifier.getBgColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          child: Container(
            width: size.width * 0.9,
            height: size.height * 0.9,
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        AppStrings.addNewDistrict,
                        overflow: TextOverflow.ellipsis,
                        style: AppTextStyles.titleStyle
                            .copyWith(color: notifier.text),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: Icon(Icons.close, color: notifier.text),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
                Divider(thickness: 5),
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      spacing: 16,
                      children: [
                        Row(
                          spacing: 16,
                          children: [
                            Expanded(
                              child: Obx(() => controller.isLoadingRegions.value
                                  ? Center(child: CircularProgressIndicator())
                                  : MyDropdownFormField(
                                      titletext: AppStrings.selectRegion,
                                      hinttext: AppStrings.selectRegionHint,
                                      items: controller.regions
                                          .map((region) => region.regionName)
                                          .toList(),
                                      initalValue: controller
                                          .selectedRegion.value?.regionName,
                                      onChanged: (value) {
                                        if (value != null) {
                                          final selectedRegion =
                                              controller.regions.firstWhere(
                                            (r) => r.regionName == value,
                                            orElse: () =>
                                                controller.regions.first,
                                          );
                                          controller.setSelectedRegion(
                                              selectedRegion);
                                        }
                                      },
                                      validator: controller.validateRegion,
                                    )),
                            ),
                            SizedBox(width: 16),
                            Expanded(
                              child: MyTextFormField(
                                titleText: AppStrings.districtName,
                                labelText: AppStrings.districtName,
                                hintText: AppStrings.districtNameHint,
                                controller: controller.districtNameController,
                                validator: controller.validateDistrictName,
                              ),
                            ),
                          ],
                        ),

                        // Action Buttons
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Obx(() => CustomButton.danger(
                                  onPressed: () => Get.back(),
                                  isDisabled: controller.isLoading.value,
                                  text: AppStrings.cancel,
                                  minimumSize:
                                      Size(isSmallScreen ? 100 : 130, 50),
                                )),
                            const SizedBox(width: 8),
                            Obx(() => CustomButton.primary(
                                  onPressed: () => controller.saveDistrict(),
                                  text: AppStrings.save,
                                  isLoading: controller.isLoading.value,
                                  minimumSize:
                                      Size(isSmallScreen ? 100 : 130, 50),
                                )),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
