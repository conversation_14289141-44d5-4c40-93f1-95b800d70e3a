import 'dart:developer';
import 'package:either_dart/either.dart';
import '../../../../core/shared_services/failure_obj.dart';
import '../../../../core/shared_services/success_obj.dart';
import '../../../../firebase_service/accounting/balance_sheet_firebase_service.dart';
import '../../../../models/accounting/financial_report_models.dart';

/// Repository for Balance Sheet report operations
class BalanceSheetRepository {
  final BalanceSheetFirebaseService _firebaseService = BalanceSheetFirebaseService();

  /// Generate Balance Sheet report
  Future<Either<FailureObj, BalanceSheetReport>> generateBalanceSheet({
    required DateTime asOfDate,
    required String companyName,
    bool includeInactiveAccounts = false,
    bool includeZeroBalances = false,
  }) async {
    try {
      log('Repository: Generating Balance Sheet report');

      // Validate input parameters
      if (asOfDate.isAfter(DateTime.now())) {
        return Left(FailureObj(
          code: 'invalid-date',
          message: 'As of date cannot be in the future',
        ));
      }

      if (companyName.trim().isEmpty) {
        return Left(FailureObj(
          code: 'invalid-company-name',
          message: 'Company name cannot be empty',
        ));
      }

      // Call Firebase service
      final result = await _firebaseService.generateBalanceSheet(
        asOfDate: asOfDate,
        companyName: companyName,
        includeInactiveAccounts: includeInactiveAccounts,
        includeZeroBalances: includeZeroBalances,
      );

      return result.fold(
        (failure) {
          log('Repository: Balance Sheet generation failed: ${failure.message}');
          return Left(failure);
        },
        (report) {
          log('Repository: Balance Sheet generated successfully');
          return Right(report);
        },
      );
    } catch (e) {
      log('Repository: Error generating Balance Sheet: $e');
      return Left(FailureObj(
        code: 'repository-error',
        message: 'Repository error: $e',
      ));
    }
  }

  /// Save Balance Sheet report
  Future<Either<FailureObj, SuccessObj>> saveBalanceSheetReport(BalanceSheetReport report) async {
    try {
      log('Repository: Saving Balance Sheet report');

      // Validate report
      if (report.reportId.trim().isEmpty) {
        return Left(FailureObj(
          code: 'invalid-report-id',
          message: 'Report ID cannot be empty',
        ));
      }

      if (report.companyName.trim().isEmpty) {
        return Left(FailureObj(
          code: 'invalid-company-name',
          message: 'Company name cannot be empty',
        ));
      }

      // Call Firebase service
      final result = await _firebaseService.saveBalanceSheetReport(report);

      return result.fold(
        (failure) {
          log('Repository: Balance Sheet save failed: ${failure.message}');
          return Left(failure);
        },
        (success) {
          log('Repository: Balance Sheet saved successfully');
          return Right(success);
        },
      );
    } catch (e) {
      log('Repository: Error saving Balance Sheet: $e');
      return Left(FailureObj(
        code: 'repository-error',
        message: 'Repository error: $e',
      ));
    }
  }

  /// Load saved Balance Sheet reports
  Future<Either<FailureObj, List<BalanceSheetReport>>> getSavedBalanceSheetReports() async {
    try {
      log('Repository: Loading saved Balance Sheet reports');

      final result = await _firebaseService.getSavedBalanceSheetReports();

      return result.fold(
        (failure) {
          log('Repository: Balance Sheet load failed: ${failure.message}');
          return Left(failure);
        },
        (reports) {
          log('Repository: Loaded ${reports.length} Balance Sheet reports');
          return Right(reports);
        },
      );
    } catch (e) {
      log('Repository: Error loading Balance Sheet reports: $e');
      return Left(FailureObj(
        code: 'repository-error',
        message: 'Repository error: $e',
      ));
    }
  }

  /// Load specific Balance Sheet report by ID
  Future<Either<FailureObj, BalanceSheetReport>> getBalanceSheetReport(String reportId) async {
    try {
      log('Repository: Loading Balance Sheet report: $reportId');

      // Validate report ID
      if (reportId.trim().isEmpty) {
        return Left(FailureObj(
          code: 'invalid-report-id',
          message: 'Report ID cannot be empty',
        ));
      }

      final result = await _firebaseService.getBalanceSheetReport(reportId);

      return result.fold(
        (failure) {
          log('Repository: Balance Sheet load failed: ${failure.message}');
          return Left(failure);
        },
        (report) {
          log('Repository: Balance Sheet loaded successfully');
          return Right(report);
        },
      );
    } catch (e) {
      log('Repository: Error loading Balance Sheet report: $e');
      return Left(FailureObj(
        code: 'repository-error',
        message: 'Repository error: $e',
      ));
    }
  }

  /// Delete Balance Sheet report
  Future<Either<FailureObj, SuccessObj>> deleteBalanceSheetReport(String reportId) async {
    try {
      log('Repository: Deleting Balance Sheet report: $reportId');

      // Validate report ID
      if (reportId.trim().isEmpty) {
        return Left(FailureObj(
          code: 'invalid-report-id',
          message: 'Report ID cannot be empty',
        ));
      }

      final result = await _firebaseService.deleteBalanceSheetReport(reportId);

      return result.fold(
        (failure) {
          log('Repository: Balance Sheet delete failed: ${failure.message}');
          return Left(failure);
        },
        (success) {
          log('Repository: Balance Sheet deleted successfully');
          return Right(success);
        },
      );
    } catch (e) {
      log('Repository: Error deleting Balance Sheet report: $e');
      return Left(FailureObj(
        code: 'repository-error',
        message: 'Repository error: $e',
      ));
    }
  }
}
