import 'dart:developer';
import 'package:either_dart/either.dart';
import '../../../../core/shared_services/failure_obj.dart';
import '../../../../core/utils/app_constants/texts/app_strings.dart';
import '../../../../models/accounting/financial_dashboard_models.dart';
import '../firebase_services/financial_dashboard_firebase_service.dart';

/// Repository for Financial Dashboard data operations
class FinancialDashboardRepository {
  final FinancialDashboardFirebaseService _firebaseService =
      FinancialDashboardFirebaseService();

  /// Get financial summary for dashboard
  Future<Either<FailureObj, FinancialSummaryModel>> getFinancialSummary(
    String uid,
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      log('Repository: Getting financial summary for period: $startDate to $endDate');
      final summary =
          await _firebaseService.getFinancialSummary(uid, startDate, endDate);
      return Right(summary);
    } catch (e) {
      log('Repository: Error getting financial summary: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  /// Get account balances for dashboard
  Future<Either<FailureObj, List<AccountBalanceModel>>> getAccountBalances(
    String uid,
    bool includeInactive,
  ) async {
    try {
      log('Repository: Getting account balances (includeInactive: $includeInactive)');
      final balances =
          await _firebaseService.getAccountBalances(uid, includeInactive);
      return Right(balances);
    } catch (e) {
      log('Repository: Error getting account balances: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  /// Get recent transactions for dashboard
  Future<Either<FailureObj, List<DashboardTransactionModel>>>
      getRecentTransactions(
    String uid,
    int limit,
  ) async {
    try {
      log('Repository: Getting recent transactions (limit: $limit)');
      final transactions =
          await _firebaseService.getRecentTransactions(uid, limit);
      return Right(transactions);
    } catch (e) {
      log('Repository: Error getting recent transactions: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  /// Get cash flow data for charts
  Future<Either<FailureObj, List<CashFlowDataModel>>> getCashFlowData(
    String uid,
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      log('Repository: Getting cash flow data for period: $startDate to $endDate');
      final data =
          await _firebaseService.getCashFlowData(uid, startDate, endDate);
      return Right(data);
    } catch (e) {
      log('Repository: Error getting cash flow data: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  /// Get expense breakdown for pie charts
  Future<Either<FailureObj, List<ExpenseBreakdownModel>>> getExpenseBreakdown(
    String uid,
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      log('Repository: Getting expense breakdown for period: $startDate to $endDate');
      final breakdown =
          await _firebaseService.getExpenseBreakdown(uid, startDate, endDate);
      return Right(breakdown);
    } catch (e) {
      log('Repository: Error getting expense breakdown: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }
}
