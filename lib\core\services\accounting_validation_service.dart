import 'dart:developer';
import '../../models/finance/journal_entry_model.dart';
import '../../models/finance/chart_of_accounts_model.dart';

/// Comprehensive validation service for accounting operations
class AccountingValidationService {
  /// Validation result class
  static const double _tolerance =
      0.01; // 1 cent tolerance for floating point precision

  /// Validate a journal entry for double-entry bookkeeping rules
  static ValidationResult validateJournalEntry(JournalEntryModel journalEntry) {
    final errors = <String>[];
    final warnings = <String>[];

    // Basic validation
    if (journalEntry.description.trim().isEmpty) {
      errors.add('Journal entry description is required');
    }

    if (journalEntry.lines.isEmpty) {
      errors.add('Journal entry must have at least one line');
      return ValidationResult(
        isValid: false,
        errors: errors,
        warnings: warnings,
      );
    }

    // Minimum lines validation
    if (journalEntry.lines.length < 2) {
      errors.add(
          'Journal entry must have at least 2 lines (minimum one debit and one credit)');
    }

    // Double-entry validation
    final balanceValidation = _validateBalance(journalEntry);
    if (!balanceValidation.isValid) {
      errors.addAll(balanceValidation.errors);
    }
    warnings.addAll(balanceValidation.warnings);

    // Line-by-line validation
    final lineValidation = _validateLines(journalEntry.lines);
    if (!lineValidation.isValid) {
      errors.addAll(lineValidation.errors);
    }
    warnings.addAll(lineValidation.warnings);

    // Business rule validation
    final businessValidation = _validateBusinessRules(journalEntry);
    warnings.addAll(businessValidation.warnings);

    // Date validation
    final dateValidation = _validateDates(journalEntry);
    if (!dateValidation.isValid) {
      errors.addAll(dateValidation.errors);
    }
    warnings.addAll(dateValidation.warnings);

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Validate balance (debits = credits)
  static ValidationResult _validateBalance(JournalEntryModel journalEntry) {
    final errors = <String>[];
    final warnings = <String>[];

    final totalDebits = journalEntry.totalDebits;
    final totalCredits = journalEntry.totalCredits;
    final difference = (totalDebits - totalCredits).abs();

    if (difference > _tolerance) {
      errors.add(
          'Journal entry is not balanced. Debits: \$${totalDebits.toStringAsFixed(2)}, '
          'Credits: \$${totalCredits.toStringAsFixed(2)}, '
          'Difference: \$${difference.toStringAsFixed(2)}');
    }

    // Check for zero amounts
    if (totalDebits == 0 && totalCredits == 0) {
      errors.add('Journal entry cannot have zero total amounts');
    }

    // Warning for large amounts
    if (totalDebits > 1000000 || totalCredits > 1000000) {
      warnings
          .add('Large transaction amount detected. Please verify accuracy.');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Validate individual journal entry lines
  static ValidationResult _validateLines(List<JournalEntryLineModel> lines) {
    final errors = <String>[];
    final warnings = <String>[];

    for (int i = 0; i < lines.length; i++) {
      final line = lines[i];
      final lineNumber = i + 1;

      // Check that line has either debit or credit, not both or neither
      final hasDebit = line.debitAmount > 0;
      final hasCredit = line.creditAmount > 0;

      if (hasDebit && hasCredit) {
        errors
            .add('Line $lineNumber: Cannot have both debit and credit amounts');
      } else if (!hasDebit && !hasCredit) {
        errors.add('Line $lineNumber: Must have either debit or credit amount');
      }

      // Check for negative amounts
      if (line.debitAmount < 0) {
        errors.add('Line $lineNumber: Debit amount cannot be negative');
      }
      if (line.creditAmount < 0) {
        errors.add('Line $lineNumber: Credit amount cannot be negative');
      }

      // Check for very small amounts (potential data entry errors)
      final amount = hasDebit ? line.debitAmount : line.creditAmount;
      if (amount > 0 && amount < 0.01) {
        warnings.add(
            'Line $lineNumber: Very small amount detected (\$${amount.toStringAsFixed(4)})');
      }

      // Check for description
      if (line.description.trim().isEmpty) {
        warnings.add('Line $lineNumber: Line description is empty');
      }

      // Check for account information
      if (line.accountId.trim().isEmpty) {
        errors.add('Line $lineNumber: Account ID is required');
      }
      if (line.accountName.trim().isEmpty) {
        warnings.add('Line $lineNumber: Account name is missing');
      }
      if (line.accountNumber.trim().isEmpty) {
        warnings.add('Line $lineNumber: Account number is missing');
      }
    }

    // Check for duplicate accounts with same debit/credit type
    final accountGroups = <String, List<JournalEntryLineModel>>{};
    for (final line in lines) {
      final key = '${line.accountId}_${line.debitAmount > 0 ? 'DR' : 'CR'}';
      accountGroups[key] = (accountGroups[key] ?? [])..add(line);
    }

    for (final entry in accountGroups.entries) {
      if (entry.value.length > 1) {
        final accountName = entry.value.first.accountName;
        final type = entry.key.endsWith('DR') ? 'debit' : 'credit';
        warnings.add(
            'Multiple $type entries for account "$accountName" - consider consolidating');
      }
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Validate business rules
  static ValidationResult _validateBusinessRules(
      JournalEntryModel journalEntry) {
    final warnings = <String>[];

    // Check for round amounts (might indicate estimates)
    final totalAmount = journalEntry.totalDebits;
    if (totalAmount % 100 == 0 && totalAmount > 100) {
      warnings.add('Round amount detected - verify if this is an estimate');
    }

    // Check for weekend posting (if applicable to business rules)
    final entryDate = journalEntry.entryDate;
    if (entryDate.weekday == DateTime.saturday ||
        entryDate.weekday == DateTime.sunday) {
      warnings.add('Journal entry dated on weekend');
    }

    // Check for future dates
    if (entryDate.isAfter(DateTime.now().add(const Duration(days: 1)))) {
      warnings.add('Journal entry dated in the future');
    }

    return ValidationResult(
      isValid: true,
      errors: [],
      warnings: warnings,
    );
  }

  /// Validate dates
  static ValidationResult _validateDates(JournalEntryModel journalEntry) {
    final errors = <String>[];
    final warnings = <String>[];

    final entryDate = journalEntry.entryDate;
    final now = DateTime.now();

    // Check for very old dates (potential data entry error)
    final daysDifference = now.difference(entryDate).inDays;
    if (daysDifference > 365) {
      warnings.add('Journal entry is more than 1 year old');
    }

    // Check for very future dates
    if (entryDate.isAfter(now.add(const Duration(days: 30)))) {
      errors.add('Journal entry cannot be more than 30 days in the future');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Validate account compatibility for journal entries
  static ValidationResult validateAccountCompatibility(
    List<ChartOfAccountsModel> accounts,
    List<JournalEntryLineModel> lines,
  ) {
    final errors = <String>[];
    final warnings = <String>[];

    for (final line in lines) {
      final account = accounts.where((a) => a.id == line.accountId).firstOrNull;

      if (account == null) {
        errors.add('Account not found: ${line.accountName}');
        continue;
      }

      // Check if account is active
      if (!account.isActive) {
        errors.add('Account "${account.accountName}" is not active');
      }

      // Check account type appropriateness
      final isDebit = line.debitAmount > 0;
      final accountType = account.accountType;

      // Normal balance validation
      if (_isUnusualBalance(accountType, isDebit)) {
        warnings.add(
            'Unusual balance for ${accountType.displayName} account "${account.accountName}": '
            '${isDebit ? 'Debit' : 'Credit'} entry');
      }
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Check if the balance type is unusual for the account category
  static bool _isUnusualBalance(AccountType accountType, bool isDebit) {
    final category = accountType.category;
    switch (category) {
      case AccountCategory.assets:
      case AccountCategory.expenses:
        return !isDebit; // Assets and expenses normally have debit balances
      case AccountCategory.liabilities:
      case AccountCategory.equity:
      case AccountCategory.revenue:
        return isDebit; // Liabilities, equity, and revenue normally have credit balances
    }
  }

  /// Validate posting requirements
  static ValidationResult validatePostingRequirements(
      JournalEntryModel journalEntry) {
    final errors = <String>[];
    final warnings = <String>[];

    // Check if entry can be posted
    if (journalEntry.status != JournalEntryStatus.draft) {
      errors.add('Only draft journal entries can be posted');
    }

    if (!journalEntry.isBalanced) {
      errors.add('Journal entry must be balanced before posting');
    }

    if (journalEntry.lines.isEmpty) {
      errors.add('Journal entry must have lines before posting');
    }

    // Check for required approvals (if applicable)
    final totalAmount = journalEntry.totalDebits;
    if (totalAmount > 10000) {
      warnings.add('Large transaction - may require additional approval');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }
}

/// Validation result class
class ValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  const ValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
  });

  bool get hasWarnings => warnings.isNotEmpty;
  bool get hasErrors => errors.isNotEmpty;

  @override
  String toString() {
    final buffer = StringBuffer();
    buffer.writeln('ValidationResult(isValid: $isValid)');

    if (errors.isNotEmpty) {
      buffer.writeln('Errors:');
      for (final error in errors) {
        buffer.writeln('  - $error');
      }
    }

    if (warnings.isNotEmpty) {
      buffer.writeln('Warnings:');
      for (final warning in warnings) {
        buffer.writeln('  - $warning');
      }
    }

    return buffer.toString();
  }
}

/// Extension to add firstOrNull method if not available
extension ListExtension<T> on List<T> {
  T? get firstOrNull => isEmpty ? null : first;
}
