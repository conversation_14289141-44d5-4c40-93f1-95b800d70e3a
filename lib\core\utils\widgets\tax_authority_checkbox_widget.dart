import 'package:flutter/material.dart';
import 'package:logestics/main.dart';

class TaxAuthorityCheckboxWidget extends StatelessWidget {
  final List<String> availableAuthorities;
  final List<String> selectedAuthorities;
  final Function(String) onToggle;
  final String? errorMessage;
  final VoidCallback? onErrorClear;

  const TaxAuthorityCheckboxWidget({
    super.key,
    required this.availableAuthorities,
    required this.selectedAuthorities,
    required this.onToggle,
    this.errorMessage,
    this.onErrorClear,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title
        Text(
          "Tax Authority Selection (15% Tax)",
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: notifier.text,
          ),
        ),
        const SizedBox(height: 8),

        // Subtitle
        Text(
          "Select up to 2 tax authorities:",
          style: TextStyle(
            fontSize: 14,
            color: notifier.text.withOpacity(0.7),
          ),
        ),
        const SizedBox(height: 12),

        // Checkbox List
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(
              color: errorMessage != null
                  ? Colors.red
                  : notifier.text.withAlpha(30),
              width: 1,
            ),
            borderRadius: BorderRadius.circular(8),
            color: notifier.getBgColor,
          ),
          child: Column(
            children: availableAuthorities
                .map((authority) => _UltraFastTaxTile(
                      key: ValueKey(authority),
                      authority: authority,
                      selectedAuthorities: selectedAuthorities,
                      onToggle: onToggle,
                    ))
                .toList(),
          ),
        ),

        // Error Message
        if (errorMessage != null) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.red.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.error_outline,
                  color: Colors.red,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    errorMessage!,
                    style: TextStyle(
                      color: Colors.red,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                if (onErrorClear != null)
                  GestureDetector(
                    onTap: onErrorClear,
                    child: Icon(
                      Icons.close,
                      color: Colors.red,
                      size: 16,
                    ),
                  ),
              ],
            ),
          ),
        ],

        // Selection Info
        const SizedBox(height: 8),
        Text(
          "Selected: ${selectedAuthorities.length}/2",
          style: TextStyle(
            fontSize: 12,
            color: notifier.text.withOpacity(0.6),
            fontStyle: FontStyle.italic,
          ),
        ),
      ],
    );
  }
}

/// Ultra-fast reactive tax authority tile with zero-delay interactions
class _UltraFastTaxTile extends StatelessWidget {
  final String authority;
  final List<String> selectedAuthorities;
  final Function(String) onToggle;

  const _UltraFastTaxTile({
    super.key,
    required this.authority,
    required this.selectedAuthorities,
    required this.onToggle,
  });

  @override
  Widget build(BuildContext context) {
    final isSelected = selectedAuthorities.contains(authority);
    final isDisabled = !isSelected && selectedAuthorities.length >= 2;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: GestureDetector(
        onTap: isDisabled
            ? null
            : () {
                // Immediate execution with no delays
                onToggle(authority);
              },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(6),
            color: isSelected
                ? const Color(0xFF0f79f3).withValues(alpha: 0.1)
                : Colors.transparent,
          ),
          child: Row(
            children: [
              // Minimal checkbox implementation for maximum speed
              Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: isDisabled
                        ? notifier.text.withOpacity(0.3)
                        : isSelected
                            ? const Color(0xFF0f79f3)
                            : notifier.text.withOpacity(0.5),
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(3),
                  color:
                      isSelected ? const Color(0xFF0f79f3) : Colors.transparent,
                ),
                child: isSelected
                    ? const Icon(
                        Icons.check,
                        size: 14,
                        color: Colors.white,
                      )
                    : null,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  authority,
                  style: TextStyle(
                    color: isDisabled
                        ? notifier.text.withOpacity(0.4)
                        : notifier.text,
                    fontSize: 14,
                    fontWeight:
                        isSelected ? FontWeight.w500 : FontWeight.normal,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
