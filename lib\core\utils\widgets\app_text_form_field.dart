import 'package:flutter/material.dart';

import 'package:logestics/core/utils/app_constants/colors/app_colors.dart';

class AppTextFormField extends StatelessWidget {
  final String? hintText;
  final String? errorMessage;
  final String? labelText;
  final bool? isObscure;
  final TextEditingController controller;
  final AutovalidateMode? autoValidateMode;
  final Function()? obscureCallback;
  final String? Function(String?)? validator;

  AppTextFormField({
    super.key,
    this.hintText,
    this.obscureCallback,
    this.isObscure,
    String? errorMessage,
    this.validator,
    this.autoValidateMode,
    required this.controller,
    this.labelText,
  }) : errorMessage = errorMessage == null
            ? errorMessage
            : errorMessage.isEmpty
                ? null
                : errorMessage;

  @override
  Widget build(BuildContext context) {
    var screenWidth = MediaQuery.of(context).size.width;
    var screenHeight = MediaQuery.of(context).size.height;
    var dpr = MediaQuery.of(context).devicePixelRatio;

    return Focus(
      child: Builder(
        builder: (context) => Center(
          child: Stack(children: [
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(
                    screenHeight * 0.004 * screenHeight * 0.004),
                boxShadow: Focus.of(context).hasFocus
                    ? [
                        BoxShadow(
                          color: AppColors.shadow,
                          offset: Offset(10 / dpr, 10 / dpr),
                          blurRadius: 10 / dpr,
                        ),
                      ]
                    : null,
              ),
            ),
            TextFormField(
              controller: controller,
              obscureText: isObscure ?? false,
              obscuringCharacter: '*',
              validator: validator,
              autovalidateMode: autoValidateMode,
              decoration: InputDecoration(
                suffix: isObscure == null
                    ? null
                    : IconButton(
                        onPressed: obscureCallback,
                        icon: Icon(
                          isObscure == false
                              ? Icons.visibility
                              : Icons.visibility_off,
                          color: AppColors.primary,
                          size: screenHeight * 0.004 * screenWidth * 0.004,
                        ),
                      ),
                labelText: labelText,
                labelStyle: TextStyle(
                    fontSize: screenHeight * 0.0046 * screenWidth * 0.0046,
                    color: AppColors.primary),
                floatingLabelStyle: TextStyle(
                    fontSize: screenHeight * 0.0046 * screenWidth * 0.0046,
                    color: AppColors.primary),
                hoverColor: errorMessage == null
                    ? AppColors.secondaryContainer
                    : AppColors.error,
                contentPadding: EdgeInsets.symmetric(
                    horizontal: screenWidth * 0.02,
                    vertical: screenHeight * 0.02),
                hintText: hintText,
                hintStyle: TextStyle(
                    fontSize: screenHeight * 0.004 * screenWidth * 0.004,
                    color: AppColors.primary),
                filled: true,
                fillColor: controller.text.isNotEmpty
                    ? AppColors.surface
                    : Focus.of(context).hasFocus
                        ? AppColors.primary.withAlpha(1)
                        : AppColors.primary.withAlpha(1),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: AppColors.primary, width: 3),
                  borderRadius: BorderRadius.circular(50), // Stadium border
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    color: AppColors.primary,
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(50), // Stadium border
                ),
                focusedErrorBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    color: AppColors.onError,
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(50), // Stadium border
                ),
                errorText: errorMessage,
                errorStyle: TextStyle(
                    color: AppColors.onError,
                    fontSize: screenHeight * 0.004 * screenWidth * 0.004),
                errorBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: AppColors.onError),
                  borderRadius: BorderRadius.circular(50), // Stadium border
                ),
              ),
              style: TextStyle(
                color: AppColors.secondary,
                fontSize: screenHeight * 0.004 * screenWidth * 0.004,
              ),
            ),
          ]),
        ),
      ),
    );
  }
}
