import 'dart:ui';
import '../../enums/broker_enums.dart';

final voucherStatusList = VoucherStatus.allValues;
const lightAccentBlue = Color(0xFF92E2FF);
const lightOrange = Color(0xFFFFD099);
const darkOrange = Color(0xFFFE9B48);

const deliveryModeList = [
  "By Air",
  "By Road",
];

const invoiceStatusList = [
  "In Progress",
  "Completed",
];

String formatDate(DateTime date) {
  // Format the day, month, and year with padding for single-digit numbers
  String day = date.day.toString().padLeft(2, '0');
  String month = date.month.toString().padLeft(2, '0');
  String year = date.year.toString();

  // Combine into the desired format
  return '$day/$month/$year';
}

extension StringCapitalization on String {
  String capitalizeFirstLetter() {
    if (isEmpty) return toLowerCase();
    return this[0].toUpperCase() + substring(1).toLowerCase();
  }
}
