import 'dart:async';
import 'dart:developer';
import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:intl/intl.dart';
import '../../../../../models/finance/fiscal_period_model.dart';
import '../../../../../models/finance/year_end_closing_model.dart';
import '../../../../../core/utils/snackbar_utils.dart';
import '../../repositories/fiscal_period_repository.dart';
import '../../repositories/year_end_closing_repository.dart';

/// GetX Controller for Fiscal Period management
class FiscalPeriodController extends GetxController {
  final FiscalPeriodRepository _repository;
  final YearEndClosingRepository _yearEndClosingRepository;

  FiscalPeriodController({
    FiscalPeriodRepository? repository,
    YearEndClosingRepository? yearEndClosingRepository,
  })  : _repository = repository ?? Get.find<FiscalPeriodRepository>(),
        _yearEndClosingRepository =
            yearEndClosingRepository ?? Get.find<YearEndClosingRepository>();

  // Current user
  String get uid => FirebaseAuth.instance.currentUser?.uid ?? '';

  // Loading states
  var isLoading = false.obs;
  var isCreatingYear = false.obs;
  var isCreatingPeriod = false.obs;
  var isClosingPeriod = false.obs;
  var isPerformingYearEndClosing = false.obs;

  // Data
  var fiscalYears = <FiscalYearModel>[].obs;
  var fiscalPeriods = <FiscalPeriodModel>[].obs;
  var activeFiscalYear = Rxn<FiscalYearModel>();
  var currentFiscalPeriod = Rxn<FiscalPeriodModel>();
  var selectedFiscalYear = Rxn<FiscalYearModel>();

  // Year-end closing data
  var yearEndClosings = <YearEndClosingModel>[].obs;
  var yearEndClosingSummary = Rxn<YearEndClosingSummary>();

  // Form data for creating fiscal year
  var yearName = ''.obs;
  var yearStartDate = Rxn<DateTime>();
  var yearEndDate = Rxn<DateTime>();
  var selectedPeriodType = PeriodType.monthly.obs;

  // Streams
  StreamSubscription? _fiscalYearsSubscription;
  StreamSubscription? _fiscalPeriodsSubscription;

  @override
  void onInit() {
    super.onInit();
    log('FiscalPeriodController initialized');
    initializeData();
  }

  @override
  void onClose() {
    _fiscalYearsSubscription?.cancel();
    _fiscalPeriodsSubscription?.cancel();
    super.onClose();
  }

  /// Initialize data loading
  Future<void> initializeData() async {
    if (uid.isEmpty) {
      log('No user authenticated');
      return;
    }

    try {
      isLoading.value = true;

      // Load initial data
      await Future.wait([
        _loadFiscalYears(),
        _loadActiveFiscalYear(),
        _loadCurrentFiscalPeriod(),
        loadYearEndClosings(),
        loadYearEndClosingSummary(),
      ]);

      // Set up real-time listeners
      _setupFiscalYearsListener();
    } catch (e) {
      log('Error initializing fiscal period data: $e');
      SnackbarUtils.showError('Initialization failed', e.toString());
    } finally {
      isLoading.value = false;
    }
  }

  /// Load fiscal years
  Future<void> _loadFiscalYears() async {
    final result = await _repository.getFiscalYears();
    result.fold(
      (failure) {
        log('Failed to load fiscal years: ${failure.message}');
      },
      (years) {
        fiscalYears.value = years;
        log('Loaded ${years.length} fiscal years');
      },
    );
  }

  /// Load active fiscal year
  Future<void> _loadActiveFiscalYear() async {
    final result = await _repository.getActiveFiscalYear();
    result.fold(
      (failure) {
        log('Failed to load active fiscal year: ${failure.message}');
      },
      (year) {
        activeFiscalYear.value = year;
        selectedFiscalYear.value = year;
        if (year != null) {
          log('Active fiscal year: ${year.yearName}');
          _loadFiscalPeriods(year.id);
        }
      },
    );
  }

  /// Load current fiscal period
  Future<void> _loadCurrentFiscalPeriod() async {
    final result = await _repository.getCurrentFiscalPeriod();
    result.fold(
      (failure) {
        log('Failed to load current fiscal period: ${failure.message}');
      },
      (period) {
        currentFiscalPeriod.value = period;
        if (period != null) {
          log('Current fiscal period: ${period.periodName}');
        }
      },
    );
  }

  /// Load fiscal periods for a fiscal year
  Future<void> _loadFiscalPeriods(String fiscalYearId) async {
    final result = await _repository.getFiscalPeriods(fiscalYearId);
    result.fold(
      (failure) {
        log('Failed to load fiscal periods: ${failure.message}');
      },
      (periods) {
        fiscalPeriods.value = periods;
        log('Loaded ${periods.length} fiscal periods');
      },
    );
  }

  /// Set up real-time listener for fiscal years
  void _setupFiscalYearsListener() {
    _fiscalYearsSubscription?.cancel();
    _fiscalYearsSubscription = _repository.listenToFiscalYears().listen(
      (years) {
        fiscalYears.value = years;
        log('Real-time update: ${years.length} fiscal years');

        // Update active fiscal year
        final active = years.where((y) => y.isActive).firstOrNull;
        activeFiscalYear.value = active;

        if (selectedFiscalYear.value == null && active != null) {
          selectedFiscalYear.value = active;
          _setupFiscalPeriodsListener(active.id);
        }
      },
      onError: (error) {
        log('Error in fiscal years stream: $error');
      },
    );
  }

  /// Set up real-time listener for fiscal periods
  void _setupFiscalPeriodsListener(String fiscalYearId) {
    _fiscalPeriodsSubscription?.cancel();
    _fiscalPeriodsSubscription =
        _repository.listenToFiscalPeriods(fiscalYearId).listen(
      (periods) {
        fiscalPeriods.value = periods;
        log('Real-time update: ${periods.length} fiscal periods');

        // Update current period
        final current = periods.where((p) => p.isCurrent).firstOrNull;
        currentFiscalPeriod.value = current;
      },
      onError: (error) {
        log('Error in fiscal periods stream: $error');
      },
    );
  }

  /// Select fiscal year and load its periods
  void selectFiscalYear(FiscalYearModel year) {
    selectedFiscalYear.value = year;
    _setupFiscalPeriodsListener(year.id);
    log('Selected fiscal year: ${year.yearName}');
  }

  /// Create new fiscal year
  Future<void> createFiscalYear() async {
    if (!_validateFiscalYearForm()) return;

    try {
      isCreatingYear.value = true;
      log('Creating fiscal year: ${yearName.value}');

      final fiscalYear = FiscalYearModel(
        id: '', // Will be set by Firebase
        yearName: yearName.value,
        startDate: yearStartDate.value!,
        endDate: yearEndDate.value!,
        isActive: fiscalYears.isEmpty, // First year is automatically active
        status: FiscalPeriodStatus.open,
        createdAt: DateTime.now(),
        uid: uid,
      );

      final result = await _repository.createFiscalYear(fiscalYear);

      result.fold(
        (failure) {
          log('Failed to create fiscal year: ${failure.message}');
          SnackbarUtils.showError('Creation failed', failure.message);
        },
        (success) {
          log('Fiscal year created successfully');
          SnackbarUtils.showSuccess(
              'Success', 'Fiscal year created successfully');
          _clearFiscalYearForm();
          Get.back(); // Close dialog
        },
      );
    } catch (e) {
      log('Error creating fiscal year: $e');
      SnackbarUtils.showError('Creation failed', e.toString());
    } finally {
      isCreatingYear.value = false;
    }
  }

  /// Generate periods for fiscal year
  Future<void> generatePeriodsForYear(
      FiscalYearModel year, PeriodType periodType) async {
    try {
      isCreatingPeriod.value = true;
      log('Generating ${periodType.displayName} periods for ${year.yearName}');

      final periods = _generatePeriods(year, periodType);

      for (final period in periods) {
        final result = await _repository.createFiscalPeriod(period);
        result.fold(
          (failure) {
            log('Failed to create period ${period.periodName}: ${failure.message}');
            throw Exception('Failed to create period: ${failure.message}');
          },
          (success) {
            log('Created period: ${period.periodName}');
          },
        );
      }

      SnackbarUtils.showSuccess(
          'Success', '${periods.length} periods created successfully');
    } catch (e) {
      log('Error generating periods: $e');
      SnackbarUtils.showError('Generation failed', e.toString());
    } finally {
      isCreatingPeriod.value = false;
    }
  }

  /// Generate period list based on fiscal year and period type
  List<FiscalPeriodModel> _generatePeriods(
      FiscalYearModel year, PeriodType periodType) {
    final periods = <FiscalPeriodModel>[];
    final startDate = year.startDate;
    final endDate = year.endDate;

    switch (periodType) {
      case PeriodType.monthly:
        var current = DateTime(startDate.year, startDate.month, 1);

        while (current.isBefore(endDate)) {
          final monthEnd = DateTime(current.year, current.month + 1, 0);
          final actualEnd = monthEnd.isAfter(endDate) ? endDate : monthEnd;

          periods.add(FiscalPeriodModel(
            id: '', // Will be set by Firebase
            fiscalYearId: year.id,
            periodName: DateFormat('MMMM yyyy').format(current),
            periodType: PeriodType.monthly,
            startDate: current,
            endDate: actualEnd,
            status: FiscalPeriodStatus.open,
            createdAt: DateTime.now(),
            uid: uid,
          ));

          current = DateTime(current.year, current.month + 1, 1);
        }
        break;

      case PeriodType.quarterly:
        var current = startDate;
        var quarterNumber = 1;

        while (current.isBefore(endDate)) {
          final quarterEnd = DateTime(current.year, current.month + 3, 0);
          final actualEnd = quarterEnd.isAfter(endDate) ? endDate : quarterEnd;

          periods.add(FiscalPeriodModel(
            id: '', // Will be set by Firebase
            fiscalYearId: year.id,
            periodName: 'Q$quarterNumber ${current.year}',
            periodType: PeriodType.quarterly,
            startDate: current,
            endDate: actualEnd,
            status: FiscalPeriodStatus.open,
            createdAt: DateTime.now(),
            uid: uid,
          ));

          current = DateTime(current.year, current.month + 3, 1);
          quarterNumber++;
        }
        break;

      case PeriodType.yearly:
        periods.add(FiscalPeriodModel(
          id: '', // Will be set by Firebase
          fiscalYearId: year.id,
          periodName: year.yearName,
          periodType: PeriodType.yearly,
          startDate: startDate,
          endDate: endDate,
          status: FiscalPeriodStatus.open,
          createdAt: DateTime.now(),
          uid: uid,
        ));
        break;
    }

    return periods;
  }

  /// Close fiscal period
  Future<void> closeFiscalPeriod(FiscalPeriodModel period) async {
    try {
      isClosingPeriod.value = true;
      log('Closing fiscal period: ${period.periodName}');

      final result = await _repository.closeFiscalPeriod(period.id, uid);

      result.fold(
        (failure) {
          log('Failed to close period: ${failure.message}');
          SnackbarUtils.showError('Close failed', failure.message);
        },
        (success) {
          log('Period closed successfully');
          SnackbarUtils.showSuccess('Success', 'Period closed successfully');
        },
      );
    } catch (e) {
      log('Error closing period: $e');
      SnackbarUtils.showError('Close failed', e.toString());
    } finally {
      isClosingPeriod.value = false;
    }
  }

  /// Reopen fiscal period
  Future<void> reopenFiscalPeriod(FiscalPeriodModel period) async {
    try {
      isClosingPeriod.value = true;
      log('Reopening fiscal period: ${period.periodName}');

      final result = await _repository.reopenFiscalPeriod(period.id);

      result.fold(
        (failure) {
          log('Failed to reopen period: ${failure.message}');
          SnackbarUtils.showError('Reopen failed', failure.message);
        },
        (success) {
          log('Period reopened successfully');
          SnackbarUtils.showSuccess('Success', 'Period reopened successfully');
        },
      );
    } catch (e) {
      log('Error reopening period: $e');
      SnackbarUtils.showError('Reopen failed', e.toString());
    } finally {
      isClosingPeriod.value = false;
    }
  }

  /// Set active fiscal year
  Future<void> setActiveFiscalYear(FiscalYearModel year) async {
    try {
      log('Setting active fiscal year: ${year.yearName}');

      final result = await _repository.setActiveFiscalYear(year.id);

      result.fold(
        (failure) {
          log('Failed to set active year: ${failure.message}');
          SnackbarUtils.showError('Update failed', failure.message);
        },
        (success) {
          log('Active fiscal year updated successfully');
          SnackbarUtils.showSuccess('Success', 'Active fiscal year updated');
        },
      );
    } catch (e) {
      log('Error setting active year: $e');
      SnackbarUtils.showError('Update failed', e.toString());
    }
  }

  /// Validate fiscal year form
  bool _validateFiscalYearForm() {
    if (yearName.value.trim().isEmpty) {
      SnackbarUtils.showError('Validation Error', 'Year name is required');
      return false;
    }

    if (yearStartDate.value == null) {
      SnackbarUtils.showError('Validation Error', 'Start date is required');
      return false;
    }

    if (yearEndDate.value == null) {
      SnackbarUtils.showError('Validation Error', 'End date is required');
      return false;
    }

    if (yearEndDate.value!.isBefore(yearStartDate.value!)) {
      SnackbarUtils.showError(
          'Validation Error', 'End date must be after start date');
      return false;
    }

    return true;
  }

  /// Clear fiscal year form
  void _clearFiscalYearForm() {
    yearName.value = '';
    yearStartDate.value = null;
    yearEndDate.value = null;
    selectedPeriodType.value = PeriodType.monthly;
  }

  /// Computed properties
  String get activeFiscalYearText {
    final active = activeFiscalYear.value;
    if (active == null) return 'No active fiscal year';
    return '${active.yearName} (${DateFormat('MMM dd, yyyy').format(active.startDate)} - ${DateFormat('MMM dd, yyyy').format(active.endDate)})';
  }

  String get currentFiscalPeriodText {
    final current = currentFiscalPeriod.value;
    if (current == null) return 'No current period';
    return '${current.periodName} (${current.status.displayName})';
  }

  List<FiscalPeriodModel> get openPeriods {
    return fiscalPeriods
        .where((p) => p.status == FiscalPeriodStatus.open)
        .toList();
  }

  List<FiscalPeriodModel> get closedPeriods {
    return fiscalPeriods
        .where((p) => p.status == FiscalPeriodStatus.closed)
        .toList();
  }

  // ==================== YEAR-END CLOSING METHODS ====================

  /// Perform year-end closing for a fiscal year
  Future<void> performYearEndClosing(FiscalYearModel fiscalYear,
      {String? notes}) async {
    if (isPerformingYearEndClosing.value) return;

    try {
      isPerformingYearEndClosing.value = true;
      log('Starting year-end closing for fiscal year: ${fiscalYear.yearName}');

      final result = await _yearEndClosingRepository.performYearEndClosing(
        fiscalYear: fiscalYear,
        performedBy: uid,
        notes: notes,
      );

      result.fold(
        (failure) {
          log('Year-end closing failed: ${failure.message}');
          SnackbarUtils.showError('Year-End Closing Failed', failure.message);
        },
        (closing) {
          log('Year-end closing completed successfully');
          SnackbarUtils.showSuccess(
            'Year-End Closing Completed',
            'Fiscal year ${fiscalYear.yearName} has been closed successfully. '
                'Net income of \$${closing.netIncomeTransferred.toStringAsFixed(2)} '
                'has been transferred to retained earnings.',
          );

          // Refresh data
          loadYearEndClosings();
          _loadFiscalYears();
        },
      );
    } catch (e) {
      log('Error performing year-end closing: $e');
      SnackbarUtils.showError(
          'Error', 'Failed to perform year-end closing: $e');
    } finally {
      isPerformingYearEndClosing.value = false;
    }
  }

  /// Load year-end closings
  Future<void> loadYearEndClosings() async {
    try {
      log('Loading year-end closings');
      final result = await _yearEndClosingRepository.getYearEndClosings();

      result.fold(
        (failure) {
          log('Failed to load year-end closings: ${failure.message}');
          SnackbarUtils.showError('Error', 'Failed to load year-end closings');
        },
        (closings) {
          yearEndClosings.value = closings;
          log('Loaded ${closings.length} year-end closings');
        },
      );
    } catch (e) {
      log('Error loading year-end closings: $e');
    }
  }

  /// Load year-end closing summary
  Future<void> loadYearEndClosingSummary() async {
    try {
      log('Loading year-end closing summary');
      final result = await _yearEndClosingRepository.getYearEndClosingSummary();

      result.fold(
        (failure) {
          log('Failed to load year-end closing summary: ${failure.message}');
        },
        (summary) {
          yearEndClosingSummary.value = summary;
          log('Loaded year-end closing summary');
        },
      );
    } catch (e) {
      log('Error loading year-end closing summary: $e');
    }
  }

  /// Check if fiscal year can be closed
  Future<bool> canPerformYearEndClosing(FiscalYearModel fiscalYear) async {
    try {
      final result = await _yearEndClosingRepository
          .validateFiscalYearForClosing(fiscalYear);
      return result.isRight();
    } catch (e) {
      log('Error validating fiscal year for closing: $e');
      return false;
    }
  }

  /// Get year-end closing for a specific fiscal year
  YearEndClosingModel? getYearEndClosingForFiscalYear(String fiscalYearId) {
    return yearEndClosings
        .where((closing) => closing.fiscalYearId == fiscalYearId)
        .where((closing) => closing.isCompleted)
        .firstOrNull;
  }

  /// Check if fiscal year has been closed
  bool isFiscalYearClosed(String fiscalYearId) {
    return getYearEndClosingForFiscalYear(fiscalYearId) != null;
  }

  /// Get fiscal years eligible for year-end closing
  List<FiscalYearModel> get fiscalYearsEligibleForClosing {
    return fiscalYears.where((year) {
      // Must be ended and not already closed
      return year.endDate.isBefore(DateTime.now()) &&
          !isFiscalYearClosed(year.id) &&
          year.status != FiscalPeriodStatus.closed;
    }).toList();
  }
}
