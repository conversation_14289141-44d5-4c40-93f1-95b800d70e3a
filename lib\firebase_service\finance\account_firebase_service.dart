import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';
import 'package:logestics/models/finance/account_model.dart';

class AccountFirebaseService {
  late FirebaseFirestore _firestore;

  // Get current user's UID or return 'anonymous' if not authenticated
  String get _uid => FirebaseAuth.instance.currentUser?.uid ?? 'anonymous';

  AccountFirebaseService() {
    _firestore = FirebaseFirestore.instance;
  }

  Future<void> createAccount(AccountModel account) async {
    log('Creating account: ${account.name}');
    try {
      // Validate input data
      if (account.name.trim().isEmpty) {
        throw ArgumentError('Account name cannot be empty');
      }
      if (account.accountNumber.trim().isEmpty) {
        throw ArgumentError('Account number cannot be empty');
      }
      if (account.branchCode.trim().isEmpty) {
        throw ArgumentError('Branch code cannot be empty');
      }
      if (_uid == 'anonymous') {
        throw Exception('User not authenticated');
      }

      // Validate numeric fields
      if (account.initialBalance < 0) {
        throw ArgumentError('Initial balance cannot be negative');
      }
      if (account.availableBalance < 0) {
        throw ArgumentError('Available balance cannot be negative');
      }

      log('Creating account for user: $_uid with data: ${account.toJson()}');

      log('Firestore instance validated. Getting collection reference...');
      final collection =
          _firestore.collection(AppCollection.accountsCollection);

      log('Collection reference obtained. Creating document reference...');
      final accountRef = collection.doc();

      final accountId = accountRef.id;
      log('Generated account ID: $accountId');

      if (accountId.isEmpty) {
        throw Exception('Generated account ID is empty');
      }

      final accountData = account.toJson();
      log('Account data before validation: $accountData');

      // Ensure all required fields are not null
      if (accountData['id'] == null ||
          accountData['name'] == null ||
          accountData['accountNumber'] == null ||
          accountData['branchCode'] == null ||
          accountData['initialBalance'] == null ||
          accountData['availableBalance'] == null ||
          accountData['createdAt'] == null ||
          accountData['uid'] == null) {
        throw ArgumentError(
            'One or more required fields are null in account data: $accountData');
      }

      accountData['id'] = accountId;
      accountData['uid'] = _uid; // Add current user's UID

      // Ensure branchAddress is never null
      accountData['branchAddress'] = accountData['branchAddress'] ?? '';

      log('Final account data to save: $accountData');

      // Validate the final data structure
      for (final key in accountData.keys) {
        log('Field $key: ${accountData[key]} (type: ${accountData[key].runtimeType})');
        if (accountData[key] == null) {
          throw ArgumentError('Field $key is null in final account data');
        }
      }

      log('All validation passed. Attempting to save to Firestore...');
      await accountRef.set(accountData);
      log('Successfully created account: $accountId');
    } catch (e, stackTrace) {
      log('Error creating account: $e');
      log('Stack trace: $stackTrace');
      rethrow;
    }
  }

  Future<List<AccountModel>> getAccounts() async {
    log('Fetching accounts from Firestore');
    try {
      final snapshot = await _firestore
          .collection(AppCollection.accountsCollection)
          .where('uid', isEqualTo: _uid) // Filter by current user's UID
          .orderBy('createdAt', descending: true)
          .get();

      final accounts = snapshot.docs
          .map((doc) => AccountModel.fromJson(doc.data()))
          .toList();

      log('Successfully fetched ${accounts.length} accounts');
      return accounts;
    } catch (e) {
      log('Error fetching accounts: $e');
      rethrow;
    }
  }

  Future<void> deleteAccount(String accountId) async {
    log('Deleting account: $accountId');
    try {
      if (accountId.isEmpty) {
        throw ArgumentError('Account ID cannot be empty');
      }

      // First check if the account belongs to the current user
      final accountDoc = await _firestore
          .collection(AppCollection.accountsCollection)
          .doc(accountId)
          .get();

      if (!accountDoc.exists) {
        throw Exception('Account not found');
      }

      final accountData = accountDoc.data() as Map<String, dynamic>;
      if (accountData['uid'] != _uid) {
        throw Exception('You do not have permission to delete this account');
      }

      await _firestore
          .collection(AppCollection.accountsCollection)
          .doc(accountId)
          .delete();
      log('Successfully deleted account: $accountId');
    } catch (e) {
      log('Error deleting account: $e');
      rethrow;
    }
  }

  Future<void> updateAccount(AccountModel account) async {
    log('Updating account: ${account.id}');
    try {
      if (account.id.isEmpty) {
        throw ArgumentError('Account ID cannot be empty');
      }

      // First check if the account belongs to the current user
      final accountDoc = await _firestore
          .collection(AppCollection.accountsCollection)
          .doc(account.id)
          .get();

      if (!accountDoc.exists) {
        throw Exception('Account not found');
      }

      final accountData = accountDoc.data() as Map<String, dynamic>;
      if (accountData['uid'] != _uid) {
        throw Exception('You do not have permission to update this account');
      }

      // Ensure we preserve the original UID
      final updatedAccountData = account.toJson();
      updatedAccountData['uid'] = accountData['uid'];

      await _firestore
          .collection(AppCollection.accountsCollection)
          .doc(account.id)
          .update(updatedAccountData);
      log('Successfully updated account: ${account.id}');
    } catch (e) {
      log('Error updating account: $e');
      rethrow;
    }
  }

  /// Stream to listen for real-time updates to accounts
  Stream<List<AccountModel>> listenToAccounts() {
    try {
      return _firestore
          .collection(AppCollection.accountsCollection)
          .where('uid', isEqualTo: _uid) // Filter by current user's UID
          .orderBy('createdAt', descending: true)
          .snapshots()
          .map((snapshot) => snapshot.docs
              .map((doc) => AccountModel.fromJson(doc.data()))
              .toList());
    } catch (e) {
      log('Error listening to accounts: $e', error: e);
      return Stream.value([]);
    }
  }

  /// Get accounts for a specific company/user (for cross-company transactions)
  Future<List<AccountModel>> getAccountsForCompany(String companyUid) async {
    log('Fetching accounts for company: $companyUid');
    try {
      if (companyUid.isEmpty) {
        throw ArgumentError('Company UID cannot be empty');
      }

      final snapshot = await _firestore
          .collection(AppCollection.accountsCollection)
          .where('uid',
              isEqualTo: companyUid) // Filter by specified company's UID
          .orderBy('createdAt', descending: true)
          .get();

      final accounts = snapshot.docs
          .map((doc) => AccountModel.fromJson(doc.data()))
          .toList();

      log('Successfully fetched ${accounts.length} accounts for company: $companyUid');
      return accounts;
    } catch (e) {
      log('Error fetching accounts for company $companyUid: $e');
      rethrow;
    }
  }

  /// Update account balance for external company (cross-company operations)
  Future<void> updateExternalCompanyAccountBalance(
    String accountId,
    double amount,
    String externalCompanyUid,
    String transactionType, // 'debit' or 'credit'
  ) async {
    log('Updating external company account balance: $accountId, amount: $amount, type: $transactionType, company: $externalCompanyUid');
    try {
      if (externalCompanyUid.isEmpty) {
        throw ArgumentError('External company UID cannot be empty');
      }
      if (accountId.isEmpty) {
        throw ArgumentError('Account ID cannot be empty');
      }
      if (amount <= 0) {
        throw ArgumentError('Amount must be positive');
      }
      if (transactionType != 'debit' && transactionType != 'credit') {
        throw ArgumentError(
            'Transaction type must be either "debit" or "credit"');
      }

      final accountRef = _firestore
          .collection(AppCollection.accountsCollection)
          .doc(accountId);

      await _firestore.runTransaction((transaction) async {
        final accountDoc = await transaction.get(accountRef);

        if (!accountDoc.exists) {
          throw Exception('Account not found: $accountId');
        }

        final accountData = accountDoc.data() as Map<String, dynamic>;

        // Verify account belongs to external company
        if (accountData['uid'] != externalCompanyUid) {
          throw Exception(
              'Account does not belong to the specified external company');
        }

        final currentBalance =
            (accountData['availableBalance'] as num).toDouble();
        double newBalance;

        if (transactionType == 'debit') {
          newBalance =
              currentBalance - amount; // Subtract for debit (money going out)
        } else {
          newBalance =
              currentBalance + amount; // Add for credit (money coming in)
        }

        // Prevent negative balance for debit transactions
        if (transactionType == 'debit' && newBalance < 0) {
          throw Exception(
              'Insufficient funds. Current balance: $currentBalance, Attempted debit: $amount');
        }

        transaction.update(accountRef, {'availableBalance': newBalance});

        log('Updated external company account balance: $accountId, old: $currentBalance, new: $newBalance, operation: $transactionType');
      });
    } catch (e) {
      log('Error updating external company account balance: $e');
      rethrow;
    }
  }

  /// Get account details for external company (for validation purposes)
  Future<AccountModel?> getExternalCompanyAccount(
    String accountId,
    String externalCompanyUid,
  ) async {
    log('Getting external company account: $accountId for company: $externalCompanyUid');
    try {
      if (externalCompanyUid.isEmpty) {
        throw ArgumentError('External company UID cannot be empty');
      }
      if (accountId.isEmpty) {
        throw ArgumentError('Account ID cannot be empty');
      }

      final accountDoc = await _firestore
          .collection(AppCollection.accountsCollection)
          .doc(accountId)
          .get();

      if (!accountDoc.exists) {
        log('Account not found: $accountId');
        return null;
      }

      final accountData = accountDoc.data() as Map<String, dynamic>;

      // Verify account belongs to external company
      if (accountData['uid'] != externalCompanyUid) {
        log('Account does not belong to the specified external company');
        return null;
      }

      final account = AccountModel.fromJson(accountData);
      log('Successfully retrieved external company account: ${account.name}');
      return account;
    } catch (e) {
      log('Error getting external company account: $e');
      rethrow;
    }
  }
}
