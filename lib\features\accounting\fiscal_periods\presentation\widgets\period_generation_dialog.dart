import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../controllers/fiscal_period_controller.dart';
import '../../../../../models/finance/fiscal_period_model.dart';

/// Dialog for generating fiscal periods for a fiscal year
class PeriodGenerationDialog extends StatelessWidget {
  final FiscalPeriodController controller;
  final FiscalYearModel fiscalYear;

  const PeriodGenerationDialog({
    super.key,
    required this.controller,
    required this.fiscalYear,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: const BoxConstraints(maxWidth: 600),
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(Icons.auto_awesome, color: Colors.blue[700]),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Generate Fiscal Periods',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'For ${fiscalYear.yearName}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Fiscal year info
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Fiscal Year Details',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[800],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      const Icon(Icons.calendar_today,
                          size: 16, color: Colors.grey),
                      const SizedBox(width: 8),
                      Text(
                          'Period: ${DateFormat('MMM dd, yyyy').format(fiscalYear.startDate)} - ${DateFormat('MMM dd, yyyy').format(fiscalYear.endDate)}'),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      const Icon(Icons.access_time,
                          size: 16, color: Colors.grey),
                      const SizedBox(width: 8),
                      Text('Duration: ${fiscalYear.durationInDays} days'),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Period type selection
            const Text(
              'Select Period Type',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 12),

            ...PeriodType.values.map((type) => _buildPeriodTypeOption(type)),

            const SizedBox(height: 24),

            // Preview section
            Obx(() => _buildPreviewSection()),

            const SizedBox(height: 24),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Obx(() => ElevatedButton(
                        onPressed: controller.isCreatingPeriod.value
                            ? null
                            : () => _generatePeriods(context),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue[700],
                          foregroundColor: Colors.white,
                        ),
                        child: controller.isCreatingPeriod.value
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white),
                                ),
                              )
                            : const Text('Generate Periods'),
                      )),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build period type option
  Widget _buildPeriodTypeOption(PeriodType type) {
    return Obx(() => RadioListTile<PeriodType>(
          title: Text(type.displayName),
          subtitle: Text(_getPeriodTypeDescription(type)),
          value: type,
          groupValue: controller.selectedPeriodType.value,
          onChanged: (value) {
            if (value != null) {
              controller.selectedPeriodType.value = value;
            }
          },
          activeColor: Colors.blue[700],
        ));
  }

  /// Build preview section
  Widget _buildPreviewSection() {
    final selectedType = controller.selectedPeriodType.value;
    final previewPeriods = _generatePreviewPeriods(selectedType);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.preview, color: Colors.blue[700], size: 20),
              const SizedBox(width: 8),
              Text(
                'Preview (${previewPeriods.length} periods)',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          if (previewPeriods.length <= 12)
            ...previewPeriods.map((period) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    children: [
                      Container(
                        width: 4,
                        height: 4,
                        decoration: BoxDecoration(
                          color: Colors.blue[700],
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          '${period.periodName} (${DateFormat('MMM dd').format(period.startDate)} - ${DateFormat('MMM dd, yyyy').format(period.endDate)})',
                          style: const TextStyle(fontSize: 13),
                        ),
                      ),
                    ],
                  ),
                ))
          else
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ...previewPeriods.take(3).map((period) => Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: Row(
                        children: [
                          Container(
                            width: 4,
                            height: 4,
                            decoration: BoxDecoration(
                              color: Colors.blue[700],
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              '${period.periodName} (${DateFormat('MMM dd').format(period.startDate)} - ${DateFormat('MMM dd, yyyy').format(period.endDate)})',
                              style: const TextStyle(fontSize: 13),
                            ),
                          ),
                        ],
                      ),
                    )),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Text(
                    '... and ${previewPeriods.length - 6} more periods ...',
                    style: TextStyle(
                      fontSize: 13,
                      fontStyle: FontStyle.italic,
                      color: Colors.grey[600],
                    ),
                  ),
                ),
                ...previewPeriods
                    .skip(previewPeriods.length - 3)
                    .map((period) => Padding(
                          padding: const EdgeInsets.only(bottom: 4),
                          child: Row(
                            children: [
                              Container(
                                width: 4,
                                height: 4,
                                decoration: BoxDecoration(
                                  color: Colors.blue[700],
                                  shape: BoxShape.circle,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  '${period.periodName} (${DateFormat('MMM dd').format(period.startDate)} - ${DateFormat('MMM dd, yyyy').format(period.endDate)})',
                                  style: const TextStyle(fontSize: 13),
                                ),
                              ),
                            ],
                          ),
                        )),
              ],
            ),
        ],
      ),
    );
  }

  /// Get period type description
  String _getPeriodTypeDescription(PeriodType type) {
    switch (type) {
      case PeriodType.monthly:
        return 'Creates 12 monthly periods (recommended for most businesses)';
      case PeriodType.quarterly:
        return 'Creates 4 quarterly periods (3-month periods)';
      case PeriodType.yearly:
        return 'Creates 1 yearly period (entire fiscal year)';
    }
  }

  /// Generate preview periods
  List<FiscalPeriodModel> _generatePreviewPeriods(PeriodType periodType) {
    final periods = <FiscalPeriodModel>[];
    final startDate = fiscalYear.startDate;
    final endDate = fiscalYear.endDate;

    switch (periodType) {
      case PeriodType.monthly:
        var current = DateTime(startDate.year, startDate.month, 1);

        while (current.isBefore(endDate)) {
          final monthEnd = DateTime(current.year, current.month + 1, 0);
          final actualEnd = monthEnd.isAfter(endDate) ? endDate : monthEnd;

          periods.add(FiscalPeriodModel(
            id: '',
            fiscalYearId: fiscalYear.id,
            periodName: DateFormat('MMMM yyyy').format(current),
            periodType: PeriodType.monthly,
            startDate: current,
            endDate: actualEnd,
            status: FiscalPeriodStatus.open,
            createdAt: DateTime.now(),
            uid: controller.uid,
          ));

          current = DateTime(current.year, current.month + 1, 1);
        }
        break;

      case PeriodType.quarterly:
        var current = startDate;
        var quarterNumber = 1;

        while (current.isBefore(endDate)) {
          final quarterEnd = DateTime(current.year, current.month + 3, 0);
          final actualEnd = quarterEnd.isAfter(endDate) ? endDate : quarterEnd;

          periods.add(FiscalPeriodModel(
            id: '',
            fiscalYearId: fiscalYear.id,
            periodName: 'Q$quarterNumber ${current.year}',
            periodType: PeriodType.quarterly,
            startDate: current,
            endDate: actualEnd,
            status: FiscalPeriodStatus.open,
            createdAt: DateTime.now(),
            uid: controller.uid,
          ));

          current = DateTime(current.year, current.month + 3, 1);
          quarterNumber++;
        }
        break;

      case PeriodType.yearly:
        periods.add(FiscalPeriodModel(
          id: '',
          fiscalYearId: fiscalYear.id,
          periodName: fiscalYear.yearName,
          periodType: PeriodType.yearly,
          startDate: startDate,
          endDate: endDate,
          status: FiscalPeriodStatus.open,
          createdAt: DateTime.now(),
          uid: controller.uid,
        ));
        break;
    }

    return periods;
  }

  /// Generate periods
  Future<void> _generatePeriods(BuildContext context) async {
    await controller.generatePeriodsForYear(
        fiscalYear, controller.selectedPeriodType.value);
    if (context.mounted) {
      Navigator.of(context).pop();
    }
  }
}
