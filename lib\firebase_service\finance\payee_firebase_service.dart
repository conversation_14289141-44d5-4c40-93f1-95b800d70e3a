import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';
import 'package:logestics/models/finance/payee_model.dart';

class PayeeFirebaseService {
  late FirebaseFirestore _firestore;

  // Get current user's UID or return 'anonymous' if not authenticated
  String get _uid => FirebaseAuth.instance.currentUser?.uid ?? 'anonymous';

  PayeeFirebaseService() {
    _firestore = FirebaseFirestore.instance;
  }

  Future<void> createPayee(PayeeModel payee) async {
    log('Creating payee: ${payee.name}');
    try {
      final payeeRef =
          _firestore.collection(AppCollection.payeesCollection).doc();
      final payeeId = payeeRef.id;

      final payeeData = payee.toJson();
      payeeData['id'] = payeeId;
      payeeData['uid'] = _uid; // Add current user's UID

      await payeeRef.set(payeeData);
      log('Successfully created payee: $payeeId');
    } catch (e) {
      log('Error creating payee: $e');
      rethrow;
    }
  }

  Future<List<PayeeModel>> getPayees() async {
    log('Fetching payees from Firestore');
    try {
      final snapshot = await _firestore
          .collection(AppCollection.payeesCollection)
          .where('uid', isEqualTo: _uid) // Filter by current user's UID
          .orderBy('createdAt', descending: true)
          .get();

      final payees =
          snapshot.docs.map((doc) => PayeeModel.fromJson(doc.data())).toList();

      log('Successfully fetched ${payees.length} payees');
      return payees;
    } catch (e) {
      log('Error fetching payees: $e');
      rethrow;
    }
  }

  Future<void> deletePayee(String payeeId) async {
    log('Deleting payee: $payeeId');
    try {
      if (payeeId.isEmpty) {
        throw ArgumentError('Payee ID cannot be empty');
      }

      // First check if the payee belongs to the current user
      final payeeDoc = await _firestore
          .collection(AppCollection.payeesCollection)
          .doc(payeeId)
          .get();

      if (!payeeDoc.exists) {
        throw Exception('Payee not found');
      }

      final payeeData = payeeDoc.data() as Map<String, dynamic>;
      if (payeeData['uid'] != _uid) {
        throw Exception('You do not have permission to delete this payee');
      }

      await _firestore
          .collection(AppCollection.payeesCollection)
          .doc(payeeId)
          .delete();
      log('Successfully deleted payee: $payeeId');
    } catch (e) {
      log('Error deleting payee: $e');
      rethrow;
    }
  }

  Future<void> updatePayee(PayeeModel payee) async {
    log('Updating payee: ${payee.id}');
    try {
      if (payee.id.isEmpty) {
        throw ArgumentError('Payee ID cannot be empty');
      }

      // First check if the payee belongs to the current user
      final payeeDoc = await _firestore
          .collection(AppCollection.payeesCollection)
          .doc(payee.id)
          .get();

      if (!payeeDoc.exists) {
        throw Exception('Payee not found');
      }

      final payeeData = payeeDoc.data() as Map<String, dynamic>;
      if (payeeData['uid'] != _uid) {
        throw Exception('You do not have permission to update this payee');
      }

      // Preserve the original UID
      final updatedPayeeData = payee.toJson();
      updatedPayeeData['uid'] = payeeData['uid'];

      await _firestore
          .collection(AppCollection.payeesCollection)
          .doc(payee.id)
          .update(updatedPayeeData);
      log('Successfully updated payee: ${payee.id}');
    } catch (e) {
      log('Error updating payee: $e');
      rethrow;
    }
  }

  /// Stream to listen for real-time updates to payees
  Stream<List<PayeeModel>> listenToPayees() {
    try {
      return _firestore
          .collection(AppCollection.payeesCollection)
          .where('uid', isEqualTo: _uid) // Filter by current user's UID
          .orderBy('createdAt', descending: true)
          .snapshots()
          .map((snapshot) => snapshot.docs
              .map((doc) => PayeeModel.fromJson(doc.data()))
              .toList());
    } catch (e) {
      log('Error listening to payees: $e', error: e);
      return Stream.value([]);
    }
  }
}
