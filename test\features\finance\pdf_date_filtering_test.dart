import 'package:flutter_test/flutter_test.dart';
import 'package:logestics/models/finance/account_transaction_model.dart';

void main() {
  group('PDF Date Filtering Tests', () {
    // Helper function to create test transactions
    List<AccountTransactionModel> createTestTransactions() {
      return [
        AccountTransactionModel(
          id: '1',
          accountId: 'acc1',
          accountName: 'Test Account',
          amount: 1000.0,
          transactionDate: DateTime(2024, 1, 15, 10, 30), // Jan 15, 2024
          description: 'Transaction 1',
          type: TransactionType.deposit,
          referenceId: 'REF001',
          payerId: 'payer1',
          payerName: 'Payer 1',
          uid: 'user1',
        ),
        AccountTransactionModel(
          id: '2',
          accountId: 'acc1',
          accountName: 'Test Account',
          amount: 500.0,
          transactionDate: DateTime(2024, 1, 20, 14, 45), // Jan 20, 2024
          description: 'Transaction 2',
          type: TransactionType.expense,
          referenceId: 'REF002',
          payerId: 'payer2',
          payerName: 'Payer 2',
          uid: 'user1',
        ),
        AccountTransactionModel(
          id: '3',
          accountId: 'acc1',
          accountName: 'Test Account',
          amount: 750.0,
          transactionDate: DateTime(2024, 1, 25, 9, 15), // Jan 25, 2024
          description: 'Transaction 3',
          type: TransactionType.deposit,
          referenceId: 'REF003',
          payerId: 'payer3',
          payerName: 'Payer 3',
          uid: 'user1',
        ),
        AccountTransactionModel(
          id: '4',
          accountId: 'acc1',
          accountName: 'Test Account',
          amount: 300.0,
          transactionDate: DateTime(2024, 2, 5, 16, 20), // Feb 5, 2024
          description: 'Transaction 4',
          type: TransactionType.expense,
          referenceId: 'REF004',
          payerId: 'payer4',
          payerName: 'Payer 4',
          uid: 'user1',
        ),
      ];
    }

    // Helper function to simulate the fixed date filtering logic
    List<AccountTransactionModel> applyDateFilter(
      List<AccountTransactionModel> transactions,
      DateTime startDate,
      DateTime endDate,
    ) {
      // Create start and end of day for accurate filtering
      final startOfDay =
          DateTime(startDate.year, startDate.month, startDate.day, 0, 0, 0);
      final endOfDay =
          DateTime(endDate.year, endDate.month, endDate.day, 23, 59, 59, 999);

      return transactions.where((transaction) {
        final transactionDate = transaction.transactionDate;
        // Include transactions that fall within the selected date range (inclusive)
        return (transactionDate.isAfter(startOfDay) ||
                transactionDate.isAtSameMomentAs(startOfDay)) &&
            (transactionDate.isBefore(endOfDay) ||
                transactionDate.isAtSameMomentAs(endOfDay));
      }).toList();
    }

    test('should filter transactions within date range correctly', () {
      final transactions = createTestTransactions();

      // Test filtering for January 15-25, 2024
      final startDate = DateTime(2024, 1, 15);
      final endDate = DateTime(2024, 1, 25);

      final filtered = applyDateFilter(transactions, startDate, endDate);

      // Should include transactions 1, 2, and 3 (Jan 15, 20, 25)
      expect(filtered.length, 3);
      expect(filtered.map((t) => t.id).toList(), ['1', '2', '3']);
    });

    test('should include transactions on start date', () {
      final transactions = createTestTransactions();

      // Test filtering for exactly January 15, 2024
      final startDate = DateTime(2024, 1, 15);
      final endDate = DateTime(2024, 1, 15);

      final filtered = applyDateFilter(transactions, startDate, endDate);

      // Should include only transaction 1 (Jan 15)
      expect(filtered.length, 1);
      expect(filtered.first.id, '1');
    });

    test('should include transactions on end date', () {
      final transactions = createTestTransactions();

      // Test filtering for exactly January 25, 2024
      final startDate = DateTime(2024, 1, 25);
      final endDate = DateTime(2024, 1, 25);

      final filtered = applyDateFilter(transactions, startDate, endDate);

      // Should include only transaction 3 (Jan 25)
      expect(filtered.length, 1);
      expect(filtered.first.id, '3');
    });

    test('should exclude transactions outside date range', () {
      final transactions = createTestTransactions();

      // Test filtering for January 16-24, 2024 (excludes Jan 15 and 25)
      final startDate = DateTime(2024, 1, 16);
      final endDate = DateTime(2024, 1, 24);

      final filtered = applyDateFilter(transactions, startDate, endDate);

      // Should include only transaction 2 (Jan 20)
      expect(filtered.length, 1);
      expect(filtered.first.id, '2');
    });

    test('should handle empty result when no transactions in range', () {
      final transactions = createTestTransactions();

      // Test filtering for March 2024 (no transactions)
      final startDate = DateTime(2024, 3, 1);
      final endDate = DateTime(2024, 3, 31);

      final filtered = applyDateFilter(transactions, startDate, endDate);

      // Should be empty
      expect(filtered.length, 0);
    });

    test('should handle same start and end date', () {
      final transactions = createTestTransactions();

      // Test filtering for exactly January 20, 2024
      final startDate = DateTime(2024, 1, 20);
      final endDate = DateTime(2024, 1, 20);

      final filtered = applyDateFilter(transactions, startDate, endDate);

      // Should include only transaction 2 (Jan 20)
      expect(filtered.length, 1);
      expect(filtered.first.id, '2');
      expect(filtered.first.amount, 500.0);
    });

    test('should handle transactions with different times on same date', () {
      // Create transactions with different times on the same date
      final sameDate = DateTime(2024, 1, 15);
      final transactions = <AccountTransactionModel>[
        AccountTransactionModel(
          id: '1',
          accountId: 'acc1',
          accountName: 'Test Account',
          amount: 100.0,
          transactionDate: DateTime(2024, 1, 15, 0, 0, 0), // Start of day
          description: 'Early transaction',
          type: TransactionType.deposit,
          uid: 'user1',
        ),
        AccountTransactionModel(
          id: '2',
          accountId: 'acc1',
          accountName: 'Test Account',
          amount: 200.0,
          transactionDate: DateTime(2024, 1, 15, 12, 30, 45), // Midday
          description: 'Midday transaction',
          type: TransactionType.expense,
          uid: 'user1',
        ),
        AccountTransactionModel(
          id: '3',
          accountId: 'acc1',
          accountName: 'Test Account',
          amount: 300.0,
          transactionDate: DateTime(2024, 1, 15, 23, 59, 59), // End of day
          description: 'Late transaction',
          type: TransactionType.deposit,
          uid: 'user1',
        ),
      ];

      final filtered = applyDateFilter(transactions, sameDate, sameDate);

      // Should include all three transactions from the same date
      expect(filtered.length, 3);
      expect(filtered.map((t) => t.id).toList(), ['1', '2', '3']);
    });

    test('should handle wide date range correctly', () {
      final transactions = createTestTransactions();

      // Test filtering for entire January-February 2024
      final startDate = DateTime(2024, 1, 1);
      final endDate = DateTime(2024, 2, 29);

      final filtered = applyDateFilter(transactions, startDate, endDate);

      // Should include all transactions
      expect(filtered.length, 4);
      expect(filtered.map((t) => t.id).toList(), ['1', '2', '3', '4']);
    });

    test('should handle edge case with millisecond precision', () {
      final transactions = <AccountTransactionModel>[
        AccountTransactionModel(
          id: '1',
          accountId: 'acc1',
          accountName: 'Test Account',
          amount: 100.0,
          transactionDate:
              DateTime(2024, 1, 15, 23, 59, 59, 999), // Last millisecond of day
          description: 'Edge case transaction',
          type: TransactionType.deposit,
          uid: 'user1',
        ),
      ];

      final startDate = DateTime(2024, 1, 15);
      final endDate = DateTime(2024, 1, 15);

      final filtered = applyDateFilter(transactions, startDate, endDate);

      // Should include the transaction even at the last millisecond
      expect(filtered.length, 1);
      expect(filtered.first.id, '1');
    });
  });
}
