import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/firebase_service/finance/broker_firebase_service.dart';
import 'package:logestics/models/finance/broker_model.dart';

class BrokerRepository {
  final BrokerFirebaseService _firebaseService;

  BrokerRepository(this._firebaseService);

  Future<Either<FailureObj, void>> createBroker(BrokerModel broker) async {
    try {
      await _firebaseService.createBroker(broker);
      return const Right(null);
    } catch (e) {
      return Left(
          FailureObj(code: 'broker-creation-error', message: e.toString()));
    }
  }

  Future<Either<FailureObj, List<BrokerModel>>> getBrokers() async {
    try {
      final brokers = await _firebaseService.getBrokers();
      return Right(brokers);
    } catch (e) {
      return Left(
          FailureObj(code: 'broker-fetch-error', message: e.toString()));
    }
  }

  Future<Either<FailureObj, void>> updateBroker(BrokerModel broker) async {
    try {
      await _firebaseService.updateBroker(broker);
      return const Right(null);
    } catch (e) {
      return Left(
          FailureObj(code: 'broker-update-error', message: e.toString()));
    }
  }

  Future<Either<FailureObj, void>> deleteBroker(String brokerId) async {
    try {
      await _firebaseService.deleteBroker(brokerId);
      return const Right(null);
    } catch (e) {
      return Left(
          FailureObj(code: 'broker-delete-error', message: e.toString()));
    }
  }

  Stream<List<BrokerModel>> listenToBrokers() {
    return _firebaseService.listenToBrokers();
  }
}
