import 'dart:developer';
import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/firebase_service/invoices/invoice_crud_firebase_service.dart';
import 'package:logestics/firebase_service/voucher/voucher_crud_firebase_service.dart';
import 'package:logestics/repositories/asset/asset_repository.dart';
import 'package:logestics/models/invoice_model.dart';
import 'package:logestics/models/asset/asset_model.dart';

class DashboardDataController extends GetxController {
  final InvoiceCrudFirebaseService _invoiceService =
      InvoiceCrudFirebaseService();
  final VoucherCrudFirebaseService _voucherService =
      VoucherCrudFirebaseService();
  late final AssetRepository _assetRepository;

  String get uid => FirebaseAuth.instance.currentUser?.uid ?? '';

  // Loading states
  var isLoadingInvoices = false.obs;
  var isLoadingVouchers = false.obs;
  var isLoadingProjects = false.obs;
  var isLoadingAssets = false.obs;

  // Error states
  var invoiceError = ''.obs;
  var voucherError = ''.obs;
  var projectError = ''.obs;
  var assetError = ''.obs;

  // Data
  var invoices = <InvoiceModel>[].obs;
  var vouchers = <Map<String, dynamic>>[].obs;
  var projects = <Map<String, dynamic>>[].obs; // Placeholder for project data
  var assets = <AssetModel>[].obs;

  // Date filtering
  var selectedDateFilter = 'all'.obs;
  var customStartDate = Rx<DateTime?>(null);
  var customEndDate = Rx<DateTime?>(null);

  // Invoice status counts
  var totalInvoices = 0.obs;
  var paidInvoices = 0.obs;
  var unpaidInvoices = 0.obs;
  var overdueInvoices = 0.obs;

  // Voucher financial data
  var totalProfit = 0.0.obs;
  var totalTaxCollected = 0.0.obs;
  var tax46Percent = 0.0.obs;
  var tax15Percent = 0.0.obs;

  // Asset statistics
  var totalAssets = 0.obs;
  var totalAssetValue = 0.0.obs;
  var assetsInUse = 0.obs;
  var assetsUnderMaintenance = 0.obs;
  var assetsRetired = 0.obs;
  var assetsSold = 0.obs;

  @override
  void onInit() {
    super.onInit();
    log('DashboardDataController initialized');
    log('Current UID: $uid');

    // Initialize asset repository
    _assetRepository = Get.find<AssetRepository>();

    loadDashboardData();
  }

  Future<void> loadDashboardData() async {
    await Future.wait([
      loadInvoiceData(),
      loadVoucherData(),
      loadProjectData(),
      loadAssetData(),
    ]);
  }

  Future<void> loadInvoiceData() async {
    log('DashboardDataController: Starting to load invoice data');
    log('DashboardDataController: Current UID: $uid');

    if (uid.isEmpty) {
      log('DashboardDataController: UID is empty, cannot load invoices');
      invoiceError.value = 'User not authenticated';
      return;
    }

    try {
      isLoadingInvoices.value = true;
      invoiceError.value = '';
      log('DashboardDataController: Set loading state to true');

      final invoiceList = await _invoiceService.getInvoicesForCompany(uid);
      invoices.value = invoiceList;
      log('DashboardDataController: Received ${invoiceList.length} invoices from service');

      _calculateInvoiceStats();
      log('DashboardDataController: Calculated invoice stats - Total: ${totalInvoices.value}, Paid: ${paidInvoices.value}, Unpaid: ${unpaidInvoices.value}, Overdue: ${overdueInvoices.value}');

      log('Loaded ${invoiceList.length} invoices for dashboard');
    } catch (e) {
      invoiceError.value = 'Failed to load invoice data: $e';
      log('Error loading invoice data: $e');
    } finally {
      isLoadingInvoices.value = false;
      log('DashboardDataController: Set loading state to false');
      update(); // Trigger GetBuilder updates
    }
  }

  Future<void> loadVoucherData() async {
    if (uid.isEmpty) return;

    try {
      isLoadingVouchers.value = true;
      voucherError.value = '';

      final voucherList = await _voucherService.getVouchersForCompany(uid: uid);
      vouchers.value = voucherList;

      _calculateVoucherFinancials();

      log('Loaded ${voucherList.length} vouchers for dashboard');
    } catch (e) {
      voucherError.value = 'Failed to load voucher data: $e';
      log('Error loading voucher data: $e');
    } finally {
      isLoadingVouchers.value = false;
    }
  }

  Future<void> loadProjectData() async {
    // Placeholder for project data loading
    // Since there's no project model found, we'll create a placeholder
    try {
      isLoadingProjects.value = true;
      projectError.value = '';

      // TODO: Implement actual project data loading when project model is available
      projects.value = [
        {
          'id': '1',
          'name': 'Project Alpha',
          'totalCost': 50000.0,
          'progress': 75.0,
          'income': 37500.0,
          'remainingBudget': 12500.0,
        },
        {
          'id': '2',
          'name': 'Project Beta',
          'totalCost': 30000.0,
          'progress': 50.0,
          'income': 15000.0,
          'remainingBudget': 15000.0,
        },
      ];

      log('Loaded ${projects.length} projects for dashboard');
    } catch (e) {
      projectError.value = 'Failed to load project data: $e';
      log('Error loading project data: $e');
    } finally {
      isLoadingProjects.value = false;
    }
  }

  Future<void> loadAssetData() async {
    log('DashboardDataController: Starting to load asset data');
    log('DashboardDataController: Current UID: $uid');

    if (uid.isEmpty) {
      log('DashboardDataController: UID is empty, cannot load assets');
      assetError.value = 'User not authenticated';
      return;
    }

    try {
      isLoadingAssets.value = true;
      assetError.value = '';
      log('DashboardDataController: Set asset loading state to true');

      final result = await _assetRepository.getAssets();
      result.fold(
        (failure) {
          log('DashboardDataController: Error loading assets: ${failure.message}');
          assetError.value = failure.message;
        },
        (assetsList) {
          log('DashboardDataController: Received ${assetsList.length} assets from repository');
          assets.value = assetsList;
          _calculateAssetStats();
          log('DashboardDataController: Calculated asset stats - Total: ${totalAssets.value}, Value: ${totalAssetValue.value}, In Use: ${assetsInUse.value}');
        },
      );
    } catch (e) {
      assetError.value = 'Failed to load asset data: $e';
      log('DashboardDataController: Error loading asset data: $e');
    } finally {
      isLoadingAssets.value = false;
    }
  }

  void _calculateInvoiceStats() {
    final filteredInvoices = _getFilteredInvoices();

    totalInvoices.value = filteredInvoices.length;

    // Calculate status counts based on invoice status
    paidInvoices.value = filteredInvoices
        .where((invoice) =>
            invoice.invoiceStatus.toLowerCase() == 'completed' ||
            invoice.invoiceStatus.toLowerCase() == 'paid')
        .length;

    unpaidInvoices.value = filteredInvoices
        .where((invoice) =>
            invoice.invoiceStatus.toLowerCase() == 'in progress' ||
            invoice.invoiceStatus.toLowerCase() == 'unpaid' ||
            invoice.invoiceStatus.toLowerCase() == 'pending')
        .length;

    // For overdue, we'll check if the invoice is older than 30 days and not completed
    final now = DateTime.now();
    overdueInvoices.value = filteredInvoices.where((invoice) {
      if (invoice.invoiceStatus.toLowerCase() == 'completed' ||
          invoice.invoiceStatus.toLowerCase() == 'paid') {
        return false;
      }

      final invoiceDate = invoice.orderDate ?? invoice.createdAt;
      final daysDifference = now.difference(invoiceDate).inDays;
      return daysDifference > 30;
    }).length;
  }

  void _calculateAssetStats() {
    final assetsList = assets.toList();

    totalAssets.value = assetsList.length;
    totalAssetValue.value =
        assetsList.fold(0.0, (sum, asset) => sum + asset.currentValue);

    // Calculate status counts based on asset status
    assetsInUse.value = assetsList
        .where((asset) => asset.status.toLowerCase() == 'in use')
        .length;

    assetsUnderMaintenance.value = assetsList
        .where((asset) => asset.status.toLowerCase() == 'under maintenance')
        .length;

    assetsRetired.value = assetsList
        .where((asset) => asset.status.toLowerCase() == 'retired')
        .length;

    assetsSold.value = assetsList
        .where((asset) => asset.status.toLowerCase() == 'sold')
        .length;
  }

  void _calculateVoucherFinancials() {
    final filteredVouchers = _getFilteredVouchers();

    double profit = 0.0;
    double tax46 = 0.0;
    double tax15 = 0.0;

    for (final voucher in filteredVouchers) {
      // Calculate profit (company freight - broker fees - munshiana fees)
      final companyFreight =
          (voucher['companyFreight'] as num?)?.toDouble() ?? 0.0;
      final brokerFees = (voucher['brokerFees'] as num?)?.toDouble() ?? 0.0;
      final munshianaFees =
          (voucher['munshianaFees'] as num?)?.toDouble() ?? 0.0;

      final voucherProfit = companyFreight - brokerFees - munshianaFees;
      profit += voucherProfit;

      // Calculate 4.6% tax
      tax46 += companyFreight * 0.046;

      // Calculate 15% tax
      tax15 += companyFreight * 0.15;
    }

    totalProfit.value = profit;
    tax46Percent.value = tax46;
    tax15Percent.value = tax15;
    totalTaxCollected.value = tax46 + tax15;
  }

  List<InvoiceModel> _getFilteredInvoices() {
    return invoices.where((invoice) {
      return _isInDateRange(invoice.orderDate ?? invoice.createdAt);
    }).toList();
  }

  List<Map<String, dynamic>> _getFilteredVouchers() {
    return vouchers.where((voucher) {
      // Parse date from voucher map
      DateTime? voucherDate;

      // Try belongsToDate first
      if (voucher['belongsToDate'] != null) {
        if (voucher['belongsToDate'] is String) {
          voucherDate = DateTime.tryParse(voucher['belongsToDate']);
        } else if (voucher['belongsToDate'] is int) {
          voucherDate =
              DateTime.fromMillisecondsSinceEpoch(voucher['belongsToDate']);
        }
      }

      // Fallback to createdAt
      if (voucherDate == null && voucher['createdAt'] != null) {
        if (voucher['createdAt'] is String) {
          voucherDate = DateTime.tryParse(voucher['createdAt']);
        } else if (voucher['createdAt'] is int) {
          voucherDate =
              DateTime.fromMillisecondsSinceEpoch(voucher['createdAt']);
        }
      }

      // Default to now if no date found
      voucherDate ??= DateTime.now();

      return _isInDateRange(voucherDate);
    }).toList();
  }

  bool _isInDateRange(DateTime date) {
    final now = DateTime.now();

    switch (selectedDateFilter.value) {
      case 'today':
        return _isSameDay(date, now);
      case 'week':
        final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
        return date.isAfter(startOfWeek.subtract(const Duration(days: 1))) &&
            date.isBefore(now.add(const Duration(days: 1)));
      case 'month':
        final startOfMonth = DateTime(now.year, now.month, 1);
        return date.isAfter(startOfMonth.subtract(const Duration(days: 1))) &&
            date.isBefore(now.add(const Duration(days: 1)));
      case 'last30days':
        final thirtyDaysAgo = now.subtract(const Duration(days: 30));
        return date.isAfter(thirtyDaysAgo.subtract(const Duration(days: 1))) &&
            date.isBefore(now.add(const Duration(days: 1)));
      case 'custom':
        if (customStartDate.value != null && customEndDate.value != null) {
          return date.isAfter(
                  customStartDate.value!.subtract(const Duration(days: 1))) &&
              date.isBefore(customEndDate.value!.add(const Duration(days: 1)));
        }
        return true;
      default:
        return true;
    }
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  void setDateFilter(String filter) {
    selectedDateFilter.value = filter;
    _calculateInvoiceStats();
    _calculateVoucherFinancials();
  }

  void setCustomDateRange(DateTime startDate, DateTime endDate) {
    customStartDate.value = startDate;
    customEndDate.value = endDate;
    selectedDateFilter.value = 'custom';
    _calculateInvoiceStats();
    _calculateVoucherFinancials();
  }

  Future<void> refreshData() async {
    await loadDashboardData();
  }
}
