import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';
import 'package:logestics/models/locations/district_model.dart';

class DistrictFirebaseService {
  late FirebaseFirestore _firestore;

  DistrictFirebaseService() {
    _firestore = FirebaseFirestore.instance;
  }

  Future<void> createDistrict(DistrictModel district) async {
    log('Creating district: ${district.districtName}');
    try {
      final districtRef =
          _firestore.collection(AppCollection.districtsCollection).doc();
      district.districtId = districtRef.id;
      await districtRef.set(district.toJson());
      log('Successfully created district: ${district.districtId}');
    } catch (e) {
      log('Error creating district: $e');
      rethrow;
    }
  }

  Future<List<DistrictModel>> getDistricts() async {
    log('Fetching districts from Firestore');
    try {
      final snapshot = await _firestore
          .collection(AppCollection.districtsCollection)
          .orderBy('createdAt', descending: true)
          .get();

      final districts = snapshot.docs
          .map((doc) => DistrictModel.fromJson(doc.data()))
          .toList();

      log('Successfully fetched ${districts.length} districts');
      return districts;
    } catch (e) {
      log('Error fetching districts: $e');
      rethrow;
    }
  }

  Future<bool> checkDistrictExists(String districtName) async {
    try {
      final snapshot = await _firestore
          .collection(AppCollection.districtsCollection)
          .where('districtName', isEqualTo: districtName)
          .limit(1)
          .get();

      return snapshot.docs.isNotEmpty;
    } catch (e) {
      log('Error checking district existence: $e');
      rethrow;
    }
  }

  Future<void> deleteDistrict(String districtId) async {
    log('Deleting district: $districtId');
    try {
      await _firestore
          .collection(AppCollection.districtsCollection)
          .doc(districtId)
          .delete();
      log('Successfully deleted district: $districtId');
    } catch (e) {
      log('Error deleting district: $e');
      rethrow;
    }
  }

  Stream<List<DistrictModel>> listenToDistricts() {
    return _firestore
        .collection(AppCollection.districtsCollection)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => DistrictModel.fromJson(doc.data()))
            .toList());
  }
}
