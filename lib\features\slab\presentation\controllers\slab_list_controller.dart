import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/features/slab/domain/usecases/get_slabs_use_case.dart';
import 'package:logestics/features/slab/domain/usecases/delete_slab_use_case.dart';
import 'package:logestics/models/slab/slab_model.dart';

class SlabListController extends GetxController {
  final GetSlabsUseCase getSlabsUseCase;
  final DeleteSlabUseCase deleteSlabUseCase;

  SlabListController({
    required this.getSlabsUseCase,
    required this.deleteSlabUseCase,
  });

  // Observable variables
  final slabs = <SlabModel>[].obs;
  final filteredSlabs = <SlabModel>[].obs;
  final isLoading = false.obs;
  final searchController = TextEditingController();
  final searchQuery = ''.obs;

  // Pagination
  final currentPage = 1.obs;
  final itemsPerPage = 25.obs;
  final totalItems = 0.obs;

  // Filtering
  final showActiveOnly = true.obs;
  final showExpiredOnly = false.obs;

  @override
  void onInit() {
    super.onInit();
    loadSlabs();

    // Listen to search changes
    searchController.addListener(() {
      searchQuery.value = searchController.text;
      _filterSlabs();
    });

    // Listen to filter changes
    ever(showActiveOnly, (_) => _filterSlabs());
    ever(showExpiredOnly, (_) => _filterSlabs());
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  /// Load all slabs from Firebase
  Future<void> loadSlabs() async {
    try {
      isLoading.value = true;

      final result = await getSlabsUseCase.call();

      result.fold(
        (failure) {
          log('Error loading slabs: ${failure.message}');
          SnackbarUtils.showError('Error', failure.message);
        },
        (slabsList) {
          slabs.value = slabsList;
          _filterSlabs();
          log('Successfully loaded ${slabsList.length} slabs');
        },
      );
    } catch (e) {
      log('Unexpected error loading slabs: $e');
      SnackbarUtils.showError('Error', 'Failed to load slabs');
    } finally {
      isLoading.value = false;
    }
  }

  /// Filter slabs based on search query and filters
  void _filterSlabs() {
    var filtered = slabs.toList();

    // Apply search filter
    if (searchQuery.value.isNotEmpty) {
      filtered = filtered.where((slab) {
        return slab.slabName
            .toLowerCase()
            .contains(searchQuery.value.toLowerCase());
      }).toList();
    }

    // Apply status filters
    if (showActiveOnly.value && !showExpiredOnly.value) {
      filtered =
          filtered.where((slab) => slab.isActive && !slab.isExpired).toList();
    } else if (showExpiredOnly.value && !showActiveOnly.value) {
      filtered = filtered.where((slab) => slab.isExpired).toList();
    } else if (!showActiveOnly.value && !showExpiredOnly.value) {
      // Show all slabs
    }

    filteredSlabs.value = filtered;
    totalItems.value = filtered.length;

    // Reset to first page when filtering
    currentPage.value = 1;
  }

  /// Get paginated slabs for current page
  List<SlabModel> get paginatedSlabs {
    final startIndex = (currentPage.value - 1) * itemsPerPage.value;
    final endIndex =
        (startIndex + itemsPerPage.value).clamp(0, filteredSlabs.length);

    if (startIndex >= filteredSlabs.length) return [];

    return filteredSlabs.sublist(startIndex, endIndex);
  }

  /// Calculate total pages
  int get totalPages {
    return (totalItems.value / itemsPerPage.value).ceil();
  }

  /// Go to specific page
  void goToPage(int page) {
    if (page >= 1 && page <= totalPages) {
      currentPage.value = page;
    }
  }

  /// Go to next page
  void nextPage() {
    if (currentPage.value < totalPages) {
      currentPage.value++;
    }
  }

  /// Go to previous page
  void previousPage() {
    if (currentPage.value > 1) {
      currentPage.value--;
    }
  }

  /// Change items per page
  void changeItemsPerPage(int newItemsPerPage) {
    itemsPerPage.value = newItemsPerPage;
    currentPage.value = 1; // Reset to first page
  }

  /// Delete a slab
  Future<void> deleteSlab(String slabId) async {
    try {
      final result = await deleteSlabUseCase.call(slabId: slabId);

      result.fold(
        (failure) {
          log('Error deleting slab: ${failure.message}');
          SnackbarUtils.showError('Error', failure.message);
        },
        (_) {
          SnackbarUtils.showSuccess('Success', 'Slab deleted successfully');
          loadSlabs(); // Refresh the list
        },
      );
    } catch (e) {
      log('Unexpected error deleting slab: $e');
      SnackbarUtils.showError('Error', 'Failed to delete slab');
    }
  }

  /// Refresh data
  Future<void> refreshData() async {
    await loadSlabs();
  }

  /// Clear search
  void clearSearch() {
    searchController.clear();
    searchQuery.value = '';
  }

  /// Toggle active filter
  void toggleActiveFilter() {
    showActiveOnly.value = !showActiveOnly.value;
    if (showActiveOnly.value) {
      showExpiredOnly.value = false;
    }
  }

  /// Toggle expired filter
  void toggleExpiredFilter() {
    showExpiredOnly.value = !showExpiredOnly.value;
    if (showExpiredOnly.value) {
      showActiveOnly.value = false;
    }
  }

  /// Get status color for slab
  Color getSlabStatusColor(SlabModel slab) {
    if (!slab.isActive) return Colors.grey;
    if (slab.isExpired) return Colors.red;
    return Colors.green;
  }

  /// Get status text for slab
  String getSlabStatusText(SlabModel slab) {
    if (!slab.isActive) return 'Inactive';
    if (slab.isExpired) return 'Expired';
    return 'Active';
  }
}
