import 'dart:developer';
import '../../models/voucher_model.dart';
import '../../models/finance/journal_entry_model.dart';
import '../../firebase_service/accounting/general_ledger_firebase_service.dart';
import 'automatic_journal_entry_service.dart';
import 'transaction_account_mapping_service.dart';

/// Service for integrating voucher transactions with automatic journal entry generation
class VoucherJournalIntegrationService {
  final AutomaticJournalEntryService _automaticJournalService;
  final GeneralLedgerFirebaseService _generalLedgerService;
  final TransactionAccountMappingService _mappingService;

  VoucherJournalIntegrationService(
    this._automaticJournalService,
    this._generalLedgerService,
    this._mappingService,
  );

  /// Generate and create journal entries for a voucher transaction
  Future<bool> processVoucherTransaction(
      VoucherModel voucher, String uid) async {
    try {
      log('Processing voucher transaction for journal entries: ${voucher.voucherNumber}');

      // Generate journal entries for the voucher
      final journalEntries =
          await _automaticJournalService.generateVoucherJournalEntries(
        voucher: voucher,
        uid: uid,
        createdBy: 'system', // Could be passed as parameter if needed
      );

      if (journalEntries.isEmpty) {
        log('No journal entries generated for voucher: ${voucher.voucherNumber}');
        return true; // Not an error, just no entries needed
      }

      // Create all journal entries
      bool allSuccessful = true;
      for (final journalEntry in journalEntries) {
        final result =
            await _generalLedgerService.createJournalEntry(journalEntry);

        result.fold(
          (failure) {
            log('Failed to create journal entry for voucher: ${failure.message}');
            allSuccessful = false;
          },
          (success) {
            log('Successfully created journal entry for voucher: ${voucher.voucherNumber}');
          },
        );
      }

      return allSuccessful;
    } catch (e) {
      log('Error processing voucher transaction: $e');
      return false;
    }
  }

  /// Process voucher transaction from Map<String, dynamic> format
  Future<bool> processVoucherTransactionFromMap(
    Map<String, dynamic> voucherData,
    String uid,
  ) async {
    try {
      // Convert Map to VoucherModel
      final voucher = VoucherModel.fromJson(voucherData);
      return await processVoucherTransaction(voucher, uid);
    } catch (e) {
      log('Error converting voucher data to model: $e');
      return false;
    }
  }

  /// Batch process multiple voucher transactions
  Future<BatchProcessResult> batchProcessVoucherTransactions(
    List<VoucherModel> vouchers,
    String uid,
  ) async {
    int successCount = 0;
    int failureCount = 0;
    final List<String> failedVoucherNumbers = [];

    log('Batch processing ${vouchers.length} voucher transactions');

    for (final voucher in vouchers) {
      final success = await processVoucherTransaction(voucher, uid);
      if (success) {
        successCount++;
      } else {
        failureCount++;
        failedVoucherNumbers.add(voucher.voucherNumber);
      }
    }

    log('Batch processing completed: $successCount successful, $failureCount failed');

    return BatchProcessResult(
      totalProcessed: vouchers.length,
      successCount: successCount,
      failureCount: failureCount,
      failedTransactionIds: failedVoucherNumbers,
    );
  }

  /// Validate voucher transaction for journal entry generation
  Future<VoucherValidationResult> validateVoucherForJournalEntry(
    VoucherModel voucher,
    String uid,
  ) async {
    try {
      final issues = <String>[];

      // Check if voucher has valid amounts
      if (voucher.totalFreight <= 0) {
        issues.add('Voucher total freight must be greater than zero');
      }

      // Check if voucher has valid number
      if (voucher.voucherNumber.isEmpty) {
        issues.add('Voucher must have a valid voucher number');
      }

      // Check if required accounts exist for journal entry generation
      final mapping = await _mappingService.getVoucherAccountMapping(uid);
      if (mapping == null) {
        issues.add('Required accounts for voucher journal entries not found');
      }

      // Validate broker fees if present
      if (voucher.brokerFees > 0 && voucher.brokerAccount.isEmpty) {
        issues.add(
            'Broker account must be specified when broker fees are present');
      }

      // Validate munshiana fees if present
      if (voucher.munshianaFees > 0 && voucher.munshianaAccount.isEmpty) {
        issues.add(
            'Munshiana account must be specified when munshiana fees are present');
      }

      return VoucherValidationResult(
        isValid: issues.isEmpty,
        issues: issues,
      );
    } catch (e) {
      log('Error validating voucher for journal entry: $e');
      return VoucherValidationResult(
        isValid: false,
        issues: ['Validation error: $e'],
      );
    }
  }

  /// Get journal entries associated with a voucher
  Future<List<JournalEntryModel>> getJournalEntriesForVoucher(
    String voucherNumber,
    String uid,
  ) async {
    try {
      final allEntries =
          await _generalLedgerService.getJournalEntries(uid, null, null);

      return allEntries.fold(
        (failure) {
          log('Failed to fetch journal entries: ${failure.message}');
          return <JournalEntryModel>[];
        },
        (entries) {
          return entries
              .where((entry) =>
                  (entry.sourceTransactionId == voucherNumber &&
                      (entry.sourceTransactionType?.startsWith('voucher') ==
                          true)) &&
                  entry.uid == uid)
              .toList();
        },
      );
    } catch (e) {
      log('Error fetching journal entries for voucher: $e');
      return <JournalEntryModel>[];
    }
  }

  /// Check if a voucher already has associated journal entries
  Future<bool> hasExistingJournalEntries(
      String voucherNumber, String uid) async {
    final entries = await getJournalEntriesForVoucher(voucherNumber, uid);
    return entries.isNotEmpty;
  }

  /// Reverse journal entries for a voucher (when voucher is deleted/modified)
  Future<bool> reverseVoucherJournalEntries(
      String voucherNumber, String uid) async {
    try {
      final entries = await getJournalEntriesForVoucher(voucherNumber, uid);

      if (entries.isEmpty) {
        log('No journal entries found for voucher: $voucherNumber');
        return true; // Nothing to reverse
      }

      bool allReversed = true;
      for (final entry in entries) {
        final result = await _generalLedgerService.reverseJournalEntry(
          entry.id,
          'Voucher transaction reversed',
          'system', // createdBy parameter
        );

        result.fold(
          (failure) {
            log('Failed to reverse journal entry ${entry.id}: ${failure.message}');
            allReversed = false;
          },
          (success) {
            log('Successfully reversed journal entry: ${entry.id}');
          },
        );
      }

      return allReversed;
    } catch (e) {
      log('Error reversing voucher journal entries: $e');
      return false;
    }
  }

  /// Get voucher financial summary for reporting
  Future<VoucherFinancialSummary> getVoucherFinancialSummary(
    VoucherModel voucher,
  ) async {
    try {
      final netProfit =
          voucher.totalFreight - voucher.brokerFees - voucher.munshianaFees;

      return VoucherFinancialSummary(
        voucherNumber: voucher.voucherNumber,
        totalFreight: voucher.totalFreight,
        brokerFees: voucher.brokerFees,
        munshianaFees: voucher.munshianaFees,
        netProfit: netProfit,
        hasJournalEntries: await hasExistingJournalEntries(
            voucher.voucherNumber, ''), // uid would be passed
      );
    } catch (e) {
      log('Error getting voucher financial summary: $e');
      return VoucherFinancialSummary(
        voucherNumber: voucher.voucherNumber,
        totalFreight: 0.0,
        brokerFees: 0.0,
        munshianaFees: 0.0,
        netProfit: 0.0,
        hasJournalEntries: false,
      );
    }
  }
}

/// Result class for batch processing operations
class BatchProcessResult {
  final int totalProcessed;
  final int successCount;
  final int failureCount;
  final List<String> failedTransactionIds;

  BatchProcessResult({
    required this.totalProcessed,
    required this.successCount,
    required this.failureCount,
    required this.failedTransactionIds,
  });

  double get successRate =>
      totalProcessed > 0 ? successCount / totalProcessed : 0.0;
  bool get hasFailures => failureCount > 0;
  bool get allSuccessful => failureCount == 0;
}

/// Result class for voucher validation
class VoucherValidationResult {
  final bool isValid;
  final List<String> issues;

  VoucherValidationResult({
    required this.isValid,
    required this.issues,
  });

  String get issuesText => issues.join(', ');
}

/// Financial summary for a voucher
class VoucherFinancialSummary {
  final String voucherNumber;
  final double totalFreight;
  final double brokerFees;
  final double munshianaFees;
  final double netProfit;
  final bool hasJournalEntries;

  VoucherFinancialSummary({
    required this.voucherNumber,
    required this.totalFreight,
    required this.brokerFees,
    required this.munshianaFees,
    required this.netProfit,
    required this.hasJournalEntries,
  });
}
