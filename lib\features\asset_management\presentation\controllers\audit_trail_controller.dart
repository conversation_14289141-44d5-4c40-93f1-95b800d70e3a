import 'dart:async';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/mixins/auto_refresh_mixin.dart';
import 'package:logestics/core/utils/mixins/pagination_mixin.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/models/asset/asset_audit_model.dart';
import 'package:logestics/repositories/asset/asset_audit_repository.dart';

class AuditTrailController extends GetxController
    with PaginationMixin, AutoRefreshMixin {
  final AssetAuditRepository _auditRepository;

  AuditTrailController(this._auditRepository);

  // Observable variables
  final RxList<AssetAuditModel> auditEntries = <AssetAuditModel>[].obs;
  final RxList<AssetAuditModel> filteredAuditEntries = <AssetAuditModel>[].obs;
  final RxList<AssetAuditModel> paginatedAuditEntries = <AssetAuditModel>[].obs;
  final isLoading = true.obs;
  final searchQuery = ''.obs;

  // Real-time subscription
  StreamSubscription<List<AssetAuditModel>>? _auditSubscription;

  // Track if listeners are already set up to prevent duplicates
  bool _listenersSetup = false;

  // Search and filter controllers
  final searchController = TextEditingController();

  // Filter variables
  final selectedActionFilter = ''.obs;
  final selectedUserFilter = ''.obs;
  final selectedAssetFilter = ''.obs;
  final startDateFilter = Rx<DateTime?>(null);
  final endDateFilter = Rx<DateTime?>(null);

  // Date range quick filters
  final selectedDateRange = ''.obs;

  // Available filter options
  final availableActions = <String>[].obs;
  final availableUsers = <String>[].obs;
  final availableAssets = <String>[].obs;

  // Asset-specific filtering
  final RxnString currentAssetId = RxnString();

  @override
  void onInit() {
    super.onInit();
    searchController.addListener(_onSearchChanged);
    itemsPerPage.value = 25; // Set default items per page to 25
    _setupRealTimeUpdates();
  }

  @override
  void onClose() {
    _auditSubscription?.cancel();
    _auditSubscription = null;
    searchController.dispose();
    super.onClose();
  }

  @override
  Future<void> forceRefresh() async {
    await loadAuditTrail();
  }

  @override
  Future<void> refreshData() async {
    await loadAuditTrail();
  }

  void _onSearchChanged() {
    searchQuery.value = searchController.text;
    _applyFiltersAndUpdatePagination();
  }

  /// Set up real-time updates for audit trail
  void _setupRealTimeUpdates() {
    log('AuditTrailController: Setting up real-time updates');

    // Set up real-time listener for audit trail - NO FILTERS, load all data
    _auditSubscription = _auditRepository
        .listenToAuditTrail(
      limit: 500, // Load more entries for better filtering
      // Remove all filters from Firebase query - do filtering client-side only
    )
        .listen(
      (auditList) {
        log('AuditTrailController: Real-time update received ${auditList.length} audit entries');
        auditEntries.value = List<AssetAuditModel>.from(auditList);
        _updateFilterOptions();
        _applyFiltersAndUpdatePagination();
        isLoading.value = false;
      },
      onError: (error) {
        log('AuditTrailController: Error in real-time audit stream: $error');
        isLoading.value = false;
        SnackbarUtils.showError('Error', 'Failed to load audit trail: $error');
      },
    );

    // Set up reactive listeners only once to prevent duplicates
    if (!_listenersSetup) {
      // Listen for changes and update filtered list (avoid infinite loops)
      ever(auditEntries, (_) => _applyFiltersAndUpdatePagination());
      ever(searchQuery, (_) => _applyFiltersAndUpdatePagination());
      ever(selectedActionFilter, (_) => _applyFiltersAndUpdatePagination());
      ever(selectedUserFilter, (_) => _applyFiltersAndUpdatePagination());
      ever(selectedAssetFilter, (_) => _applyFiltersAndUpdatePagination());
      ever(startDateFilter, (_) => _applyFiltersAndUpdatePagination());
      ever(endDateFilter, (_) => _applyFiltersAndUpdatePagination());

      // Listen for pagination changes only (no filtering)
      ever(currentPage, (_) => _updatePaginatedEntriesOnly());
      ever(itemsPerPage, (_) => _updatePaginatedEntriesOnly());

      _listenersSetup = true;
      log('AuditTrailController: Reactive listeners set up');
    }
  }

  /// Set up asset-specific real-time updates
  void _setupAssetSpecificRealTimeUpdates(String assetId) {
    log('AuditTrailController: Setting up asset-specific real-time updates for asset: $assetId');

    // Set up real-time listener for specific asset audit trail
    _auditSubscription = _auditRepository
        .listenToAuditTrail(
      limit: 500,
      assetId: assetId, // Filter by specific asset
    )
        .listen(
      (auditList) {
        log('AuditTrailController: Asset-specific real-time update received ${auditList.length} audit entries');
        auditEntries.value = List<AssetAuditModel>.from(auditList);
        _updateFilterOptions();
        _applyFiltersAndUpdatePagination();
        isLoading.value = false;
      },
      onError: (error) {
        log('AuditTrailController: Error in asset-specific audit stream: $error');
        isLoading.value = false;
        SnackbarUtils.showError('Error', 'Failed to load audit trail: $error');
      },
    );
  }

  /// Load audit trail from repository (fallback method)
  Future<void> loadAuditTrail() async {
    try {
      isLoading.value = true;
      log('Loading audit trail...');

      final result = await _auditRepository.getAuditTrail(
        limit: 500,
        // Remove all filters from Firebase query - do filtering client-side only
      );

      result.fold(
        (failure) {
          log('Error loading audit trail: ${failure.message}');
          SnackbarUtils.showError('Error', failure.message);
        },
        (auditList) {
          log('Successfully loaded ${auditList.length} audit entries');
          auditEntries.value = List<AssetAuditModel>.from(auditList);
          _updateFilterOptions();
          _applyFiltersAndUpdatePagination();
        },
      );
    } catch (e) {
      log('Error loading audit trail: $e');
      SnackbarUtils.showError('Error', 'Failed to load audit trail');
    } finally {
      isLoading.value = false;
    }
  }

  /// Load audit trail for a specific asset (using real-time stream only)
  Future<void> loadAssetAuditTrail(String assetId) async {
    try {
      isLoading.value = true;
      log('Loading audit trail for asset: $assetId');

      // Set current asset ID for filtering
      currentAssetId.value = assetId;

      // Cancel existing subscription and set up asset-specific real-time stream
      _auditSubscription?.cancel();

      // Clear existing data to prevent conflicts
      auditEntries.clear();
      filteredAuditEntries.clear();
      paginatedAuditEntries.clear();

      // Set up real-time stream - this will handle all data loading
      _setupAssetSpecificRealTimeUpdates(assetId);

      // Set up a timeout to prevent infinite loading
      Timer(Duration(seconds: 10), () {
        if (isLoading.value) {
          log('AuditTrailController: Loading timeout reached for asset: $assetId');
          isLoading.value = false;
          if (auditEntries.isEmpty) {
            SnackbarUtils.showError(
                'Timeout', 'Loading took too long. Please try again.');
          }
        }
      });

      log('Asset-specific real-time stream setup completed for asset: $assetId');
    } catch (e) {
      log('Error setting up asset audit trail stream: $e');
      SnackbarUtils.showError('Error', 'Failed to load asset audit trail');
      isLoading.value = false;
    }
  }

  /// Update available filter options based on current data
  void _updateFilterOptions() {
    // Update available actions
    final actions = auditEntries.map((e) => e.action).toSet().toList();
    actions.sort();
    availableActions.value = actions;

    // Update available users
    final users = auditEntries.map((e) => e.userName).toSet().toList();
    users.sort();
    availableUsers.value = users;

    // Update available assets
    final assets = auditEntries.map((e) => e.assetName).toSet().toList();
    assets.sort();
    availableAssets.value = assets;
  }

  /// Apply filters and update pagination in one atomic operation (prevents infinite loops)
  void _applyFiltersAndUpdatePagination() {
    log('AuditTrailController: _applyFiltersAndUpdatePagination() called with ${auditEntries.length} total entries');
    log('AuditTrailController: Current asset ID: ${currentAssetId.value}');
    log('AuditTrailController: Current filters - startDate: ${startDateFilter.value}, endDate: ${endDateFilter.value}, action: ${selectedActionFilter.value}, user: ${selectedUserFilter.value}, search: ${searchQuery.value}');
    var filtered = auditEntries.toList();

    // If we're viewing asset-specific audit trail, the data should already be filtered by asset
    // No additional asset filtering needed since the stream is already asset-specific

    // Apply search filter
    if (searchQuery.value.isNotEmpty) {
      final query = searchQuery.value.toLowerCase();
      filtered = filtered
          .where((entry) =>
              entry.action.toLowerCase().contains(query) ||
              entry.userName.toLowerCase().contains(query) ||
              entry.assetName.toLowerCase().contains(query) ||
              entry.notes.toLowerCase().contains(query))
          .toList();
    }

    // Apply action filter
    if (selectedActionFilter.value.isNotEmpty) {
      filtered = filtered
          .where((entry) => entry.action == selectedActionFilter.value)
          .toList();
    }

    // Apply user filter
    if (selectedUserFilter.value.isNotEmpty) {
      filtered = filtered
          .where((entry) => entry.userName == selectedUserFilter.value)
          .toList();
    }

    // Apply date range filter
    if (startDateFilter.value != null) {
      final startOfDay = DateTime(
        startDateFilter.value!.year,
        startDateFilter.value!.month,
        startDateFilter.value!.day,
      );
      filtered = filtered
          .where((entry) =>
              entry.timestamp.isAfter(startOfDay) ||
              entry.timestamp.isAtSameMomentAs(startOfDay))
          .toList();
    }

    if (endDateFilter.value != null) {
      final endOfDay = DateTime(
        endDateFilter.value!.year,
        endDateFilter.value!.month,
        endDateFilter.value!.day,
        23,
        59,
        59,
      );
      filtered = filtered
          .where((entry) =>
              entry.timestamp.isBefore(endOfDay) ||
              entry.timestamp.isAtSameMomentAs(endOfDay))
          .toList();
    }

    log('AuditTrailController: Final filtered entries count: ${filtered.length}');

    // Update filtered entries
    filteredAuditEntries.value = filtered;
    setTotalItems(filtered.length);

    // Reset to first page when filters change (but don't trigger pagination listener)
    if (currentPage.value > 1) {
      currentPage.value =
          1; // Direct assignment to avoid triggering ever() listener
    }

    // Update pagination immediately
    _updatePaginatedEntriesOnly();
  }

  /// Update paginated audit entries for current page only (no filtering)
  void _updatePaginatedEntriesOnly() {
    final paginated = paginateList(filteredAuditEntries);
    log('AuditTrailController: _updatePaginatedEntriesOnly called - setting ${paginated.length} entries');
    log('AuditTrailController: filteredAuditEntries has ${filteredAuditEntries.length} entries');
    log('AuditTrailController: currentPage=${currentPage.value}, itemsPerPage=${itemsPerPage.value}');
    paginatedAuditEntries.value = paginated;
  }

  /// Clear all filters
  void clearFilters() {
    searchController.clear();
    selectedActionFilter.value = '';
    selectedUserFilter.value = '';
    selectedAssetFilter.value = '';
    startDateFilter.value = null;
    endDateFilter.value = null;
    selectedDateRange.value = '';
    _applyFiltersAndUpdatePagination();
  }

  /// Set action filter
  void setActionFilter(String? action) {
    selectedActionFilter.value = action ?? '';
    _applyFiltersAndUpdatePagination();
  }

  /// Set user filter
  void setUserFilter(String? user) {
    selectedUserFilter.value = user ?? '';
    _applyFiltersAndUpdatePagination();
  }

  /// Set asset filter
  void setAssetFilter(String? asset) {
    selectedAssetFilter.value = asset ?? '';
    _applyFiltersAndUpdatePagination();
  }

  /// Set date range filter
  void setDateRange(DateTime? start, DateTime? end) {
    startDateFilter.value = start;
    endDateFilter.value = end;
    _applyFiltersAndUpdatePagination();
  }

  /// Set quick date range filters
  void setQuickDateRange(String range) {
    selectedDateRange.value = range;
    final now = DateTime.now();

    switch (range) {
      case 'Today':
        setDateRange(DateTime(now.year, now.month, now.day),
            DateTime(now.year, now.month, now.day, 23, 59, 59));
        break;
      case 'This Week':
        final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
        setDateRange(
            DateTime(startOfWeek.year, startOfWeek.month, startOfWeek.day),
            DateTime(now.year, now.month, now.day, 23, 59, 59));
        break;
      case 'This Month':
        setDateRange(DateTime(now.year, now.month, 1),
            DateTime(now.year, now.month + 1, 0, 23, 59, 59));
        break;
      case 'Last 30 Days':
        setDateRange(now.subtract(const Duration(days: 30)),
            DateTime(now.year, now.month, now.day, 23, 59, 59));
        break;
      case 'Clear':
        setDateRange(null, null);
        break;
    }
  }

  /// Refresh audit trail manually
  Future<void> refreshAuditTrail() async {
    await loadAuditTrail();
  }

  /// Get color for action type
  Color getActionColor(String action) {
    switch (action) {
      case 'created':
        return Colors.green;
      case 'updated':
        return Colors.blue;
      case 'deleted':
        return Colors.red;
      case 'status_changed':
        return Colors.orange;
      case 'maintenance_added':
      case 'maintenance_updated':
        return Colors.purple;
      case 'file_uploaded':
      case 'file_deleted':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }

  /// Get icon for action type
  IconData getActionIcon(String action) {
    switch (action) {
      case 'created':
        return Icons.add_circle;
      case 'updated':
        return Icons.edit;
      case 'deleted':
        return Icons.delete;
      case 'status_changed':
        return Icons.swap_horiz;
      case 'maintenance_added':
      case 'maintenance_updated':
        return Icons.build;
      case 'file_uploaded':
        return Icons.upload_file;
      case 'file_deleted':
        return Icons.delete_forever;
      default:
        return Icons.info;
    }
  }
}
