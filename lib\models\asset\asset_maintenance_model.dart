class AssetMaintenanceModel {
  final String id;
  final String assetId;
  final String assetName;
  final DateTime maintenanceDate;
  final double cost;
  final String description;
  final String maintenanceType; // Preventive, Corrective, Emergency, Routine
  final String performedBy; // Technician/Company name
  final String notes;
  final List<String> attachmentUrls; // Receipts, photos, etc.
  final DateTime createdAt;
  final DateTime updatedAt;
  final String uid; // User ID who owns this maintenance record

  AssetMaintenanceModel({
    required this.id,
    required this.assetId,
    required this.assetName,
    required this.maintenanceDate,
    required this.cost,
    required this.description,
    required this.maintenanceType,
    required this.performedBy,
    this.notes = '',
    this.attachmentUrls = const [],
    required this.createdAt,
    required this.updatedAt,
    this.uid = '',
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'assetId': assetId,
      'assetName': assetName,
      'maintenanceDate': maintenanceDate.millisecondsSinceEpoch,
      'cost': cost,
      'description': description,
      'maintenanceType': maintenanceType,
      'performedBy': performedBy,
      'notes': notes,
      'attachmentUrls': attachmentUrls,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
      'uid': uid,
    };
  }

  factory AssetMaintenanceModel.fromJson(Map<String, dynamic> json) {
    return AssetMaintenanceModel(
      id: json['id'] ?? '',
      assetId: json['assetId'] ?? '',
      assetName: json['assetName'] ?? '',
      maintenanceDate: DateTime.fromMillisecondsSinceEpoch(json['maintenanceDate'] ?? 0),
      cost: (json['cost'] ?? 0).toDouble(),
      description: json['description'] ?? '',
      maintenanceType: json['maintenanceType'] ?? '',
      performedBy: json['performedBy'] ?? '',
      notes: json['notes'] ?? '',
      attachmentUrls: List<String>.from(json['attachmentUrls'] ?? []),
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt'] ?? 0),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(json['updatedAt'] ?? 0),
      uid: json['uid'] ?? '',
    );
  }

  AssetMaintenanceModel copyWith({
    String? id,
    String? assetId,
    String? assetName,
    DateTime? maintenanceDate,
    double? cost,
    String? description,
    String? maintenanceType,
    String? performedBy,
    String? notes,
    List<String>? attachmentUrls,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? uid,
  }) {
    return AssetMaintenanceModel(
      id: id ?? this.id,
      assetId: assetId ?? this.assetId,
      assetName: assetName ?? this.assetName,
      maintenanceDate: maintenanceDate ?? this.maintenanceDate,
      cost: cost ?? this.cost,
      description: description ?? this.description,
      maintenanceType: maintenanceType ?? this.maintenanceType,
      performedBy: performedBy ?? this.performedBy,
      notes: notes ?? this.notes,
      attachmentUrls: attachmentUrls ?? this.attachmentUrls,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      uid: uid ?? this.uid,
    );
  }

  @override
  String toString() {
    return 'AssetMaintenanceModel(id: $id, assetName: $assetName, type: $maintenanceType, cost: $cost)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AssetMaintenanceModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Maintenance type constants
class MaintenanceType {
  static const String preventive = 'Preventive';
  static const String corrective = 'Corrective';
  static const String emergency = 'Emergency';
  static const String routine = 'Routine';

  static List<String> get allTypes => [
    preventive,
    corrective,
    emergency,
    routine,
  ];
}
