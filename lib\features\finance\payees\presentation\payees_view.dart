import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/core/utils/widgets/data_table_cell.dart';
import 'package:logestics/core/utils/widgets/pagination_widget.dart';
import 'package:logestics/features/finance/payees/presentation/payee_controller.dart';
import 'package:logestics/features/finance/payees/repositories/payee_repository.dart';
import 'package:logestics/firebase_service/finance/payee_firebase_service.dart';
import 'package:logestics/models/finance/payee_model.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';

class PayeesView extends StatefulWidget {
  const PayeesView({super.key});

  @override
  State<PayeesView> createState() => _PayeesViewState();
}

class _PayeesViewState extends State<PayeesView> {
  late final PayeeController payeeController;

  @override
  void initState() {
    super.initState();
    // Initialize Firebase API
    final firebaseService = PayeeFirebaseService();
    // Initialize repository with Firebase API
    final repository = PayeeRepositoryImpl(firebaseService);
    // Initialize controller with repository
    payeeController = Get.put(PayeeController(repository: repository));
  }

  @override
  Widget build(BuildContext context) {
    var width = Get.width;
    notifier = Provider.of(context, listen: true);

    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          child: Column(
            children: [
              SizedBox(
                height: width < 650 ? 55 : 40,
                width: width,
                child: width < 650
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Payees',
                            overflow: TextOverflow.ellipsis,
                            style: AppTextStyles.titleStyle
                                .copyWith(color: notifier.text),
                          ),
                          const Spacer(),
                        ],
                      )
                    : Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Payees',
                            overflow: TextOverflow.ellipsis,
                            style: AppTextStyles.titleStyle
                                .copyWith(color: notifier.text),
                          ),
                          const Spacer(),
                        ],
                      ),
              ),
              const SizedBox(height: 20),
              SizedBox(
                height: 570,
                child: _buildPayeesList(),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPayeesList() {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            color: notifier.getBgColor,
            borderRadius: BorderRadius.circular(10),
          ),
          padding: const EdgeInsets.symmetric(vertical: 15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 15),
                child: Get.width < 650
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          InkWell(
                            onTap: () => payeeController.openDialog(),
                            child: Text(
                              'Add New Payee',
                              style: AppTextStyles.addNewInvoiceStyle,
                            ),
                          ),
                          TextField(
                            controller: payeeController.searchController,
                            decoration: InputDecoration(
                              hintText: 'Search payees...',
                              prefixIcon: const Icon(Icons.search),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                              filled: true,
                              fillColor: notifier.textFileColor,
                            ),
                          ),
                        ],
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          InkWell(
                            onTap: () => payeeController.openDialog(),
                            child: Text(
                              'Add New Payee',
                              style: AppTextStyles.addNewInvoiceStyle,
                            ),
                          ),
                          SizedBox(
                            width: Get.width < 850
                                ? Get.width / 2
                                : Get.width / 3.5,
                            child: TextField(
                              controller: payeeController.searchController,
                              decoration: InputDecoration(
                                hintText: 'Search payees...',
                                prefixIcon: const Icon(Icons.search),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8.0),
                                ),
                                filled: true,
                                fillColor: notifier.textFileColor,
                              ),
                            ),
                          ),
                        ],
                      ),
              ),
              const SizedBox(height: 20),
              Expanded(
                child: SizedBox(
                  width: Get.width,
                  child: Obx(() {
                    if (payeeController.isLoading.value) {
                      return const Center(
                        child: CircularProgressIndicator(),
                      );
                    }

                    return ListView(
                      shrinkWrap: true,
                      children: [
                        Table(
                          columnWidths: const {
                            0: FlexColumnWidth(0.5), // S.No
                            1: FlexColumnWidth(1.5), // Name
                            2: FlexColumnWidth(1.5), // Phone Number
                            3: FlexColumnWidth(1.0), // Actions
                          },
                          border: TableBorder(
                            horizontalInside: BorderSide(
                              color: notifier.getfillborder,
                            ),
                          ),
                          children: [
                            TableRow(
                              decoration: BoxDecoration(
                                color: notifier.getHoverColor,
                              ),
                              children: [
                                DataTableCell(
                                  text: 'S.No',
                                  style:
                                      AppTextStyles.invoiceDataStyle.copyWith(
                                    color: notifier.text,
                                  ),
                                ),
                                DataTableCell(
                                  text: 'Name',
                                  style:
                                      AppTextStyles.invoiceDataStyle.copyWith(
                                    color: notifier.text,
                                  ),
                                ),
                                DataTableCell(
                                  text: 'Phone Number',
                                  style:
                                      AppTextStyles.invoiceDataStyle.copyWith(
                                    color: notifier.text,
                                  ),
                                ),
                                DataTableCell(
                                  text: 'Actions',
                                  style:
                                      AppTextStyles.invoiceDataStyle.copyWith(
                                    color: notifier.text,
                                  ),
                                ),
                              ],
                            ),
                            ...payeeController.paginatedPayees
                                .asMap()
                                .entries
                                .map((entry) {
                              final i = entry.key;
                              final payee = entry.value;
                              return TableRow(
                                children: [
                                  DataTableCell(
                                    text: ((payeeController.currentPage.value -
                                                    1) *
                                                payeeController
                                                    .itemsPerPage.value +
                                            i +
                                            1)
                                        .toString(),
                                    style:
                                        AppTextStyles.invoiceDataStyle.copyWith(
                                      color: notifier.text,
                                    ),
                                  ),
                                  DataTableCell(
                                    text: payee.name,
                                    style:
                                        AppTextStyles.invoiceDataStyle.copyWith(
                                      color: notifier.text,
                                      decoration: TextDecoration.underline,
                                    ),
                                    onTap: () => _showPayeeDetails(payee),
                                  ),
                                  DataTableCell(
                                    text: payee.phoneNumber ?? '-',
                                    style:
                                        AppTextStyles.invoiceDataStyle.copyWith(
                                      color: notifier.text,
                                    ),
                                  ),
                                  DataTableActionsCell(
                                    menuItems: [
                                      DataTablePopupMenuItem(
                                        text: 'Edit',
                                        icon: Icons.edit_outlined,
                                        onTap: () => _showPayeeDetails(payee),
                                      ),
                                      DataTablePopupMenuItem(
                                        text: 'Delete',
                                        icon: Icons.delete_outline,
                                        isDanger: true,
                                        onTap: () async {
                                          if (!payeeController
                                              .isLoading.value) {
                                            await _confirmDelete(payee.id);
                                          }
                                        },
                                      ),
                                    ],
                                  ),
                                ],
                              );
                            }),
                          ],
                        ),
                      ],
                    );
                  }),
                ),
              ),
              Obx(() => PaginationWidget(
                    currentPage: payeeController.currentPage.value,
                    totalPages: payeeController.totalPages,
                    itemsPerPage: payeeController.itemsPerPage.value,
                    onPageChanged: (page) =>
                        payeeController.setCurrentPage(page),
                    onItemsPerPageChanged: (count) =>
                        payeeController.setItemsPerPage(count),
                  )),
            ],
          ),
        ),
        Obx(() {
          if (!payeeController.isDialogOpen.value) {
            return const SizedBox.shrink();
          }

          return Positioned(
            right: 0,
            top: 0,
            bottom: 0,
            width: Get.width < 650 ? Get.width : 400,
            child: Container(
              decoration: BoxDecoration(
                color: notifier.getBgColor,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(10),
                    blurRadius: 10,
                    offset: const Offset(0, 0),
                  ),
                ],
              ),
              child: Form(
                key: payeeController.formKey,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Add New Payee',
                            style: AppTextStyles.titleStyle.copyWith(
                              color: notifier.text,
                            ),
                          ),
                          IconButton(
                            onPressed: payeeController.closeDialog,
                            icon: const Icon(Icons.close),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      TextFormField(
                        controller: payeeController.nameController,
                        decoration: InputDecoration(
                          labelText: 'Name',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          filled: true,
                          fillColor: notifier.textFileColor,
                        ),
                        validator: payeeController.validateName,
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: payeeController.phoneNumberController,
                        decoration: InputDecoration(
                          labelText: 'Phone Number',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          filled: true,
                          fillColor: notifier.textFileColor,
                        ),
                        keyboardType: TextInputType.phone,
                        validator: payeeController.validatePhoneNumber,
                      ),
                      const SizedBox(height: 24),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: payeeController.addPayee,
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text('Add Payee'),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }),
      ],
    );
  }

  Future<void> _confirmDelete(String id) {
    return Get.dialog(
      AlertDialog(
        title: const Text('Confirm Delete'),
        content: const Text('Are you sure you want to delete this payee?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              try {
                Get.back(); // Close confirmation dialog
                Get.dialog(
                  const Center(
                    child: CircularProgressIndicator(),
                  ),
                  barrierDismissible: false,
                );
                await payeeController.deletePayee(id);
                if (Get.isDialogOpen ?? false) {
                  Get.back(); // Close loading dialog only if it's open
                }
              } catch (e) {
                if (Get.isDialogOpen ?? false) {
                  Get.back(); // Close loading dialog if there was an error
                }
                SnackbarUtils.showError(
                  AppStrings.errorS,
                  'Failed to delete payee: $e',
                );
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showPayeeDetails(PayeeModel payee) {
    payeeController.nameController.text = payee.name;
    payeeController.phoneNumberController.text = payee.phoneNumber ?? '';

    Get.dialog(
      Dialog(
        insetPadding: const EdgeInsets.all(20),
        child: Container(
          width: Get.width < 650 ? Get.width : 500,
          padding: const EdgeInsets.all(20),
          child: Form(
            key: payeeController.formKey,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Payee Details',
                        style: AppTextStyles.titleStyle.copyWith(
                          color: notifier.text,
                        ),
                      ),
                      IconButton(
                        onPressed: () => Get.back(),
                        icon: const Icon(Icons.close),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  TextFormField(
                    controller: payeeController.nameController,
                    decoration: InputDecoration(
                      labelText: 'Name',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      filled: true,
                      fillColor: notifier.textFileColor,
                    ),
                    validator: payeeController.validateName,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: payeeController.phoneNumberController,
                    decoration: InputDecoration(
                      labelText: 'Phone Number',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      filled: true,
                      fillColor: notifier.textFileColor,
                    ),
                    keyboardType: TextInputType.phone,
                    validator: payeeController.validatePhoneNumber,
                  ),
                  const SizedBox(height: 24),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        if (payeeController.formKey.currentState!.validate()) {
                          final updatedPayee = PayeeModel(
                            id: payee.id,
                            name: payeeController.nameController.text,
                            phoneNumber:
                                payeeController.phoneNumberController.text,
                            createdAt: payee.createdAt,
                            updatedAt: DateTime.now(),
                          );
                          payeeController.updatePayee(updatedPayee);
                          Get.back();
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('Update Payee'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
