import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/features/accounting/aged_reports/presentation/controllers/aged_reports_controller.dart';

/// Filters widget for Aged Reports
/// Provides filtering options for date, customers, vendors, and other criteria
class AgedReportsFilters extends StatelessWidget {
  const AgedReportsFilters({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<AgedReportsController>();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade300,
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Filter header
          Row(
            children: [
              const Icon(Icons.filter_list, color: Colors.blue),
              const SizedBox(width: 8),
              const Text(
                'Report Filters',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
              const Spacer(),
              TextButton.icon(
                onPressed: controller.clearFilters,
                icon: const Icon(Icons.clear, size: 18),
                label: const Text('Clear Filters'),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.grey.shade600,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Filter controls
          Wrap(
            spacing: 16,
            runSpacing: 16,
            children: [
              // As of Date
              SizedBox(
                width: 200,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'As of Date *',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextFormField(
                      controller: controller.asOfDateController,
                      decoration: InputDecoration(
                        hintText: 'Select date',
                        prefixIcon: const Icon(Icons.calendar_today),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                      ),
                      readOnly: true,
                      onTap: () => _selectDate(context, controller),
                    ),
                  ],
                ),
              ),
              
              // Include Zero Balances
              SizedBox(
                width: 200,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Options',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Obx(() => CheckboxListTile(
                      title: const Text('Include Zero Balances'),
                      value: controller.includeZeroBalances.value,
                      onChanged: (value) {
                        controller.includeZeroBalances.value = value ?? false;
                      },
                      controlAffinity: ListTileControlAffinity.leading,
                      contentPadding: EdgeInsets.zero,
                      dense: true,
                    )),
                  ],
                ),
              ),
              
              // Customer Filter (for receivables)
              Obx(() {
                if (controller.selectedReportType.value == 'receivables') {
                  return SizedBox(
                    width: 250,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Filter by Customers',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade400),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: DropdownButtonFormField<String>(
                            decoration: const InputDecoration(
                              hintText: 'Select customers',
                              prefixIcon: Icon(Icons.people),
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 8,
                              ),
                            ),
                            items: controller.availableCustomers
                                .map((customer) => DropdownMenuItem(
                                      value: customer,
                                      child: Text(customer),
                                    ))
                                .toList(),
                            onChanged: (value) {
                              if (value != null) {
                                controller.toggleCustomerSelection(value);
                              }
                            },
                          ),
                        ),
                        const SizedBox(height: 8),
                        Obx(() {
                          if (controller.selectedCustomers.isEmpty) {
                            return const Text(
                              'All customers selected',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey,
                              ),
                            );
                          }
                          return Wrap(
                            spacing: 4,
                            runSpacing: 4,
                            children: controller.selectedCustomers
                                .map((customer) => Chip(
                                      label: Text(
                                        customer,
                                        style: const TextStyle(fontSize: 12),
                                      ),
                                      onDeleted: () {
                                        controller.toggleCustomerSelection(customer);
                                      },
                                      deleteIconColor: Colors.red,
                                      backgroundColor: Colors.blue.shade50,
                                    ))
                                .toList(),
                          );
                        }),
                      ],
                    ),
                  );
                }
                return const SizedBox.shrink();
              }),
              
              // Vendor Filter (for payables)
              Obx(() {
                if (controller.selectedReportType.value == 'payables') {
                  return SizedBox(
                    width: 250,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Filter by Vendors',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade400),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: DropdownButtonFormField<String>(
                            decoration: const InputDecoration(
                              hintText: 'Select vendors',
                              prefixIcon: Icon(Icons.business),
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 8,
                              ),
                            ),
                            items: controller.availableVendors
                                .map((vendor) => DropdownMenuItem(
                                      value: vendor,
                                      child: Text(vendor),
                                    ))
                                .toList(),
                            onChanged: (value) {
                              if (value != null) {
                                controller.toggleVendorSelection(value);
                              }
                            },
                          ),
                        ),
                        const SizedBox(height: 8),
                        Obx(() {
                          if (controller.selectedVendors.isEmpty) {
                            return const Text(
                              'All vendors selected',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey,
                              ),
                            );
                          }
                          return Wrap(
                            spacing: 4,
                            runSpacing: 4,
                            children: controller.selectedVendors
                                .map((vendor) => Chip(
                                      label: Text(
                                        vendor,
                                        style: const TextStyle(fontSize: 12),
                                      ),
                                      onDeleted: () {
                                        controller.toggleVendorSelection(vendor);
                                      },
                                      deleteIconColor: Colors.red,
                                      backgroundColor: Colors.red.shade50,
                                    ))
                                .toList(),
                          );
                        }),
                      ],
                    ),
                  );
                }
                return const SizedBox.shrink();
              }),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Quick date filters
          Row(
            children: [
              const Text(
                'Quick Filters: ',
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(width: 8),
              _buildQuickDateButton('Today', DateTime.now(), controller),
              const SizedBox(width: 8),
              _buildQuickDateButton(
                'End of Month',
                DateTime(DateTime.now().year, DateTime.now().month + 1, 0),
                controller,
              ),
              const SizedBox(width: 8),
              _buildQuickDateButton(
                'End of Quarter',
                _getEndOfQuarter(DateTime.now()),
                controller,
              ),
              const SizedBox(width: 8),
              _buildQuickDateButton(
                'End of Year',
                DateTime(DateTime.now().year, 12, 31),
                controller,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build quick date filter button
  Widget _buildQuickDateButton(String label, DateTime date, AgedReportsController controller) {
    return OutlinedButton(
      onPressed: () {
        controller.asOfDateController.text = date.toString().split(' ')[0];
      },
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        minimumSize: Size.zero,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
      child: Text(
        label,
        style: const TextStyle(fontSize: 12),
      ),
    );
  }

  /// Select date using date picker
  Future<void> _selectDate(BuildContext context, AgedReportsController controller) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: Colors.blue,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      controller.asOfDateController.text = picked.toString().split(' ')[0];
    }
  }

  /// Get end of current quarter
  DateTime _getEndOfQuarter(DateTime date) {
    final quarter = ((date.month - 1) ~/ 3) + 1;
    final endMonth = quarter * 3;
    return DateTime(date.year, endMonth + 1, 0);
  }
}
