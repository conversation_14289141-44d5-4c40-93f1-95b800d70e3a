---
description: 
globs: 
alwaysApply: false
---
# Voucher System Implementation Guide

## Voucher Controller Architecture

### Main Controller
Reference: [add_voucher_controller.dart](mdc:lib/features/voucher/presentation/controllers/add_voucher_controller.dart)

The voucher system is the core of the logistics application and handles complex financial flows.

## Financial Transaction Flows

### 1. Broker Fees (Outsource Only)
When `brokerType == 'Outsource'` and broker fees > 0:
- **Action**: Create DEPOSIT record
- **Effect**: Increases selected broker account balance
- **Appears in**: Deposits collection and deposits view
- **Reference**: `V-${voucherNumber}`

### 2. Munshiana Fees
When munshiana fees > 0:
- **Action**: Create EXPENSE record  
- **Effect**: Decreases selected munshiana account balance
- **Appears in**: Expenses collection and expenses view
- **Reference**: `V-${voucherNumber}`

### 3. Payment Transactions
For each payment transaction (check, fuel card, etc.):
- **Action**: Create EXPENSE record
- **Effect**: Decreases selected payment account balance
- **Appears in**: Expenses collection and expenses view
- **Reference**: `V-${voucherNumber}`

## Implementation Sequence

### Critical Flow Order
```dart
// 1. Validate voucher form
if (!addVoucherFormStateKey.currentState!.validate()) {
  return null;
}

// 2. Save voucher to database
bool success = await createVoucher(voucher: voucher.toJson());

// 3. ONLY create financial transactions if voucher save succeeds
if (success) {
  await _createAccountTransactions(voucher);
}
```

### Account Selection Requirements
- Broker fees require `selectedBrokerAccount.value` to be set
- Munshiana fees require `selectedMunshianaAccount.value` to be set  
- Payment transactions require `accountId` in each transaction

## Voucher Model Structure

### Required Fields
```dart
VoucherModel(
  voucherNumber: String,
  voucherStatus: String,
  departureDate: String,
  productName: String,
  totalNumberOfBags: int,
  brokerName: String,
  driverName: String,
  driverPhoneNumber: String,
  truckNumber: String,
  conveyNoteNumber: String,
  totalFreight: double,
  settledFreight: double,
  brokerType: String, // 'Own' or 'Outsource'
  brokerFees: double,
  munshianaFees: double,
  paymentTransactions: List<Map<String, dynamic>>,
)
```

### Payment Transaction Structure
Each payment transaction contains:
- `id`: Unique identifier
- `voucherId`: Reference to voucher
- `method`: PaymentMethod enum (check, fuelCard, etc.)
- `amount`: Transaction amount
- `accountId`: Account to debit from
- `status`: PaymentStatus enum
- `transactionDate`: When payment was made

## Validation Rules

### Form Validation
- Voucher number must be unique and numeric
- Driver phone must be 11 digits starting with '03'
- Truck number must contain letters and numbers
- Total freight cannot be less than settled freight
- Departure date cannot be in the past

### Business Logic Validation
```dart
// Broker fees validation
if (brokerType.value == 'Outsource' && brokerFees > 0) {
  if (selectedBrokerAccount.value == null) {
    return 'Broker account must be selected for outsource broker fees';
  }
}

// Munshiana fees validation  
if (munshianaFees > 0) {
  if (selectedMunshianaAccount.value == null) {
    return 'Munshiana account must be selected for munshiana fees';
  }
}
```

## Account Balance Impact

### Positive Impact (Money IN)
- Broker fees when outsource → Increases broker account balance

### Negative Impact (Money OUT)
- Munshiana fees → Decreases munshiana account balance
- Payment transactions → Decreases payment account balances

## Error Handling Strategy

### Transaction Creation Errors
- Log all errors with detailed context
- Show user-friendly error messages
- Continue voucher operation even if some transactions fail
- Never rollback voucher creation due to transaction failures

### Account Validation Errors
```dart
if (account == null || account.id.isEmpty) {
  log('ERROR: Account not selected or invalid');
  SnackbarUtils.showError('Account Error', 'Please select a valid account');
  return;
}
```

## UI Integration Patterns

### Form Controllers
- Use separate controllers for each input field
- Implement proper validation methods
- Clear forms after successful submission

### Account Selection Dropdowns
- Load accounts from Firebase on controller init
- Filter by current user UID
- Update selected account variables on change

### Payment Transaction Management
- Maintain separate lists for existing vs new payments
- Allow editing only new payments in edit mode
- Update settled freight automatically when payments change

## Performance Considerations

### Lazy Loading
- Load accounts and payees only when needed
- Cache fuel cards in controller
- Use pagination for large datasets

### Auto-refresh Strategy
```dart
// Force refresh related controllers after voucher creation
if (Get.isRegistered<DepositController>()) {
  Get.find<DepositController>().forceRefresh();
}
```

## Testing Scenarios

### Critical Test Cases
1. Create voucher with broker fees (outsource) → Check deposits view
2. Create voucher with munshiana fees → Check expenses view  
3. Create voucher with payment transactions → Check expenses view
4. Verify account balances update correctly
5. Test error handling for missing accounts
6. Validate form validation rules
7. Test edit mode behavior with existing payments

