import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:uuid/uuid.dart';
import 'package:either_dart/either.dart';
import '../../core/shared_services/failure_obj.dart';
import '../../core/shared_services/success_obj.dart';
import '../../core/utils/app_constants/firebase/collection_names.dart';
import '../../models/accounting/financial_report_models.dart';
import '../../models/finance/chart_of_accounts_model.dart';

/// Firebase service for Profit & Loss report operations
class ProfitLossFirebaseService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final String _uid;

  ProfitLossFirebaseService()
      : _uid = FirebaseAuth.instance.currentUser?.uid ?? 'anonymous';

  /// Generate Profit & Loss report for a specific date range
  Future<Either<FailureObj, ProfitLossReport>> generateProfitLoss({
    required DateTime startDate,
    required DateTime endDate,
    required String companyName,
    bool includeInactiveAccounts = false,
    bool includeZeroBalances = false,
  }) async {
    try {
      log('Generating P&L for period: ${startDate.toIso8601String()} to ${endDate.toIso8601String()}');

      if (_uid == 'anonymous') {
        return Left(FailureObj(
          code: 'auth-error',
          message: 'User not authenticated',
        ));
      }

      // Get all revenue and expense accounts
      final accountsSnapshot = await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .where('uid', isEqualTo: _uid)
          .where('category', whereIn: ['revenue', 'expenses'])
          .orderBy('accountNumber')
          .get();

      if (accountsSnapshot.docs.isEmpty) {
        log('No revenue or expense accounts found');
        return Right(ProfitLossReport(
          reportId: const Uuid().v4(),
          companyName: companyName,
          uid: _uid,
          startDate: startDate,
          endDate: endDate,
          generatedAt: DateTime.now(),
          generatedBy: _uid,
          revenueGroups: [],
          expenseGroups: [],
          totalRevenue: 0.0,
          totalExpenses: 0.0,
          grossProfit: 0.0,
        ));
      }

      // Group accounts by category and type
      Map<String, List<ChartOfAccountsModel>> revenueAccounts = {};
      Map<String, List<ChartOfAccountsModel>> expenseAccounts = {};

      for (var doc in accountsSnapshot.docs) {
        final account = ChartOfAccountsModel.fromJson(doc.data());

        // Skip inactive accounts if not included
        if (!includeInactiveAccounts && !account.isActive) continue;

        if (account.category == AccountCategory.revenue) {
          final typeKey = account.accountType.toString().split('.').last;
          revenueAccounts.putIfAbsent(typeKey, () => []).add(account);
        } else if (account.category == AccountCategory.expenses) {
          final typeKey = account.accountType.toString().split('.').last;
          expenseAccounts.putIfAbsent(typeKey, () => []).add(account);
        }
      }

      // Calculate balances for each account
      List<PLAccountGroup> revenueGroups = [];
      List<PLAccountGroup> expenseGroups = [];
      double totalRevenue = 0.0;
      double totalExpenses = 0.0;

      // Process revenue accounts
      for (var entry in revenueAccounts.entries) {
        List<PLAccountItem> accounts = [];
        double groupTotal = 0.0;

        for (var account in entry.value) {
          final balance =
              await _calculateAccountBalance(account.id, startDate, endDate);

          // Skip zero balances if not included
          if (!includeZeroBalances && balance == 0.0) continue;

          accounts.add(PLAccountItem(
            accountId: account.id,
            accountNumber: account.accountNumber,
            accountName: account.accountName,
            amount: balance.abs(), // Revenue amounts are positive
          ));

          groupTotal += balance.abs();
        }

        if (accounts.isNotEmpty) {
          revenueGroups.add(PLAccountGroup(
            groupName: _formatGroupName(entry.key),
            groupType: 'revenue',
            accounts: accounts,
            groupTotal: groupTotal,
          ));
          totalRevenue += groupTotal;
        }
      }

      // Process expense accounts
      for (var entry in expenseAccounts.entries) {
        List<PLAccountItem> accounts = [];
        double groupTotal = 0.0;

        for (var account in entry.value) {
          final balance =
              await _calculateAccountBalance(account.id, startDate, endDate);

          // Skip zero balances if not included
          if (!includeZeroBalances && balance == 0.0) continue;

          accounts.add(PLAccountItem(
            accountId: account.id,
            accountNumber: account.accountNumber,
            accountName: account.accountName,
            amount: balance.abs(), // Expense amounts are positive
          ));

          groupTotal += balance.abs();
        }

        if (accounts.isNotEmpty) {
          expenseGroups.add(PLAccountGroup(
            groupName: _formatGroupName(entry.key),
            groupType: 'expense',
            accounts: accounts,
            groupTotal: groupTotal,
          ));
          totalExpenses += groupTotal;
        }
      }

      // Calculate net income and gross profit
      final netIncome = totalRevenue - totalExpenses;
      final grossProfit =
          totalRevenue; // Simplified - could be more complex with COGS

      // Create the P&L report
      final report = ProfitLossReport(
        reportId: const Uuid().v4(),
        companyName: companyName,
        uid: _uid,
        startDate: startDate,
        endDate: endDate,
        generatedAt: DateTime.now(),
        generatedBy: _uid,
        revenueGroups: revenueGroups,
        expenseGroups: expenseGroups,
        totalRevenue: totalRevenue,
        totalExpenses: totalExpenses,
        grossProfit: grossProfit,
      );

      log('P&L generated successfully: Revenue: $totalRevenue, Expenses: $totalExpenses, Net Income: $netIncome');

      return Right(report);
    } catch (e) {
      log('Error generating P&L: $e');
      return Left(FailureObj(
        code: 'generation-error',
        message: 'Failed to generate P&L report: $e',
      ));
    }
  }

  /// Calculate account balance for a specific period
  Future<double> _calculateAccountBalance(
      String accountId, DateTime startDate, DateTime endDate) async {
    try {
      // Get all journal entries for this account within the date range
      final entriesSnapshot = await _firestore
          .collection(AppCollection.journalEntriesCollection)
          .where('uid', isEqualTo: _uid)
          .where('entryDate',
              isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
          .where('entryDate', isLessThanOrEqualTo: Timestamp.fromDate(endDate))
          .get();

      double balance = 0.0;

      for (var doc in entriesSnapshot.docs) {
        final entry = doc.data();
        final lineItems = entry['lineItems'] as List<dynamic>? ?? [];

        for (var lineItem in lineItems) {
          if (lineItem['accountId'] == accountId) {
            final debitAmount = (lineItem['debitAmount'] ?? 0.0).toDouble();
            final creditAmount = (lineItem['creditAmount'] ?? 0.0).toDouble();

            // For revenue accounts, credits increase the balance (but we want positive amounts)
            // For expense accounts, debits increase the balance
            balance += debitAmount - creditAmount;
          }
        }
      }

      return balance;
    } catch (e) {
      log('Error calculating account balance for $accountId: $e');
      return 0.0;
    }
  }

  /// Format group name for display
  String _formatGroupName(String typeKey) {
    switch (typeKey) {
      case 'sales':
        return 'Sales Revenue';
      case 'serviceRevenue':
        return 'Service Revenue';
      case 'otherRevenue':
        return 'Other Revenue';
      case 'operatingExpenses':
        return 'Operating Expenses';
      case 'administrativeExpenses':
        return 'Administrative Expenses';
      case 'costOfGoodsSold':
        return 'Cost of Goods Sold';
      case 'depreciation':
        return 'Depreciation';
      case 'interestExpense':
        return 'Interest Expense';
      case 'taxExpense':
        return 'Tax Expense';
      case 'otherExpenses':
        return 'Other Expenses';
      default:
        return typeKey
            .replaceAllMapped(
              RegExp(r'([A-Z])'),
              (match) => ' ${match.group(1)}',
            )
            .trim();
    }
  }

  /// Save P&L report to Firebase
  Future<Either<FailureObj, SuccessObj>> saveProfitLossReport(
      ProfitLossReport report) async {
    try {
      log('Saving P&L report: ${report.reportId}');

      if (_uid == 'anonymous') {
        return Left(FailureObj(
          code: 'auth-error',
          message: 'User not authenticated',
        ));
      }

      await _firestore
          .collection('profit_loss_reports')
          .doc(report.reportId)
          .set(report.toJson());

      log('P&L report saved successfully');
      return Right(SuccessObj(message: 'P&L report saved successfully'));
    } catch (e) {
      log('Error saving P&L report: $e');
      return Left(FailureObj(
        code: 'save-error',
        message: 'Failed to save P&L report: $e',
      ));
    }
  }

  /// Load saved P&L reports
  Future<Either<FailureObj, List<ProfitLossReport>>>
      getSavedProfitLossReports() async {
    try {
      log('Loading saved P&L reports');

      if (_uid == 'anonymous') {
        return Left(FailureObj(
          code: 'auth-error',
          message: 'User not authenticated',
        ));
      }

      final snapshot = await _firestore
          .collection('profit_loss_reports')
          .where('uid', isEqualTo: _uid)
          .orderBy('generatedAt', descending: true)
          .get();

      final reports = snapshot.docs
          .map((doc) => ProfitLossReport.fromJson(doc.data()))
          .toList();

      log('Loaded ${reports.length} saved P&L reports');
      return Right(reports);
    } catch (e) {
      log('Error loading saved P&L reports: $e');
      return Left(FailureObj(
        code: 'load-error',
        message: 'Failed to load saved P&L reports: $e',
      ));
    }
  }

  /// Load specific P&L report by ID
  Future<Either<FailureObj, ProfitLossReport>> getProfitLossReport(
      String reportId) async {
    try {
      log('Loading P&L report: $reportId');

      if (_uid == 'anonymous') {
        return Left(FailureObj(
          code: 'auth-error',
          message: 'User not authenticated',
        ));
      }

      final doc = await _firestore
          .collection('profit_loss_reports')
          .doc(reportId)
          .get();

      if (!doc.exists) {
        return Left(FailureObj(
          code: 'not-found',
          message: 'P&L report not found',
        ));
      }

      final report = ProfitLossReport.fromJson(doc.data()!);
      log('P&L report loaded successfully');
      return Right(report);
    } catch (e) {
      log('Error loading P&L report: $e');
      return Left(FailureObj(
        code: 'load-error',
        message: 'Failed to load P&L report: $e',
      ));
    }
  }

  /// Delete P&L report
  Future<Either<FailureObj, SuccessObj>> deleteProfitLossReport(
      String reportId) async {
    try {
      log('Deleting P&L report: $reportId');

      if (_uid == 'anonymous') {
        return Left(FailureObj(
          code: 'auth-error',
          message: 'User not authenticated',
        ));
      }

      await _firestore.collection('profit_loss_reports').doc(reportId).delete();

      log('P&L report deleted successfully');
      return Right(SuccessObj(message: 'P&L report deleted successfully'));
    } catch (e) {
      log('Error deleting P&L report: $e');
      return Left(FailureObj(
        code: 'delete-error',
        message: 'Failed to delete P&L report: $e',
      ));
    }
  }
}
