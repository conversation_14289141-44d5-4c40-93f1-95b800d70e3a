import 'package:flutter/material.dart';
import 'package:logestics/core/utils/app_constants/assets/app_assets.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import '../../../../../../core/utils/widgets/on_boarding_card.dart';

class OnBoardingComponent extends StatelessWidget {
  const OnBoardingComponent({super.key});

  @override
  Widget build(BuildContext context) {
    var screenHeight = MediaQuery.of(context).size.height;
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        <PERSON><PERSON><PERSON><PERSON>(
          height: screenHeight * 0.1,
        ),
        OnBoardingCard(
          title: AppStrings.automateBusiness,
          paragraph: "",
          imagePath: AppAssets.onBoardingPng1,
        ),
        SizedBox(
          height: screenHeight * 0.1,
        ),
        Text(
          AppStrings.companySigne,
          style: Theme.of(context)
              .textTheme
              .headlineSmall
              ?.copyWith(fontSize: screenHeight * 0.006 * screenHeight * 0.006),
        ),
        <PERSON>zedBox(
          height: screenHeight * 0.1,
        ),
      ],
    );
  }
}
