import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/models/locations/station_model.dart';
import 'package:logestics/features/locations/repositories/station_repository.dart';

class CreateStationUseCase {
  final StationRepository _stationRepository;

  CreateStationUseCase(this._stationRepository);

  /// Executes the use case to create an Station.
  Future<Either<FailureObj, SuccessObj>> call({
    required StationModel station,
  }) async {
    // Call the repository
    return await _stationRepository.createStation(
      station: station,
    );
  }
}
