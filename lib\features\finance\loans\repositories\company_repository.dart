import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/models/user_model.dart';
import 'package:logestics/firebase_service/finance/company_firebase_service.dart';

abstract class CompanyRepository {
  Future<Either<FailureObj, List<UserModel>>> getAllUsers();
  Future<Either<FailureObj, UserModel>> getUserById(String userId);
  Future<Either<FailureObj, List<UserModel>>> searchUsers(String query);
}

class CompanyRepositoryImpl implements CompanyRepository {
  final CompanyFirebaseService firebaseService;

  CompanyRepositoryImpl(this.firebaseService);

  @override
  Future<Either<FailureObj, List<UserModel>>> getAllUsers() async {
    return await firebaseService.getAllUsers();
  }

  @override
  Future<Either<FailureObj, UserModel>> getUserById(String userId) async {
    return await firebaseService.getUserById(userId);
  }

  @override
  Future<Either<FailureObj, List<UserModel>>> searchUsers(String query) async {
    return await firebaseService.searchUsers(query);
  }
}
