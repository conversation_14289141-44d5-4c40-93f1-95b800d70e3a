import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/features/locations/repositories/zone_repository.dart';

class DeleteZoneUseCase {
  final ZoneRepository _zoneRepository;

  DeleteZoneUseCase(this._zoneRepository);

  /// Executes the use case to delete an Zone.
  Future<Either<FailureObj, SuccessObj>> call({
    required String zoneId,
  }) async {
    // Validate inputs before calling the repository

    if (zoneId.isEmpty) {
      return Left(FailureObj(
        code: 'invalid-Zone',
        message: 'Zone "zoneId" must not be empty.',
      ));
    }

    // Call the repository
    return await _zoneRepository.deleteZone(
      zoneId: zoneId,
    );
  }
}
