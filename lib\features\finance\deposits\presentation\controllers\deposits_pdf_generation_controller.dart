import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/features/finance/deposits/repository/deposit_repository.dart';
import 'package:logestics/features/finance/accounts/repositories/account_repository.dart';
import 'package:logestics/features/finance/payers/repository/payer_repository.dart';
import 'package:logestics/features/finance/deposit_categories/repositories/deposit_category_repository.dart';
import 'package:logestics/models/finance/deposit_model.dart';
import 'package:logestics/models/finance/account_model.dart';
import 'package:logestics/models/finance/payer_model.dart';
import 'package:logestics/models/finance/deposit_category_model.dart';
import 'package:logestics/models/user_model.dart';
import 'package:logestics/firebase_service/firebase_auth_service.dart';
import 'package:logestics/firebase_service/finance/company_firebase_service.dart';
import 'package:logestics/services/pdf_generation_service.dart';

class DepositsPDFGenerationController extends GetxController {
  final DepositRepository depositRepository;
  final AccountRepository accountRepository;
  final PayerRepository payerRepository;
  final DepositCategoryRepository categoryRepository;
  final PDFGenerationService pdfService;
  final FirebaseAuthService authService;
  final CompanyFirebaseService companyService;

  // Observable data
  final deposits = <DepositModel>[].obs;
  final accounts = <AccountModel>[].obs;
  final payers = <PayerModel>[].obs;
  final categories = <DepositCategoryModel>[].obs;
  final filteredDeposits = <DepositModel>[].obs;

  // Selected filters
  final selectedAccounts = <AccountModel>[].obs;
  final selectedPayers = <PayerModel>[].obs;
  final selectedCategories = <DepositCategoryModel>[].obs;
  final startDate = Rxn<DateTime>();
  final endDate = Rxn<DateTime>();
  final minAmount = Rxn<double>();
  final maxAmount = Rxn<double>();

  // Form controllers
  final companyNameController = TextEditingController();
  final companyAddressController = TextEditingController();
  final customTitleController = TextEditingController();
  final minAmountController = TextEditingController();
  final maxAmountController = TextEditingController();

  // State
  final isLoading = false.obs;
  final isLoadingData = false.obs;
  final isLoadingUser = false.obs;
  final currentUser = Rxn<UserModel>();
  final userLoadError = RxnString();

  // Computed properties
  bool get isDateRangeValid =>
      startDate.value != null &&
      endDate.value != null &&
      !startDate.value!.isAfter(endDate.value!);

  bool get isUserDataReady => currentUser.value != null && !isLoadingUser.value;

  bool get canGeneratePDF =>
      isDateRangeValid &&
      !isLoading.value &&
      isUserDataReady &&
      filteredDeposits.isNotEmpty;

  DepositsPDFGenerationController({
    required this.depositRepository,
    required this.accountRepository,
    required this.payerRepository,
    required this.categoryRepository,
    required this.pdfService,
    required this.authService,
    required this.companyService,
  });

  @override
  void onInit() {
    super.onInit();
    _initializeData();
    _setupAmountListeners();
  }

  @override
  void onClose() {
    companyNameController.dispose();
    companyAddressController.dispose();
    customTitleController.dispose();
    minAmountController.dispose();
    maxAmountController.dispose();
    super.onClose();
  }

  void _setupAmountListeners() {
    minAmountController.addListener(() {
      final value = double.tryParse(minAmountController.text);
      minAmount.value = value;
      applyFilters();
    });

    maxAmountController.addListener(() {
      final value = double.tryParse(maxAmountController.text);
      maxAmount.value = value;
      applyFilters();
    });
  }

  /// Initialize data
  Future<void> _initializeData() async {
    try {
      isLoadingData.value = true;

      // Load current user first (critical for PDF generation)
      await _loadCurrentUser();

      // Load all data in parallel
      await Future.wait([
        _loadDeposits(),
        _loadAccounts(),
        _loadPayers(),
        _loadCategories(),
      ]);

      // Set default date range (last 30 days)
      final now = DateTime.now();
      endDate.value = now;
      startDate.value = DateTime(now.year, now.month - 1, now.day);

      applyFilters();
    } catch (e) {
      log('Error initializing data: $e');
      SnackbarUtils.showError('Error', 'Failed to load data: $e');
    } finally {
      isLoadingData.value = false;
    }
  }

  /// Load deposits
  Future<void> _loadDeposits() async {
    try {
      final result = await depositRepository.getDeposits();
      result.fold(
        (failure) {
          log('Failed to load deposits: ${failure.message}');
          SnackbarUtils.showError(
              'Error', 'Failed to load deposits: ${failure.message}');
        },
        (depositList) {
          deposits.value = depositList;
          log('Loaded ${depositList.length} deposits successfully');
        },
      );
    } catch (e) {
      log('Error loading deposits: $e');
      SnackbarUtils.showError('Error', 'Unexpected error loading deposits: $e');
    }
  }

  /// Load accounts
  Future<void> _loadAccounts() async {
    final result = await accountRepository.getAccounts();
    result.fold(
      (failure) => log('Failed to load accounts: ${failure.message}'),
      (accountList) => accounts.value = accountList,
    );
  }

  /// Load payers
  Future<void> _loadPayers() async {
    final result = await payerRepository.getPayers();
    result.fold(
      (failure) => log('Failed to load payers: ${failure.message}'),
      (payerList) => payers.value = payerList,
    );
  }

  /// Load categories
  Future<void> _loadCategories() async {
    final result = await categoryRepository.getCategories();
    result.fold(
      (failure) => log('Failed to load categories: ${failure.message}'),
      (categoryList) => categories.value = categoryList,
    );
  }

  /// Apply filters to deposits
  void applyFilters() {
    var filtered = deposits.toList();

    // Account filter
    if (selectedAccounts.isNotEmpty) {
      final accountIds = selectedAccounts.map((a) => a.id).toSet();
      filtered =
          filtered.where((d) => accountIds.contains(d.accountId)).toList();
    }

    // Payer filter
    if (selectedPayers.isNotEmpty) {
      final payerIds = selectedPayers.map((p) => p.id).toSet();
      filtered = filtered.where((d) => payerIds.contains(d.payerId)).toList();
    }

    // Category filter
    if (selectedCategories.isNotEmpty) {
      final categoryIds = selectedCategories.map((c) => c.id).toSet();
      filtered =
          filtered.where((d) => categoryIds.contains(d.categoryId)).toList();
    }

    // Date range filter
    if (startDate.value != null && endDate.value != null) {
      final start = startDate.value!;
      final end = DateTime(endDate.value!.year, endDate.value!.month,
          endDate.value!.day, 23, 59, 59);
      filtered = filtered
          .where((d) =>
              d.createdAt.isAfter(start.subtract(const Duration(days: 1))) &&
              d.createdAt.isBefore(end.add(const Duration(days: 1))))
          .toList();
    }

    // Amount range filter
    if (minAmount.value != null) {
      filtered = filtered.where((d) => d.amount >= minAmount.value!).toList();
    }
    if (maxAmount.value != null) {
      filtered = filtered.where((d) => d.amount <= maxAmount.value!).toList();
    }

    filteredDeposits.value = filtered;
    log('Applied filters: ${filteredDeposits.length} deposits match criteria');
  }

  /// Set date range
  void setDateRange(DateTime start, DateTime end) {
    startDate.value = start;
    endDate.value = end;
    applyFilters();
  }

  /// Toggle account selection
  void toggleAccountSelection(AccountModel account) {
    if (selectedAccounts.contains(account)) {
      selectedAccounts.remove(account);
    } else {
      selectedAccounts.add(account);
    }
    applyFilters();
  }

  /// Toggle payer selection
  void togglePayerSelection(PayerModel payer) {
    if (selectedPayers.contains(payer)) {
      selectedPayers.remove(payer);
    } else {
      selectedPayers.add(payer);
    }
    applyFilters();
  }

  /// Toggle category selection
  void toggleCategorySelection(DepositCategoryModel category) {
    if (selectedCategories.contains(category)) {
      selectedCategories.remove(category);
    } else {
      selectedCategories.add(category);
    }
    applyFilters();
  }

  /// Clear all filters
  void clearAllFilters() {
    selectedAccounts.clear();
    selectedPayers.clear();
    selectedCategories.clear();
    startDate.value = null;
    endDate.value = null;
    minAmount.value = null;
    maxAmount.value = null;
    minAmountController.clear();
    maxAmountController.clear();
    applyFilters();
  }

  /// Validate that date range is provided (mandatory filter)
  bool _validateDateRange() {
    if (startDate.value == null || endDate.value == null) {
      SnackbarUtils.showError('Date Range Required',
          'Please select both start date and end date to generate the report');
      return false;
    }

    if (startDate.value!.isAfter(endDate.value!)) {
      SnackbarUtils.showError(
          'Invalid Date Range', 'Start date cannot be after end date');
      return false;
    }

    return true;
  }

  /// Generate PDF
  Future<void> generatePDF() async {
    try {
      // Validation - Date range is mandatory
      if (!_validateDateRange()) {
        return;
      }

      if (currentUser.value == null) {
        SnackbarUtils.showError('Error', 'User information not available');
        return;
      }

      if (deposits.isEmpty) {
        SnackbarUtils.showError('Error',
            'No deposits available in the system. Please add some deposits first.');
        return;
      }

      if (filteredDeposits.isEmpty) {
        final dateRange = startDate.value != null && endDate.value != null
            ? 'from ${DateFormat('dd/MM/yyyy').format(startDate.value!)} to ${DateFormat('dd/MM/yyyy').format(endDate.value!)}'
            : 'for the selected criteria';
        SnackbarUtils.showError('No Data Found',
            'No deposits found $dateRange. Please adjust your filters and try again.');
        return;
      }

      isLoading.value = true;

      log('Starting PDF generation with ${filteredDeposits.length} deposits');
      log('Date range: ${startDate.value} to ${endDate.value}');
      log('User: ${currentUser.value!.companyName}');

      // Generate PDF
      final pdfBytes = await pdfService.generateDepositsReportPDF(
        deposits: filteredDeposits,
        currentUser: currentUser.value!,
        startDate: startDate.value,
        endDate: endDate.value,
        selectedAccounts: selectedAccounts,
        selectedPayers: selectedPayers,
        selectedCategories: selectedCategories,
        minAmount: minAmount.value,
        maxAmount: maxAmount.value,
        companyName: companyNameController.text.isNotEmpty
            ? companyNameController.text
            : null,
        companyAddress: companyAddressController.text.isNotEmpty
            ? companyAddressController.text
            : null,
        customTitle: customTitleController.text.isNotEmpty
            ? customTitleController.text
            : null,
      );

      log('PDF generated successfully. Size: ${pdfBytes.length} bytes');

      // Generate filename
      final dateFormat = DateFormat('yyyy-MM-dd');
      final startDateStr =
          startDate.value != null ? dateFormat.format(startDate.value!) : 'All';
      final endDateStr =
          endDate.value != null ? dateFormat.format(endDate.value!) : 'Time';
      final fileName = 'Deposits_Report_${startDateStr}_to_$endDateStr.pdf';

      // Preview PDF
      await pdfService.previewPDF(pdfBytes, fileName);

      SnackbarUtils.showSuccess('Success', 'PDF generated successfully');
    } catch (e) {
      log('Error generating PDF: $e');
      String errorMessage = 'Failed to generate PDF';
      if (e.toString().contains('No data')) {
        errorMessage = 'No data available for PDF generation';
      } else if (e.toString().contains('permission')) {
        errorMessage = 'Permission denied. Please check your browser settings.';
      } else if (e.toString().contains('network')) {
        errorMessage = 'Network error. Please check your connection.';
      } else {
        errorMessage = 'Failed to generate PDF: ${e.toString()}';
      }
      SnackbarUtils.showError('PDF Generation Error', errorMessage);
    } finally {
      isLoading.value = false;
    }
  }

  /// Share PDF
  Future<void> sharePDF() async {
    try {
      // Validation - Date range is mandatory
      if (!_validateDateRange()) {
        return;
      }

      if (currentUser.value == null) {
        SnackbarUtils.showError('Error', 'User information not available');
        return;
      }

      if (deposits.isEmpty) {
        SnackbarUtils.showError('Error',
            'No deposits available in the system. Please add some deposits first.');
        return;
      }

      if (filteredDeposits.isEmpty) {
        final dateRange = startDate.value != null && endDate.value != null
            ? 'from ${DateFormat('dd/MM/yyyy').format(startDate.value!)} to ${DateFormat('dd/MM/yyyy').format(endDate.value!)}'
            : 'for the selected criteria';
        SnackbarUtils.showError('No Data Found',
            'No deposits found $dateRange. Please adjust your filters and try again.');
        return;
      }

      isLoading.value = true;

      log('Starting PDF sharing with ${filteredDeposits.length} deposits');
      log('Date range: ${startDate.value} to ${endDate.value}');
      log('User: ${currentUser.value!.companyName}');

      // Generate PDF
      final pdfBytes = await pdfService.generateDepositsReportPDF(
        deposits: filteredDeposits,
        currentUser: currentUser.value!,
        startDate: startDate.value,
        endDate: endDate.value,
        selectedAccounts: selectedAccounts,
        selectedPayers: selectedPayers,
        selectedCategories: selectedCategories,
        minAmount: minAmount.value,
        maxAmount: maxAmount.value,
        companyName: companyNameController.text.isNotEmpty
            ? companyNameController.text
            : null,
        companyAddress: companyAddressController.text.isNotEmpty
            ? companyAddressController.text
            : null,
        customTitle: customTitleController.text.isNotEmpty
            ? customTitleController.text
            : null,
      );

      // Generate filename
      final dateFormat = DateFormat('yyyy-MM-dd');
      final startDateStr =
          startDate.value != null ? dateFormat.format(startDate.value!) : 'All';
      final endDateStr =
          endDate.value != null ? dateFormat.format(endDate.value!) : 'Time';
      final fileName = 'Deposits_Report_${startDateStr}_to_$endDateStr.pdf';

      // Share PDF
      await pdfService.sharePDF(pdfBytes, fileName);

      SnackbarUtils.showSuccess('Success', 'PDF shared successfully');
    } catch (e) {
      log('Error sharing PDF: $e');
      String errorMessage = 'Failed to share PDF';
      if (e.toString().contains('No data')) {
        errorMessage = 'No data available for PDF sharing';
      } else if (e.toString().contains('permission')) {
        errorMessage = 'Permission denied. Please check your browser settings.';
      } else if (e.toString().contains('network')) {
        errorMessage = 'Network error. Please check your connection.';
      } else {
        errorMessage = 'Failed to share PDF: ${e.toString()}';
      }
      SnackbarUtils.showError('PDF Sharing Error', errorMessage);
    } finally {
      isLoading.value = false;
    }
  }

  /// Load current user with proper error handling and loading states
  Future<void> _loadCurrentUser() async {
    try {
      isLoadingUser.value = true;
      userLoadError.value = null;

      log('Loading current user for PDF generation...');

      // Check if authService is properly initialized
      if (!authService.isAuthenticated) {
        log('AuthService indicates user is not authenticated');
        userLoadError.value =
            'Authentication session expired. Please log in again.';
        SnackbarUtils.showError('Authentication Required',
            'Your session has expired. Please log in again to generate PDFs.');
        return;
      }

      // Get authenticated user
      final authUser = authService.currentUser;
      if (authUser == null) {
        log('AuthService currentUser is null');
        userLoadError.value =
            'Authentication session not found. Please log in again.';
        SnackbarUtils.showError('Authentication Error',
            'Unable to verify your identity. Please log in again.');
        return;
      }

      log('Found authenticated user: ${authUser.email}');

      // Load user details from database
      log('Loading user details from database for UID: ${authUser.uid}');
      final result = await companyService.getUserById(authUser.uid);
      result.fold(
        (failure) {
          final errorMessage =
              'Failed to load user profile: ${failure.message}';
          userLoadError.value = errorMessage;
          log('Error loading user from database: ${failure.message}');

          // Try to create a fallback user from auth data
          log('Creating fallback user from authentication data');
          _createFallbackUser(authUser);
        },
        (userModel) {
          currentUser.value = userModel;
          userLoadError.value = null;
          log('User loaded successfully from database: ${userModel.companyName}');
        },
      );
    } catch (e) {
      final errorMessage = 'Unexpected error loading user: $e';
      userLoadError.value = errorMessage;
      log('Error loading user details: $e');
      SnackbarUtils.showError('Error', errorMessage);
    } finally {
      isLoadingUser.value = false;
    }
  }

  /// Retry loading user data
  Future<void> retryLoadUser() async {
    await _loadCurrentUser();
  }

  /// Create fallback user from authentication data when database load fails
  void _createFallbackUser(dynamic authUser) {
    try {
      final fallbackUser = UserModel(
        uid: authUser.uid,
        email: authUser.email ?? '',
        companyName:
            authUser.displayName ?? _extractNameFromEmail(authUser.email ?? ''),
        phoneNumber: authUser.phoneNumber ?? '',
      );

      currentUser.value = fallbackUser;
      userLoadError.value = null;
      log('Fallback user created successfully: ${fallbackUser.companyName}');

      SnackbarUtils.showInfo('User Profile',
          'Using basic profile information. You can update your company details in settings.');
    } catch (e) {
      log('Error creating fallback user: $e');
      userLoadError.value = 'Failed to create user profile: $e';
      SnackbarUtils.showError('Profile Error',
          'Unable to load user profile. Please try logging in again.');
    }
  }

  /// Extract name from email for fallback user creation
  String _extractNameFromEmail(String email) {
    if (email.isEmpty) return 'User';

    // Extract name from email (before @)
    final name = email.split('@').first;

    // Capitalize first letter and replace dots/underscores with spaces
    return name
        .replaceAll(RegExp(r'[._]'), ' ')
        .split(' ')
        .map((word) => word.isNotEmpty
            ? '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}'
            : '')
        .join(' ')
        .trim();
  }
}
