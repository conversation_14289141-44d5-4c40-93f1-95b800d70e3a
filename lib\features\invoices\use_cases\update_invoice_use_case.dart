import 'package:dartz/dartz.dart';
import 'package:logestics/models/invoice_model.dart';

import '../../../core/shared_services/failure_obj.dart';
import '../../../core/shared_services/success_obj.dart';
import '../repositories/invoice_repository.dart';

class UpdateInvoiceUseCase {
  final InvoiceRepository _invoiceRepository;

  UpdateInvoiceUseCase(this._invoiceRepository);

  /// Executes the use case to update an invoice.
  Future<Either<FailureObj, SuccessObj>> call({
    required String uid,
    required InvoiceModel invoice,
  }) async {
    // Validate inputs
    if (uid.isEmpty) {
      return Left(FailureObj(
        code: 'invalid-company-id',
        message: 'Company ID cannot be empty.',
      ));
    }

    if (invoice.tasNumber.isEmpty) {
      return Left(FailureObj(
        code: 'invalid-invoice',
        message: 'Invoice "tasNumber" must not be empty or null.',
      ));
    }

    // Call the repository
    return await _invoiceRepository.updateInvoice(
      uid: uid,
      invoice: invoice,
    );
  }
}
