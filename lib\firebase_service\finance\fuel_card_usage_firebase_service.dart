import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dartz/dartz.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';
import 'package:logestics/models/finance/fuel_card_usage_model.dart';
import 'package:uuid/uuid.dart';

class FuelCardUsageFirebaseService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final String _collectionPath = AppCollection.fuelCardUsageCollection;

  // Get current user ID
  String get _uid => FirebaseAuth.instance.currentUser?.uid ?? 'anonymous';

  // Create a new fuel card usage record
  Future<Either<FailureObj, SuccessObj>> createFuelCardUsage(
      FuelCardUsageModel usage) async {
    try {
      // Create a new document with auto-generated ID if not provided
      final id = usage.id.isEmpty ? const Uuid().v4() : usage.id;
      final updatedUsage = usage.copyWith(id: id, uid: _uid);

      // Add the usage record with user ID
      final usageData = updatedUsage.toMap();

      await _firestore.collection(_collectionPath).doc(id).set(usageData);

      log('Created fuel card usage record: $id');
      return Right(SuccessObj(
        message: 'Fuel card usage recorded successfully',
      ));
    } catch (e) {
      log('Error creating fuel card usage: $e');
      return Left(FailureObj(
        code: 'fuel-card-usage-create-error',
        message: 'Failed to record fuel card usage: $e',
      ));
    }
  }

  // Get all fuel card usage records for the current user
  Future<Either<FailureObj, List<FuelCardUsageModel>>>
      getFuelCardUsage() async {
    try {
      final querySnapshot = await _firestore
          .collection(_collectionPath)
          .where('uid', isEqualTo: _uid)
          .orderBy('usageDate', descending: true)
          .get();

      final usageRecords = querySnapshot.docs
          .map((doc) => FuelCardUsageModel.fromMap(doc.data()))
          .toList();

      log('Retrieved ${usageRecords.length} fuel card usage records');
      return Right(usageRecords);
    } catch (e) {
      log('Error getting fuel card usage: $e');
      return Left(FailureObj(
        code: 'fuel-card-usage-get-error',
        message: 'Failed to get fuel card usage: $e',
      ));
    }
  }

  // Get fuel card usage records for a specific fuel card
  Future<Either<FailureObj, List<FuelCardUsageModel>>> getFuelCardUsageByCard(
      String fuelCardId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collectionPath)
          .where('uid', isEqualTo: _uid)
          .where('fuelCardId', isEqualTo: fuelCardId)
          .orderBy('usageDate', descending: true)
          .get();

      final usageRecords = querySnapshot.docs
          .map((doc) => FuelCardUsageModel.fromMap(doc.data()))
          .toList();

      log('Retrieved ${usageRecords.length} usage records for fuel card: $fuelCardId');
      return Right(usageRecords);
    } catch (e) {
      log('Error getting fuel card usage by card: $e');
      return Left(FailureObj(
        code: 'fuel-card-usage-get-error',
        message: 'Failed to get fuel card usage: $e',
      ));
    }
  }

  // Get fuel card usage records for a specific voucher
  Future<Either<FailureObj, List<FuelCardUsageModel>>>
      getFuelCardUsageByVoucher(String voucherId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collectionPath)
          .where('uid', isEqualTo: _uid)
          .where('voucherId', isEqualTo: voucherId)
          .orderBy('usageDate', descending: true)
          .get();

      final usageRecords = querySnapshot.docs
          .map((doc) => FuelCardUsageModel.fromMap(doc.data()))
          .toList();

      log('Retrieved ${usageRecords.length} usage records for voucher: $voucherId');
      return Right(usageRecords);
    } catch (e) {
      log('Error getting fuel card usage by voucher: $e');
      return Left(FailureObj(
        code: 'fuel-card-usage-get-error',
        message: 'Failed to get fuel card usage: $e',
      ));
    }
  }

  // Update a fuel card usage record
  Future<Either<FailureObj, SuccessObj>> updateFuelCardUsage(
      FuelCardUsageModel usage) async {
    try {
      final usageData = usage.toMap();
      await _firestore
          .collection(_collectionPath)
          .doc(usage.id)
          .update(usageData);

      log('Updated fuel card usage record: ${usage.id}');
      return Right(SuccessObj(
        message: 'Fuel card usage updated successfully',
      ));
    } catch (e) {
      log('Error updating fuel card usage: $e');
      return Left(FailureObj(
        code: 'fuel-card-usage-update-error',
        message: 'Failed to update fuel card usage: $e',
      ));
    }
  }

  // Delete a fuel card usage record
  Future<Either<FailureObj, SuccessObj>> deleteFuelCardUsage(String id) async {
    try {
      await _firestore.collection(_collectionPath).doc(id).delete();

      log('Deleted fuel card usage record: $id');
      return Right(SuccessObj(
        message: 'Fuel card usage deleted successfully',
      ));
    } catch (e) {
      log('Error deleting fuel card usage: $e');
      return Left(FailureObj(
        code: 'fuel-card-usage-delete-error',
        message: 'Failed to delete fuel card usage: $e',
      ));
    }
  }

  // Stream to listen for real-time updates to fuel card usage
  Stream<List<FuelCardUsageModel>> listenToFuelCardUsage() {
    try {
      return _firestore
          .collection(_collectionPath)
          .where('uid', isEqualTo: _uid)
          .orderBy('usageDate', descending: true)
          .snapshots()
          .map((snapshot) => snapshot.docs
              .map((doc) => FuelCardUsageModel.fromMap(doc.data()))
              .toList());
    } catch (e) {
      log('Error listening to fuel card usage: $e', error: e);
      return Stream.value([]);
    }
  }

  // Stream to listen for fuel card usage for a specific card
  Stream<List<FuelCardUsageModel>> listenToFuelCardUsageByCard(
      String fuelCardId) {
    try {
      return _firestore
          .collection(_collectionPath)
          .where('uid', isEqualTo: _uid)
          .where('fuelCardId', isEqualTo: fuelCardId)
          .orderBy('usageDate', descending: true)
          .snapshots()
          .map((snapshot) => snapshot.docs
              .map((doc) => FuelCardUsageModel.fromMap(doc.data()))
              .toList());
    } catch (e) {
      log('Error listening to fuel card usage by card: $e', error: e);
      return Stream.value([]);
    }
  }
}
