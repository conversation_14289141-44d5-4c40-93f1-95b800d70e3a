import 'package:cloud_firestore/cloud_firestore.dart';
import '../accounting/financial_report_models.dart';

/// Cash Flow Activity Types
enum CashFlowActivityType {
  operating('Operating Activities'),
  investing('Investing Activities'),
  financing('Financing Activities');

  const CashFlowActivityType(this.displayName);
  final String displayName;

  static CashFlowActivityType fromString(String value) {
    return CashFlowActivityType.values.firstWhere(
      (type) => type.name == value,
      orElse: () => CashFlowActivityType.operating,
    );
  }
}

/// Cash Flow Line Item Model
class CashFlowLineItem {
  final String accountId;
  final String accountNumber;
  final String accountName;
  final String description;
  final double amount;
  final CashFlowActivityType activityType;
  final bool isInflow; // true for cash inflows, false for outflows

  CashFlowLineItem({
    required this.accountId,
    required this.accountNumber,
    required this.accountName,
    required this.description,
    required this.amount,
    required this.activityType,
    required this.isInflow,
  });

  Map<String, dynamic> toJson() {
    return {
      'accountId': accountId,
      'accountNumber': accountNumber,
      'accountName': accountName,
      'description': description,
      'amount': amount,
      'activityType': activityType.name,
      'isInflow': isInflow,
    };
  }

  factory CashFlowLineItem.fromJson(Map<String, dynamic> json) {
    return CashFlowLineItem(
      accountId: json['accountId'] ?? '',
      accountNumber: json['accountNumber'] ?? '',
      accountName: json['accountName'] ?? '',
      description: json['description'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      activityType: CashFlowActivityType.fromString(json['activityType'] ?? 'operating'),
      isInflow: json['isInflow'] ?? false,
    );
  }

  /// Get display amount (negative for outflows)
  double get displayAmount => isInflow ? amount : -amount;
}

/// Cash Flow Activity Section Model
class CashFlowActivitySection {
  final CashFlowActivityType activityType;
  final List<CashFlowLineItem> lineItems;
  final double totalInflows;
  final double totalOutflows;
  final double netCashFlow;

  CashFlowActivitySection({
    required this.activityType,
    required this.lineItems,
    required this.totalInflows,
    required this.totalOutflows,
  }) : netCashFlow = totalInflows - totalOutflows;

  Map<String, dynamic> toJson() {
    return {
      'activityType': activityType.name,
      'lineItems': lineItems.map((item) => item.toJson()).toList(),
      'totalInflows': totalInflows,
      'totalOutflows': totalOutflows,
      'netCashFlow': netCashFlow,
    };
  }

  factory CashFlowActivitySection.fromJson(Map<String, dynamic> json) {
    return CashFlowActivitySection(
      activityType: CashFlowActivityType.fromString(json['activityType'] ?? 'operating'),
      lineItems: (json['lineItems'] as List<dynamic>?)
              ?.map((item) => CashFlowLineItem.fromJson(item))
              .toList() ??
          [],
      totalInflows: (json['totalInflows'] ?? 0).toDouble(),
      totalOutflows: (json['totalOutflows'] ?? 0).toDouble(),
    );
  }

  /// Get line items by type
  List<CashFlowLineItem> get inflowItems => lineItems.where((item) => item.isInflow).toList();
  List<CashFlowLineItem> get outflowItems => lineItems.where((item) => !item.isInflow).toList();
}

/// Cash Flow Statement Report Model
class CashFlowStatementReport extends FinancialReport {
  final CashFlowActivitySection operatingActivities;
  final CashFlowActivitySection investingActivities;
  final CashFlowActivitySection financingActivities;
  final double netIncomeFromPL; // Starting point for operating activities
  final double beginningCashBalance;
  final double endingCashBalance;
  final double netChangeInCash;

  CashFlowStatementReport({
    required super.reportId,
    required super.companyName,
    required super.uid,
    required super.startDate,
    required super.endDate,
    required super.generatedAt,
    required super.generatedBy,
    required this.operatingActivities,
    required this.investingActivities,
    required this.financingActivities,
    required this.netIncomeFromPL,
    required this.beginningCashBalance,
    required this.endingCashBalance,
  }) : netChangeInCash = operatingActivities.netCashFlow + 
                        investingActivities.netCashFlow + 
                        financingActivities.netCashFlow,
       super(reportType: 'cash_flow_statement');

  @override
  String get reportTitle => 'Cash Flow Statement';

  /// Total cash flows from all activities
  double get totalNetCashFlow => 
      operatingActivities.netCashFlow + 
      investingActivities.netCashFlow + 
      financingActivities.netCashFlow;

  /// Verify cash balance reconciliation
  bool get isCashBalanceReconciled => 
      (beginningCashBalance + netChangeInCash - endingCashBalance).abs() < 0.01;

  /// Get all line items across all activities
  List<CashFlowLineItem> get allLineItems => [
    ...operatingActivities.lineItems,
    ...investingActivities.lineItems,
    ...financingActivities.lineItems,
  ];

  @override
  Map<String, dynamic> toJson() {
    return {
      'reportId': reportId,
      'reportType': reportType,
      'companyName': companyName,
      'uid': uid,
      'startDate': Timestamp.fromDate(startDate),
      'endDate': Timestamp.fromDate(endDate),
      'generatedAt': Timestamp.fromDate(generatedAt),
      'generatedBy': generatedBy,
      'operatingActivities': operatingActivities.toJson(),
      'investingActivities': investingActivities.toJson(),
      'financingActivities': financingActivities.toJson(),
      'netIncomeFromPL': netIncomeFromPL,
      'beginningCashBalance': beginningCashBalance,
      'endingCashBalance': endingCashBalance,
      'netChangeInCash': netChangeInCash,
    };
  }

  factory CashFlowStatementReport.fromJson(Map<String, dynamic> json) {
    return CashFlowStatementReport(
      reportId: json['reportId'] ?? '',
      companyName: json['companyName'] ?? '',
      uid: json['uid'] ?? '',
      startDate: (json['startDate'] as Timestamp).toDate(),
      endDate: (json['endDate'] as Timestamp).toDate(),
      generatedAt: (json['generatedAt'] as Timestamp).toDate(),
      generatedBy: json['generatedBy'] ?? '',
      operatingActivities: CashFlowActivitySection.fromJson(json['operatingActivities'] ?? {}),
      investingActivities: CashFlowActivitySection.fromJson(json['investingActivities'] ?? {}),
      financingActivities: CashFlowActivitySection.fromJson(json['financingActivities'] ?? {}),
      netIncomeFromPL: (json['netIncomeFromPL'] ?? 0).toDouble(),
      beginningCashBalance: (json['beginningCashBalance'] ?? 0).toDouble(),
      endingCashBalance: (json['endingCashBalance'] ?? 0).toDouble(),
    );
  }
}

/// Cash Flow Statement Parameters
class CashFlowStatementParameters {
  final DateTime startDate;
  final DateTime endDate;
  final String companyName;
  final bool includeInactiveAccounts;
  final bool includeZeroBalances;
  final bool useDirectMethod; // true for direct method, false for indirect method

  CashFlowStatementParameters({
    required this.startDate,
    required this.endDate,
    required this.companyName,
    this.includeInactiveAccounts = false,
    this.includeZeroBalances = false,
    this.useDirectMethod = false, // Default to indirect method
  });

  Map<String, dynamic> toJson() {
    return {
      'startDate': Timestamp.fromDate(startDate),
      'endDate': Timestamp.fromDate(endDate),
      'companyName': companyName,
      'includeInactiveAccounts': includeInactiveAccounts,
      'includeZeroBalances': includeZeroBalances,
      'useDirectMethod': useDirectMethod,
    };
  }

  factory CashFlowStatementParameters.fromJson(Map<String, dynamic> json) {
    return CashFlowStatementParameters(
      startDate: (json['startDate'] as Timestamp).toDate(),
      endDate: (json['endDate'] as Timestamp).toDate(),
      companyName: json['companyName'] ?? '',
      includeInactiveAccounts: json['includeInactiveAccounts'] ?? false,
      includeZeroBalances: json['includeZeroBalances'] ?? false,
      useDirectMethod: json['useDirectMethod'] ?? false,
    );
  }
}
