import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/widgets/pagination_widget.dart';
import 'package:logestics/features/finance/check_usage/presentation/controllers/check_usage_controller.dart';
import 'package:logestics/features/finance/check_usage/presentation/widgets/check_usage_filters.dart';
import 'package:logestics/main.dart';
import 'package:logestics/models/finance/check_usage_model.dart';
import 'package:provider/provider.dart';

class CheckUsageView extends StatelessWidget {
  const CheckUsageView({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<CheckUsageController>();
    var width = Get.width;
    notifier = Provider.of(context, listen: true);

    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          child: Column(
            children: [
              // Header with filter toggle
              SizedBox(
                height: width < 650 ? 55 : 40,
                width: width,
                child: width < 650
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                'Check Usage Records',
                                overflow: TextOverflow.ellipsis,
                                style: AppTextStyles.titleStyle
                                    .copyWith(color: notifier.text),
                              ),
                              const Spacer(),
                              Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  IconButton(
                                    onPressed: () =>
                                        _selectExpiryDateForSorting(
                                            context, controller),
                                    icon: const Icon(Icons.date_range),
                                    tooltip: 'Filter by Expiry Date',
                                  ),
                                  Obx(() => controller
                                              .selectedExpiryDateFilter.value !=
                                          null
                                      ? IconButton(
                                          onPressed: () => controller
                                              .clearExpiryDateFilter(),
                                          icon:
                                              const Icon(Icons.clear, size: 18),
                                          tooltip: 'Clear Date Filter',
                                          padding: const EdgeInsets.all(4),
                                          constraints: const BoxConstraints(
                                            minWidth: 32,
                                            minHeight: 32,
                                          ),
                                        )
                                      : const SizedBox.shrink()),
                                ],
                              ),
                              IconButton(
                                onPressed: () => controller.toggleFilters(),
                                icon: Obx(() => Icon(
                                      controller.showFilters.value
                                          ? Icons.filter_list_off
                                          : Icons.filter_list,
                                      color: notifier.text,
                                    )),
                                tooltip: 'Toggle Filters',
                              ),
                            ],
                          ),
                        ],
                      )
                    : Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Check Usage Records',
                            overflow: TextOverflow.ellipsis,
                            style: AppTextStyles.titleStyle
                                .copyWith(color: notifier.text),
                          ),
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              IconButton(
                                onPressed: () => _selectExpiryDateForSorting(
                                    context, controller),
                                icon: const Icon(Icons.date_range),
                                tooltip: 'Filter by Expiry Date',
                              ),
                              Obx(() => controller
                                          .selectedExpiryDateFilter.value !=
                                      null
                                  ? IconButton(
                                      onPressed: () =>
                                          controller.clearExpiryDateFilter(),
                                      icon: const Icon(Icons.clear, size: 18),
                                      tooltip: 'Clear Date Filter',
                                      padding: const EdgeInsets.all(4),
                                      constraints: const BoxConstraints(
                                        minWidth: 32,
                                        minHeight: 32,
                                      ),
                                    )
                                  : const SizedBox.shrink()),
                              IconButton(
                                onPressed: () => controller.toggleFilters(),
                                icon: Obx(() => Icon(
                                      controller.showFilters.value
                                          ? Icons.filter_list_off
                                          : Icons.filter_list,
                                      color: notifier.text,
                                    )),
                                tooltip: 'Toggle Filters',
                              ),
                            ],
                          ),
                        ],
                      ),
              ),

              const SizedBox(height: 20),

              // Filter section
              Obx(() => controller.showFilters.value
                  ? CheckUsageFilters(controller: controller)
                  : const SizedBox.shrink()),

              if (controller.showFilters.value) const SizedBox(height: 20),

              // Check usage table
              SizedBox(
                height: 570,
                child: _buildCheckUsageTable(controller),
              ),

              const SizedBox(height: 20),

              // Pagination with loading indicator
              Obx(() => Column(
                    children: [
                      // Page loading indicator
                      if (controller.isLoadingPage.value)
                        Container(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const SizedBox(
                                width: 16,
                                height: 16,
                                child:
                                    CircularProgressIndicator(strokeWidth: 2),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'Loading page ${controller.currentPage.value}...',
                                style: TextStyle(
                                  color: notifier.text.withOpacity(0.7),
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),

                      // Error indicator
                      if (controller.lastError.value.isNotEmpty)
                        Container(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.error_outline,
                                  color: Colors.red, size: 16),
                              const SizedBox(width: 8),
                              Text(
                                'Error loading page. ',
                                style:
                                    TextStyle(color: Colors.red, fontSize: 12),
                              ),
                              TextButton(
                                onPressed: () => controller.loadPageIfNeeded(
                                    controller.currentPage.value),
                                child: const Text('Retry',
                                    style: TextStyle(fontSize: 12)),
                              ),
                            ],
                          ),
                        ),

                      // Pagination widget
                      PaginationWidget(
                        currentPage: controller.currentPage.value,
                        totalPages: controller.totalPages,
                        itemsPerPage: controller.itemsPerPage.value,
                        onPageChanged: (page) =>
                            controller.setCurrentPage(page),
                        onItemsPerPageChanged: (count) =>
                            controller.setItemsPerPage(count),
                      ),
                    ],
                  )),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCheckUsageTable(CheckUsageController controller) {
    return Container(
      decoration: BoxDecoration(
        color: notifier.getBgColor,
        borderRadius: BorderRadius.circular(10),
      ),
      padding: const EdgeInsets.symmetric(vertical: 15),
      child: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        final checks = controller.paginatedCheckUsages;

        if (controller.filteredCheckUsages.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.receipt_long_outlined,
                  size: 64,
                  color: Colors.grey,
                ),
                SizedBox(height: 16),
                Text(
                  'No check usage records found',
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.grey,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Check usage records will appear here when you use check payments',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: DataTable(
            columnSpacing: 20,
            headingRowColor: WidgetStateProperty.all(notifier.getfillborder),
            columns: [
              DataColumn(
                label: Text(
                  'S.No',
                  style: AppTextStyles.titleStyle.copyWith(
                    color: notifier.text,
                    fontSize: 14,
                  ),
                ),
              ),
              DataColumn(
                label: Text(
                  'Check #',
                  style: AppTextStyles.titleStyle.copyWith(
                    color: notifier.text,
                    fontSize: 14,
                  ),
                ),
              ),
              DataColumn(
                label: Text(
                  'Bank',
                  style: AppTextStyles.titleStyle.copyWith(
                    color: notifier.text,
                    fontSize: 14,
                  ),
                ),
              ),
              DataColumn(
                label: Text(
                  'Amount',
                  style: AppTextStyles.titleStyle.copyWith(
                    color: notifier.text,
                    fontSize: 14,
                  ),
                ),
              ),
              DataColumn(
                label: Text(
                  'Payee',
                  style: AppTextStyles.titleStyle.copyWith(
                    color: notifier.text,
                    fontSize: 14,
                  ),
                ),
              ),
              DataColumn(
                label: Text(
                  'Voucher',
                  style: AppTextStyles.titleStyle.copyWith(
                    color: notifier.text,
                    fontSize: 14,
                  ),
                ),
              ),
              DataColumn(
                label: Text(
                  'Type',
                  style: AppTextStyles.titleStyle.copyWith(
                    color: notifier.text,
                    fontSize: 14,
                  ),
                ),
              ),
              DataColumn(
                label: Text(
                  'Company',
                  style: AppTextStyles.titleStyle.copyWith(
                    color: notifier.text,
                    fontSize: 14,
                  ),
                ),
              ),
              DataColumn(
                label: Text(
                  'Status',
                  style: AppTextStyles.titleStyle.copyWith(
                    color: notifier.text,
                    fontSize: 14,
                  ),
                ),
              ),
              DataColumn(
                label: Text(
                  'Actions',
                  style: AppTextStyles.titleStyle.copyWith(
                    color: notifier.text,
                    fontSize: 14,
                  ),
                ),
              ),
            ],
            rows: checks.asMap().entries.map((entry) {
              final index = entry.key;
              final check = entry.value;
              final serialNo = (controller.currentPage.value - 1) *
                      controller.itemsPerPage.value +
                  index +
                  1;
              return _buildCheckRow(serialNo, check, controller);
            }).toList(),
          ),
        );
      }),
    );
  }

  DataRow _buildCheckRow(
      int serialNo, CheckUsageModel check, CheckUsageController controller) {
    return DataRow(
      cells: [
        DataCell(Text(
          serialNo.toString(),
          style: TextStyle(color: notifier.text),
        )),
        DataCell(Text(
          check.checkNumber,
          style: TextStyle(color: notifier.text),
        )),
        DataCell(Text(
          check.bankName,
          style: TextStyle(color: notifier.text),
        )),
        DataCell(Text(
          'PKR ${check.amount.toStringAsFixed(2)}',
          style: TextStyle(color: notifier.text),
        )),
        DataCell(Text(
          check.payeeName,
          style: TextStyle(color: notifier.text),
        )),
        DataCell(Text(
          check.voucherNumber,
          style: TextStyle(color: notifier.text),
        )),
        DataCell(
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: check.checkType == 'own' ? Colors.blue : Colors.orange,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              check.checkType.toUpperCase(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        DataCell(
          check.checkType == 'other' && check.externalCompanyName != null
              ? Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.amber.shade100,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.amber.shade300),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.business,
                          color: Colors.amber.shade700, size: 14),
                      const SizedBox(width: 4),
                      Text(
                        check.externalCompanyName!,
                        style: TextStyle(
                          color: Colors.amber.shade700,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                )
              : Text(
                  'Own Company',
                  style: TextStyle(color: notifier.text, fontSize: 12),
                ),
        ),
        DataCell(
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getStatusColor(check.status),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              check.status.toUpperCase(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        DataCell(
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                onPressed: () => _showCheckDetails(check),
                icon: const Icon(Icons.info_outline, size: 18),
                tooltip: 'View Details',
              ),
              if (check.status == 'issued')
                IconButton(
                  onPressed: () => _showUpdateStatusDialog(check, controller),
                  icon: const Icon(Icons.edit, size: 18),
                  tooltip: 'Update Status',
                ),
            ],
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'issued':
        return Colors.orange;
      case 'cleared':
        return Colors.green;
      case 'bounced':
        return Colors.red;
      case 'cancelled':
        return Colors.grey;
      default:
        return Colors.blue;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showCheckDetails(CheckUsageModel check) {
    Get.dialog(
      AlertDialog(
        title: Text('Check #${check.checkNumber}'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDetailRow(
                  'Amount', 'PKR ${check.amount.toStringAsFixed(2)}'),
              _buildDetailRow('Bank', check.bankName),
              _buildDetailRow('Account', check.accountName),
              _buildDetailRow('Payee', check.payeeName),
              _buildDetailRow('Voucher', check.voucherNumber),
              _buildDetailRow('Status', check.status.toUpperCase()),
              _buildDetailRow('Type', check.checkType.toUpperCase()),
              if (check.externalCompanyName != null)
                _buildDetailRow('External Company', check.externalCompanyName!),
              _buildDetailRow('Issue Date', _formatDate(check.issueDate)),
              if (check.expiryDate != null)
                _buildDetailRow('Expiry Date', _formatDate(check.expiryDate!)),
              _buildDetailRow('Usage Date', _formatDate(check.usageDate)),
              _buildDetailRow('Created Date', _formatDate(check.createdAt)),
              if (check.notes.isNotEmpty) _buildDetailRow('Notes', check.notes),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showUpdateStatusDialog(
      CheckUsageModel check, CheckUsageController controller) {
    Get.dialog(
      AlertDialog(
        title: const Text('Update Check Status'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Check #${check.checkNumber}'),
            const SizedBox(height: 16),
            Text('Current Status: ${check.status.toUpperCase()}'),
            const SizedBox(height: 16),
            const Text('Select new status:'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          if (check.status != 'cleared')
            TextButton(
              onPressed: () {
                controller.updateCheckStatus(check.id, 'cleared');
                Get.back();
              },
              child: const Text('Mark as Cleared'),
            ),
          if (check.status != 'bounced')
            TextButton(
              onPressed: () {
                controller.updateCheckStatus(check.id, 'bounced');
                Get.back();
              },
              child: const Text('Mark as Bounced'),
            ),
          if (check.status != 'cancelled')
            TextButton(
              onPressed: () {
                controller.updateCheckStatus(check.id, 'cancelled');
                Get.back();
              },
              child: const Text('Mark as Cancelled'),
            ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _selectExpiryDateForSorting(
      BuildContext context, CheckUsageController controller) async {
    final DateTime? selectedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
      helpText: 'Select date to filter by expiry date',
    );

    if (selectedDate != null) {
      controller.setSortByExpiryDate(selectedDate);
    }
  }
}
