class AppCollection {
  static const String invoicesCollection = 'invoices';
  static const String vouchersCollection = 'vouchers';

  static const String regionDestinationsCollection = 'Region_Destinations';
  static const String regionCollection = 'Regions';
  // static const String companyCollection = 'companies';

  // Counter document IDs
  static const String regionCounter = 'regionNumber';
  static const String invoiceCounter = 'invoiceNumber';
  static const String voucherCounter = 'voucherNumber';
  static const String billCounter = 'billNumber';

  // Asset Management Collections
  static const String assetsCollection = 'assets';
  static const String assetMaintenanceCollection = 'asset_maintenance';
  static const String assetAuditTrailCollection = 'asset_audit_trail';
  static const String assetCounter = 'assetNumber';

  static const String regionsCollection = 'regions';
  static const String districtsCollection = 'districts';
  static const String stationsCollection = 'stations';
  static const String zonesCollection = 'zones';
  static const String slabsCollection = 'slabs';

  // Finance collections
  static const String depositCategoriesCollection = 'depositCategories';
  static const String accountsCollection = 'accounts';
  static const String payersCollection = 'payers';
  static const String payeesCollection = 'payees';
  static const String brokersCollection = 'brokers';
  static const String depositsCollection = 'deposits';
  static const String expensesCollection = 'expenses';
  static const String transactionsCollection = 'transactions';
  static const String accountTypesCollection = 'accountTypes';
  static const String paymentMethodsCollection = 'paymentMethods';
  static const String fuelCardsCollection = 'fuelCards';
  static const String fuelRatesCollection = 'fuelRates';
  static const String fuelCardUsageCollection = 'fuelCardUsage';
  static const String checkUsageCollection = 'checkUsage';
  static const String loansCollection = 'loans';
  static const String billsCollection = 'bills';

  static const String expenseCategoriesCollection = 'expenseCategories';

  // Accounting collections
  static const String chartOfAccountsCollection = 'chart_of_accounts';
  static const String journalEntriesCollection = 'journal_entries';
  static const String journalEntryLinesCollection = 'journal_entry_lines';
  static const String fiscalYearsCollection = 'fiscal_years';
  static const String fiscalPeriodsCollection = 'fiscal_periods';
  static const String financialReportsCollection = 'financial_reports';
  static const String agedReceivablesReportsCollection =
      'aged_receivables_reports';
  static const String agedPayablesReportsCollection = 'aged_payables_reports';
  static const String yearEndClosingsCollection = 'year_end_closings';

  // Accounting counters
  static const String journalEntryCounter = 'journalEntryNumber';

  // Users collection (each user is a company)
  static const String usersCollection = 'users';
}
