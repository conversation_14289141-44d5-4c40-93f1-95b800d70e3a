import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/widgets/my_text_field.dart';
import 'package:logestics/core/utils/widgets/custom_button.dart';
import 'package:logestics/core/utils/widgets/loading_indicator.dart';
import 'package:logestics/models/slab/slab_model.dart';
import 'package:intl/intl.dart';
import '../controllers/slab_form_controller.dart';

class SlabFormView extends GetView<SlabFormController> {
  final SlabModel? currentSlab;
  final bool readOnly;

  const SlabFormView({
    super.key,
    this.currentSlab,
    this.readOnly = false,
  });

  @override
  Widget build(BuildContext context) {
    // Initialize controller with current slab if editing
    if (currentSlab != null) {
      controller.setCurrentSlab(currentSlab!);
    }

    notifier = Provider.of(context, listen: true);
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 600;

    return Scaffold(
      backgroundColor: notifier.getBgColor,
      appBar: AppBar(
        backgroundColor: notifier.getBgColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: notifier.text),
          onPressed: () => Get.back(),
        ),
        title: Text(
          readOnly
              ? 'View Slab'
              : currentSlab != null
                  ? 'Edit Slab'
                  : 'Create Slab',
          style: AppTextStyles.invoiceHeaderStyle.copyWith(
            color: notifier.text,
          ),
        ),
        actions: [
          if (!readOnly)
            Obx(() => Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: CustomButton.primary(
                    text: controller.isSaving.value
                        ? 'Saving...'
                        : currentSlab != null
                            ? 'Update Slab'
                            : 'Create Slab',
                    onPressed:
                        controller.isSaving.value ? () {} : controller.saveSlab,
                    isLoading: controller.isSaving.value,
                    isDisabled: controller.isSaving.value,
                  ),
                )),
        ],
      ),
      body: Form(
        key: controller.formKey,
        child: Column(
          children: [
            // Basic Information Section
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: notifier.getHoverColor,
                border: Border(
                  bottom: BorderSide(color: notifier.getfillborder),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Slab Information',
                    style: AppTextStyles.invoiceHeaderStyle.copyWith(
                      color: notifier.text,
                      fontSize: 18,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Form fields in responsive layout
                  isSmallScreen
                      ? Column(
                          children: [
                            _buildSlabNameField(),
                            const SizedBox(height: 16),
                            _buildDateFields(),
                          ],
                        )
                      : Row(
                          children: [
                            Expanded(flex: 2, child: _buildSlabNameField()),
                            const SizedBox(width: 16),
                            Expanded(flex: 3, child: _buildDateFields()),
                          ],
                        ),
                ],
              ),
            ),

            // Custom Columns Management
            if (!readOnly)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: notifier.getBgColor,
                  border: Border(
                    bottom: BorderSide(color: notifier.getfillborder),
                  ),
                ),
                child: _buildCustomColumnsSection(),
              ),

            // Excel-like Grid Section
            Expanded(
              child: Container(
                margin: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: notifier.getBgColor,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: notifier.getfillborder),
                ),
                child: Column(
                  children: [
                    // Grid Header
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: notifier.getHoverColor,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(8),
                          topRight: Radius.circular(8),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.grid_view, color: notifier.text),
                          const SizedBox(width: 8),
                          Text(
                            'District Rates',
                            style: AppTextStyles.invoiceHeaderStyle.copyWith(
                              color: notifier.text,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Grid Content
                    Expanded(
                      child: Obx(() {
                        if (controller.isLoadingDistricts.value) {
                          return const Center(child: LoadingIndicator());
                        }

                        return _buildRatesGrid();
                      }),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSlabNameField() {
    return MyTextFormField(
      titleText: 'Slab Name',
      labelText: 'Enter slab name',
      hintText: 'e.g., Q1 2024 Rates',
      controller: controller.slabNameController,
      validator: controller.validateSlabName,
      readOnly: readOnly,
    );
  }

  Widget _buildDateFields() {
    return Row(
      children: [
        Expanded(
          child: Obx(() => InkWell(
                onTap: readOnly
                    ? null
                    : () => controller.selectStartDate(Get.context!),
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Start Date',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        controller.startDate.value != null
                            ? DateFormat('dd/MM/yyyy')
                                .format(controller.startDate.value!)
                            : 'Select start date',
                        style: TextStyle(
                          fontSize: 16,
                          color: controller.startDate.value != null
                              ? notifier.text
                              : Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
              )),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Obx(() => InkWell(
                onTap: readOnly
                    ? null
                    : () => controller.selectExpiryDate(Get.context!),
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Expiry Date',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        controller.expiryDate.value != null
                            ? DateFormat('dd/MM/yyyy')
                                .format(controller.expiryDate.value!)
                            : 'Select expiry date',
                        style: TextStyle(
                          fontSize: 16,
                          color: controller.expiryDate.value != null
                              ? notifier.text
                              : Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
              )),
        ),
      ],
    );
  }

  Widget _buildCustomColumnsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Custom Columns',
              style: AppTextStyles.invoiceHeaderStyle.copyWith(
                color: notifier.text,
                fontSize: 16,
              ),
            ),
            const Spacer(),
            ElevatedButton.icon(
              onPressed: () => _showAddColumnDialog(),
              icon: const Icon(Icons.add, size: 16),
              label: const Text('Add Column'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xff0f79f3),
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Obx(() => Wrap(
              spacing: 8,
              runSpacing: 8,
              children: controller.customColumns.map((column) {
                return Chip(
                  label: Text(column),
                  deleteIcon: const Icon(Icons.close, size: 16),
                  onDeleted: () => controller.removeCustomColumn(column),
                  backgroundColor:
                      const Color(0xff0f79f3).withValues(alpha: 0.1),
                  deleteIconColor: Colors.red,
                );
              }).toList(),
            )),
      ],
    );
  }

  Widget _buildRatesGrid() {
    return Obx(() {
      final rates = controller.slabRates;
      final customColumns = controller.customColumns;

      if (rates.isEmpty) {
        return const Center(
          child: Text('No districts available'),
        );
      }

      return SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: SingleChildScrollView(
          child: DataTable(
            border: TableBorder.all(color: notifier.getfillborder),
            headingRowColor: WidgetStateProperty.all(notifier.getHoverColor),
            columns: [
              DataColumn(
                label: Text(
                  'Region',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: notifier.text,
                  ),
                ),
              ),
              DataColumn(
                label: Text(
                  'District',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: notifier.text,
                  ),
                ),
              ),
              DataColumn(
                label: Text(
                  'Agree HPT (Fuel + Non Fuel) Rate Inc WHT',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: notifier.text,
                  ),
                ),
              ),
              DataColumn(
                label: Text(
                  'Non Fuel (Inc WHT) HMT Rate',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: notifier.text,
                  ),
                ),
              ),
              ...customColumns.map((column) => DataColumn(
                    label: Text(
                      column,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: notifier.text,
                      ),
                    ),
                  )),
            ],
            rows: rates.map((rate) {
              return DataRow(
                cells: [
                  DataCell(Text(rate.regionName)),
                  DataCell(Text(rate.districtName)),
                  DataCell(_buildRateInputCell(
                      rate.districtId, 'hmtRate', rate.hmtRate)),
                  DataCell(_buildRateInputCell(
                      rate.districtId, 'nonFuelRate', rate.nonFuelRate)),
                  ...customColumns.map((column) => DataCell(
                        _buildRateInputCell(
                          rate.districtId,
                          column,
                          rate.getCustomValue(column) ?? 0.0,
                        ),
                      )),
                ],
              );
            }).toList(),
          ),
        ),
      );
    });
  }

  Widget _buildRateInputCell(String districtId, String field, dynamic value) {
    // Get the controller for this specific field, fallback to initialValue if controller doesn't exist
    final textController = controller.getRateController(districtId, field);

    return SizedBox(
      width: 100,
      child: TextFormField(
        controller: textController,
        initialValue: textController == null ? value.toString() : null,
        enabled: !readOnly,
        keyboardType: TextInputType.number,
        inputFormatters: [
          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
        ],
        decoration: const InputDecoration(
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        ),
        style: TextStyle(color: notifier.text),
        onChanged: (newValue) {
          controller.updateRateValue(districtId, field, newValue);
        },
      ),
    );
  }

  void _showAddColumnDialog() {
    final columnController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: const Text('Add Custom Column'),
        content: TextField(
          controller: columnController,
          decoration: const InputDecoration(
            labelText: 'Column Name',
            hintText: 'e.g., WHT, GST, Service Fee',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              final columnName = columnController.text.trim();
              if (columnName.isNotEmpty) {
                controller.addCustomColumn(columnName);
                Get.back();
              }
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }
}
