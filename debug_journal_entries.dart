import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';

/// Debug script to check journal entries in Firebase
void main() async {
  try {
    // Initialize Firebase (you may need to configure this for your project)
    await Firebase.initializeApp();
    
    log('🔍 [DEBUG] Firebase initialized');
    
    // Check authentication status
    final currentUser = FirebaseAuth.instance.currentUser;
    log('🔍 [DEBUG] Current user: ${currentUser?.email ?? 'null'}');
    log('🔍 [DEBUG] Current UID: ${currentUser?.uid ?? 'anonymous'}');
    
    // Check all journal entries in the collection
    final firestore = FirebaseFirestore.instance;
    
    log('🔍 [DEBUG] Checking all journal entries...');
    final allSnapshot = await firestore
        .collection('journal_entries')
        .limit(10)
        .get();
    
    log('🔍 [DEBUG] Total journal entries found: ${allSnapshot.docs.length}');
    
    for (final doc in allSnapshot.docs) {
      final data = doc.data();
      log('🔍 [DEBUG] Entry ${doc.id}: uid=${data['uid']}, entryNumber=${data['entryNumber']}, description=${data['description']}');
    }
    
    // Check journal entries for current user
    final uid = currentUser?.uid ?? 'anonymous';
    log('🔍 [DEBUG] Checking journal entries for UID: $uid');
    
    final userSnapshot = await firestore
        .collection('journal_entries')
        .where('uid', isEqualTo: uid)
        .get();
    
    log('🔍 [DEBUG] Journal entries for current user: ${userSnapshot.docs.length}');
    
    for (final doc in userSnapshot.docs) {
      final data = doc.data();
      log('🔍 [DEBUG] User Entry ${doc.id}: entryNumber=${data['entryNumber']}, description=${data['description']}');
    }
    
  } catch (e) {
    log('❌ [DEBUG] Error: $e');
  }
}
