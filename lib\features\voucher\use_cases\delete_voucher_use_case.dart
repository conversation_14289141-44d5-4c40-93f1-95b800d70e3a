import 'package:dartz/dartz.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';

import '../repositories/voucher_repository.dart';

class DeleteVoucherUseCase {
  final VoucherRepository _voucherRepository;

  DeleteVoucherUseCase(this._voucherRepository);

  /// Executes the use case to delete an invoice.
  Future<Either<FailureObj, SuccessObj>> call({
    required String voucherNumber,
  }) async {
    // Get the current user ID from Firebase Auth
    String uid = FirebaseAuth.instance.currentUser?.uid ?? '';

    // Validate inputs before calling the repository
    if (uid.isEmpty) {
      return Left(FailureObj(
        code: 'invalid-company-id',
        message: 'No authenticated user found. Please login again.',
      ));
    }

    if (voucherNumber.isEmpty) {
      return Left(FailureObj(
        code: 'invalid-voucher',
        message: 'Voucher number must not be empty.',
      ));
    }

    // Call the repository
    return await _voucherRepository.deleteVoucher(
      uid: uid,
      voucherNumber: voucherNumber,
    );
  }
}
