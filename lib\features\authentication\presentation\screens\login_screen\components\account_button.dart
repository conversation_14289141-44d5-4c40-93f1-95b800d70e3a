import 'package:flutter/material.dart';

import '../../../../../../core/utils/constants/constants.dart';

class AccountButton extends StatelessWidget {
  final String text;
  final bool loading;
  final VoidCallback onTap;
  final String? tag;
  const AccountButton(
      {super.key,
      required this.text,
      required this.loading,
      required this.onTap,
      this.tag});

  @override
  Widget build(BuildContext context) {
    return Hero(
      tag: tag ?? "TAG",
      child: Material(
        color: Colors.transparent,
        child: Container(
          height: 50,
          width: double.infinity,
          margin: const EdgeInsets.symmetric(horizontal: 10),
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: lightOrange.withAlpha(5),
                offset: Offset(2, 2),
                blurRadius: 10,
              ),
              BoxShadow(
                color: lightOrange.withAlpha(5),
                offset: Offset(-2, -2),
                blurRadius: 10,
              ),
            ],
            borderRadius: BorderRadius.circular(15),
            gradient: const LinearGradient(
              colors: [lightOrange, darkOrange, darkOrange],
            ),
          ),
          child: InkWell(
            borderRadius: BorderRadius.circular(15),
            onTap: onTap,
            child: Center(
              child: loading
                  ? const SizedBox(
                      height: 15,
                      width: 15,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                      ),
                    )
                  : Text(
                      text,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
          ),
        ),
      ),
    );
  }
}
