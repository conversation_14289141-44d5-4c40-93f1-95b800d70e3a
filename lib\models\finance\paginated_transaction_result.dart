import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logestics/models/finance/account_transaction_model.dart';

/// Result class for paginated transaction queries
class PaginatedTransactionResult {
  final List<AccountTransactionModel> transactions;
  final QueryDocumentSnapshot? nextPageCursor;
  final bool hasNextPage;

  const PaginatedTransactionResult({
    required this.transactions,
    this.nextPageCursor,
    required this.hasNextPage,
  });

  /// Create a copy with modified properties
  PaginatedTransactionResult copyWith({
    List<AccountTransactionModel>? transactions,
    QueryDocumentSnapshot? nextPageCursor,
    bool? hasNextPage,
  }) {
    return PaginatedTransactionResult(
      transactions: transactions ?? this.transactions,
      nextPageCursor: nextPageCursor ?? this.nextPageCursor,
      hasNextPage: hasNextPage ?? this.hasNextPage,
    );
  }

  @override
  String toString() {
    return 'PaginatedTransactionResult(transactions: ${transactions.length}, hasNextPage: $hasNextPage)';
  }
}
