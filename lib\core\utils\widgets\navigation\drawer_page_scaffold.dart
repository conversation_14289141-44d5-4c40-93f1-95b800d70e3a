import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/app_constants/assets/app_assets.dart';
import 'package:logestics/features/home/<USER>/drawer_controllers.dart';
import 'package:logestics/features/home/<USER>/dashbord/e_commerece/e_commerce.dart';
import 'package:logestics/features/home/<USER>/theme.dart';

/// A reusable scaffold for drawer-accessed pages that provides consistent navigation
/// including back button, breadcrumb navigation, and device back button handling
class DrawerPageScaffold extends StatelessWidget {
  final String pageTitle;
  final Widget body;
  final List<String> breadcrumbItems;
  final VoidCallback? onBackPressed;
  final List<Widget>? actions;

  const DrawerPageScaffold({
    super.key,
    required this.pageTitle,
    required this.body,
    this.breadcrumbItems = const [],
    this.onBackPressed,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    final notifier = Provider.of<ColorNotifier>(context, listen: true);
    final mainDrawerController = Get.find<MainDrawerController>();

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          _handleBackNavigation(mainDrawerController);
        }
      },
      child: Scaffold(
        backgroundColor: notifier.getBgColor,
        appBar: AppBar(
          backgroundColor: notifier.getBgColor,
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back,
              color: notifier.text,
            ),
            onPressed: () {
              if (onBackPressed != null) {
                onBackPressed!();
              } else {
                _handleBackNavigation(mainDrawerController);
              }
            },
          ),
          title: Text(
            pageTitle,
            style: AppTextStyles.activeNavigationTextStyle.copyWith(
              color: notifier.text,
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          actions: [
            // Breadcrumb navigation in app bar
            if (breadcrumbItems.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(right: 16.0),
                child: Center(
                  child:
                      _buildCompactBreadcrumb(notifier, mainDrawerController),
                ),
              ),
            ...?actions,
          ],
        ),
        body: body,
      ),
    );
  }

  void _handleBackNavigation(MainDrawerController mainDrawerController) {
    // Navigate back to dashboard using the drawer controller
    mainDrawerController.updateSelectedScreen(
        const ECommercePageView(), "main/dashboard");
    // Also handle route-based navigation
    if (Get.currentRoute != '/home') {
      Get.offAllNamed('/home');
    }
  }

  Widget _buildCompactBreadcrumb(
      ColorNotifier notifier, MainDrawerController mainDrawerController) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        InkWell(
          onTap: () => _handleBackNavigation(mainDrawerController),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(
                AppAssets.homeIcon,
                height: 12,
                color: const Color(0xFF0f7bf4),
              ),
              const SizedBox(width: 4),
              const Text(
                AppStrings.dashboard,
                style: TextStyle(
                  color: Color(0xFF0f7bf4),
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        ...breadcrumbItems
            .map((item) => [
                  _buildNavigationDot(),
                  Text(
                    item,
                    style: const TextStyle(
                      color: Colors.grey,
                      fontSize: 12,
                    ),
                  ),
                ])
            .expand((element) => element),
      ],
    );
  }

  Widget _buildNavigationDot() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 6),
      width: 3,
      height: 3,
      decoration: const BoxDecoration(
        color: Color(0xFF0f7bf4),
        shape: BoxShape.circle,
      ),
    );
  }
}
