import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:excel/excel.dart';
import 'package:logestics/features/finance/loans/presentation/controllers/loan_history_controller.dart';
import 'package:logestics/models/finance/loan_model.dart';

// Web-specific imports - conditional import
import 'dart:html' as html show Blob, Url, AnchorElement;
// ignore: avoid_web_libraries_in_flutter

class LoanExportController extends GetxController {
  final LoanHistoryController loanHistoryController;

  LoanExportController(this.loanHistoryController);

  // Date range for export
  final startDate = DateTime.now().subtract(const Duration(days: 30)).obs;
  final endDate = DateTime.now().obs;

  // Loading state
  final isLoading = false.obs;

  // Filtered loans for export
  final filteredLoans = <LoanModel>[].obs;

  @override
  void onInit() {
    super.onInit();
    _filterLoansForExport();

    // Listen to date changes
    ever(startDate, (_) => _filterLoansForExport());
    ever(endDate, (_) => _filterLoansForExport());
  }

  /// Filter loans based on selected date range
  void _filterLoansForExport() {
    final allLoans = loanHistoryController.allLoans;

    final filtered = allLoans.where((loan) {
      final loanDate = loan.requestDate;
      return loanDate
              .isAfter(startDate.value.subtract(const Duration(days: 1))) &&
          loanDate.isBefore(endDate.value.add(const Duration(days: 1)));
    }).toList();

    // Sort by request date (most recent first)
    filtered.sort((a, b) => b.requestDate.compareTo(a.requestDate));

    filteredLoans.value = filtered;
    log('Filtered ${filtered.length} loans for export between ${DateFormat('dd/MM/yyyy').format(startDate.value)} and ${DateFormat('dd/MM/yyyy').format(endDate.value)}');
  }

  /// Select start date
  Future<void> selectStartDate() async {
    final date = await showDatePicker(
      context: Get.context!,
      initialDate: startDate.value,
      firstDate: DateTime(2020),
      lastDate: endDate.value,
    );
    if (date != null) {
      startDate.value = date;
    }
  }

  /// Select end date
  Future<void> selectEndDate() async {
    final date = await showDatePicker(
      context: Get.context!,
      initialDate: endDate.value,
      firstDate: startDate.value,
      lastDate: DateTime.now(),
    );
    if (date != null) {
      endDate.value = date;
    }
  }

  /// Generate Excel file
  Future<void> generateExcel() async {
    if (filteredLoans.isEmpty) {
      Get.snackbar(
        'No Data',
        'No loans found for the selected date range',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
      );
      return;
    }

    isLoading.value = true;

    try {
      // Generate filename
      final dateFormat = DateFormat('yyyy-MM-dd');
      final fileName =
          'Loan_History_${dateFormat.format(startDate.value)}_to_${dateFormat.format(endDate.value)}.xlsx';

      log('Generating Excel file: $fileName with ${filteredLoans.length} loans');

      // Create Excel workbook
      final excel = Excel.createExcel();
      final sheet = excel['Loan History'];

      // Remove default sheet if it exists
      if (excel.sheets.containsKey('Sheet1')) {
        excel.delete('Sheet1');
      }

      // Add headers
      final headers = [
        'SN.',
        'Loan ID',
        'Borrower Name',
        'Lender Name',
        'Borrower Account',
        'Lender Account',
        'Loan Amount (PKR)',
        'Request Date',
        'Due Date',
        'Approval Date',
        'Repayment Date',
        'Status',
        'Loan Type',
        'Paid Amount (PKR)',
        'Remaining Balance (PKR)',
        'Notes',
        'Rejection Reason',
      ];

      // Add header row
      for (int i = 0; i < headers.length; i++) {
        final cell =
            sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0));
        cell.value = TextCellValue(headers[i]);
        cell.cellStyle = CellStyle(
          bold: true,
        );
      }

      // Add data rows
      for (int i = 0; i < filteredLoans.length; i++) {
        final loan = filteredLoans[i];
        final rowIndex = i + 1;

        final rowData = [
          (i + 1).toString(), // SN
          loan.id, // Loan ID
          loan.requestedByName, // Borrower Name
          loan.requestedToName, // Lender Name
          loan.toAccountName, // Borrower Account
          loan.fromAccountName, // Lender Account
          loan.amount.toStringAsFixed(2), // Loan Amount
          DateFormat('dd/MM/yyyy').format(loan.requestDate), // Request Date
          DateFormat('dd/MM/yyyy').format(loan.dueDate), // Due Date
          loan.approvalDate != null
              ? DateFormat('dd/MM/yyyy').format(loan.approvalDate!)
              : '', // Approval Date
          loan.repaymentDate != null
              ? DateFormat('dd/MM/yyyy').format(loan.repaymentDate!)
              : '', // Repayment Date
          loan.status.toUpperCase(), // Status
          _getLoanType(loan), // Loan Type
          loanHistoryController
              .getPaidAmount(loan)
              .toStringAsFixed(2), // Paid Amount
          loanHistoryController
              .getRemainingBalance(loan)
              .toStringAsFixed(2), // Remaining Balance
          loan.notes ?? '', // Notes
          loan.rejectionReason ?? '', // Rejection Reason
        ];

        for (int j = 0; j < rowData.length; j++) {
          final cell = sheet.cell(
              CellIndex.indexByColumnRow(columnIndex: j, rowIndex: rowIndex));
          cell.value = TextCellValue(rowData[j]);
        }
      }

      // Auto-fit columns (approximate)
      for (int i = 0; i < headers.length; i++) {
        sheet.setColumnWidth(i, 15.0);
      }

      // Generate Excel bytes
      final excelBytes = excel.encode();
      if (excelBytes == null) {
        throw Exception('Failed to generate Excel file');
      }

      // Download file for web
      if (kIsWeb) {
        await _downloadExcelWeb(Uint8List.fromList(excelBytes), fileName);
      } else {
        // For mobile/desktop, you would save to file system
        throw UnimplementedError(
            'Mobile/Desktop Excel download not implemented');
      }

      log('Excel file generated successfully: $fileName');

      // Close the dialog after successful generation
      Get.back();

      // Show success message
      Get.snackbar(
        'Success',
        'Excel file "$fileName" has been downloaded',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
      );
    } catch (e) {
      log('Error generating Excel file: $e');

      // Show error message
      Get.snackbar(
        'Error',
        'Failed to generate Excel file: $e',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// Download Excel file for web platform
  Future<void> _downloadExcelWeb(Uint8List excelBytes, String fileName) async {
    try {
      final blob = html.Blob([excelBytes],
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      final url = html.Url.createObjectUrlFromBlob(blob);

      html.AnchorElement(href: url)
        ..setAttribute('download', fileName)
        ..click();

      html.Url.revokeObjectUrl(url);

      log('Excel file downloaded successfully on web: $fileName');
    } catch (e) {
      log('Error downloading Excel file on web: $e');
      rethrow;
    }
  }

  /// Get loan type based on notes and amount
  String _getLoanType(LoanModel loan) {
    if (loan.notes?.toLowerCase().contains('personal') ?? false) {
      return 'Personal';
    }
    if (loan.notes?.toLowerCase().contains('business') ?? false) {
      return 'Business';
    }
    if (loan.notes?.toLowerCase().contains('emergency') ?? false) {
      return 'Emergency';
    }
    if (loan.notes?.toLowerCase().contains('investment') ?? false) {
      return 'Investment';
    }
    if (loan.amount <= 100000) {
      return 'Personal';
    }
    return 'Business';
  }
}
