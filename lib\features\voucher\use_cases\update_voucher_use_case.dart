import 'package:dartz/dartz.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';

import '../repositories/voucher_repository.dart';

class UpdateVoucherUseCase {
  final VoucherRepository _voucherRepository;

  UpdateVoucherUseCase(this._voucherRepository);

  /// Executes the use case to update an invoice.
  Future<Either<FailureObj, SuccessObj>> call({
    required Map<String, dynamic> voucher,
  }) async {
    // Get the current user ID from Firebase Auth
    String uid = FirebaseAuth.instance.currentUser?.uid ?? '';

    // Validate inputs
    if (uid.isEmpty) {
      return Left(FailureObj(
        code: 'invalid-company-id',
        message: 'No authenticated user found. Please login again.',
      ));
    }

    if (voucher['voucherNumber'] == null ||
        (voucher['voucherNumber'] as String).isEmpty) {
      return Left(FailureObj(
        code: 'invalid-voucher',
        message: 'Voucher number must not be null or empty.',
      ));
    }

    // Call the repository
    return await _voucherRepository.updateVoucher(
      uid: uid,
      voucher: voucher,
    );
  }
}
