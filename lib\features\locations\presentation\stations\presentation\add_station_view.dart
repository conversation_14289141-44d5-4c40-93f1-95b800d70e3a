import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/colors/app_colors.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/widgets/custom_button.dart';
import 'package:logestics/core/utils/widgets/my_text_field.dart';
import 'package:logestics/core/utils/widgets/my_dropdown_field.dart';
import 'package:logestics/core/utils/widgets/data_table_cell.dart';
import 'package:logestics/models/locations/station_model.dart';
import 'package:logestics/features/locations/domain/usecases/station_use_case/create_station_use_case.dart';
import 'package:logestics/features/locations/domain/usecases/station_use_case/update_station_use_case.dart';

import 'package:logestics/main.dart';
import 'package:provider/provider.dart';

import '../controller/add_station_controller.dart';

class AddStationView extends StatefulWidget {
  final StationModel? existingStation;

  const AddStationView({super.key, this.existingStation});

  @override
  State<AddStationView> createState() => _AddStationViewState();
}

class _AddStationViewState extends State<AddStationView> {
  late AddStationController controller;

  @override
  void initState() {
    super.initState();
    controller = Get.put(AddStationController(
      createStationUseCase: CreateStationUseCase(Get.find()),
      updateStationUseCase: UpdateStationUseCase(Get.find()),
      getDistrictsUseCase: Get.find(),
    ));

    // Initialize with existing station if in edit mode
    if (widget.existingStation != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        controller.initializeWithExistingStation(widget.existingStation!);
      });
    }
  }

  void _handleClose() {
    controller.clearControllers();
    if (Navigator.of(context).canPop()) {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    notifier = Provider.of(context, listen: false);
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 600;

    return PopScope(
      canPop: false, // Prevent automatic popping
      onPopInvokedWithResult: (didPop, result) {
        // Only handle close if not already popped
        if (!didPop) {
          _handleClose();
        }
      },
      child: Scaffold(
        backgroundColor: notifier.getBgColor,
        body: Dialog(
          backgroundColor: notifier.getBgColor,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          child: Container(
            width: size.width * 0.9,
            height: size.height * 0.9,
            padding: const EdgeInsets.all(16),
            child: Form(
              key: controller.formKey,
              child: Column(
                children: [
                  // Header
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          widget.existingStation != null
                              ? 'Edit Station'
                              : AppStrings.addNewStation,
                          style: AppTextStyles.titleStyle.copyWith(
                            color: notifier.text,
                          ),
                        ),
                      ),
                      IconButton(
                        onPressed: _handleClose,
                        icon: Icon(Icons.close, color: notifier.text),
                      ),
                    ],
                  ),
                  Divider(),

                  // Form Content
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          // District Dropdown
                          Obx(() => MyDropdownFormField(
                                titletext: AppStrings.selectDistrict,
                                hinttext: AppStrings.selectADistrict,
                                items: controller.districts
                                    .map((district) => district.districtName)
                                    .toList(),
                                initalValue: controller
                                    .selectedDistrict.value?.districtName,
                                onChanged: (value) =>
                                    controller.onDistrictSelected(value),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please select a district';
                                  }
                                  return null;
                                },
                              )),

                          // Station Name
                          MyTextFormField(
                            titleText: AppStrings.stationName,
                            labelText: AppStrings.stationName,
                            hintText: AppStrings.enterStationName,
                            controller: controller.stationNameController,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter a station name';
                              }
                              return null;
                            },
                          ),

                          // Places Section
                          _buildPlacesSection(),
                        ],
                      ),
                    ),
                  ),

                  // Action Buttons
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Obx(() => CustomButton.danger(
                            onPressed: _handleClose,
                            isDisabled: controller.isLoading.value,
                            text: AppStrings.cancel,
                            minimumSize: Size(isSmallScreen ? 100 : 130, 50),
                          )),
                      SizedBox(width: 8),
                      Obx(() => CustomButton.primary(
                            onPressed: controller.saveStation,
                            text: widget.existingStation != null
                                ? 'Update'
                                : AppStrings.save,
                            isLoading: controller.isLoading.value,
                            minimumSize: Size(isSmallScreen ? 100 : 130, 50),
                          )),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPlacesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Non-editable Zone and Region fields
        Obx(() => MyTextFormField(
              titleText: AppStrings.zone,
              labelText: AppStrings.zone,
              hintText: AppStrings.enterZoneName,
              controller: TextEditingController(
                text: controller.zoneName.value,
              ),
              enabled: false,
            )),
        Obx(() => MyTextFormField(
              titleText: AppStrings.region,
              labelText: AppStrings.region,
              hintText: AppStrings.enterRegionName,
              controller: TextEditingController(
                text: controller.regionName.value,
              ),
              enabled: false,
            )),

        // Add Place Section
        Card(
          color: AppColors.primary,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Add Place",
                  style: AppTextStyles.activeNavigationTextStyle.copyWith(
                    color: notifier.text,
                  ),
                ),
                Row(
                  children: [
                    Expanded(
                      child: MyTextFormField(
                        hintText: AppStrings.enterPlaceName,
                        titleText: AppStrings.placeName,
                        labelText: AppStrings.placeName,
                        controller: controller.placeNameController,
                      ),
                    ),
                    SizedBox(width: 16),
                    Expanded(
                      child: MyTextFormField(
                        hintText: AppStrings.enterKilometers,
                        titleText: AppStrings.kilometers,
                        labelText: AppStrings.kilometers,
                        controller: controller.kilometersController,
                        keyboardType: TextInputType.number,
                        textInputFormatter: [
                          FilteringTextInputFormatter.digitsOnly
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: controller.addPlace,
                      icon: Icon(
                        Icons.add_circle,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),

        // Places Table
        Obx(() => controller.places.isEmpty
            ? Center(
                child: Text(
                  AppStrings.noPlacesAddedYet,
                  style: TextStyle(color: notifier.text),
                ),
              )
            : Table(
                border: TableBorder.all(
                  color: notifier.getfillborder,
                ),
                children: [
                  TableRow(
                    decoration: BoxDecoration(
                      color: notifier.getHoverColor,
                    ),
                    children: [
                      DataTableHeaderCell(
                        text: AppStrings.placeName,
                        textColor: notifier.text,
                      ),
                      DataTableHeaderCell(
                        text: AppStrings.kilometers,
                        textColor: notifier.text,
                      ),
                      DataTableHeaderCell(
                        text: AppStrings.actions,
                        textColor: notifier.text,
                      ),
                    ],
                  ),
                  ...controller.places.asMap().entries.map(
                        (entry) => TableRow(
                          children: [
                            DataTableCell(
                              text: entry.value.fromPlace,
                            ),
                            DataTableCell(
                              text: "${entry.value.kilometers} km",
                            ),
                            TableCell(
                              child: IconButton(
                                onPressed: () =>
                                    controller.removePlace(entry.key),
                                icon: Icon(
                                  Icons.delete,
                                  color: Colors.red,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                ],
              )),
      ],
    );
  }
}
