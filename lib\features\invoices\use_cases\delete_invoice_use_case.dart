import 'package:dartz/dartz.dart';
import '../../../core/shared_services/failure_obj.dart';
import '../../../core/shared_services/success_obj.dart';
import '../repositories/invoice_repository.dart';

class DeleteInvoiceUseCase {
  final InvoiceRepository _invoiceRepository;

  DeleteInvoiceUseCase(this._invoiceRepository);

  /// Executes the use case to delete an invoice.
  Future<Either<FailureObj, SuccessObj>> call({
    required String uid,
    required String tasNumber,
  }) async {
    // Validate inputs before calling the repository
    if (uid.isEmpty) {
      return Left(FailureObj(
        code: 'invalid-company-id',
        message: 'Company ID cannot be empty.',
      ));
    }

    if (tasNumber.isEmpty) {
      return Left(FailureObj(
        code: 'invalid-invoice',
        message: 'Invoice "tasNumber" must not be empty.',
      ));
    }

    // Call the repository
    return await _invoiceRepository.deleteInvoice(
      uid: uid,
      tasNumber: tasNumber,
    );
  }
}
