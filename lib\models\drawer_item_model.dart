import 'package:flutter/material.dart';

class DrawerItemModel {
  final String title;
  final String icon;
  final Widget? screen;
  final List<DrawerItemModel>? children;
  final String? navigateTo;

  DrawerItemModel({
    required this.title,
    required this.icon,
    this.screen,
    this.children,
    this.navigateTo,
  });
}

class DrawerSectionModel {
  final String title;
  final List<DrawerItemModel> items;

  DrawerSectionModel({
    required this.title,
    required this.items,
  });
}
