import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../../models/finance/journal_entry_model.dart';
import '../controllers/general_ledger_controller.dart';

class AccountTransactionsList extends StatefulWidget {
  final String accountId;
  final String accountName;

  const AccountTransactionsList({
    Key? key,
    required this.accountId,
    required this.accountName,
  }) : super(key: key);

  @override
  State<AccountTransactionsList> createState() => _AccountTransactionsListState();
}

class _AccountTransactionsListState extends State<AccountTransactionsList> {
  final RxList<JournalEntryLineModel> transactions = <JournalEntryLineModel>[].obs;
  final RxBool isLoading = false.obs;
  double runningBalance = 0.0;

  @override
  void initState() {
    super.initState();
    loadTransactions();
  }

  Future<void> loadTransactions() async {
    try {
      isLoading.value = true;
      final controller = Get.find<GeneralLedgerController>();
      final result = await controller.getAccountTransactions(widget.accountId);
      
      // Sort transactions by date (newest first)
      result.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      
      transactions.value = result;
      calculateRunningBalances();
    } catch (e) {
      print('Error loading transactions: $e');
    } finally {
      isLoading.value = false;
    }
  }

  void calculateRunningBalances() {
    // Calculate running balance (oldest to newest for proper calculation)
    final reversedTransactions = transactions.reversed.toList();
    runningBalance = 0.0;
    
    for (int i = 0; i < reversedTransactions.length; i++) {
      final transaction = reversedTransactions[i];
      runningBalance += transaction.debitAmount - transaction.creditAmount;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with account info
        Card(
          color: Theme.of(context).primaryColor.withOpacity(0.1),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(
                  Icons.account_balance_wallet,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.accountName,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Account ID: ${widget.accountId}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Obx(() => Text(
                  '${transactions.length} transactions',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).primaryColor,
                  ),
                )),
              ],
            ),
          ),
        ),

        const SizedBox(height: 16),

        // Transactions List
        Expanded(
          child: Obx(() {
            if (isLoading.value) {
              return const Center(
                child: CircularProgressIndicator(),
              );
            }

            if (transactions.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.receipt_long_outlined,
                      size: 64,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No transactions found',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'This account has no journal entry transactions yet',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),
              );
            }

            return RefreshIndicator(
              onRefresh: loadTransactions,
              child: ListView.builder(
                itemCount: transactions.length,
                itemBuilder: (context, index) {
                  final transaction = transactions[index];
                  final isDebit = transaction.debitAmount > 0;
                  
                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Transaction Header
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: isDebit 
                                      ? Colors.green.withOpacity(0.1)
                                      : Colors.red.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Icon(
                                  isDebit ? Icons.add : Icons.remove,
                                  color: isDebit ? Colors.green : Colors.red,
                                  size: 20,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      transaction.description,
                                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      transaction.createdAt.toString().split(' ')[0],
                                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Text(
                                    isDebit ? 'DEBIT' : 'CREDIT',
                                    style: TextStyle(
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                      color: isDebit ? Colors.green : Colors.red,
                                    ),
                                  ),
                                  const SizedBox(height: 2),
                                  Text(
                                    '\$${(isDebit ? transaction.debitAmount : transaction.creditAmount).toStringAsFixed(2)}',
                                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                      fontWeight: FontWeight.bold,
                                      color: isDebit ? Colors.green[700] : Colors.red[700],
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),

                          const SizedBox(height: 12),

                          // Transaction Details
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.grey[50],
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Column(
                              children: [
                                _buildDetailRow(
                                  'Journal Entry ID',
                                  transaction.journalEntryId,
                                ),
                                _buildDetailRow(
                                  'Account',
                                  '${transaction.accountNumber} - ${transaction.accountName}',
                                ),
                                if (transaction.debitAmount > 0)
                                  _buildDetailRow(
                                    'Debit Amount',
                                    '\$${transaction.debitAmount.toStringAsFixed(2)}',
                                    valueColor: Colors.green[700],
                                  ),
                                if (transaction.creditAmount > 0)
                                  _buildDetailRow(
                                    'Credit Amount',
                                    '\$${transaction.creditAmount.toStringAsFixed(2)}',
                                    valueColor: Colors.red[700],
                                  ),
                                _buildDetailRow(
                                  'Created At',
                                  transaction.createdAt.toString(),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            );
          }),
        ),

        // Summary Footer
        Obx(() {
          if (transactions.isEmpty) return const SizedBox.shrink();
          
          final totalDebits = transactions.fold(0.0, (sum, t) => sum + t.debitAmount);
          final totalCredits = transactions.fold(0.0, (sum, t) => sum + t.creditAmount);
          final netBalance = totalDebits - totalCredits;

          return Card(
            color: Theme.of(context).primaryColor.withOpacity(0.1),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Text(
                    'Transaction Summary',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildSummaryItem(
                        context,
                        'Total Debits',
                        '\$${totalDebits.toStringAsFixed(2)}',
                        Colors.green,
                      ),
                      _buildSummaryItem(
                        context,
                        'Total Credits',
                        '\$${totalCredits.toStringAsFixed(2)}',
                        Colors.red,
                      ),
                      _buildSummaryItem(
                        context,
                        'Net Balance',
                        '\$${netBalance.toStringAsFixed(2)}',
                        netBalance >= 0 ? Colors.green : Colors.red,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        }),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 12,
                color: valueColor ?? Colors.grey[700],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(
    BuildContext context,
    String label,
    String value,
    Color color,
  ) {
    return Column(
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }
}
