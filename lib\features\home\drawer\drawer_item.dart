import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';

import '../Controllers/drawer_controllers.dart';

class DrawerItem extends StatelessWidget {
  final List<String> imageList;
  final List<String> titleList;
  final String sectionPath;
  final VoidCallback onItemTap;

  const DrawerItem({
    super.key,
    required this.imageList,
    required this.titleList,
    required this.sectionPath,
    required this.onItemTap,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<MainDrawerController>();
    notifier = Provider.of(context, listen: true);
    log("sectionPath: $sectionPath");
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: imageList.length,
      itemBuilder: (context, index) {
        return InkWell(
          onTap: onItemTap,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            color: controller.isItemSelected(sectionPath)
                ? const Color(0xFF0f7bf4)
                : Colors.transparent,
            child: Row(
              children: [
                Image.asset(
                  imageList[index],
                  height: 20,
                  color: controller.isItemSelected(sectionPath)
                      ? Colors.white
                      : notifier.text,
                ),
                const SizedBox(width: 10),
                Text(
                  titleList[index],
                  style: TextStyle(
                    fontFamily: "Outfit",
                    fontSize: 15,
                    fontWeight: FontWeight.w500,
                    color: controller.isItemSelected(sectionPath)
                        ? Colors.white
                        : notifier.text,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
