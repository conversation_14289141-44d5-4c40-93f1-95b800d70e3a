import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/models/user_model.dart';
import 'package:logestics/features/finance/loans/repositories/company_repository.dart';

class GetUsersUseCase {
  final CompanyRepository _repository;

  GetUsersUseCase(this._repository);

  Future<Either<FailureObj, List<UserModel>>> call() async {
    return await _repository.getAllUsers();
  }

  Future<Either<FailureObj, UserModel>> getUserById(String userId) async {
    return await _repository.getUserById(userId);
  }

  Future<Either<FailureObj, List<UserModel>>> searchUsers(String query) async {
    return await _repository.searchUsers(query);
  }
}
