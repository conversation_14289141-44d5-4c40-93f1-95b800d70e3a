import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../../models/finance/chart_of_accounts_model.dart';
import '../controllers/chart_of_accounts_controller.dart';

class ChartOfAccountsForm extends StatelessWidget {
  const ChartOfAccountsForm({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ChartOfAccountsController>();

    return Material(
      elevation: 8,
      shadowColor: Colors.black26,
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 10,
              offset: const Offset(-2, 0),
            ),
          ],
        ),
        child: Form(
          key: controller.formKey,
          child: Column(
            children: [
              _buildHeader(context, controller),
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: <PERSON>umn(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildAccount<PERSON><PERSON><PERSON><PERSON>(controller),
                      const SizedBox(height: 16),
                      _buildAccount<PERSON><PERSON><PERSON><PERSON>ield(controller),
                      const SizedBox(height: 16),
                      _buildCategoryDropdown(controller),
                      const SizedBox(height: 16),
                      _buildAccountTypeDropdown(controller),
                      const SizedBox(height: 16),
                      _buildParentAccountDropdown(controller),
                      const SizedBox(height: 16),
                      _buildDescriptionField(controller),
                      const SizedBox(height: 16),
                      _buildActiveSwitch(controller),
                    ],
                  ),
                ),
              ),
              _buildActionButtons(controller),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(
      BuildContext context, ChartOfAccountsController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Obx(() => Text(
                  controller.editingAccount.value == null
                      ? 'Add Account'
                      : 'Edit Account',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                )),
          ),
          IconButton(
            onPressed: controller.closeDrawer,
            icon: const Icon(Icons.close, color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountNameField(ChartOfAccountsController controller) {
    return TextFormField(
      controller: controller.accountNameController,
      decoration: const InputDecoration(
        labelText: 'Account Name *',
        hintText: 'Enter account name',
        border: OutlineInputBorder(),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Account name is required';
        }
        return null;
      },
    );
  }

  Widget _buildAccountNumberField(ChartOfAccountsController controller) {
    return Obx(() => TextFormField(
          controller: controller.accountNumberController,
          readOnly:
              controller.editingAccount.value != null, // Read-only in edit mode
          decoration: InputDecoration(
            labelText: 'Account Number',
            hintText: controller.editingAccount.value != null
                ? 'Account number cannot be changed'
                : 'Auto-generated if empty',
            border: const OutlineInputBorder(),
            helperText: controller.editingAccount.value != null
                ? 'Account number is fixed for existing accounts'
                : 'Leave empty to auto-generate based on category',
          ),
          validator: (value) {
            if (value != null && value.isNotEmpty) {
              if (!RegExp(r'^\d+$').hasMatch(value)) {
                return 'Account number must contain only digits';
              }
            }
            return null;
          },
        ));
  }

  Widget _buildCategoryDropdown(ChartOfAccountsController controller) {
    return Obx(() => DropdownButtonFormField<AccountCategory>(
          value: controller.selectedCategory.value,
          decoration: const InputDecoration(
            labelText: 'Category *',
            border: OutlineInputBorder(),
          ),
          items: AccountCategory.values.map((category) {
            return DropdownMenuItem(
              value: category,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      color: category.color,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Flexible(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          category.displayName,
                          style: const TextStyle(fontSize: 13),
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          '${category.startRange}-${category.endRange}',
                          style: const TextStyle(
                            fontSize: 10,
                            color: Colors.grey,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
          onChanged: (category) {
            controller.selectedCategory.value = category;
            // Reset parent account when category changes
            controller.selectedParentAccount.value = null;
            // Reset account type to force user to select appropriate type
            controller.selectedAccountType.value = null;
          },
          validator: (value) {
            if (value == null) {
              return 'Category is required';
            }
            return null;
          },
        ));
  }

  Widget _buildAccountTypeDropdown(ChartOfAccountsController controller) {
    return Obx(() => DropdownButtonFormField<AccountType>(
          value: controller.selectedAccountType.value,
          decoration: const InputDecoration(
            labelText: 'Account Type *',
            border: OutlineInputBorder(),
          ),
          items: AccountType.values.map((type) {
            return DropdownMenuItem(
              value: type,
              child: Text(type.displayName),
            );
          }).toList(),
          onChanged: (type) {
            controller.selectedAccountType.value = type;
          },
          validator: (value) {
            if (value == null) {
              return 'Account type is required';
            }
            return null;
          },
        ));
  }

  Widget _buildParentAccountDropdown(ChartOfAccountsController controller) {
    return Obx(() {
      final availableParents = controller.getAvailableParentAccounts();

      if (availableParents.isEmpty) {
        return const SizedBox.shrink();
      }

      return DropdownButtonFormField<ChartOfAccountsModel>(
        value: controller.selectedParentAccount.value,
        decoration: const InputDecoration(
          labelText: 'Parent Account (Optional)',
          border: OutlineInputBorder(),
          helperText: 'Select a parent account to create a sub-account',
        ),
        items: [
          const DropdownMenuItem<ChartOfAccountsModel>(
            value: null,
            child: Text('None (Top-level account)'),
          ),
          ...availableParents.map((account) {
            return DropdownMenuItem(
              value: account,
              child: Text('${account.accountNumber} - ${account.accountName}'),
            );
          }),
        ],
        onChanged: (account) {
          controller.selectedParentAccount.value = account;
        },
      );
    });
  }

  Widget _buildDescriptionField(ChartOfAccountsController controller) {
    return TextFormField(
      controller: controller.descriptionController,
      decoration: const InputDecoration(
        labelText: 'Description (Optional)',
        hintText: 'Enter account description',
        border: OutlineInputBorder(),
      ),
      maxLines: 3,
    );
  }

  Widget _buildActiveSwitch(ChartOfAccountsController controller) {
    return Obx(() => SwitchListTile(
          title: const Text('Active'),
          subtitle: Text(
            controller.isActive.value
                ? 'Account is active and can be used in transactions'
                : 'Account is inactive and cannot be used in transactions',
          ),
          value: controller.isActive.value,
          onChanged: (value) {
            controller.isActive.value = value;
          },
        ));
  }

  Widget _buildActionButtons(ChartOfAccountsController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Colors.grey.shade300),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: controller.closeDrawer,
              child: const Text('Cancel'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Obx(() => ElevatedButton(
                  onPressed: controller.isLoading.value
                      ? null
                      : controller.saveAccount,
                  child: controller.isLoading.value
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : Text(
                          controller.editingAccount.value == null
                              ? 'Create'
                              : 'Update',
                        ),
                )),
          ),
        ],
      ),
    );
  }
}
