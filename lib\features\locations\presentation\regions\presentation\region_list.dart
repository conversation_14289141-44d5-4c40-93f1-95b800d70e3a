import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/bindings/app_bindings.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/widgets/data_table_cell.dart';
import 'package:logestics/core/utils/widgets/pagination_widget.dart';
import 'package:logestics/core/utils/widgets/my_text_field.dart';
import 'package:logestics/features/locations/repositories/region_repository.dart';
import 'package:logestics/features/locations/domain/usecases/region_use_case/delete_region_use_case.dart';
import 'package:logestics/features/locations/domain/usecases/region_use_case/get_region_use_case.dart';
import 'package:logestics/firebase_service/locations/region_firebase_service.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';

import '../controller/region_list_controller.dart';
import 'add_region_view.dart';

class RegionList extends StatelessWidget {
  const RegionList({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    RegionListController regionListController;
    try {
      regionListController = Get.find<RegionListController>();
    } catch (e) {
      log('Creating temporary controller instance');
      regionListController = RegionListController(
        getRegionsUseCase: GetRegionsUseCase(
          RegionRepositoryImpl(
            RegionFirebaseService(),
          ),
        ),
        deleteRegionUseCase: DeleteRegionUseCase(
          RegionRepositoryImpl(
            RegionFirebaseService(),
          ),
        ),
      );
    }

    notifier = Provider.of(context, listen: true);

    return GetBuilder<RegionListController>(
      init: regionListController,
      builder: (regionListController) => LayoutBuilder(
        builder: (context, constraints) {
          return StatefulBuilder(
            builder: (context, setState) {
              return Container(
                decoration: BoxDecoration(
                  color: notifier.getBgColor,
                  borderRadius: BorderRadius.circular(10),
                ),
                padding: const EdgeInsets.symmetric(vertical: 15),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 15),
                      child: Get.width < 650
                          ? Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                InkWell(
                                  onTap: () {
                                    Get.to(() => AddRegionView(),
                                        binding: AppBindings());
                                  },
                                  child: Text(
                                    AppStrings.addNewRegionButton,
                                    style: AppTextStyles.addNewInvoiceStyle,
                                  ),
                                ),
                                MyTextFormField(
                                  labelText: AppStrings.searchHint,
                                  hintText: AppStrings.searchHint,
                                  controller:
                                      regionListController.searchController,
                                ),
                              ],
                            )
                          : Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                InkWell(
                                  onTap: () {
                                    Get.to(() => AddRegionView(),
                                        binding: AppBindings());
                                  },
                                  child: Text(
                                    AppStrings.addNewRegionButton,
                                    style: AppTextStyles.addNewInvoiceStyle,
                                  ),
                                ),
                                SizedBox(
                                  width: 300,
                                  child: MyTextFormField(
                                    labelText: AppStrings.searchHint,
                                    hintText: AppStrings.searchHint,
                                    controller:
                                        regionListController.searchController,
                                  ),
                                ),
                              ],
                            ),
                    ),
                    Expanded(
                      child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: SizedBox(
                          width: constraints.maxWidth,
                          child: Obx(() {
                            if (regionListController.isLoading.value) {
                              return const Center(
                                child: CircularProgressIndicator(),
                              );
                            }

                            final paginatedRegions =
                                regionListController.paginatedRegions;

                            return Column(
                              children: [
                                Expanded(
                                  child: ListView(
                                    shrinkWrap: true,
                                    children: [
                                      Table(
                                        border: TableBorder(
                                          horizontalInside: BorderSide(
                                            color: notifier.getfillborder,
                                          ),
                                        ),
                                        children: [
                                          TableRow(
                                            decoration: BoxDecoration(
                                              color: notifier.getHoverColor,
                                            ),
                                            children: [
                                              DataTableHeaderCell(
                                                text: 'Sr No.',
                                                textColor: notifier.text,
                                              ),
                                              DataTableHeaderCell(
                                                text: AppStrings.regionName,
                                                textColor: notifier.text,
                                              ),
                                              DataTableHeaderCell(
                                                text: 'Region Code',
                                                textColor: notifier.text,
                                              ),
                                              DataTableHeaderCell(
                                                text: 'Zone Name',
                                                textColor: notifier.text,
                                              ),
                                              DataTableHeaderCell(
                                                text: 'Actions',
                                                textColor: notifier.text,
                                              ),
                                            ],
                                          ),
                                          for (var i = 0;
                                              i < paginatedRegions.length;
                                              i++)
                                            TableRow(
                                              children: [
                                                DataTableCell(
                                                  text: ((regionListController
                                                                      .currentPage
                                                                      .value -
                                                                  1) *
                                                              regionListController
                                                                  .itemsPerPage
                                                                  .value +
                                                          i +
                                                          1)
                                                      .toString(),
                                                ),
                                                DataTableCell(
                                                  text: paginatedRegions[i]
                                                      .regionName,
                                                ),
                                                DataTableCell(
                                                  text: paginatedRegions[i]
                                                      .regionCode,
                                                ),
                                                DataTableCell(
                                                  text: paginatedRegions[i]
                                                      .zoneName,
                                                ),
                                                DataTableActionsCell(
                                                  menuItems: [
                                                    DataTablePopupMenuItem(
                                                      text: 'Delete',
                                                      icon:
                                                          Icons.delete_outline,
                                                      isDanger: true,
                                                      onTap: () async {
                                                        if (!regionListController
                                                            .isLoading.value) {
                                                          await regionListController
                                                              .deleteRegion(
                                                            paginatedRegions[i]
                                                                .regionId,
                                                          );
                                                        }
                                                      },
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                                Obx(() => PaginationWidget(
                                      currentPage: regionListController
                                          .currentPage.value,
                                      totalPages:
                                          regionListController.totalPages,
                                      itemsPerPage: regionListController
                                          .itemsPerPage.value,
                                      onPageChanged: (page) =>
                                          regionListController
                                              .setCurrentPage(page),
                                      onItemsPerPageChanged: (count) =>
                                          regionListController
                                              .setItemsPerPage(count),
                                    )),
                              ],
                            );
                          }),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }
}

/*arguments: {
                                          'invoice': Invoice(
                                              consignorName: 'sdf',
                                              consignorPickUpAddress: 'sdf',
                                              conveyNoteNumber: 'sdfd',
                                              customerCNIC: '34534534534534',
                                          customerGstNumber: 'gsdf',
                                            deliveryMode: 'By Road',
                                            destinationAddress: 'ssdf',
                                            customerName: 'ali',
                                            distanceInKilometers: 324,
                                            invoiceNumber: 34,
                                            invoiceStatus: 'pending',
                                            numberOfBags: 343,
                                            orderDate: '12/12/1223',
                                            orderNumber: '123',
                                            productName: 'sona',
                                            regionNumber: 'shahid',
                                            shipmentDate: '12/12/1212',
                                             shipmentNumber: '234',
                                            tasNumber: '343',
                                            truckNumber: '343'
                                          ),
                                          // Pass currentInvoice here
                                        }*/
