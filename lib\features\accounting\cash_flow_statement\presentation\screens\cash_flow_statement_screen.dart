import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../../core/utils/widgets/navigation/drawer_page_scaffold.dart';
import '../../../../../core/utils/widgets/loading_indicator.dart';
import '../controllers/cash_flow_statement_controller.dart';
import '../widgets/cash_flow_statement_form_widget.dart';
import '../widgets/cash_flow_statement_report_widget.dart';

/// Cash Flow Statement main screen
class CashFlowStatementScreen extends StatelessWidget {
  const CashFlowStatementScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(CashFlowStatementController());

    return DrawerPageScaffold(
      pageTitle: 'Cash Flow Statement',
      body: Obx(() {
        if (controller.isLoading) {
          return const LoadingIndicator.overlay(
            message: 'Generating Cash Flow Statement...',
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header section
              _buildHeaderSection(context),
              const SizedBox(height: 24),

              // Form section
              Card(
                elevation: 2,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: CashFlowStatementFormWidget(controller: controller),
                ),
              ),
              const SizedBox(height: 24),

              // Report section
              if (controller.hasReportData) ...[
                Card(
                  elevation: 2,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child:
                        CashFlowStatementReportWidget(controller: controller),
                  ),
                ),
              ],
            ],
          ),
        );
      }),
    );
  }

  /// Build header section with title and description
  Widget _buildHeaderSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Cash Flow Statement',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
        ),
        const SizedBox(height: 8),
        Text(
          'Generate a comprehensive cash flow statement showing cash receipts and payments during a specific period, categorized by operating, investing, and financing activities.',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue[200]!),
          ),
          child: Row(
            children: [
              Icon(Icons.info_outline, color: Colors.blue[700], size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'The cash flow statement provides insights into how cash is generated and used in your business operations.',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.blue[700],
                      ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
