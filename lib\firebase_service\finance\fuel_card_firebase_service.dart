import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dartz/dartz.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';
import 'package:logestics/models/finance/fuel_card_model.dart';
import 'package:logestics/models/finance/fuel_rate_model.dart';
import 'package:uuid/uuid.dart';

class FuelCardFirebaseService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get current user's UID or return 'anonymous' if not authenticated
  String get _uid => FirebaseAuth.instance.currentUser?.uid ?? 'anonymous';

  // Collection references
  CollectionReference get _fuelCardsCollection =>
      _firestore.collection(AppCollection.fuelCardsCollection);

  CollectionReference get _fuelRatesCollection =>
      _firestore.collection(AppCollection.fuelRatesCollection);

  // Create a new fuel card
  Future<Either<FailureObj, SuccessObj>> createFuelCard(
      FuelCardModel fuelCard) async {
    try {
      // Create a new document with auto-generated ID if not provided
      final id = fuelCard.id.isEmpty ? const Uuid().v4() : fuelCard.id;
      final updatedFuelCard = fuelCard.copyWith(id: id);

      // Add the fuel card with user ID
      final fuelCardData = updatedFuelCard.toMap();
      fuelCardData['uid'] = _uid; // Add current user's UID

      await _fuelCardsCollection.doc(id).set(fuelCardData);

      // Return success
      return Right(SuccessObj(
        message: 'Fuel card created successfully',
      ));
    } catch (e) {
      // Return failure
      return Left(FailureObj(
        code: 'fuel-card-create-error',
        message: 'Failed to create fuel card: $e',
      ));
    }
  }

  // Update an existing fuel card
  Future<Either<FailureObj, SuccessObj>> updateFuelCard(
      FuelCardModel fuelCard) async {
    try {
      // First check if the fuel card belongs to the current user
      final fuelCardDoc = await _fuelCardsCollection.doc(fuelCard.id).get();

      if (!fuelCardDoc.exists) {
        return Left(FailureObj(
          code: 'fuel-card-not-found',
          message: 'Fuel card not found',
        ));
      }

      final fuelCardData = fuelCardDoc.data() as Map<String, dynamic>;
      if (fuelCardData['uid'] != _uid) {
        return Left(FailureObj(
          code: 'fuel-card-access-denied',
          message: 'You do not have permission to update this fuel card',
        ));
      }

      // Update the document while preserving the original UID
      final updatedFuelCardData = fuelCard.toMap();
      updatedFuelCardData['uid'] = fuelCardData['uid'];

      await _fuelCardsCollection.doc(fuelCard.id).update(updatedFuelCardData);

      // Return success
      return Right(SuccessObj(
        message: 'Fuel card updated successfully',
      ));
    } catch (e) {
      // Return failure
      return Left(FailureObj(
        code: 'fuel-card-update-error',
        message: 'Failed to update fuel card: $e',
      ));
    }
  }

  // Delete a fuel card
  Future<Either<FailureObj, SuccessObj>> deleteFuelCard(String id) async {
    try {
      // First check if the fuel card belongs to the current user
      final fuelCardDoc = await _fuelCardsCollection.doc(id).get();

      if (!fuelCardDoc.exists) {
        return Left(FailureObj(
          code: 'fuel-card-not-found',
          message: 'Fuel card not found',
        ));
      }

      final fuelCardData = fuelCardDoc.data() as Map<String, dynamic>;
      if (fuelCardData['uid'] != _uid) {
        return Left(FailureObj(
          code: 'fuel-card-access-denied',
          message: 'You do not have permission to delete this fuel card',
        ));
      }

      // Delete the document
      await _fuelCardsCollection.doc(id).delete();

      // Return success
      return Right(SuccessObj(
        message: 'Fuel card deleted successfully',
      ));
    } catch (e) {
      // Return failure
      return Left(FailureObj(
        code: 'fuel-card-delete-error',
        message: 'Failed to delete fuel card: $e',
      ));
    }
  }

  // Get all fuel cards for the current user
  Future<Either<FailureObj, List<FuelCardModel>>> getFuelCards() async {
    try {
      // Get all documents for the current user
      final querySnapshot = await _fuelCardsCollection
          .where('uid', isEqualTo: _uid) // Filter by current user's UID
          .orderBy('createdAt', descending: true)
          .get();

      // Convert to list of FuelCardModel
      final fuelCards = querySnapshot.docs
          .map((doc) =>
              FuelCardModel.fromMap(doc.data() as Map<String, dynamic>))
          .toList();

      // Return success
      return Right(fuelCards);
    } catch (e) {
      // Return failure
      return Left(FailureObj(
        code: 'fuel-card-fetch-error',
        message: 'Failed to get fuel cards: $e',
      ));
    }
  }

  // Get fuel cards by company for the current user
  Future<Either<FailureObj, List<FuelCardModel>>> getFuelCardsByCompany(
      String companyName) async {
    try {
      // Get all documents for the company and current user
      final querySnapshot = await _fuelCardsCollection
          .where('uid', isEqualTo: _uid) // Filter by current user's UID
          .where('companyName', isEqualTo: companyName)
          .orderBy('createdAt', descending: true)
          .get();

      // Convert to list of FuelCardModel
      final fuelCards = querySnapshot.docs
          .map((doc) =>
              FuelCardModel.fromMap(doc.data() as Map<String, dynamic>))
          .toList();

      // Return success
      return Right(fuelCards);
    } catch (e) {
      // Return failure
      return Left(FailureObj(
        code: 'fuel-card-fetch-by-company-error',
        message: 'Failed to get fuel cards for company $companyName: $e',
      ));
    }
  }

  // Add a new fuel rate
  Future<Either<FailureObj, SuccessObj>> addFuelRate(
      FuelRateModel fuelRate) async {
    try {
      // Create a new document with auto-generated ID if not provided
      final id = fuelRate.id.isEmpty ? const Uuid().v4() : fuelRate.id;
      final updatedFuelRate = fuelRate.copyWith(id: id);

      // Add the fuel rate with user ID
      final fuelRateData = updatedFuelRate.toMap();
      fuelRateData['uid'] = _uid; // Add current user's UID

      await _fuelRatesCollection.doc(id).set(fuelRateData);

      // Return success
      return Right(SuccessObj(
        message: 'Fuel rate added successfully',
      ));
    } catch (e) {
      // Return failure
      return Left(FailureObj(
        code: 'fuel-rate-add-error',
        message: 'Failed to add fuel rate: $e',
      ));
    }
  }

  // Get latest fuel rate for a company for the current user
  Future<Either<FailureObj, FuelRateModel>> getLatestFuelRate(
      String companyName) async {
    try {
      // Get the latest rate for the company and current user
      final querySnapshot = await _fuelRatesCollection
          .where('uid', isEqualTo: _uid) // Filter by current user's UID
          .where('companyName', isEqualTo: companyName)
          .orderBy('effectiveDate', descending: true)
          .limit(1)
          .get();

      if (querySnapshot.docs.isEmpty) {
        return Left(FailureObj(
          code: 'fuel-rate-not-found',
          message: 'No fuel rates found for company $companyName',
        ));
      }

      // Convert to FuelRateModel
      final fuelRate = FuelRateModel.fromMap(
          querySnapshot.docs.first.data() as Map<String, dynamic>);

      // Return success
      return Right(fuelRate);
    } catch (e) {
      // Return failure
      return Left(FailureObj(
        code: 'fuel-rate-fetch-error',
        message: 'Failed to get latest fuel rate for company $companyName: $e',
      ));
    }
  }

  // Get fuel rates history for a company for the current user
  Future<Either<FailureObj, List<FuelRateModel>>> getFuelRateHistory(
      String companyName) async {
    try {
      // Get all rates for the company and current user
      final querySnapshot = await _fuelRatesCollection
          .where('uid', isEqualTo: _uid) // Filter by current user's UID
          .where('companyName', isEqualTo: companyName)
          .orderBy('effectiveDate', descending: true)
          .get();

      // Convert to list of FuelRateModel
      final fuelRates = querySnapshot.docs
          .map((doc) =>
              FuelRateModel.fromMap(doc.data() as Map<String, dynamic>))
          .toList();

      // Return success
      return Right(fuelRates);
    } catch (e) {
      // Return failure
      return Left(FailureObj(
        code: 'fuel-rate-history-fetch-error',
        message: 'Failed to get fuel rate history for company $companyName: $e',
      ));
    }
  }
}
