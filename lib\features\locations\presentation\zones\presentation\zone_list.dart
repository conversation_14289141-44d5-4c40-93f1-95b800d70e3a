import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/bindings/app_bindings.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/widgets/data_table_cell.dart';
import 'package:logestics/core/utils/widgets/pagination_widget.dart';
import 'package:logestics/core/utils/widgets/my_text_field.dart';
import 'package:logestics/features/locations/repositories/zone_repository.dart';
import 'package:logestics/features/locations/domain/usecases/zone_use_case/delete_zone_use_case.dart';
import 'package:logestics/features/locations/domain/usecases/zone_use_case/get_zones_use_case.dart';
import 'package:logestics/firebase_service/locations/zone_firebase_service.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';

import '../controller/zone_list_controller.dart';
import 'add_zone_view.dart';

class ZoneList extends StatelessWidget {
  const ZoneList({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    ZoneListController zoneListController;
    try {
      zoneListController = Get.find<ZoneListController>();
    } catch (e) {
      log('Creating temporary controller instance');
      zoneListController = ZoneListController(
        getZonesUseCase: GetZonesUseCase(
          ZoneRepositoryImpl(
            ZoneFirebaseService(),
          ),
        ),
        deleteZoneUseCase: DeleteZoneUseCase(
          ZoneRepositoryImpl(
            ZoneFirebaseService(),
          ),
        ),
      );
    }

    notifier = Provider.of(context, listen: true);

    return GetBuilder<ZoneListController>(
      init: zoneListController,
      builder: (zoneListController) => LayoutBuilder(
        builder: (context, constraints) {
          return StatefulBuilder(
            builder: (context, setState) {
              return Container(
                decoration: BoxDecoration(
                  color: notifier.getBgColor,
                  borderRadius: BorderRadius.circular(10),
                ),
                padding: const EdgeInsets.symmetric(vertical: 15),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 15),
                      child: Get.width < 650
                          ? Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                InkWell(
                                  onTap: () {
                                    Get.to(() => AddZoneView(),
                                        binding: AppBindings());
                                  },
                                  child: Text(
                                    AppStrings.addNewZoneButton,
                                    style: AppTextStyles.addNewInvoiceStyle,
                                  ),
                                ),
                                MyTextFormField(
                                  labelText: AppStrings.searchHint,
                                  hintText: AppStrings.searchHint,
                                  controller:
                                      zoneListController.searchController,
                                ),
                              ],
                            )
                          : Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                InkWell(
                                  onTap: () {
                                    Get.to(() => AddZoneView(),
                                        binding: AppBindings());
                                  },
                                  child: Text(
                                    AppStrings.addNewZoneButton,
                                    style: AppTextStyles.addNewInvoiceStyle,
                                  ),
                                ),
                                SizedBox(
                                  width: 300,
                                  child: MyTextFormField(
                                    labelText: AppStrings.searchHint,
                                    hintText: AppStrings.searchHint,
                                    controller:
                                        zoneListController.searchController,
                                  ),
                                ),
                              ],
                            ),
                    ),
                    Expanded(
                      child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: SizedBox(
                          width: constraints.maxWidth,
                          child: Obx(() {
                            if (zoneListController.isLoading.value) {
                              return const Center(
                                child: CircularProgressIndicator(),
                              );
                            }

                            final paginatedZones =
                                zoneListController.paginatedZones;

                            return Column(
                              children: [
                                Expanded(
                                  child: ListView(
                                    shrinkWrap: true,
                                    children: [
                                      Table(
                                        border: TableBorder(
                                          horizontalInside: BorderSide(
                                            color: notifier.getfillborder,
                                          ),
                                        ),
                                        children: [
                                          TableRow(
                                            decoration: BoxDecoration(
                                              color: notifier.getHoverColor,
                                            ),
                                            children: [
                                              DataTableHeaderCell(
                                                text: 'Sr No.',
                                                textColor: notifier.text,
                                              ),
                                              DataTableHeaderCell(
                                                text: AppStrings.zoneName,
                                                textColor: notifier.text,
                                              ),
                                              DataTableHeaderCell(
                                                text: 'Actions',
                                                textColor: notifier.text,
                                              ),
                                            ],
                                          ),
                                          for (var i = 0;
                                              i < paginatedZones.length;
                                              i++)
                                            TableRow(
                                              children: [
                                                DataTableCell(
                                                  text: ((zoneListController
                                                                      .currentPage
                                                                      .value -
                                                                  1) *
                                                              zoneListController
                                                                  .itemsPerPage
                                                                  .value +
                                                          i +
                                                          1)
                                                      .toString(),
                                                ),
                                                DataTableCell(
                                                  text: paginatedZones[i]
                                                      .zoneName,
                                                ),
                                                DataTableActionsCell(
                                                  menuItems: [
                                                    DataTablePopupMenuItem(
                                                      text: 'Delete',
                                                      icon:
                                                          Icons.delete_outline,
                                                      isDanger: true,
                                                      onTap: () async {
                                                        if (!zoneListController
                                                            .isLoading.value) {
                                                          await zoneListController
                                                              .deleteZone(
                                                            paginatedZones[i]
                                                                .zoneId,
                                                          );
                                                        }
                                                      },
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                                Obx(() => PaginationWidget(
                                      currentPage:
                                          zoneListController.currentPage.value,
                                      totalPages: zoneListController.totalPages,
                                      itemsPerPage:
                                          zoneListController.itemsPerPage.value,
                                      onPageChanged: (page) =>
                                          zoneListController
                                              .setCurrentPage(page),
                                      onItemsPerPageChanged: (count) =>
                                          zoneListController
                                              .setItemsPerPage(count),
                                    )),
                              ],
                            );
                          }),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }
}

/*arguments: {
                                          'invoice': Invoice(
                                              consignorName: 'sdf',
                                              consignorPickUpAddress: 'sdf',
                                              conveyNoteNumber: 'sdfd',
                                              customerCNIC: '34534534534534',
                                          customerGstNumber: 'gsdf',
                                            deliveryMode: 'By Road',
                                            destinationAddress: 'ssdf',
                                            customerName: 'ali',
                                            distanceInKilometers: 324,
                                            invoiceNumber: 34,
                                            invoiceStatus: 'pending',
                                            numberOfBags: 343,
                                            orderDate: '12/12/1223',
                                            orderNumber: '123',
                                            productName: 'sona',
                                            regionNumber: 'shahid',
                                            shipmentDate: '12/12/1212',
                                             shipmentNumber: '234',
                                            tasNumber: '343',
                                            truckNumber: '343'
                                          ),
                                          // Pass currentInvoice here
                                        }*/
