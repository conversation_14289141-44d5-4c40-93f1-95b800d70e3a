enum FiscalPeriodStatus {
  open('Open'),
  closed('Closed'),
  locked('Locked');

  const FiscalPeriodStatus(this.displayName);
  final String displayName;

  static FiscalPeriodStatus fromString(String value) {
    return FiscalPeriodStatus.values.firstWhere(
      (status) => status.name == value,
      orElse: () => FiscalPeriodStatus.open,
    );
  }
}

class FiscalYearModel {
  final String id;
  final String yearName; // e.g., "FY 2024", "2024-2025"
  final DateTime startDate;
  final DateTime endDate;
  final bool isActive; // Only one active fiscal year at a time
  final FiscalPeriodStatus status;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String uid; // Company isolation

  FiscalYearModel({
    required this.id,
    required this.yearName,
    required this.startDate,
    required this.endDate,
    required this.isActive,
    required this.status,
    required this.createdAt,
    this.updatedAt,
    required this.uid,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'yearName': yearName,
      'startDate': startDate.millisecondsSinceEpoch,
      'endDate': endDate.millisecondsSinceEpoch,
      'isActive': isActive,
      'status': status.name,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt?.millisecondsSinceEpoch,
      'uid': uid,
    };
  }

  factory FiscalYearModel.fromJson(Map<String, dynamic> json) {
    return FiscalYearModel(
      id: json['id'] ?? '',
      yearName: json['yearName'] ?? '',
      startDate: DateTime.fromMillisecondsSinceEpoch(json['startDate'] ?? 0),
      endDate: DateTime.fromMillisecondsSinceEpoch(json['endDate'] ?? 0),
      isActive: json['isActive'] ?? false,
      status: FiscalPeriodStatus.fromString(json['status'] ?? 'open'),
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt'] ?? 0),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(json['updatedAt'])
          : null,
      uid: json['uid'] ?? '',
    );
  }

  FiscalYearModel copyWith({
    String? id,
    String? yearName,
    DateTime? startDate,
    DateTime? endDate,
    bool? isActive,
    FiscalPeriodStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? uid,
  }) {
    return FiscalYearModel(
      id: id ?? this.id,
      yearName: yearName ?? this.yearName,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      isActive: isActive ?? this.isActive,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      uid: uid ?? this.uid,
    );
  }

  bool get isCurrent {
    final now = DateTime.now();
    return now.isAfter(startDate) && now.isBefore(endDate) && isActive;
  }

  int get durationInDays => endDate.difference(startDate).inDays;

  @override
  String toString() {
    return 'FiscalYearModel(id: $id, yearName: $yearName, active: $isActive, status: ${status.displayName})';
  }
}

enum PeriodType {
  monthly('Monthly'),
  quarterly('Quarterly'),
  yearly('Yearly');

  const PeriodType(this.displayName);
  final String displayName;

  static PeriodType fromString(String value) {
    return PeriodType.values.firstWhere(
      (type) => type.name == value,
      orElse: () => PeriodType.monthly,
    );
  }
}

class FiscalPeriodModel {
  final String id;
  final String fiscalYearId;
  final String periodName; // e.g., "January 2024", "Q1 2024"
  final PeriodType periodType;
  final DateTime startDate;
  final DateTime endDate;
  final FiscalPeriodStatus status;
  final DateTime? closedDate;
  final String? closedBy; // User ID who closed the period
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String uid; // Company isolation

  FiscalPeriodModel({
    required this.id,
    required this.fiscalYearId,
    required this.periodName,
    required this.periodType,
    required this.startDate,
    required this.endDate,
    required this.status,
    this.closedDate,
    this.closedBy,
    required this.createdAt,
    this.updatedAt,
    required this.uid,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fiscalYearId': fiscalYearId,
      'periodName': periodName,
      'periodType': periodType.name,
      'startDate': startDate.millisecondsSinceEpoch,
      'endDate': endDate.millisecondsSinceEpoch,
      'status': status.name,
      'closedDate': closedDate?.millisecondsSinceEpoch,
      'closedBy': closedBy,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt?.millisecondsSinceEpoch,
      'uid': uid,
    };
  }

  factory FiscalPeriodModel.fromJson(Map<String, dynamic> json) {
    return FiscalPeriodModel(
      id: json['id'] ?? '',
      fiscalYearId: json['fiscalYearId'] ?? '',
      periodName: json['periodName'] ?? '',
      periodType: PeriodType.fromString(json['periodType'] ?? 'monthly'),
      startDate: DateTime.fromMillisecondsSinceEpoch(json['startDate'] ?? 0),
      endDate: DateTime.fromMillisecondsSinceEpoch(json['endDate'] ?? 0),
      status: FiscalPeriodStatus.fromString(json['status'] ?? 'open'),
      closedDate: json['closedDate'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(json['closedDate'])
          : null,
      closedBy: json['closedBy'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt'] ?? 0),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(json['updatedAt'])
          : null,
      uid: json['uid'] ?? '',
    );
  }

  FiscalPeriodModel copyWith({
    String? id,
    String? fiscalYearId,
    String? periodName,
    PeriodType? periodType,
    DateTime? startDate,
    DateTime? endDate,
    FiscalPeriodStatus? status,
    DateTime? closedDate,
    String? closedBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? uid,
  }) {
    return FiscalPeriodModel(
      id: id ?? this.id,
      fiscalYearId: fiscalYearId ?? this.fiscalYearId,
      periodName: periodName ?? this.periodName,
      periodType: periodType ?? this.periodType,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      status: status ?? this.status,
      closedDate: closedDate ?? this.closedDate,
      closedBy: closedBy ?? this.closedBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      uid: uid ?? this.uid,
    );
  }

  bool get isCurrent {
    final now = DateTime.now();
    return now.isAfter(startDate) && now.isBefore(endDate) && status == FiscalPeriodStatus.open;
  }

  bool get canBeClosed => status == FiscalPeriodStatus.open;
  bool get canBeReopened => status == FiscalPeriodStatus.closed;

  int get durationInDays => endDate.difference(startDate).inDays;

  @override
  String toString() {
    return 'FiscalPeriodModel(id: $id, periodName: $periodName, status: ${status.displayName})';
  }
}
