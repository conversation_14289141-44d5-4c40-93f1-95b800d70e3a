import 'dart:developer';

import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/firebase_service/finance/account_firebase_service.dart';
import 'package:logestics/models/finance/account_model.dart';

abstract class AccountRepository {
  Future<Either<FailureObj, List<AccountModel>>> getAccounts();
  Future<Either<FailureObj, List<AccountModel>>> getAccountsForCompany(
      String companyUid);
  Future<Either<FailureObj, SuccessObj>> createAccount(AccountModel account);
  Future<Either<FailureObj, SuccessObj>> updateAccount(AccountModel account);
  Future<Either<FailureObj, SuccessObj>> deleteAccount(String accountId);
}

class AccountRepositoryImpl implements AccountRepository {
  final AccountFirebaseService firebaseService;

  AccountRepositoryImpl(this.firebaseService);

  @override
  Future<Either<FailureObj, SuccessObj>> createAccount(
      AccountModel account) async {
    try {
      log('Repository: Creating account in repository: ${account.name}');
      log('Repository: Account data received: ${account.toString()}');

      log('Repository: About to call Firebase service...');
      await firebaseService.createAccount(account);

      log('Repository: Firebase service call completed successfully');
      return Right(SuccessObj(message: 'Account created successfully'));
    } catch (e, stackTrace) {
      log('Repository: Error creating account: $e');
      log('Repository: Stack trace: $stackTrace');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, List<AccountModel>>> getAccounts() async {
    try {
      log('Fetching all accounts');
      final accounts = await firebaseService.getAccounts();
      return Right(accounts);
    } catch (e) {
      log('Error fetching accounts: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, List<AccountModel>>> getAccountsForCompany(
      String companyUid) async {
    try {
      log('Fetching accounts for company: $companyUid');
      final accounts = await firebaseService.getAccountsForCompany(companyUid);
      return Right(accounts);
    } catch (e) {
      log('Error fetching accounts for company $companyUid: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> deleteAccount(String accountId) async {
    try {
      log('Deleting account: $accountId');
      await firebaseService.deleteAccount(accountId);
      return Right(SuccessObj(message: 'Account deleted successfully'));
    } catch (e) {
      log('Error deleting account: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> updateAccount(
      AccountModel account) async {
    try {
      log('Updating account: ${account.id}');
      await firebaseService.updateAccount(account);
      return Right(SuccessObj(message: 'Account updated successfully'));
    } catch (e) {
      log('Error updating account: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }
}
