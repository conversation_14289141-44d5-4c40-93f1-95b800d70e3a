import 'package:cloud_firestore/cloud_firestore.dart';

enum JournalEntryType {
  manual('Manual Entry'),
  automatic('Automatic Entry'),
  adjustment('Adjustment Entry'),
  closing('Closing Entry'),
  opening('Opening Entry'),
  reversal('Reversal Entry');

  const JournalEntryType(this.displayName);
  final String displayName;

  static JournalEntryType fromString(String value) {
    return JournalEntryType.values.firstWhere(
      (type) => type.name == value,
      orElse: () => JournalEntryType.manual,
    );
  }
}

enum JournalEntryStatus {
  draft('Draft'),
  posted('Posted'),
  reversed('Reversed');

  const JournalEntryStatus(this.displayName);
  final String displayName;

  static JournalEntryStatus fromString(String value) {
    return JournalEntryStatus.values.firstWhere(
      (status) => status.name == value,
      orElse: () => JournalEntryStatus.draft,
    );
  }
}

class JournalEntryLineModel {
  final String id;
  final String journalEntryId;
  final String accountId;
  final String accountNumber;
  final String accountName;
  final double debitAmount;
  final double creditAmount;
  final String description;
  final String? referenceId; // Link to source transaction
  final String? referenceType; // expense, deposit, voucher, etc.
  final DateTime createdAt;

  JournalEntryLineModel({
    required this.id,
    required this.journalEntryId,
    required this.accountId,
    required this.accountNumber,
    required this.accountName,
    required this.debitAmount,
    required this.creditAmount,
    required this.description,
    this.referenceId,
    this.referenceType,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'journalEntryId': journalEntryId,
      'accountId': accountId,
      'accountNumber': accountNumber,
      'accountName': accountName,
      'debitAmount': debitAmount,
      'creditAmount': creditAmount,
      'description': description,
      'referenceId': referenceId,
      'referenceType': referenceType,
      'createdAt': createdAt.millisecondsSinceEpoch,
    };
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'id': id,
      'journalEntryId': journalEntryId,
      'accountId': accountId,
      'accountNumber': accountNumber,
      'accountName': accountName,
      'debitAmount': debitAmount,
      'creditAmount': creditAmount,
      'description': description,
      'referenceId': referenceId,
      'referenceType': referenceType,
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }

  factory JournalEntryLineModel.fromJson(Map<String, dynamic> json) {
    return JournalEntryLineModel(
      id: json['id'] ?? '',
      journalEntryId: json['journalEntryId'] ?? '',
      accountId: json['accountId'] ?? '',
      accountNumber: json['accountNumber'] ?? '',
      accountName: json['accountName'] ?? '',
      debitAmount: (json['debitAmount'] ?? 0).toDouble(),
      creditAmount: (json['creditAmount'] ?? 0).toDouble(),
      description: json['description'] ?? '',
      referenceId: json['referenceId'],
      referenceType: json['referenceType'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt'] ?? 0),
    );
  }

  /// Factory constructor from Firestore document
  factory JournalEntryLineModel.fromFirestore(Map<String, dynamic> data) {
    return JournalEntryLineModel(
      id: data['id'] ?? '',
      journalEntryId: data['journalEntryId'] ?? '',
      accountId: data['accountId'] ?? '',
      accountNumber: data['accountNumber'] ?? '',
      accountName: data['accountName'] ?? '',
      debitAmount: (data['debitAmount'] ?? 0.0).toDouble(),
      creditAmount: (data['creditAmount'] ?? 0.0).toDouble(),
      description: data['description'] ?? '',
      referenceId: data['referenceId'],
      referenceType: data['referenceType'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
    );
  }

  JournalEntryLineModel copyWith({
    String? id,
    String? journalEntryId,
    String? accountId,
    String? accountNumber,
    String? accountName,
    double? debitAmount,
    double? creditAmount,
    String? description,
    String? referenceId,
    String? referenceType,
    DateTime? createdAt,
  }) {
    return JournalEntryLineModel(
      id: id ?? this.id,
      journalEntryId: journalEntryId ?? this.journalEntryId,
      accountId: accountId ?? this.accountId,
      accountNumber: accountNumber ?? this.accountNumber,
      accountName: accountName ?? this.accountName,
      debitAmount: debitAmount ?? this.debitAmount,
      creditAmount: creditAmount ?? this.creditAmount,
      description: description ?? this.description,
      referenceId: referenceId ?? this.referenceId,
      referenceType: referenceType ?? this.referenceType,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  bool get isDebit => debitAmount > 0;
  bool get isCredit => creditAmount > 0;
  double get amount => isDebit ? debitAmount : creditAmount;

  @override
  String toString() {
    return 'JournalEntryLineModel(id: $id, account: $accountNumber - $accountName, debit: $debitAmount, credit: $creditAmount)';
  }
}

class JournalEntryModel {
  final String id;
  final String entryNumber; // Auto-generated sequential number
  final DateTime entryDate;
  final String description;
  final JournalEntryType entryType;
  final JournalEntryStatus status;
  final List<JournalEntryLineModel> lines;
  final double totalDebits;
  final double totalCredits;
  final String? referenceNumber; // External reference
  final String? sourceTransactionId; // Link to original transaction
  final String? sourceTransactionType; // expense, deposit, voucher, etc.
  final String? reversalOfEntryId; // If this reverses another entry
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String createdBy; // User ID
  final String uid; // Company isolation

  JournalEntryModel({
    required this.id,
    required this.entryNumber,
    required this.entryDate,
    required this.description,
    required this.entryType,
    required this.status,
    required this.lines,
    required this.totalDebits,
    required this.totalCredits,
    this.referenceNumber,
    this.sourceTransactionId,
    this.sourceTransactionType,
    this.reversalOfEntryId,
    required this.createdAt,
    this.updatedAt,
    required this.createdBy,
    required this.uid,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'entryNumber': entryNumber,
      'entryDate': entryDate.millisecondsSinceEpoch,
      'description': description,
      'entryType': entryType.name,
      'status': status.name,
      'lines': lines.map((line) => line.toJson()).toList(),
      'totalDebits': totalDebits,
      'totalCredits': totalCredits,
      'referenceNumber': referenceNumber,
      'sourceTransactionId': sourceTransactionId,
      'sourceTransactionType': sourceTransactionType,
      'reversalOfEntryId': reversalOfEntryId,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt?.millisecondsSinceEpoch,
      'createdBy': createdBy,
      'uid': uid,
    };
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'entryNumber': entryNumber,
      'entryDate': Timestamp.fromDate(entryDate),
      'description': description,
      'entryType': entryType.name,
      'status': status.name,
      'lines': lines.map((line) => line.toFirestore()).toList(),
      'totalDebits': totalDebits,
      'totalCredits': totalCredits,
      'referenceNumber': referenceNumber,
      'sourceTransactionId': sourceTransactionId,
      'sourceTransactionType': sourceTransactionType,
      'reversalOfEntryId': reversalOfEntryId,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'createdBy': createdBy,
      'uid': uid,
    };
  }

  factory JournalEntryModel.fromJson(Map<String, dynamic> json) {
    return JournalEntryModel(
      id: json['id'] ?? '',
      entryNumber: json['entryNumber'] ?? '',
      entryDate: DateTime.fromMillisecondsSinceEpoch(json['entryDate'] ?? 0),
      description: json['description'] ?? '',
      entryType: JournalEntryType.fromString(json['entryType'] ?? 'manual'),
      status: JournalEntryStatus.fromString(json['status'] ?? 'draft'),
      lines: (json['lines'] as List<dynamic>?)
              ?.map((line) => JournalEntryLineModel.fromJson(line))
              .toList() ??
          [],
      totalDebits: (json['totalDebits'] ?? 0).toDouble(),
      totalCredits: (json['totalCredits'] ?? 0).toDouble(),
      referenceNumber: json['referenceNumber'],
      sourceTransactionId: json['sourceTransactionId'],
      sourceTransactionType: json['sourceTransactionType'],
      reversalOfEntryId: json['reversalOfEntryId'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt'] ?? 0),
      updatedAt: json['updatedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['updatedAt'])
          : null,
      createdBy: json['createdBy'] ?? '',
      uid: json['uid'] ?? '',
    );
  }

  /// Factory constructor from Firestore document
  factory JournalEntryModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return JournalEntryModel(
      id: doc.id,
      entryNumber: data['entryNumber'] ?? '',
      entryDate: (data['entryDate'] as Timestamp).toDate(),
      description: data['description'] ?? '',
      entryType: JournalEntryType.fromString(data['entryType'] ?? 'manual'),
      status: JournalEntryStatus.fromString(data['status'] ?? 'draft'),
      lines: (data['lines'] as List<dynamic>?)
              ?.map((line) => JournalEntryLineModel.fromFirestore(
                  line as Map<String, dynamic>))
              .toList() ??
          [],
      totalDebits: (data['totalDebits'] ?? 0.0).toDouble(),
      totalCredits: (data['totalCredits'] ?? 0.0).toDouble(),
      referenceNumber: data['referenceNumber'],
      sourceTransactionId: data['sourceTransactionId'],
      sourceTransactionType: data['sourceTransactionType'],
      reversalOfEntryId: data['reversalOfEntryId'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: data['updatedAt'] != null
          ? (data['updatedAt'] as Timestamp).toDate()
          : null,
      createdBy: data['createdBy'] ?? '',
      uid: data['uid'] ?? '',
    );
  }

  bool get isBalanced => (totalDebits - totalCredits).abs() < 0.01;
  bool get canBePosted =>
      status == JournalEntryStatus.draft && isBalanced && lines.isNotEmpty;
  bool get canBeReversed => status == JournalEntryStatus.posted;

  /// Get the difference between debits and credits
  double get balanceDifference => totalDebits - totalCredits;

  /// Get debit lines only
  List<JournalEntryLineModel> get debitLines =>
      lines.where((line) => line.isDebit).toList();

  /// Get credit lines only
  List<JournalEntryLineModel> get creditLines =>
      lines.where((line) => line.isCredit).toList();

  /// Copy with method for immutability
  JournalEntryModel copyWith({
    String? id,
    String? entryNumber,
    DateTime? entryDate,
    String? description,
    JournalEntryType? entryType,
    JournalEntryStatus? status,
    List<JournalEntryLineModel>? lines,
    double? totalDebits,
    double? totalCredits,
    String? referenceNumber,
    String? sourceTransactionId,
    String? sourceTransactionType,
    String? reversalOfEntryId,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? uid,
  }) {
    return JournalEntryModel(
      id: id ?? this.id,
      entryNumber: entryNumber ?? this.entryNumber,
      entryDate: entryDate ?? this.entryDate,
      description: description ?? this.description,
      entryType: entryType ?? this.entryType,
      status: status ?? this.status,
      lines: lines ?? this.lines,
      totalDebits: totalDebits ?? this.totalDebits,
      totalCredits: totalCredits ?? this.totalCredits,
      referenceNumber: referenceNumber ?? this.referenceNumber,
      sourceTransactionId: sourceTransactionId ?? this.sourceTransactionId,
      sourceTransactionType:
          sourceTransactionType ?? this.sourceTransactionType,
      reversalOfEntryId: reversalOfEntryId ?? this.reversalOfEntryId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      uid: uid ?? this.uid,
    );
  }

  @override
  String toString() {
    return 'JournalEntryModel(id: $id, entryNumber: $entryNumber, description: $description, balanced: $isBalanced)';
  }
}
