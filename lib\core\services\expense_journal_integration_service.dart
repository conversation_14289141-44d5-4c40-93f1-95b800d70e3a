import 'dart:developer';
import '../../models/finance/expense_model.dart';
import '../../models/finance/journal_entry_model.dart';
import '../../firebase_service/accounting/general_ledger_firebase_service.dart';
import 'automatic_journal_entry_service.dart';
import 'transaction_account_mapping_service.dart';

/// Service for integrating expense transactions with automatic journal entry generation
class ExpenseJournalIntegrationService {
  final AutomaticJournalEntryService _automaticJournalService;
  final GeneralLedgerFirebaseService _generalLedgerService;
  final TransactionAccountMappingService _mappingService;

  ExpenseJournalIntegrationService(
    this._automaticJournalService,
    this._generalLedgerService,
    this._mappingService,
  );

  /// Generate and create journal entry for an expense transaction
  Future<bool> processExpenseTransaction(ExpenseModel expense) async {
    try {
      log('Processing expense transaction for journal entry: ${expense.title}');

      // Generate journal entry for the expense
      final journalEntry =
          await _automaticJournalService.generateExpenseJournalEntry(
        expense: expense,
        uid: expense.uid,
        createdBy: 'system',
      );

      if (journalEntry == null) {
        log('Failed to generate journal entry for expense: ${expense.id}');
        return false;
      }

      // Create the journal entry in Firebase
      final result =
          await _generalLedgerService.createJournalEntry(journalEntry);

      return result.fold(
        (failure) {
          log('Failed to create journal entry for expense: ${failure.message}');
          return false;
        },
        (success) {
          log('Successfully created journal entry for expense: ${expense.id}');
          return true;
        },
      );
    } catch (e) {
      log('Error processing expense transaction: $e');
      return false;
    }
  }

  /// Batch process multiple expense transactions
  Future<BatchProcessResult> batchProcessExpenseTransactions(
    List<ExpenseModel> expenses,
  ) async {
    int successCount = 0;
    int failureCount = 0;
    final List<String> failedExpenseIds = [];

    log('Batch processing ${expenses.length} expense transactions');

    for (final expense in expenses) {
      final success = await processExpenseTransaction(expense);
      if (success) {
        successCount++;
      } else {
        failureCount++;
        failedExpenseIds.add(expense.id);
      }
    }

    log('Batch processing completed: $successCount successful, $failureCount failed');

    return BatchProcessResult(
      totalProcessed: expenses.length,
      successCount: successCount,
      failureCount: failureCount,
      failedTransactionIds: failedExpenseIds,
    );
  }

  /// Validate expense transaction for journal entry generation
  Future<ExpenseValidationResult> validateExpenseForJournalEntry(
    ExpenseModel expense,
  ) async {
    try {
      final issues = <String>[];

      // Check if expense has valid amount
      if (expense.amount <= 0) {
        issues.add('Expense amount must be greater than zero');
      }

      // Check if expense has valid account
      if (expense.accountId.isEmpty || expense.accountName.isEmpty) {
        issues.add('Expense must have a valid account');
      }

      // Check if expense has valid category
      if (expense.categoryId.isEmpty || expense.categoryName.isEmpty) {
        issues.add('Expense must have a valid category');
      }

      // Check if expense has valid payee
      if (expense.payeeId.isEmpty || expense.payeeName.isEmpty) {
        issues.add('Expense must have a valid payee');
      }

      // Check if required accounts exist for journal entry generation
      final mapping =
          await _mappingService.getExpenseAccountMapping(expense.uid);
      if (mapping == null) {
        issues.add('Required accounts for expense journal entries not found');
      }

      return ExpenseValidationResult(
        isValid: issues.isEmpty,
        issues: issues,
      );
    } catch (e) {
      log('Error validating expense for journal entry: $e');
      return ExpenseValidationResult(
        isValid: false,
        issues: ['Validation error: $e'],
      );
    }
  }

  /// Get journal entries associated with an expense
  Future<List<JournalEntryModel>> getJournalEntriesForExpense(
    String expenseId,
    String uid,
  ) async {
    try {
      final allEntries =
          await _generalLedgerService.getJournalEntries(uid, null, null);

      return allEntries.fold(
        (failure) {
          log('Failed to fetch journal entries: ${failure.message}');
          return <JournalEntryModel>[];
        },
        (entries) {
          return entries
              .where((entry) =>
                  entry.sourceTransactionId == expenseId &&
                  entry.sourceTransactionType == 'expense' &&
                  entry.uid == uid)
              .toList();
        },
      );
    } catch (e) {
      log('Error fetching journal entries for expense: $e');
      return <JournalEntryModel>[];
    }
  }

  /// Check if an expense already has associated journal entries
  Future<bool> hasExistingJournalEntries(String expenseId, String uid) async {
    final entries = await getJournalEntriesForExpense(expenseId, uid);
    return entries.isNotEmpty;
  }

  /// Reverse journal entries for an expense (when expense is deleted/modified)
  Future<bool> reverseExpenseJournalEntries(
      String expenseId, String uid) async {
    try {
      final entries = await getJournalEntriesForExpense(expenseId, uid);

      if (entries.isEmpty) {
        log('No journal entries found for expense: $expenseId');
        return true; // Nothing to reverse
      }

      bool allReversed = true;
      for (final entry in entries) {
        final result = await _generalLedgerService.reverseJournalEntry(
          entry.id,
          'Expense transaction reversed',
          'system', // createdBy parameter
        );

        result.fold(
          (failure) {
            log('Failed to reverse journal entry ${entry.id}: ${failure.message}');
            allReversed = false;
          },
          (success) {
            log('Successfully reversed journal entry: ${entry.id}');
          },
        );
      }

      return allReversed;
    } catch (e) {
      log('Error reversing expense journal entries: $e');
      return false;
    }
  }
}

/// Result class for batch processing operations
class BatchProcessResult {
  final int totalProcessed;
  final int successCount;
  final int failureCount;
  final List<String> failedTransactionIds;

  BatchProcessResult({
    required this.totalProcessed,
    required this.successCount,
    required this.failureCount,
    required this.failedTransactionIds,
  });

  double get successRate =>
      totalProcessed > 0 ? successCount / totalProcessed : 0.0;
  bool get hasFailures => failureCount > 0;
  bool get allSuccessful => failureCount == 0;
}

/// Result class for expense validation
class ExpenseValidationResult {
  final bool isValid;
  final List<String> issues;

  ExpenseValidationResult({
    required this.isValid,
    required this.issues,
  });

  String get issuesText => issues.join(', ');
}
