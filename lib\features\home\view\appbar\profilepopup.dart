import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/features/home/<USER>/drawer_controllers.dart';
import 'package:logestics/features/user/presentation/controllers/user_controller.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';
import 'package:styled_divider/styled_divider.dart';

enum SamplePopup {
  itemOne,
  itemTwo,
  itemThree,
  itemfour,
  itemfive,
  itemsix,
  itemsevan,
  itemeight
}

class Profilepopup extends StatefulWidget {
  const Profilepopup({super.key});

  @override
  State<Profilepopup> createState() => _ProfilepopupState();
}

class _ProfilepopupState extends State<Profilepopup> {
  List profilepopupmenu = [
    "assets/images/user.png",
    "assets/images/settings.png",
    "assets/images/exclamation-circle.png",
    Icons.refresh,
    "assets/images/log-out.png",
  ];

  List profilepopupmenutext = [
    "My profile",
    "Settings",
    "Support",
    "Refresh",
    "Logout",
  ];

  SamplePopup? selectePopup;

  @override
  Widget build(BuildContext context) {
    notifier = Provider.of(context, listen: true);
    final userController = Get.find<UserController>();

    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: PopupMenuButton(
        tooltip: "",
        color: notifier.getBgColor,
        offset: const Offset(0, 50),
        initialValue: selectePopup,
        elevation: 1,
        splashRadius: 1,
        constraints: const BoxConstraints(
          maxWidth: 200,
          minWidth: 200,
        ),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        itemBuilder: (context) => <PopupMenuEntry<SamplePopup>>[
          PopupMenuItem(
            value: SamplePopup.itemOne,
            enabled: false,
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                children: [
                  Obx(() => userController.isLoading.value
                      ? const ListTile(
                          leading: CircularProgressIndicator(),
                          title: Text(
                            'Loading user info...',
                            style: TextStyle(
                              fontFamily: "Outfit",
                              fontSize: 16,
                              color: Colors.grey,
                            ),
                          ),
                        )
                      : ListTile(
                          leading: userController.currentUser.value != null
                              ? CircleAvatar(
                                  backgroundColor: Colors.blue.shade100,
                                  child: Text(
                                    userController.userInitials,
                                    style: TextStyle(
                                      color: Colors.blue.shade700,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                    ),
                                  ),
                                )
                              : Image.asset(
                                  "assets/images/avatar-7 1.png",
                                  height: Get.height / 20,
                                ),
                          title: Text(
                            userController.userName,
                            style: TextStyle(
                              fontFamily: "Outfit",
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: notifier.text,
                            ),
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                userController.userRole,
                                style: const TextStyle(
                                  color: Colors.grey,
                                  fontFamily: "Outfit",
                                  fontSize: 15,
                                ),
                              ),
                              if (userController.userEmail.isNotEmpty)
                                Text(
                                  userController.userEmail,
                                  style: const TextStyle(
                                    color: Colors.grey,
                                    fontFamily: "Outfit",
                                    fontSize: 12,
                                  ),
                                ),
                            ],
                          ),
                        )),
                  StyledDivider(
                    color: notifier.getfillborder,
                    lineStyle: DividerLineStyle.dashed,
                  ),
                  ListView.builder(
                    itemCount: profilepopupmenu.length,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (context, index) {
                      return TextButton(
                        onPressed: () {
                          if (index == 0) {
                            // Show user profile
                            _showUserProfile(userController);
                          } else if (index == 3) {
                            // Refresh user data
                            userController.refreshUserData();
                            Get.back(); // Close popup
                          } else if (index == 4) {
                            // Logout
                            Get.find<MainDrawerController>().signOut();
                          }
                        },
                        child: ListTile(
                          leading: profilepopupmenu[index] is IconData
                              ? Icon(
                                  profilepopupmenu[index],
                                  color: const Color(0xff0165FC),
                                )
                              : Image.asset(
                                  profilepopupmenu[index],
                                  color: const Color(0xff0165FC),
                                ),
                          title: Text(
                            profilepopupmenutext[index],
                            style: TextStyle(
                              fontFamily: "Outfit",
                              color: notifier.text,
                              fontSize: 15,
                            ),
                          ),
                        ),
                      );
                    },
                  )
                ],
              ),
            ),
          ),
        ],
        child: Container(
          height: Get.height / 20,
          decoration: const BoxDecoration(),
          child: Obx(() => Row(
                children: [
                  userController.currentUser.value != null
                      ? CircleAvatar(
                          backgroundColor: Colors.blue.shade100,
                          radius: Get.height / 40,
                          child: Text(
                            userController.userInitials,
                            style: TextStyle(
                              color: Colors.blue.shade700,
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                        )
                      : Image.asset(
                          "assets/images/avatar-7 1.png",
                          height: Get.height / 20,
                        ),
                  const SizedBox(width: 4),
                  Icon(
                    Icons.keyboard_arrow_down,
                    color: notifier.text,
                  ),
                ],
              )),
        ),
      ),
    );
  }

  void _showUserProfile(UserController userController) {
    Get.back(); // Close popup first

    Get.dialog(
      AlertDialog(
        title: const Text('User Profile'),
        content: Obx(() => Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (userController.isLoading.value)
                  const Center(child: CircularProgressIndicator())
                else ...[
                  _buildProfileRow('Name', userController.userName),
                  _buildProfileRow('Email', userController.userEmail),
                  _buildProfileRow('Role', userController.userRole),
                  if (userController.userPhone.isNotEmpty)
                    _buildProfileRow('Phone', userController.userPhone),
                  _buildProfileRow('User ID', userController.userId),
                ],
              ],
            )),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Close'),
          ),
          TextButton(
            onPressed: () {
              userController.refreshUserData();
            },
            child: const Text('Refresh'),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value.isEmpty ? 'Not available' : value),
          ),
        ],
      ),
    );
  }
}
