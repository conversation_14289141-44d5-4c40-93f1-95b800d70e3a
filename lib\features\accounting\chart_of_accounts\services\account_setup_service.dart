import 'dart:developer';
import '../../../../../models/finance/chart_of_accounts_model.dart';
import '../repositories/chart_of_accounts_repository.dart';

class AccountSetupService {
  final ChartOfAccountsRepository repository;

  AccountSetupService(this.repository);

  /// Set up default chart of accounts with predefined categories and common accounts
  Future<bool> setupDefaultChartOfAccounts() async {
    try {
      log('Setting up default chart of accounts...');

      // Check if accounts already exist
      final existingAccountsResult = await repository.getAccounts();
      final hasExistingAccounts = existingAccountsResult.fold(
        (failure) => false,
        (accounts) => accounts.isNotEmpty,
      );

      if (hasExistingAccounts) {
        log('Chart of accounts already exists, skipping setup');
        return true;
      }

      // Create default accounts for each category
      await _createAssetAccounts();
      await _createLiabilityAccounts();
      await _createEquityAccounts();
      await _createRevenueAccounts();
      await _createExpenseAccounts();

      log('Default chart of accounts setup completed successfully');
      return true;
    } catch (e) {
      log('Error setting up default chart of accounts: $e');
      return false;
    }
  }

  /// Create default asset accounts (1000-1999)
  Future<void> _createAssetAccounts() async {
    final assetAccounts = [
      // Current Assets (1000-1199)
      ChartOfAccountsModel(
        id: '',
        accountName: 'Cash and Cash Equivalents',
        accountNumber: '1000',
        category: AccountCategory.assets,
        accountType: AccountType.currentAssets,
        description: 'Cash on hand and in bank accounts',
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: '',
      ),
      ChartOfAccountsModel(
        id: '',
        accountName: 'Petty Cash',
        accountNumber: '1001',
        category: AccountCategory.assets,
        accountType: AccountType.currentAssets,
        description: 'Small cash fund for minor expenses',
        parentAccountId: null, // Will be set after creating parent
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: '',
      ),
      ChartOfAccountsModel(
        id: '',
        accountName: 'Bank Account - Operating',
        accountNumber: '1002',
        category: AccountCategory.assets,
        accountType: AccountType.bank,
        description: 'Primary operating bank account',
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: '',
      ),
      ChartOfAccountsModel(
        id: '',
        accountName: 'Accounts Receivable',
        accountNumber: '1100',
        category: AccountCategory.assets,
        accountType: AccountType.accountsReceivable,
        description: 'Money owed by customers',
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: '',
      ),
      ChartOfAccountsModel(
        id: '',
        accountName: 'Inventory',
        accountNumber: '1200',
        category: AccountCategory.assets,
        accountType: AccountType.inventory,
        description: 'Goods held for sale',
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: '',
      ),
      ChartOfAccountsModel(
        id: '',
        accountName: 'Prepaid Expenses',
        accountNumber: '1300',
        category: AccountCategory.assets,
        accountType: AccountType.currentAssets,
        description: 'Expenses paid in advance',
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: '',
      ),

      // Fixed Assets (1400-1999)
      ChartOfAccountsModel(
        id: '',
        accountName: 'Property, Plant & Equipment',
        accountNumber: '1400',
        category: AccountCategory.assets,
        accountType: AccountType.fixedAssets,
        description: 'Long-term physical assets',
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: '',
      ),
      ChartOfAccountsModel(
        id: '',
        accountName: 'Vehicles',
        accountNumber: '1500',
        category: AccountCategory.assets,
        accountType: AccountType.fixedAssets,
        description: 'Company vehicles and transportation equipment',
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: '',
      ),
      ChartOfAccountsModel(
        id: '',
        accountName: 'Office Equipment',
        accountNumber: '1600',
        category: AccountCategory.assets,
        accountType: AccountType.fixedAssets,
        description: 'Computers, furniture, and office equipment',
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: '',
      ),
      ChartOfAccountsModel(
        id: '',
        accountName: 'Accumulated Depreciation',
        accountNumber: '1900',
        category: AccountCategory.assets,
        accountType: AccountType.fixedAssets,
        description: 'Accumulated depreciation on fixed assets',
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: '',
      ),
    ];

    for (final account in assetAccounts) {
      await _createAccount(account);
    }
  }

  /// Create default liability accounts (2000-2999)
  Future<void> _createLiabilityAccounts() async {
    final liabilityAccounts = [
      // Current Liabilities (2000-2199)
      ChartOfAccountsModel(
        id: '',
        accountName: 'Accounts Payable',
        accountNumber: '2000',
        category: AccountCategory.liabilities,
        accountType: AccountType.accountsPayable,
        description: 'Money owed to suppliers',
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: '',
      ),
      ChartOfAccountsModel(
        id: '',
        accountName: 'Accrued Expenses',
        accountNumber: '2100',
        category: AccountCategory.liabilities,
        accountType: AccountType.currentLiabilities,
        description: 'Expenses incurred but not yet paid',
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: '',
      ),
      ChartOfAccountsModel(
        id: '',
        accountName: 'Short-term Loans',
        accountNumber: '2200',
        category: AccountCategory.liabilities,
        accountType: AccountType.loansPayable,
        description: 'Loans payable within one year',
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: '',
      ),

      // Long-term Liabilities (2300-2999)
      ChartOfAccountsModel(
        id: '',
        accountName: 'Long-term Loans',
        accountNumber: '2300',
        category: AccountCategory.liabilities,
        accountType: AccountType.longTermLiabilities,
        description: 'Loans payable over more than one year',
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: '',
      ),
    ];

    for (final account in liabilityAccounts) {
      await _createAccount(account);
    }
  }

  /// Create default equity accounts (3000-3999)
  Future<void> _createEquityAccounts() async {
    final equityAccounts = [
      ChartOfAccountsModel(
        id: '',
        accountName: 'Owner\'s Equity',
        accountNumber: '3000',
        category: AccountCategory.equity,
        accountType: AccountType.ownersEquity,
        description: 'Owner\'s investment in the business',
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: '',
      ),
      ChartOfAccountsModel(
        id: '',
        accountName: 'Retained Earnings',
        accountNumber: '3100',
        category: AccountCategory.equity,
        accountType: AccountType.retainedEarnings,
        description: 'Accumulated profits retained in the business',
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: '',
      ),
    ];

    for (final account in equityAccounts) {
      await _createAccount(account);
    }
  }

  /// Create default revenue accounts (4000-4999)
  Future<void> _createRevenueAccounts() async {
    final revenueAccounts = [
      ChartOfAccountsModel(
        id: '',
        accountName: 'Service Revenue',
        accountNumber: '4000',
        category: AccountCategory.revenue,
        accountType: AccountType.serviceRevenue,
        description: 'Revenue from logistics and transportation services',
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: '',
      ),
      ChartOfAccountsModel(
        id: '',
        accountName: 'Freight Revenue',
        accountNumber: '4100',
        category: AccountCategory.revenue,
        accountType: AccountType.salesRevenue,
        description: 'Revenue from freight transportation',
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: '',
      ),
      ChartOfAccountsModel(
        id: '',
        accountName: 'Other Income',
        accountNumber: '4900',
        category: AccountCategory.revenue,
        accountType: AccountType.otherRevenue,
        description: 'Miscellaneous income',
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: '',
      ),
    ];

    for (final account in revenueAccounts) {
      await _createAccount(account);
    }
  }

  /// Create default expense accounts (5000-5999)
  Future<void> _createExpenseAccounts() async {
    final expenseAccounts = [
      // Operating Expenses (5000-5499)
      ChartOfAccountsModel(
        id: '',
        accountName: 'Fuel Expenses',
        accountNumber: '5000',
        category: AccountCategory.expenses,
        accountType: AccountType.operatingExpenses,
        description: 'Fuel costs for vehicles',
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: '',
      ),
      ChartOfAccountsModel(
        id: '',
        accountName: 'Vehicle Maintenance',
        accountNumber: '5100',
        category: AccountCategory.expenses,
        accountType: AccountType.operatingExpenses,
        description: 'Vehicle repair and maintenance costs',
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: '',
      ),
      ChartOfAccountsModel(
        id: '',
        accountName: 'Office Expenses',
        accountNumber: '5200',
        category: AccountCategory.expenses,
        accountType: AccountType.administrativeExpenses,
        description: 'General office and administrative expenses',
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: '',
      ),
      ChartOfAccountsModel(
        id: '',
        accountName: 'Salaries and Wages',
        accountNumber: '5300',
        category: AccountCategory.expenses,
        accountType: AccountType.operatingExpenses,
        description: 'Employee compensation',
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: '',
      ),

      // Non-Operating Expenses (5500-5999)
      ChartOfAccountsModel(
        id: '',
        accountName: 'Interest Expense',
        accountNumber: '5500',
        category: AccountCategory.expenses,
        accountType: AccountType.interestExpense,
        description: 'Interest paid on loans and credit',
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: '',
      ),
      ChartOfAccountsModel(
        id: '',
        accountName: 'Depreciation Expense',
        accountNumber: '5600',
        category: AccountCategory.expenses,
        accountType: AccountType.operatingExpenses,
        description: 'Depreciation of fixed assets',
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: '',
      ),
    ];

    for (final account in expenseAccounts) {
      await _createAccount(account);
    }
  }

  /// Helper method to create an account
  Future<void> _createAccount(ChartOfAccountsModel account) async {
    final result = await repository.createAccount(account);
    result.fold(
      (failure) => log(
          'Failed to create account ${account.accountName}: ${failure.message}'),
      (success) => log(
          'Created account: ${account.accountNumber} - ${account.accountName}'),
    );
  }

  /// Check if default accounts are already set up
  Future<bool> isDefaultSetupComplete() async {
    final result = await repository.getAccounts();
    return result.fold(
      (failure) => false,
      (accounts) => accounts.isNotEmpty,
    );
  }

  /// Get setup progress (percentage of default accounts created)
  Future<double> getSetupProgress() async {
    final result = await repository.getAccounts();
    return result.fold(
      (failure) => 0.0,
      (accounts) {
        // Total expected default accounts across all categories
        const totalDefaultAccounts = 20; // Approximate count from above
        return (accounts.length / totalDefaultAccounts).clamp(0.0, 1.0);
      },
    );
  }
}
