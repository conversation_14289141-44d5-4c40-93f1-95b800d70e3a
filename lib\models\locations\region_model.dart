class RegionModel {
  String regionId;
  final String regionName;
  final String zoneId;
  final String zoneName;
  final String regionCode;
  final DateTime createdAt;

  RegionModel({
    required this.regionId,
    required this.regionName,
    required this.zoneId,
    required this.zoneName,
    required this.regionCode,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'zoneId': zoneId,
      'zoneName': zoneName,
      'regionId': regionId,
      'regionName': regionName,
      'regionCode': regionCode,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory RegionModel.fromJson(Map<String, dynamic> json) {
    return RegionModel(
      zoneId: json['zoneId'] ?? '',
      zoneName: json['zoneName'] ?? '',
      regionId: json['regionId'] ?? '',
      regionName: json['regionName'] ?? '',
      regionCode: json['regionCode'] ?? '',
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  @override
  String toString() {
    return 'RegionModel{zoneId: $zoneId, zoneName: $zoneName, regionId: $regionId, regionName: $regionName, regionCode: $regionCode}';
  }
}
