import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:logestics/features/finance/loans/presentation/controllers/loan_history_controller.dart';
import 'package:logestics/features/home/<USER>/theme.dart';

class LoanHistoryFilters extends StatelessWidget {
  final LoanHistoryController controller;

  const LoanHistoryFilters({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    final notifier = Provider.of<ColorNotifier>(context, listen: true);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: notifier.getBgColor,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: notifier.text.withOpacity(0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Filter header
          Row(
            children: [
              Icon(
                Icons.filter_list,
                color: notifier.text,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Filters',
                style: TextStyle(
                  color: notifier.text,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              // Clear filters button
              Obx(() {
                final hasActiveFilters = controller.selectedStatus.value != 'All' ||
                    controller.selectedLoanType.value != 'All' ||
                    controller.dateFilterType.value != 'all' ||
                    controller.searchQuery.value.isNotEmpty;

                if (!hasActiveFilters) return const SizedBox.shrink();

                return TextButton.icon(
                  onPressed: () => controller.clearFilters(),
                  icon: Icon(Icons.clear, size: 16, color: notifier.text.withOpacity(0.7)),
                  label: Text(
                    'Clear All',
                    style: TextStyle(color: notifier.text.withOpacity(0.7)),
                  ),
                );
              }),
            ],
          ),
          const SizedBox(height: 16),

          // Filter controls
          Wrap(
            spacing: 16,
            runSpacing: 12,
            children: [
              // Status filter
              _buildStatusFilter(notifier),
              
              // Loan type filter
              _buildLoanTypeFilter(notifier),
              
              // Date filter
              _buildDateFilter(notifier),
            ],
          ),

          // Date range picker (shown when custom is selected)
          Obx(() {
            if (controller.dateFilterType.value != 'custom') {
              return const SizedBox.shrink();
            }
            return _buildCustomDateRange(notifier);
          }),

          // Quick date filter buttons
          const SizedBox(height: 12),
          _buildQuickDateFilters(notifier),
        ],
      ),
    );
  }

  Widget _buildStatusFilter(ColorNotifier notifier) {
    return SizedBox(
      width: 200,
      child: Obx(() => DropdownButtonFormField<String>(
            decoration: InputDecoration(
              labelText: 'Status',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            value: controller.selectedStatus.value,
            items: controller.statusOptions.map((status) {
              return DropdownMenuItem(
                value: status,
                child: Text(status == 'All' ? 'All Status' : status.toUpperCase()),
              );
            }).toList(),
            onChanged: (value) => controller.filterByStatus(value),
          )),
    );
  }

  Widget _buildLoanTypeFilter(ColorNotifier notifier) {
    return SizedBox(
      width: 200,
      child: Obx(() => DropdownButtonFormField<String>(
            decoration: InputDecoration(
              labelText: 'Loan Type',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            value: controller.selectedLoanType.value,
            items: controller.loanTypeOptions.map((type) {
              return DropdownMenuItem(
                value: type,
                child: Text(type == 'All' ? 'All Types' : type),
              );
            }).toList(),
            onChanged: (value) => controller.filterByLoanType(value),
          )),
    );
  }

  Widget _buildDateFilter(ColorNotifier notifier) {
    return SizedBox(
      width: 200,
      child: Obx(() => DropdownButtonFormField<String>(
            decoration: InputDecoration(
              labelText: 'Date Filter',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            value: controller.dateFilterType.value,
            items: const [
              DropdownMenuItem(value: 'all', child: Text('All Time')),
              DropdownMenuItem(value: 'today', child: Text('Today')),
              DropdownMenuItem(value: 'week', child: Text('This Week')),
              DropdownMenuItem(value: 'month', child: Text('This Month')),
              DropdownMenuItem(value: 'last30days', child: Text('Last 30 Days')),
              DropdownMenuItem(value: 'custom', child: Text('Custom Range')),
            ],
            onChanged: (value) {
              if (value != null) {
                controller.setDateFilter(value);
              }
            },
          )),
    );
  }

  Widget _buildCustomDateRange(ColorNotifier notifier) {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: notifier.getHoverColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: notifier.text.withOpacity(0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Custom Date Range',
            style: TextStyle(
              color: notifier.text,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              // Start date
              Expanded(
                child: Obx(() => InkWell(
                      onTap: () => _selectStartDate(),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                        decoration: BoxDecoration(
                          border: Border.all(color: notifier.text.withOpacity(0.3)),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.calendar_today, size: 16, color: notifier.text.withOpacity(0.6)),
                            const SizedBox(width: 8),
                            Text(
                              DateFormat('dd/MM/yyyy').format(controller.selectedStartDate.value),
                              style: TextStyle(color: notifier.text),
                            ),
                          ],
                        ),
                      ),
                    )),
              ),
              const SizedBox(width: 16),
              Text('to', style: TextStyle(color: notifier.text.withOpacity(0.6))),
              const SizedBox(width: 16),
              // End date
              Expanded(
                child: Obx(() => InkWell(
                      onTap: () => _selectEndDate(),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                        decoration: BoxDecoration(
                          border: Border.all(color: notifier.text.withOpacity(0.3)),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.calendar_today, size: 16, color: notifier.text.withOpacity(0.6)),
                            const SizedBox(width: 8),
                            Text(
                              DateFormat('dd/MM/yyyy').format(controller.selectedEndDate.value),
                              style: TextStyle(color: notifier.text),
                            ),
                          ],
                        ),
                      ),
                    )),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickDateFilters(ColorNotifier notifier) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        _buildQuickFilterButton(notifier, 'Today', () => _setDateRange(DateTime.now(), DateTime.now())),
        _buildQuickFilterButton(notifier, 'This Week', () => _setDateRange(_getStartOfWeek(), DateTime.now())),
        _buildQuickFilterButton(notifier, 'This Month', () => _setDateRange(_getStartOfMonth(), DateTime.now())),
        _buildQuickFilterButton(notifier, 'Last 30 Days', () => _setDateRange(DateTime.now().subtract(const Duration(days: 30)), DateTime.now())),
        _buildQuickFilterButton(notifier, 'Clear Dates', () => controller.setDateFilter('all')),
      ],
    );
  }

  Widget _buildQuickFilterButton(ColorNotifier notifier, String label, VoidCallback onPressed) {
    return OutlinedButton(
      onPressed: onPressed,
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        side: BorderSide(color: notifier.text.withOpacity(0.3)),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(6)),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: notifier.text.withOpacity(0.8),
          fontSize: 12,
        ),
      ),
    );
  }

  void _setDateRange(DateTime startDate, DateTime endDate) {
    controller.setCustomDateRange(startDate, endDate);
  }

  DateTime _getStartOfWeek() {
    final now = DateTime.now();
    return now.subtract(Duration(days: now.weekday - 1));
  }

  DateTime _getStartOfMonth() {
    final now = DateTime.now();
    return DateTime(now.year, now.month, 1);
  }

  Future<void> _selectStartDate() async {
    final date = await showDatePicker(
      context: Get.context!,
      initialDate: controller.selectedStartDate.value,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (date != null) {
      controller.setCustomDateRange(date, controller.selectedEndDate.value);
    }
  }

  Future<void> _selectEndDate() async {
    final date = await showDatePicker(
      context: Get.context!,
      initialDate: controller.selectedEndDate.value,
      firstDate: controller.selectedStartDate.value,
      lastDate: DateTime.now(),
    );
    if (date != null) {
      controller.setCustomDateRange(controller.selectedStartDate.value, date);
    }
  }
}
