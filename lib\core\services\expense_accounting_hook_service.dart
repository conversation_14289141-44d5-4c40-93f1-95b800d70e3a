import 'dart:developer';
import '../../models/finance/expense_model.dart';
import '../../firebase_service/accounting/general_ledger_firebase_service.dart';
import '../../firebase_service/accounting/chart_of_accounts_firebase_service.dart';
import '../../firebase_service/accounting/journal_entry_firebase_service.dart';
import 'automatic_journal_entry_service.dart';
import 'transaction_account_mapping_service.dart';
import 'expense_journal_integration_service.dart';

/// Service that hooks into expense creation to automatically generate journal entries
class ExpenseAccountingHookService {
  static ExpenseAccountingHookService? _instance;
  late final ExpenseJournalIntegrationService _integrationService;
  late final AutomaticJournalEntryService _automaticJournalService;
  late final GeneralLedgerFirebaseService _generalLedgerService;
  late final TransactionAccountMappingService _mappingService;
  late final ChartOfAccountsFirebaseService _chartOfAccountsService;
  late final JournalEntryFirebaseService _journalEntryService;

  ExpenseAccountingHookService._internal() {
    _initializeServices();
  }

  factory ExpenseAccountingHookService() {
    _instance ??= ExpenseAccountingHookService._internal();
    return _instance!;
  }

  void _initializeServices() {
    _chartOfAccountsService = ChartOfAccountsFirebaseService();
    _journalEntryService = JournalEntryFirebaseService();
    _generalLedgerService = GeneralLedgerFirebaseService();
    _mappingService = TransactionAccountMappingService(_chartOfAccountsService);
    _automaticJournalService = AutomaticJournalEntryService(
      _journalEntryService,
      _chartOfAccountsService,
    );
    _integrationService = ExpenseJournalIntegrationService(
      _automaticJournalService,
      _generalLedgerService,
      _mappingService,
    );
  }

  /// Hook method to be called after expense creation
  Future<void> onExpenseCreated(ExpenseModel expense) async {
    try {
      log('Expense accounting hook triggered for: ${expense.title}');

      // Validate expense for journal entry generation
      final validation =
          await _integrationService.validateExpenseForJournalEntry(expense);
      if (!validation.isValid) {
        log('Expense validation failed: ${validation.issuesText}');
        return;
      }

      // Check if journal entries already exist
      final hasExisting = await _integrationService.hasExistingJournalEntries(
        expense.id,
        expense.uid,
      );

      if (hasExisting) {
        log('Journal entries already exist for expense: ${expense.id}');
        return;
      }

      // Process the expense transaction
      final success =
          await _integrationService.processExpenseTransaction(expense);
      if (success) {
        log('Successfully created journal entries for expense: ${expense.id}');
      } else {
        log('Failed to create journal entries for expense: ${expense.id}');
      }
    } catch (e) {
      log('Error in expense accounting hook: $e');
    }
  }

  /// Hook method to be called before expense deletion
  Future<void> onExpenseDeleted(ExpenseModel expense) async {
    try {
      log('Expense deletion hook triggered for: ${expense.title}');

      // Reverse any existing journal entries
      final success = await _integrationService.reverseExpenseJournalEntries(
        expense.id,
        expense.uid,
      );

      if (success) {
        log('Successfully reversed journal entries for deleted expense: ${expense.id}');
      } else {
        log('Failed to reverse journal entries for deleted expense: ${expense.id}');
      }
    } catch (e) {
      log('Error in expense deletion hook: $e');
    }
  }

  /// Hook method to be called after expense update
  Future<void> onExpenseUpdated(
      ExpenseModel oldExpense, ExpenseModel newExpense) async {
    try {
      log('Expense update hook triggered for: ${newExpense.title}');

      // If the amount or account changed, we need to reverse old entries and create new ones
      if (oldExpense.amount != newExpense.amount ||
          oldExpense.accountId != newExpense.accountId ||
          oldExpense.categoryId != newExpense.categoryId) {
        // Reverse old journal entries
        await _integrationService.reverseExpenseJournalEntries(
          oldExpense.id,
          oldExpense.uid,
        );

        // Create new journal entries
        await onExpenseCreated(newExpense);
      }
    } catch (e) {
      log('Error in expense update hook: $e');
    }
  }

  /// Batch process existing expenses to create journal entries
  Future<BatchProcessResult> processExistingExpenses(
      List<ExpenseModel> expenses) async {
    try {
      log('Processing ${expenses.length} existing expenses for journal entries');
      return await _integrationService
          .batchProcessExpenseTransactions(expenses);
    } catch (e) {
      log('Error processing existing expenses: $e');
      return BatchProcessResult(
        totalProcessed: expenses.length,
        successCount: 0,
        failureCount: expenses.length,
        failedTransactionIds: expenses.map((e) => e.id).toList(),
      );
    }
  }

  /// Get journal entries for a specific expense
  Future<List<dynamic>> getExpenseJournalEntries(
      String expenseId, String uid) async {
    return await _integrationService.getJournalEntriesForExpense(
        expenseId, uid);
  }

  /// Check if accounting integration is properly configured
  Future<bool> isAccountingConfigured(String uid) async {
    try {
      final mapping = await _mappingService.getExpenseAccountMapping(uid);
      return mapping != null;
    } catch (e) {
      log('Error checking accounting configuration: $e');
      return false;
    }
  }

  /// Get configuration status for UI display
  Future<AccountingConfigurationStatus> getConfigurationStatus(
      String uid) async {
    try {
      final mapping = await _mappingService.getExpenseAccountMapping(uid);

      if (mapping == null) {
        return AccountingConfigurationStatus(
          isConfigured: false,
          missingAccounts: ['Expense Account', 'Cash Account'],
          message:
              'Required accounts for expense journal entries are not configured',
        );
      }

      return AccountingConfigurationStatus(
        isConfigured: true,
        missingAccounts: [],
        message: 'Accounting integration is properly configured',
      );
    } catch (e) {
      return AccountingConfigurationStatus(
        isConfigured: false,
        missingAccounts: [],
        message: 'Error checking configuration: $e',
      );
    }
  }
}

/// Configuration status for accounting integration
class AccountingConfigurationStatus {
  final bool isConfigured;
  final List<String> missingAccounts;
  final String message;

  AccountingConfigurationStatus({
    required this.isConfigured,
    required this.missingAccounts,
    required this.message,
  });
}
