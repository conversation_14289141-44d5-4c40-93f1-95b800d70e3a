import 'dart:developer';
import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/firebase_service/finance/payer_firebase_service.dart';
import 'package:logestics/models/finance/payer_model.dart';

abstract class PayerRepository {
  Future<Either<FailureObj, List<PayerModel>>> getPayers();
  Future<Either<FailureObj, SuccessObj>> createPayer(PayerModel payer);
  Future<Either<FailureObj, SuccessObj>> updatePayer(PayerModel payer);
  Future<Either<FailureObj, SuccessObj>> deletePayer(String payerId);
}

class PayerRepositoryImpl implements PayerRepository {
  final PayerFirebaseService firebaseService;

  PayerRepositoryImpl(this.firebaseService);

  @override
  Future<Either<FailureObj, SuccessObj>> createPayer(PayerModel payer) async {
    try {
      log('Creating payer in repository: ${payer.name}');
      await firebaseService.createPayer(payer);
      return Right(SuccessObj(message: 'Payer created successfully'));
    } catch (e) {
      log('Error creating payer: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, List<PayerModel>>> getPayers() async {
    try {
      log('Fetching all payers');
      final payers = await firebaseService.getPayers();
      return Right(payers);
    } catch (e) {
      log('Error fetching payers: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> deletePayer(String payerId) async {
    try {
      log('Deleting payer: $payerId');
      await firebaseService.deletePayer(payerId);
      return Right(SuccessObj(message: 'Payer deleted successfully'));
    } catch (e) {
      log('Error deleting payer: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> updatePayer(PayerModel payer) async {
    try {
      log('Updating payer: ${payer.id}');
      await firebaseService.updatePayer(payer);
      return Right(SuccessObj(message: 'Payer updated successfully'));
    } catch (e) {
      log('Error updating payer: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }
}
