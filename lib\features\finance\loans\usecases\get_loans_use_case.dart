import 'package:either_dart/either.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/models/finance/loan_model.dart';
import 'package:logestics/features/finance/loans/repositories/loan_repository.dart';

class GetActiveLoansUseCase {
  final LoanRepository _loanRepository;

  GetActiveLoansUseCase(this._loanRepository);

  Future<Either<FailureObj, List<LoanModel>>> execute() {
    return _loanRepository.getActiveLoans();
  }
}

class GetLoanHistoryUseCase {
  final LoanRepository _loanRepository;

  GetLoanHistoryUseCase(this._loanRepository);

  Future<Either<FailureObj, List<LoanModel>>> execute() {
    return _loanRepository.getLoanHistory();
  }
}
