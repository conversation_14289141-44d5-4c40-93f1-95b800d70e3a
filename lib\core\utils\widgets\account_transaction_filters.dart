import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:logestics/core/utils/widgets/searchfield.dart';
import 'package:logestics/models/finance/account_transaction_model.dart';
import 'package:logestics/features/finance/accounts/presentation/controllers/account_transaction_controller.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';

class AccountTransactionFilters extends StatelessWidget {
  final AccountTransactionController controller;
  final ScrollController? scrollController;

  const AccountTransactionFilters({
    super.key,
    required this.controller,
    this.scrollController,
  });

  @override
  Widget build(BuildContext context) {
    notifier = Provider.of(context, listen: true);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Drag handle
          Center(
            child: Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                color: notifier.text.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),

          // Title
          Text(
            'Filter Transactions',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: notifier.text,
            ),
          ),
          const SizedBox(height: 20),

          // Search input
          Searchfield(
            onChanged: (value) => controller.setSearchQuery(value),
          ),
          const SizedBox(height: 20),

          // Date range filter
          Text(
            'Date Range',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: notifier.text,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Obx(() => OutlinedButton(
                      onPressed: () => _selectDateRange(context),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        controller.selectedDateRange.value != null
                            ? '${DateFormat('dd MMM').format(controller.selectedDateRange.value!.start)} - ${DateFormat('dd MMM').format(controller.selectedDateRange.value!.end)}'
                            : 'Select Date Range',
                        style: TextStyle(color: notifier.text),
                      ),
                    )),
              ),
              const SizedBox(width: 8),
              Obx(() => controller.selectedDateRange.value != null
                  ? IconButton(
                      icon: Icon(Icons.close, color: notifier.text),
                      onPressed: () => controller.setDateRange(null),
                      tooltip: 'Clear Date Range',
                    )
                  : const SizedBox.shrink()),
            ],
          ),
          const SizedBox(height: 20),

          // Transaction type filter
          Text(
            'Transaction Type',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: notifier.text,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: TransactionType.values.map((type) {
              return Obx(() => FilterChip(
                    label: Text(controller.getTransactionTypeName(type)),
                    selected: controller.selectedTypes.contains(type),
                    onSelected: (selected) => controller.toggleTypeFilter(type),
                    selectedColor: controller
                        .getTransactionTypeColor(type)
                        .withValues(alpha: 0.2),
                    checkmarkColor: controller.getTransactionTypeColor(type),
                    avatar: Icon(
                      controller.getTransactionTypeIcon(type),
                      size: 16,
                      color: controller.selectedTypes.contains(type)
                          ? controller.getTransactionTypeColor(type)
                          : notifier.text.withValues(alpha: 0.7),
                    ),
                  ));
            }).toList(),
          ),
          const SizedBox(height: 20),

          // Apply and reset buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () => controller.resetFilters(),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    'Reset Filters',
                    style: TextStyle(color: notifier.text),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: () => Get.back(),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('Apply Filters'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _selectDateRange(BuildContext context) async {
    final initialDateRange = controller.selectedDateRange.value;
    final newDateRange = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365 * 2)),
      lastDate: DateTime.now(),
      initialDateRange: initialDateRange,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Colors.blue,
              onPrimary: Colors.white,
              surface: notifier.getBgColor,
              onSurface: notifier.text,
            ),
          ),
          child: child!,
        );
      },
    );

    if (newDateRange != null) {
      controller.setDateRange(newDateRange);
    }
  }
}
