import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/balance_sheet_controller.dart';

/// Form widget for Balance Sheet report parameters
class BalanceSheetFormWidget extends StatelessWidget {
  const BalanceSheetFormWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<BalanceSheetController>();

    return Form(
      key: controller.formKey,
      child: Column(
        children: [
          // As Of Date and Company Name Row
          Row(
            children: [
              // As Of Date
              Expanded(
                child: TextForm<PERSON>ield(
                  controller: controller.asOfDateController,
                  decoration: const InputDecoration(
                    labelText: 'As of Date',
                    hintText: 'DD/MM/YYYY',
                    prefixIcon: Icon(Icons.calendar_today),
                    border: OutlineInputBorder(),
                    helperText: 'Balance sheet as of this date',
                  ),
                  validator: controller.validateDate,
                  onTap: () =>
                      _selectDate(context, controller.asOfDateController),
                  readOnly: true,
                ),
              ),
              const SizedBox(width: 16),
              // Company Name
              Expanded(
                child: TextForm<PERSON>ield(
                  controller: controller.companyNameController,
                  decoration: const InputDecoration(
                    labelText: 'Company Name',
                    prefixIcon: Icon(Icons.business),
                    border: OutlineInputBorder(),
                  ),
                  validator: controller.validateCompanyName,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Options Row
          Row(
            children: [
              // Include Inactive Accounts
              Expanded(
                child: Obx(() => CheckboxListTile(
                      title: const Text('Include Inactive Accounts'),
                      subtitle:
                          const Text('Include accounts marked as inactive'),
                      value: controller.includeInactiveAccounts,
                      onChanged: (value) =>
                          controller.toggleIncludeInactiveAccounts(),
                      controlAffinity: ListTileControlAffinity.leading,
                    )),
              ),
              const SizedBox(width: 16),
              // Include Zero Balances
              Expanded(
                child: Obx(() => CheckboxListTile(
                      title: const Text('Include Zero Balances'),
                      subtitle:
                          const Text('Include accounts with zero balance'),
                      value: controller.includeZeroBalances,
                      onChanged: (value) =>
                          controller.toggleIncludeZeroBalances(),
                      controlAffinity: ListTileControlAffinity.leading,
                    )),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Generate Button
          SizedBox(
            width: double.infinity,
            child: Obx(() => ElevatedButton.icon(
                  onPressed:
                      controller.isLoading ? null : controller.generateReport,
                  icon: controller.isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.account_balance),
                  label: Text(controller.isLoading
                      ? 'Generating...'
                      : 'Generate Report'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                  ),
                )),
          ),

          const SizedBox(height: 16),

          // Quick Date Filters
          Wrap(
            spacing: 8,
            children: [
              _buildQuickDateButton(
                context,
                'Today',
                controller.setToday,
              ),
              _buildQuickDateButton(
                context,
                'End of Month',
                controller.setEndOfMonth,
              ),
              _buildQuickDateButton(
                context,
                'End of Quarter',
                controller.setEndOfQuarter,
              ),
              _buildQuickDateButton(
                context,
                'End of Year',
                controller.setEndOfYear,
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Information Card
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.blue[700], size: 20),
                    const SizedBox(width: 8),
                    Text(
                      'Balance Sheet Information',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue[700],
                          ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  'The Balance Sheet shows your company\'s financial position at a specific point in time. It displays Assets, Liabilities, and Equity, where Assets = Liabilities + Equity.',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.blue[700],
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build quick date filter button
  Widget _buildQuickDateButton(
    BuildContext context,
    String label,
    VoidCallback onPressed,
  ) {
    return OutlinedButton(
      onPressed: onPressed,
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      child: Text(label),
    );
  }

  /// Select date using date picker
  Future<void> _selectDate(
      BuildContext context, TextEditingController controller) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      controller.text =
          '${picked.day.toString().padLeft(2, '0')}/${picked.month.toString().padLeft(2, '0')}/${picked.year}';
    }
  }
}
