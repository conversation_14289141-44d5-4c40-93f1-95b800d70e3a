import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/firebase_service/firebase_auth_service.dart';
import 'package:logestics/firebase_service/finance/company_firebase_service.dart';
import 'package:logestics/core/utils/mixins/auto_refresh_mixin.dart';
import 'package:logestics/features/finance/accounts/repositories/account_repository.dart';
import 'package:logestics/features/finance/loans/usecases/get_companies_use_case.dart';
import 'package:logestics/features/finance/loans/usecases/get_loan_requests_use_case.dart';
import 'package:logestics/features/finance/loans/usecases/get_loans_use_case.dart';
import 'package:logestics/features/finance/loans/usecases/loan_action_use_cases.dart';
import 'package:logestics/features/finance/loans/usecases/request_loan_use_case.dart';
import 'package:logestics/firebase_service/finance/loan_firebase_service.dart';
import 'package:logestics/models/finance/account_model.dart';
import 'package:logestics/models/finance/loan_model.dart';
import 'package:logestics/models/user_model.dart';

class LoansController extends GetxController with AutoRefreshMixin {
  final RequestLoanUseCase _requestLoanUseCase;
  final GetIncomingLoanRequestsUseCase _getIncomingLoanRequestsUseCase;
  final GetOutgoingLoanRequestsUseCase _getOutgoingLoanRequestsUseCase;
  final ApproveLoanUseCase _approveLoanUseCase;
  final RejectLoanUseCase _rejectLoanUseCase;
  final RepayLoanUseCase _repayLoanUseCase;
  final GetActiveLoansUseCase _getActiveLoansUseCase;
  final GetLoanHistoryUseCase _getLoanHistoryUseCase;
  final GetUsersUseCase _getUsersUseCase;
  final AccountRepository _accountRepository;
  final LoanFirebaseService _loanService;
  final FirebaseAuthService _authService = Get.find<FirebaseAuthService>();

  // Real-time stream subscriptions
  StreamSubscription? _incomingRequestsSubscription;
  StreamSubscription? _outgoingRequestsSubscription;
  StreamSubscription? _activeLoansSubscription;
  StreamSubscription? _loanHistorySubscription;

  LoansController({
    required RequestLoanUseCase requestLoanUseCase,
    required GetIncomingLoanRequestsUseCase getIncomingLoanRequestsUseCase,
    required GetOutgoingLoanRequestsUseCase getOutgoingLoanRequestsUseCase,
    required ApproveLoanUseCase approveLoanUseCase,
    required RejectLoanUseCase rejectLoanUseCase,
    required RepayLoanUseCase repayLoanUseCase,
    required GetActiveLoansUseCase getActiveLoansUseCase,
    required GetLoanHistoryUseCase getLoanHistoryUseCase,
    required GetUsersUseCase getUsersUseCase,
    required AccountRepository accountRepository,
    required LoanFirebaseService loanService,
  })  : _requestLoanUseCase = requestLoanUseCase,
        _getIncomingLoanRequestsUseCase = getIncomingLoanRequestsUseCase,
        _getOutgoingLoanRequestsUseCase = getOutgoingLoanRequestsUseCase,
        _approveLoanUseCase = approveLoanUseCase,
        _rejectLoanUseCase = rejectLoanUseCase,
        _repayLoanUseCase = repayLoanUseCase,
        _getActiveLoansUseCase = getActiveLoansUseCase,
        _getLoanHistoryUseCase = getLoanHistoryUseCase,
        _getUsersUseCase = getUsersUseCase,
        _accountRepository = accountRepository,
        _loanService = loanService;

  // Form key for validation
  final formKey = GlobalKey<FormState>();

  // Current user and company
  final Rx<UserModel?> currentUser = Rx<UserModel?>(null);

  // Loan lists
  final RxList<LoanModel> incomingRequests = <LoanModel>[].obs;
  final RxList<LoanModel> outgoingRequests = <LoanModel>[].obs;
  final RxList<LoanModel> activeLoans = <LoanModel>[].obs;
  final RxList<LoanModel> loanHistory = <LoanModel>[].obs;

  // Accounts list for account selection
  final RxList<AccountModel> accounts = <AccountModel>[].obs;
  final Rx<AccountModel?> selectedAccount = Rx<AccountModel?>(null);

  // Users list for loan requests
  final RxList<UserModel> availableUsers = <UserModel>[].obs;
  final Rx<String?> selectedUser = Rx<String?>(null);

  // Form controllers
  final amountController = TextEditingController();
  final companyController = TextEditingController();
  final notesController = TextEditingController();
  final reasonController = TextEditingController();

  // Date selection
  final Rx<DateTime?> selectedDueDate =
      Rx<DateTime?>(DateTime.now().add(const Duration(days: 30)));

  // Loading states
  final RxBool isLoadingIncoming = false.obs;
  final RxBool isLoadingOutgoing = false.obs;
  final RxBool isLoadingActive = false.obs;
  final RxBool isLoadingHistory = false.obs;
  final RxBool isProcessing = false.obs;
  final RxBool isLoadingAccounts = false.obs;
  final RxBool isLoadingUsers = false.obs;

  // Search functionality for active loans
  final searchQuery = ''.obs;
  final searchController = TextEditingController();
  Timer? _searchDebounceTimer;

  // Filtered active loans
  final RxList<LoanModel> filteredActiveLoans = <LoanModel>[].obs;

  @override
  void onInit() {
    super.onInit();
    _initializeCurrentUser();
    _setupSearchListener();
    // Don't start real-time listeners immediately
    // They will be started when specific tabs are accessed
  }

  @override
  void onClose() {
    // Dispose controllers
    amountController.dispose();
    companyController.dispose();
    notesController.dispose();
    reasonController.dispose();
    searchController.dispose();

    // Cancel timers
    _searchDebounceTimer?.cancel();

    // Cancel stream subscriptions
    _incomingRequestsSubscription?.cancel();
    _outgoingRequestsSubscription?.cancel();
    _activeLoansSubscription?.cancel();
    _loanHistorySubscription?.cancel();

    super.onClose();
  }

  void _initializeCurrentUser() {
    final authUser = _authService.currentUser;
    if (authUser != null) {
      _loadUserDetailsFromDatabase(authUser);
    } else {
      log('No authenticated user found');
      Get.snackbar('Error', 'Please log in to access loans');
    }
  }

  /// Load user details from database to get proper company name
  Future<void> _loadUserDetailsFromDatabase(User authUser) async {
    try {
      final companyService = CompanyFirebaseService();
      final result = await companyService.getUserById(authUser.uid);

      result.fold(
        (failure) {
          // If user not found in Firestore, create basic user from auth
          log('User not found in Firestore, using auth data: ${failure.message}');
          _createUserFromAuth(authUser);
        },
        (userModel) {
          // User found in Firestore - use registered company name
          currentUser.value = userModel;
          log('Loans Controller: User loaded from Firestore with company: ${userModel.companyName}');
        },
      );
    } catch (e) {
      log('Error loading user details for loans: $e');
      _createUserFromAuth(authUser);
    }
  }

  /// Fallback method to create user from auth data
  void _createUserFromAuth(User authUser) {
    currentUser.value = UserModel(
      uid: authUser.uid,
      email: authUser.email ?? '',
      companyName:
          authUser.displayName ?? _extractNameFromEmail(authUser.email ?? ''),
      phoneNumber: authUser.phoneNumber ?? '',
    );
    log('Loans Controller: Created user from auth: ${currentUser.value?.companyName}');
  }

  /// Extract name from email as fallback
  String _extractNameFromEmail(String email) {
    if (email.isEmpty) return 'Current User';

    // Extract name from email (before @)
    final name = email.split('@').first;

    // Capitalize first letter and replace dots/underscores with spaces
    return name
        .replaceAll(RegExp(r'[._]'), ' ')
        .split(' ')
        .map((word) => word.isNotEmpty
            ? '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}'
            : '')
        .join(' ')
        .trim();
  }

  /// Implementation of AutoRefreshMixin.refreshData
  @override
  Future<void> refreshData() async {
    log('Refreshing all loan data...');

    // Load accounts and users data
    await Future.wait([
      loadAccounts(),
      loadUsers(),
    ]);

    // Set initial loading states for real-time data
    isLoadingIncoming.value = true;
    isLoadingOutgoing.value = true;
    isLoadingActive.value = true;
    isLoadingHistory.value = true;

    // Real-time listeners will update the data automatically
    // If they don't respond within 10 seconds, fall back to manual fetch
    Timer(const Duration(seconds: 10), () {
      if (isLoadingIncoming.value ||
          isLoadingOutgoing.value ||
          isLoadingActive.value ||
          isLoadingHistory.value) {
        log('Real-time listeners timeout, falling back to manual fetch');
        _fallbackManualFetch();
      }
    });
  }

  Future<void> _fallbackManualFetch() async {
    if (currentUser.value == null) return;

    final uid = currentUser.value!.uid;
    await Future.wait([
      if (isLoadingIncoming.value) fetchIncomingLoanRequests(uid),
      if (isLoadingOutgoing.value) fetchOutgoingLoanRequests(uid),
      if (isLoadingActive.value) fetchActiveLoans(uid),
      if (isLoadingHistory.value) fetchLoanHistory(uid),
    ]);
  }

  // Load accounts for selection
  Future<void> loadAccounts() async {
    isLoadingAccounts.value = true;
    try {
      log('Loading accounts for loan selection');
      final accountsResult = await _accountRepository.getAccounts();

      accountsResult.fold(
        (error) {
          log('Error loading accounts: ${error.message}');
          Get.snackbar('Error', 'Failed to load accounts: ${error.message}');
        },
        (accountsList) {
          log('Loaded ${accountsList.length} accounts');
          accounts.value = accountsList;
          if (accounts.isNotEmpty && selectedAccount.value == null) {
            selectedAccount.value = accounts.first;
            log('Selected default account: ${selectedAccount.value?.name}');
          }
        },
      );
    } catch (e) {
      log('Exception loading accounts: $e');
      Get.snackbar('Error', 'Failed to load accounts');
    } finally {
      isLoadingAccounts.value = false;
    }
  }

  // Load users for selection with enhanced error handling
  Future<void> loadUsers() async {
    isLoadingUsers.value = true;
    try {
      log('Loading users for loan requests');
      final result = await _getUsersUseCase.call();

      result.fold(
        (error) {
          log('Error loading users: ${error.message}');

          // Provide more specific error messages
          String userMessage = 'Failed to load users';
          if (error.code == 'no-users-found') {
            userMessage =
                'No users found in the system. Please contact administrator.';
          } else if (error.code == 'no-other-users') {
            userMessage = 'No other users available for loan requests.';
          } else if (error.code == 'no-valid-users') {
            userMessage = 'No valid users found. Please contact administrator.';
          } else if (error.code == 'firebase-error') {
            userMessage =
                'Connection error. Please check your internet connection.';
          }

          Get.snackbar('Error', userMessage);
          availableUsers.value = [];
        },
        (users) {
          log('Successfully loaded ${users.length} users');

          // Filter out current user if somehow included
          final currentUserId = currentUser.value?.uid;
          final filteredUsers =
              users.where((user) => user.uid != currentUserId).toList();

          availableUsers.value = filteredUsers;

          if (availableUsers.isNotEmpty && selectedUser.value == null) {
            selectedUser.value = availableUsers.first.uid;
            log('Selected default user: ${availableUsers.first.companyName}');
          } else if (availableUsers.isEmpty) {
            log('No users available after filtering');
            Get.snackbar('Info', 'No other users available for loan requests');
          }
        },
      );
    } catch (e) {
      log('Exception loading users: $e');
      Get.snackbar('Error', 'Failed to load users: $e');
      availableUsers.value = [];
    } finally {
      isLoadingUsers.value = false;
    }
  }

  // Set selected user
  void setSelectedUser(String userId) {
    selectedUser.value = userId;
    log('Selected user: ${getUserName(userId)}');
  }

  // Get user name from ID
  String getUserName(String userId) {
    final user = availableUsers.firstWhereOrNull((u) => u.uid == userId);
    return user?.companyName ?? 'Unknown User';
  }

  // Set selected account
  void setSelectedAccount(AccountModel account) {
    selectedAccount.value = account;
    log('Selected account: ${account.name}');
  }

  // Set due date
  void setDueDate(DateTime date) {
    selectedDueDate.value = date;
    log('Selected due date: ${date.toIso8601String()}');
  }

  // Backward compatibility method names for the UI
  String getCompanyName(String uid) => getUserName(uid);
  void setSelectedCompany(String uid) => setSelectedUser(uid);
  RxList<UserModel> get availableCompanies => availableUsers;
  Rx<String?> get selectedCompany => selectedUser;

  // Load companies for backward compatibility
  Future<void> loadCompanies() => loadUsers();

  // Refresh all loan data
  Future<void> loadAllLoans() async {
    log('Loading all loans data');
    await refreshData();
  }

  // Refresh all loan data
  Future<void> refreshAll() async {
    log('Refreshing all loan data');
    await refreshData();
  }

  // Tab-specific loading methods
  Future<void> loadLoanRequestsTab() async {
    log('Loading loan requests tab data');
    await Future.wait([
      loadAccounts(),
      loadUsers(),
    ]);

    // Start real-time listeners for requests only
    _startRequestsListeners();
  }

  Future<void> loadActiveLoansTab() async {
    log('Loading active loans tab data');
    await loadAccounts();

    // Start real-time listener for active loans only
    _startActiveLoansListener();
  }

  Future<void> loadLoanHistoryTab() async {
    log('Loading loan history tab data');

    // Start real-time listener for loan history only
    _startLoanHistoryListener();
  }

  void _startRequestsListeners() {
    try {
      // Set loading states
      isLoadingIncoming.value = true;
      isLoadingOutgoing.value = true;

      // Listen to incoming loan requests
      _incomingRequestsSubscription =
          _loanService.listenToIncomingLoanRequests().listen(
        (loans) {
          log('Real-time update: ${loans.length} incoming loan requests');
          incomingRequests.value = loans;
          isLoadingIncoming.value = false;
        },
        onError: (error) {
          log('Error in incoming requests stream: $error');
          isLoadingIncoming.value = false;
          Get.snackbar(
            'Error',
            'Failed to load incoming loan requests: $error',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red.shade100,
            colorText: Colors.red.shade800,
          );
        },
      );

      // Listen to outgoing loan requests
      _outgoingRequestsSubscription =
          _loanService.listenToOutgoingLoanRequests().listen(
        (loans) {
          log('Real-time update: ${loans.length} outgoing loan requests');
          outgoingRequests.value = loans;
          isLoadingOutgoing.value = false;
        },
        onError: (error) {
          log('Error in outgoing requests stream: $error');
          isLoadingOutgoing.value = false;
          Get.snackbar(
            'Error',
            'Failed to load outgoing loan requests: $error',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red.shade100,
            colorText: Colors.red.shade800,
          );
        },
      );

      log('Started real-time listeners for loan requests');
    } catch (e) {
      log('Error starting requests listeners: $e');
      isLoadingIncoming.value = false;
      isLoadingOutgoing.value = false;
      Get.snackbar(
        'Error',
        'Failed to initialize loan requests: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  void _startActiveLoansListener() {
    try {
      // Set loading state
      isLoadingActive.value = true;

      // Listen to active loans
      _activeLoansSubscription = _loanService.listenToActiveLoans().listen(
        (loans) {
          log('Real-time update: ${loans.length} active loans');
          activeLoans.value = loans;
          _updateFilteredActiveLoans();
          isLoadingActive.value = false;
        },
        onError: (error) {
          log('Error in active loans stream: $error');
          isLoadingActive.value = false;
          Get.snackbar(
            'Error',
            'Failed to load active loans: $error',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red.shade100,
            colorText: Colors.red.shade800,
          );
        },
      );

      log('Started real-time listener for active loans');
    } catch (e) {
      log('Error starting active loans listener: $e');
      isLoadingActive.value = false;
      Get.snackbar(
        'Error',
        'Failed to initialize active loans: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  void _startLoanHistoryListener() {
    try {
      // Set loading state
      isLoadingHistory.value = true;

      // Listen to loan history
      _loanHistorySubscription = _loanService.listenToLoanHistory().listen(
        (loans) {
          log('Real-time update: ${loans.length} loan history entries');
          loanHistory.value = loans;
          isLoadingHistory.value = false;
        },
        onError: (error) {
          log('Error in loan history stream: $error');
          isLoadingHistory.value = false;
          Get.snackbar(
            'Error',
            'Failed to load loan history: $error',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red.shade100,
            colorText: Colors.red.shade800,
          );
        },
      );

      log('Started real-time listener for loan history');
    } catch (e) {
      log('Error starting loan history listener: $e');
      isLoadingHistory.value = false;
      Get.snackbar(
        'Error',
        'Failed to initialize loan history: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  // Manual fetch methods (fallback for real-time listeners)
  Future<void> fetchIncomingLoanRequests(String uid) async {
    isLoadingIncoming.value = true;
    try {
      final result = await _getIncomingLoanRequestsUseCase.execute();
      result.fold(
        (error) {
          log('Error fetching incoming loan requests: ${error.message}');
          Get.snackbar('Error', 'Failed to load incoming loan requests');
        },
        (loans) {
          log('Fetched ${loans.length} incoming loan requests');
          incomingRequests.value = loans;
        },
      );
    } catch (e) {
      log('Exception fetching incoming loan requests: $e');
      Get.snackbar('Error', 'An unexpected error occurred');
    } finally {
      isLoadingIncoming.value = false;
    }
  }

  Future<void> fetchOutgoingLoanRequests(String uid) async {
    isLoadingOutgoing.value = true;
    try {
      final result = await _getOutgoingLoanRequestsUseCase.execute();
      result.fold(
        (error) {
          log('Error fetching outgoing loan requests: ${error.message}');
          Get.snackbar('Error', 'Failed to load outgoing loan requests');
        },
        (loans) {
          log('Fetched ${loans.length} outgoing loan requests');
          outgoingRequests.value = loans;
        },
      );
    } catch (e) {
      log('Exception fetching outgoing loan requests: $e');
      Get.snackbar('Error', 'An unexpected error occurred');
    } finally {
      isLoadingOutgoing.value = false;
    }
  }

  Future<void> fetchActiveLoans(String uid) async {
    isLoadingActive.value = true;
    try {
      final result = await _getActiveLoansUseCase.execute();
      result.fold(
        (error) {
          log('Error fetching active loans: ${error.message}');
          Get.snackbar('Error', 'Failed to load active loans');
        },
        (loans) {
          log('Fetched ${loans.length} active loans');
          activeLoans.value = loans;
        },
      );
    } catch (e) {
      log('Exception fetching active loans: $e');
      Get.snackbar('Error', 'An unexpected error occurred');
    } finally {
      isLoadingActive.value = false;
    }
  }

  Future<void> fetchLoanHistory(String uid) async {
    isLoadingHistory.value = true;
    try {
      final result = await _getLoanHistoryUseCase.execute();
      result.fold(
        (error) {
          log('Error fetching loan history: ${error.message}');
          Get.snackbar('Error', 'Failed to load loan history');
        },
        (loans) {
          log('Fetched ${loans.length} loan history entries');
          loanHistory.value = loans;
        },
      );
    } catch (e) {
      log('Exception fetching loan history: $e');
      Get.snackbar('Error', 'An unexpected error occurred');
    } finally {
      isLoadingHistory.value = false;
    }
  }

  // Form validation methods
  String? validateAccount(AccountModel? account) {
    if (account == null) {
      return 'Please select an account';
    }
    return null;
  }

  String? validateAmount(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter an amount';
    }
    final amount = double.tryParse(value);
    if (amount == null || amount <= 0) {
      return 'Please enter a valid amount';
    }
    return null;
  }

  String? validateCompany(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please select a user';
    }
    if (availableUsers.isEmpty) {
      return 'No users available. Please contact administrator.';
    }
    return null;
  }

  String? validateDueDate(String? value) {
    if (selectedDueDate.value == null) {
      return 'Please select a due date';
    }
    if (selectedDueDate.value!.isBefore(DateTime.now())) {
      return 'Due date must be in the future';
    }
    return null;
  }

  // Submit loan request with enhanced validation
  Future<bool> submitLoanRequest() async {
    if (currentUser.value == null) {
      Get.snackbar('Error', 'User not authenticated');
      return false;
    }

    if (selectedAccount.value == null) {
      Get.snackbar('Error', 'Please select an account');
      return false;
    }

    if (selectedUser.value == null || availableUsers.isEmpty) {
      Get.snackbar('Error', 'No users available for loan requests');
      return false;
    }

    final amountText = amountController.text.trim();
    if (amountText.isEmpty) {
      Get.snackbar('Error', 'Please enter an amount');
      return false;
    }

    final amount = double.tryParse(amountText);
    if (amount == null || amount <= 0) {
      Get.snackbar('Error', 'Please enter a valid amount');
      return false;
    }

    if (selectedDueDate.value == null ||
        selectedDueDate.value!.isBefore(DateTime.now())) {
      Get.snackbar('Error', 'Please select a valid due date');
      return false;
    }

    isProcessing.value = true;
    try {
      final userId = selectedUser.value!;
      final userName = getUserName(userId);

      log('Submitting loan request: amount=$amount, to=$userName');

      // Create the loan model
      final loan = LoanModel(
        id: '', // Will be generated by the API
        uid: currentUser.value!.uid,
        requestedBy: currentUser.value!.uid,
        requestedByName: currentUser.value!.companyName,
        requestedTo: userId,
        requestedToName: userName,
        fromAccountId: '', // Will be set when approved
        toAccountId: selectedAccount.value!.id,
        fromAccountName: '', // Will be set when approved
        toAccountName: selectedAccount.value!.name,
        amount: amount,
        dueDate: selectedDueDate.value!,
        status: 'pending',
        requestDate: DateTime.now(),
        notes: notesController.text.trim(),
      );

      final result = await _requestLoanUseCase.execute(loan);

      return result.fold(
        (error) {
          log('Error requesting loan: ${error.message}');
          Get.snackbar('Error', error.message);
          return false;
        },
        (success) {
          log('Loan request submitted successfully');
          Get.snackbar('Success', success.message);

          // Clear form
          amountController.clear();
          notesController.clear();
          selectedDueDate.value = DateTime.now().add(const Duration(days: 30));

          // Reset selections to first available
          if (accounts.isNotEmpty) selectedAccount.value = accounts.first;
          if (availableUsers.isNotEmpty) {
            selectedUser.value = availableUsers.first.uid;
          }

          // Data will be updated automatically via real-time listeners
          return true;
        },
      );
    } catch (e) {
      log('Exception requesting loan: $e');
      Get.snackbar('Error', 'An unexpected error occurred');
      return false;
    } finally {
      isProcessing.value = false;
    }
  }

  // Approve a loan request
  Future<bool> approveLoanRequest(String loanId) async {
    if (selectedAccount.value == null) {
      Get.snackbar('Error', 'Please select an account to approve from');
      return false;
    }

    isProcessing.value = true;
    try {
      log('Approving loan request: $loanId from account: ${selectedAccount.value!.name}');

      final result =
          await _approveLoanUseCase.execute(loanId, selectedAccount.value!.id);

      return result.fold(
        (error) {
          log('Error approving loan: ${error.message}');
          Get.snackbar('Error', error.message);
          return false;
        },
        (success) {
          log('Loan approved successfully');
          Get.snackbar('Success', success.message);

          // Data will be updated automatically via real-time listeners
          return true;
        },
      );
    } catch (e) {
      log('Exception approving loan: $e');
      Get.snackbar('Error', 'An unexpected error occurred');
      return false;
    } finally {
      isProcessing.value = false;
    }
  }

  // Reject a loan request
  Future<bool> rejectLoanRequest(String loanId) async {
    final reason = reasonController.text.trim();
    if (reason.isEmpty) {
      Get.snackbar('Error', 'Please provide a reason for rejection');
      return false;
    }

    isProcessing.value = true;
    try {
      log('Rejecting loan request: $loanId, reason: $reason');

      final result = await _rejectLoanUseCase.execute(loanId, reason);

      return result.fold(
        (error) {
          log('Error rejecting loan: ${error.message}');
          Get.snackbar('Error', error.message);
          return false;
        },
        (success) {
          log('Loan rejected successfully');
          Get.snackbar('Success', success.message);
          reasonController.clear();

          // Data will be updated automatically via real-time listeners
          return true;
        },
      );
    } catch (e) {
      log('Exception rejecting loan: $e');
      Get.snackbar('Error', 'An unexpected error occurred');
      return false;
    } finally {
      isProcessing.value = false;
    }
  }

  // Repay a loan
  Future<bool> repayLoan(String loanId, String fromAccountId) async {
    isProcessing.value = true;
    try {
      log('Repaying loan: $loanId from account: $fromAccountId');

      final result = await _repayLoanUseCase.execute(loanId, fromAccountId);

      return result.fold(
        (error) {
          log('Error repaying loan: ${error.message}');
          Get.snackbar('Error', error.message);
          return false;
        },
        (success) {
          log('Loan repaid successfully');
          Get.snackbar('Success', success.message);

          // Data will be updated automatically via real-time listeners
          return true;
        },
      );
    } catch (e) {
      log('Exception repaying loan: $e');
      Get.snackbar('Error', 'An unexpected error occurred');
      return false;
    } finally {
      isProcessing.value = false;
    }
  }

  // Search functionality methods
  void _setupSearchListener() {
    searchController.addListener(() {
      // Cancel previous timer
      _searchDebounceTimer?.cancel();

      // Set new timer with 300ms delay
      _searchDebounceTimer = Timer(const Duration(milliseconds: 300), () {
        searchQuery.value = searchController.text;
        _applyActiveLoansFilter();
      });
    });
  }

  void _applyActiveLoansFilter() {
    if (searchQuery.value.isEmpty) {
      filteredActiveLoans.value = activeLoans;
      return;
    }

    final query = searchQuery.value.toLowerCase();
    filteredActiveLoans.value = activeLoans.where((loan) {
      return loan.requestedByName.toLowerCase().contains(query) ||
          loan.requestedToName.toLowerCase().contains(query) ||
          loan.amount.toString().contains(query) ||
          loan.fromAccountName.toLowerCase().contains(query) ||
          loan.toAccountName.toLowerCase().contains(query) ||
          (loan.notes?.toLowerCase().contains(query) ?? false);
    }).toList();
  }

  // Update filtered loans when active loans change
  void _updateFilteredActiveLoans() {
    if (searchQuery.value.isEmpty) {
      filteredActiveLoans.value = activeLoans;
    } else {
      _applyActiveLoansFilter();
    }
  }
}
