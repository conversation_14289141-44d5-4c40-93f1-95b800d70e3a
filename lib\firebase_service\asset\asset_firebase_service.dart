import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';
import 'package:logestics/firebase_service/asset/asset_audit_firebase_service.dart';
import 'package:logestics/models/asset/asset_model.dart';
import 'package:uuid/uuid.dart';

class AssetFirebaseService {
  late FirebaseFirestore _firestore;
  late FirebaseStorage _storage;
  late AssetAuditFirebaseService _auditService;

  // Get current user's UID or return 'anonymous' if not authenticated
  String get _uid => FirebaseAuth.instance.currentUser?.uid ?? 'anonymous';

  AssetFirebaseService() {
    _firestore = FirebaseFirestore.instance;
    _storage = FirebaseStorage.instance;
    _auditService = AssetAuditFirebaseService();
  }

  /// Create a new asset
  Future<void> createAsset(AssetModel asset,
      {List<File>? files,
      List<Uint8List>? fileBytes,
      List<String>? fileNames}) async {
    log('Creating asset: ${asset.name}');
    try {
      final assetRef =
          _firestore.collection(AppCollection.assetsCollection).doc();
      final assetId = assetRef.id;

      // Upload files if provided
      List<String> attachmentUrls = [];
      if (files != null && files.isNotEmpty) {
        attachmentUrls = await _uploadFiles(assetId, files: files);
      } else if (fileBytes != null &&
          fileBytes.isNotEmpty &&
          fileNames != null) {
        attachmentUrls = await _uploadFiles(assetId,
            fileBytes: fileBytes, fileNames: fileNames);
      }

      final createdAsset = asset.copyWith(
        id: assetId,
        attachmentUrls: attachmentUrls,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final assetData = createdAsset.toJson();
      assetData['uid'] = _uid; // Add current user's UID

      await assetRef.set(assetData);
      log('Successfully created asset: $assetId');

      // Log asset creation in audit trail
      try {
        await _auditService.logAssetCreated(createdAsset,
            notes: 'Asset created via Asset Management system');
      } catch (e) {
        log('Warning: Failed to log asset creation audit: $e');
        // Don't fail the main operation if audit logging fails
      }
    } catch (e) {
      log('Error creating asset: $e');
      rethrow;
    }
  }

  /// Get all assets for current user
  Future<List<AssetModel>> getAssets() async {
    log('AssetFirebaseService: Fetching assets for user: $_uid');
    if (_uid.isEmpty) {
      log('AssetFirebaseService: ERROR - UID is empty! Cannot fetch assets.');
      return [];
    }

    try {
      final snapshot = await _firestore
          .collection(AppCollection.assetsCollection)
          .where('uid', isEqualTo: _uid)
          .orderBy('createdAt', descending: true)
          .get();

      final assets = snapshot.docs.map((doc) {
        final data = doc.data();
        log('AssetFirebaseService: Manual fetch - Processing asset ${doc.id} with status: ${data['status']}, uid: ${data['uid']}');
        return AssetModel.fromJson(data);
      }).toList();

      log('AssetFirebaseService: Successfully fetched ${assets.length} assets via manual fetch');
      return assets;
    } catch (e) {
      log('AssetFirebaseService: Error fetching assets: $e');
      rethrow;
    }
  }

  /// Get asset by ID
  Future<AssetModel?> getAssetById(String assetId) async {
    log('Fetching asset by ID: $assetId');
    try {
      final doc = await _firestore
          .collection(AppCollection.assetsCollection)
          .doc(assetId)
          .get();

      if (!doc.exists) {
        log('Asset not found: $assetId');
        return null;
      }

      final data = doc.data()!;
      if (data['uid'] != _uid) {
        log('Asset does not belong to current user: $assetId');
        return null;
      }

      return AssetModel.fromJson(data);
    } catch (e) {
      log('Error fetching asset by ID: $e');
      rethrow;
    }
  }

  /// Update an existing asset
  Future<void> updateAsset(AssetModel asset,
      {List<File>? files,
      List<Uint8List>? fileBytes,
      List<String>? fileNames}) async {
    log('Updating asset: ${asset.id} with status: ${asset.status}');
    try {
      // Get the current asset data for audit comparison
      AssetModel? oldAsset;
      try {
        final oldAssetDoc = await _firestore
            .collection(AppCollection.assetsCollection)
            .doc(asset.id)
            .get();
        if (oldAssetDoc.exists) {
          oldAsset = AssetModel.fromJson(oldAssetDoc.data()!);
        }
      } catch (e) {
        log('Warning: Could not fetch old asset data for audit: $e');
      }
      // Upload new files if provided
      List<String> newAttachmentUrls = [];
      if (files != null && files.isNotEmpty) {
        newAttachmentUrls = await _uploadFiles(asset.id, files: files);
      } else if (fileBytes != null &&
          fileBytes.isNotEmpty &&
          fileNames != null) {
        newAttachmentUrls = await _uploadFiles(asset.id,
            fileBytes: fileBytes, fileNames: fileNames);
      }

      // Combine existing and new attachment URLs
      final allAttachmentUrls = [...asset.attachmentUrls, ...newAttachmentUrls];

      final assetData = asset
          .copyWith(
            attachmentUrls: allAttachmentUrls,
            updatedAt: DateTime.now(),
          )
          .toJson();

      // CRITICAL FIX: Ensure UID is preserved during updates
      if (asset.uid.isNotEmpty) {
        assetData['uid'] = asset.uid;
      } else {
        assetData['uid'] = _uid; // Fallback to current user's UID
      }

      log('Asset data to update: status=${assetData['status']}, uid=${assetData['uid']}, updatedAt=${assetData['updatedAt']}');

      final updatedAsset = asset.copyWith(
        attachmentUrls: allAttachmentUrls,
        updatedAt: DateTime.now(),
      );

      await _firestore
          .collection(AppCollection.assetsCollection)
          .doc(asset.id)
          .update(assetData);

      log('Successfully updated asset: ${asset.id} with new status: ${asset.status}');

      // Log asset update in audit trail
      if (oldAsset != null) {
        try {
          await _auditService.logAssetUpdated(oldAsset, updatedAsset,
              notes: 'Asset updated via Asset Management system');
        } catch (e) {
          log('Warning: Failed to log asset update audit: $e');
          // Don't fail the main operation if audit logging fails
        }
      }

      // Log file uploads if any
      if (newAttachmentUrls.isNotEmpty) {
        try {
          for (final url in newAttachmentUrls) {
            final fileName = url.split('/').last.split('?').first;
            await _auditService.logFileUploaded(asset.id, asset.name, fileName,
                notes: 'File uploaded during asset update');
          }
        } catch (e) {
          log('Warning: Failed to log file upload audit: $e');
        }
      }
    } catch (e) {
      log('Error updating asset: $e');
      rethrow;
    }
  }

  /// Delete an asset
  Future<void> deleteAsset(String assetId) async {
    log('Deleting asset: $assetId');
    try {
      // Get asset to check ownership and get attachment URLs
      final assetDoc = await _firestore
          .collection(AppCollection.assetsCollection)
          .doc(assetId)
          .get();

      if (!assetDoc.exists) {
        throw Exception('Asset not found');
      }

      final assetData = assetDoc.data()!;
      if (assetData['uid'] != _uid) {
        throw Exception('Asset does not belong to current user');
      }

      // Create asset model for audit logging
      final assetToDelete = AssetModel.fromJson(assetData);

      // Delete attachment files from storage
      final attachmentUrls =
          List<String>.from(assetData['attachmentUrls'] ?? []);
      for (final url in attachmentUrls) {
        try {
          await _storage.refFromURL(url).delete();
          // Log file deletion
          try {
            final fileName = url.split('/').last.split('?').first;
            await _auditService.logFileDeleted(
                assetId, assetToDelete.name, fileName,
                notes: 'File deleted during asset deletion');
          } catch (e) {
            log('Warning: Failed to log file deletion audit: $e');
          }
        } catch (e) {
          log('Error deleting attachment file: $e');
          // Continue with asset deletion even if file deletion fails
        }
      }

      // Delete the asset document
      await _firestore
          .collection(AppCollection.assetsCollection)
          .doc(assetId)
          .delete();

      log('Successfully deleted asset: $assetId');

      // Log asset deletion in audit trail
      try {
        await _auditService.logAssetDeleted(assetToDelete,
            notes: 'Asset deleted via Asset Management system');
      } catch (e) {
        log('Warning: Failed to log asset deletion audit: $e');
        // Don't fail the main operation if audit logging fails
      }
    } catch (e) {
      log('Error deleting asset: $e');
      rethrow;
    }
  }

  /// Upload files to Firebase Storage
  Future<List<String>> _uploadFiles(String assetId,
      {List<File>? files,
      List<Uint8List>? fileBytes,
      List<String>? fileNames}) async {
    List<String> downloadUrls = [];

    try {
      if (files != null) {
        for (int i = 0; i < files.length; i++) {
          final file = files[i];
          final fileName = '${const Uuid().v4()}_${file.path.split('/').last}';
          final ref = _storage.ref().child('assets/$assetId/$fileName');

          final uploadTask = await ref.putFile(file);
          final downloadUrl = await uploadTask.ref.getDownloadURL();
          downloadUrls.add(downloadUrl);
        }
      } else if (fileBytes != null && fileNames != null) {
        for (int i = 0; i < fileBytes.length; i++) {
          final bytes = fileBytes[i];
          final fileName = '${const Uuid().v4()}_${fileNames[i]}';
          final ref = _storage.ref().child('assets/$assetId/$fileName');

          final uploadTask = await ref.putData(bytes);
          final downloadUrl = await uploadTask.ref.getDownloadURL();
          downloadUrls.add(downloadUrl);
        }
      }

      log('Successfully uploaded ${downloadUrls.length} files for asset: $assetId');
      return downloadUrls;
    } catch (e) {
      log('Error uploading files: $e');
      rethrow;
    }
  }

  /// Remove specific attachment from asset
  Future<void> removeAttachment(String assetId, String attachmentUrl) async {
    log('Removing attachment from asset: $assetId');
    try {
      // Get current asset data
      final assetDoc = await _firestore
          .collection(AppCollection.assetsCollection)
          .doc(assetId)
          .get();

      if (!assetDoc.exists) {
        throw Exception('Asset not found');
      }

      final assetData = assetDoc.data()!;
      if (assetData['uid'] != _uid) {
        throw Exception('Asset does not belong to current user');
      }

      // Remove URL from attachment list
      final attachmentUrls =
          List<String>.from(assetData['attachmentUrls'] ?? []);
      attachmentUrls.remove(attachmentUrl);

      // Update asset with new attachment list
      await _firestore
          .collection(AppCollection.assetsCollection)
          .doc(assetId)
          .update({
        'attachmentUrls': attachmentUrls,
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      });

      // Delete file from storage
      try {
        await _storage.refFromURL(attachmentUrl).delete();
      } catch (e) {
        log('Error deleting attachment file from storage: $e');
        // Continue even if file deletion fails
      }

      log('Successfully removed attachment from asset: $assetId');
    } catch (e) {
      log('Error removing attachment: $e');
      rethrow;
    }
  }

  /// Stream to listen for real-time updates to assets
  Stream<List<AssetModel>> listenToAssets() {
    try {
      log('AssetFirebaseService: Setting up real-time listener for user: $_uid');
      if (_uid.isEmpty) {
        log('AssetFirebaseService: ERROR - UID is empty! Cannot query assets.');
        return Stream.value(<AssetModel>[]);
      }

      return _firestore
          .collection(AppCollection.assetsCollection)
          .where('uid', isEqualTo: _uid)
          .orderBy('createdAt', descending: true)
          .snapshots()
          .map<List<AssetModel>>((snapshot) {
        log('AssetFirebaseService: Snapshot received with ${snapshot.docs.length} documents for UID: $_uid');
        final List<AssetModel> assets = snapshot.docs.map<AssetModel>((doc) {
          final data = doc.data();
          log('AssetFirebaseService: Processing asset ${doc.id} with status: ${data['status']}, uid: ${data['uid']}');
          return AssetModel.fromJson(data);
        }).toList();
        log('AssetFirebaseService: Returning ${assets.length} assets to stream');
        return assets;
      });
    } catch (e) {
      log('AssetFirebaseService: Error listening to assets: $e', error: e);
      return Stream.value(<AssetModel>[]);
    }
  }

  /// Search assets by name, type, or registration number
  Future<List<AssetModel>> searchAssets(String query) async {
    log('Searching assets with query: $query');
    try {
      final snapshot = await _firestore
          .collection(AppCollection.assetsCollection)
          .where('uid', isEqualTo: _uid)
          .get();

      final assets = snapshot.docs
          .map((doc) => AssetModel.fromJson(doc.data()))
          .where((asset) =>
              asset.name.toLowerCase().contains(query.toLowerCase()) ||
              asset.type.toLowerCase().contains(query.toLowerCase()) ||
              asset.registrationNumber
                  .toLowerCase()
                  .contains(query.toLowerCase()) ||
              asset.serialNumber.toLowerCase().contains(query.toLowerCase()))
          .toList();

      log('Found ${assets.length} assets matching query: $query');
      return assets;
    } catch (e) {
      log('Error searching assets: $e');
      rethrow;
    }
  }
}
