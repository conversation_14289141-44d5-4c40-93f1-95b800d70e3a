import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/widgets/searchfield.dart';

import 'package:logestics/main.dart';
import 'package:provider/provider.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/widgets/data_table_cell.dart';
import 'package:logestics/core/utils/widgets/pagination_widget.dart';
import 'package:logestics/core/utils/widgets/loading_indicator.dart';
import 'package:intl/intl.dart';
import 'package:logestics/models/finance/bill_model.dart';

import '../../../../company/presentation/conrollers/company_controller.dart';

import '../controllers/bills_list_controller.dart';

class BillsList extends StatelessWidget {
  const BillsList({super.key, required this.titleShow});

  String formatDate(DateTime? date) {
    if (date == null) return '';
    return DateFormat('dd/MM/yyyy').format(date);
  }

  String formatCurrency(double amount) {
    return 'PKR ${amount.toStringAsFixed(2)}';
  }

  final bool titleShow;
  @override
  Widget build(BuildContext context) {
    // Get Bills controller from dependency injection
    BillsListController billsListController = Get.find<BillsListController>();

    // Handle screen activation and data refresh - only once
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!billsListController.hasInitialized) {
        billsListController.hasInitialized = true;

        // Call onScreenActivated to handle navigation scenarios
        billsListController.onScreenActivated();

        // Always refresh data when Bills screen is loaded
        billsListController.refreshData();
      }
    });

    var width = Get.width;

    notifier = Provider.of(context, listen: true);

    return GetBuilder<CompanyController>(
      init: billsListController.companyController,
      builder: (companyController) => LayoutBuilder(
        builder: (context, constraints) {
          return StatefulBuilder(
            builder: (context, setState) {
              return Container(
                decoration: BoxDecoration(
                  color: notifier.getBgColor,
                  borderRadius: BorderRadius.circular(10),
                ),
                padding: const EdgeInsets.symmetric(vertical: 15),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 15),
                      child: width < 650
                          ? Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                titleShow == false
                                    ? Container()
                                    : Text(
                                        AppStrings.billsTitle,
                                        overflow: TextOverflow.ellipsis,
                                        style: AppTextStyles.invoiceTitleStyle
                                            .copyWith(
                                          color: notifier.text,
                                        ),
                                      ),
                                const SizedBox(height: 5),
                                Searchfield()
                              ],
                            )
                          : Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                titleShow == false
                                    ? Container()
                                    : Text(
                                        AppStrings.billsTitle,
                                        style: AppTextStyles.invoiceTitleStyle
                                            .copyWith(
                                          color: notifier.text,
                                        ),
                                      ),
                                SizedBox(
                                  width: width < 850 ? width / 2 : width / 3.5,
                                  child: Row(
                                    children: [
                                      Expanded(
                                        child: Searchfield(
                                          onChanged: (query) {
                                            billsListController
                                                .searchBills(query);
                                          },
                                        ),
                                      ),
                                      const SizedBox(width: 10),
                                      Container(
                                        height: 40,
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 10),
                                        decoration: BoxDecoration(
                                          color: notifier.getBgColor,
                                          borderRadius:
                                              BorderRadius.circular(5),
                                          border: Border.all(
                                            color: notifier.getfillborder,
                                          ),
                                        ),
                                        child: DropdownButton<String>(
                                          value: billsListController
                                              .selectedStatus.value,
                                          hint: Text(
                                            'Filter by Status',
                                            style: AppTextStyles
                                                .searchFieldStyle
                                                .copyWith(
                                              color: notifier.text
                                                  .withValues(alpha: 0.5),
                                            ),
                                          ),
                                          underline: Container(),
                                          isDense: true,
                                          style: AppTextStyles.searchFieldStyle,
                                          items: [
                                            const DropdownMenuItem<String>(
                                              value: '',
                                              child: Text('All Status'),
                                            ),
                                            ...AppStrings.billStatusOptions
                                                .map((String status) {
                                              return DropdownMenuItem<String>(
                                                value: status,
                                                child: Text(status),
                                              );
                                            }),
                                          ],
                                          onChanged: (String? newValue) {
                                            billsListController
                                                .filterByStatus(newValue);
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                    ),
                    const SizedBox(height: 20),
                    Obx(() {
                      // Show loading indicator during initial load or when no data is available yet
                      if (billsListController.isInitialLoading.value) {
                        return const LoadingIndicator.circular(
                          message: 'Loading bills...',
                          size: LoadingIndicatorSize.medium,
                        );
                      }

                      // Show empty state if no bills are found
                      if (billsListController.paginatedBills.isEmpty) {
                        return Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.receipt_long_outlined,
                                size: 64,
                                color: Colors.grey.shade400,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'No bills found',
                                style: TextStyle(
                                  fontSize: 18,
                                  color: Colors.grey.shade600,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Bills created from invoices will appear here',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey.shade500,
                                ),
                              ),
                              const SizedBox(height: 16),
                              ElevatedButton.icon(
                                onPressed: () {
                                  billsListController.refreshData();
                                },
                                icon: const Icon(Icons.refresh),
                                label: const Text('Refresh'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.blue,
                                  foregroundColor: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        );
                      }

                      return SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: SizedBox(
                          width: constraints.maxWidth < 900
                              ? 900
                              : constraints.maxWidth,
                          child: Stack(
                            children: [
                              ListView(
                                shrinkWrap: true,
                                children: [
                                  Table(
                                    border: TableBorder(
                                      horizontalInside: BorderSide(
                                        color: notifier.getfillborder,
                                      ),
                                    ),
                                    children: [
                                      TableRow(
                                        decoration: BoxDecoration(
                                          color: notifier.getHoverColor,
                                        ),
                                        children: [
                                          DataTableHeaderCell(
                                            text: 'Bill Number',
                                            textColor: notifier.text,
                                          ),
                                          DataTableHeaderCell(
                                            text: 'Bill Date',
                                            textColor: notifier.text,
                                          ),
                                          DataTableHeaderCell(
                                            text: 'Customer',
                                            textColor: notifier.text,
                                          ),
                                          DataTableHeaderCell(
                                            text: 'Total Amount',
                                            textColor: notifier.text,
                                          ),
                                          DataTableHeaderCell(
                                            text: 'Linked Invoices',
                                            textColor: notifier.text,
                                          ),
                                          DataTableHeaderCell(
                                            text: 'Status',
                                            textColor: notifier.text,
                                          ),
                                          DataTableHeaderCell(
                                            text: 'Actions',
                                            textColor: notifier.text,
                                          ),
                                        ],
                                      ),
                                      ...billsListController.paginatedBills
                                          .asMap()
                                          .entries
                                          .map((entry) {
                                        final bill = entry.value;
                                        return TableRow(
                                          children: [
                                            DataTableCell(
                                              text: bill.billNumber,
                                            ),
                                            DataTableCell(
                                              text: formatDate(bill.billDate),
                                            ),
                                            DataTableCell(
                                              text: bill.customerName ?? '',
                                            ),
                                            DataTableCell(
                                              text: formatCurrency(
                                                  bill.totalAmount),
                                            ),
                                            DataTableCell(
                                              text: bill.numberOfLinkedInvoices
                                                  .toString(),
                                            ),
                                            TableCell(
                                              child: Container(
                                                padding:
                                                    const EdgeInsets.all(4.0),
                                                constraints:
                                                    const BoxConstraints(
                                                  minWidth: 100,
                                                  maxWidth: 140,
                                                ),
                                                child: DropdownButton<String>(
                                                  value: bill.billStatus
                                                      .toString()
                                                      .split('.')
                                                      .last,
                                                  underline: Container(),
                                                  isDense: true,
                                                  isExpanded: true,
                                                  style: AppTextStyles
                                                      .invoiceDataStyle
                                                      .copyWith(
                                                    color: bill.billStatus ==
                                                            BillStatus.pending
                                                        ? Colors.orange
                                                        : Colors.green,
                                                    fontSize: 12,
                                                  ),
                                                  items: AppStrings
                                                      .billStatusOptions
                                                      .map((String status) {
                                                    return DropdownMenuItem<
                                                        String>(
                                                      value: status,
                                                      child: Text(
                                                        status,
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                        style: const TextStyle(
                                                            fontSize: 12),
                                                      ),
                                                    );
                                                  }).toList(),
                                                  onChanged:
                                                      (String? newValue) {
                                                    if (newValue != null) {
                                                      final newStatus =
                                                          newValue == 'Pending'
                                                              ? BillStatus
                                                                  .pending
                                                              : BillStatus
                                                                  .completed;

                                                      // Show confirmation for completing bills
                                                      if (newStatus ==
                                                          BillStatus
                                                              .completed) {
                                                        billsListController
                                                            .showCompleteConfirmation(
                                                                bill);
                                                      } else {
                                                        billsListController
                                                            .updateBillStatus(
                                                          billId: bill
                                                              .billNumber, // Fixed: Use billNumber instead of billId
                                                          newStatus: newStatus,
                                                        );
                                                      }
                                                    }
                                                  },
                                                ),
                                              ),
                                            ),
                                            DataTableActionsCell(
                                              menuItems: [
                                                DataTablePopupMenuItem(
                                                  text: 'View Details',
                                                  icon: Icons.visibility,
                                                  onTap: () {
                                                    // TODO: Navigate to bill details view
                                                  },
                                                ),
                                                DataTablePopupMenuItem(
                                                  text: 'Delete',
                                                  icon: Icons.delete_outline,
                                                  isDanger: true,
                                                  onTap: () {
                                                    billsListController
                                                        .showDeleteConfirmation(
                                                            bill);
                                                  },
                                                ),
                                              ],
                                            ),
                                          ],
                                        );
                                      }),
                                    ],
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      );
                    }),
                    Obx(() => PaginationWidget(
                          currentPage: billsListController.currentPage.value,
                          totalPages: billsListController.totalPages,
                          itemsPerPage: billsListController.itemsPerPage.value,
                          onPageChanged: (page) =>
                              billsListController.setCurrentPage(page),
                          onItemsPerPageChanged: (count) =>
                              billsListController.setItemsPerPage(count),
                        )),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }
}
