import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:logestics/main.dart';
import 'package:provider/provider.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/widgets/data_table_cell.dart';
import 'package:logestics/core/utils/widgets/pagination_widget.dart';
import 'package:logestics/core/utils/widgets/loading_indicator.dart';
import 'package:intl/intl.dart';
import 'package:logestics/bindings/app_bindings.dart';
import 'package:logestics/models/slab/slab_model.dart';
import '../controllers/slab_list_controller.dart';
import 'slab_form_view.dart';

class SlabList extends StatelessWidget {
  const SlabList({super.key, required this.titleShow});

  final bool titleShow;

  String formatDate(DateTime? date) {
    if (date == null) return '';
    return DateFormat('dd/MM/yyyy').format(date);
  }

  void _navigateToSlabForm({SlabModel? slab, bool readOnly = false}) {
    // Reset the bindings to ensure all dependencies are properly registered
    AppBindings().dependencies();

    // Navigate to the slab form
    Get.to(
      () => SlabFormView(
        currentSlab: slab,
        readOnly: readOnly,
      ),
    )?.then((_) {
      // Refresh the slab list when returning from the form
      final controller = Get.find<SlabListController>();
      controller.refreshData();
    });
  }

  void _showDeleteConfirmation(SlabModel slab) {
    Get.dialog(
      AlertDialog(
        title: const Text('Delete Slab'),
        content: Text('Are you sure you want to delete "${slab.slabName}"?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              final controller = Get.find<SlabListController>();
              controller.deleteSlab(slab.slabId);
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    SlabListController slabListController = Get.put(SlabListController(
      getSlabsUseCase: Get.find(),
      deleteSlabUseCase: Get.find(),
    ));

    notifier = Provider.of(context, listen: true);

    return Scaffold(
      backgroundColor: notifier.getBgColor,
      body: Column(
        children: [
          // Header Section
          if (titleShow)
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: notifier.getHoverColor,
                border: Border(
                  bottom: BorderSide(color: notifier.getfillborder),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.grid_view,
                    color: notifier.text,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Slab Management',
                    style: AppTextStyles.invoiceHeaderStyle.copyWith(
                      color: notifier.text,
                    ),
                  ),
                  const Spacer(),
                  ElevatedButton.icon(
                    onPressed: () => _navigateToSlabForm(),
                    icon: const Icon(Icons.add),
                    label: const Text('Create Slab'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xff0f79f3),
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ),

          // Filters and Search Section
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: notifier.getBgColor,
              border: Border(
                bottom: BorderSide(color: notifier.getfillborder),
              ),
            ),
            child: Column(
              children: [
                // Search and Filters Row
                Row(
                  children: [
                    // Search Field
                    Expanded(
                      flex: 3,
                      child: TextField(
                        controller: slabListController.searchController,
                        decoration: InputDecoration(
                          hintText: 'Search slabs...',
                          prefixIcon: const Icon(Icons.search),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                        ),
                        onChanged: (value) {
                          // Search is handled by controller listener
                        },
                      ),
                    ),
                    const SizedBox(width: 16),

                    // Filter Buttons
                    Obx(() => FilterChip(
                          label: const Text('Active Only'),
                          selected: slabListController.showActiveOnly.value,
                          onSelected: (_) =>
                              slabListController.toggleActiveFilter(),
                          selectedColor: Colors.green.withValues(alpha: 0.2),
                          checkmarkColor: Colors.green,
                        )),
                    const SizedBox(width: 8),

                    Obx(() => FilterChip(
                          label: const Text('Expired Only'),
                          selected: slabListController.showExpiredOnly.value,
                          onSelected: (_) =>
                              slabListController.toggleExpiredFilter(),
                          selectedColor: Colors.red.withValues(alpha: 0.2),
                          checkmarkColor: Colors.red,
                        )),
                    const SizedBox(width: 16),

                    // Clear Search Button
                    IconButton(
                      onPressed: slabListController.clearSearch,
                      icon: const Icon(Icons.clear),
                      tooltip: 'Clear Search',
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Data Table Section
          Expanded(
            child: Obx(() {
              if (slabListController.isLoading.value) {
                return const Center(child: LoadingIndicator());
              }

              final paginatedSlabs = slabListController.paginatedSlabs;

              if (paginatedSlabs.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.grid_view,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        slabListController.searchQuery.value.isNotEmpty
                            ? 'No slabs found matching your search'
                            : 'No slabs available',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Create your first slab to get started',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[500],
                        ),
                      ),
                    ],
                  ),
                );
              }

              return SingleChildScrollView(
                child: Container(
                  margin: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: notifier.getBgColor,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: notifier.getfillborder),
                  ),
                  child: Column(
                    children: [
                      // Table Header
                      Container(
                        decoration: BoxDecoration(
                          color: notifier.getHoverColor,
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(8),
                            topRight: Radius.circular(8),
                          ),
                        ),
                        child: Table(
                          border: TableBorder.all(
                            color: notifier.getfillborder,
                          ),
                          children: [
                            TableRow(
                              children: [
                                DataTableHeaderCell(
                                  text: 'Slab Name',
                                  textColor: notifier.text,
                                ),
                                DataTableHeaderCell(
                                  text: 'Start Date',
                                  textColor: notifier.text,
                                ),
                                DataTableHeaderCell(
                                  text: 'Expiry Date',
                                  textColor: notifier.text,
                                ),
                                DataTableHeaderCell(
                                  text: 'Status',
                                  textColor: notifier.text,
                                ),
                                DataTableHeaderCell(
                                  text: 'Districts',
                                  textColor: notifier.text,
                                ),
                                DataTableHeaderCell(
                                  text: 'Created',
                                  textColor: notifier.text,
                                ),
                                DataTableHeaderCell(
                                  text: 'Actions',
                                  textColor: notifier.text,
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),

                      // Table Body
                      Table(
                        border: TableBorder.all(
                          color: notifier.getfillborder,
                        ),
                        children: paginatedSlabs.map((slab) {
                          final statusColor =
                              slabListController.getSlabStatusColor(slab);
                          final statusText =
                              slabListController.getSlabStatusText(slab);

                          return TableRow(
                            children: [
                              DataTableCell(text: slab.slabName),
                              DataTableCell(text: formatDate(slab.startDate)),
                              DataTableCell(text: formatDate(slab.expiryDate)),
                              TableCell(
                                child: Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 4,
                                    ),
                                    decoration: BoxDecoration(
                                      color: statusColor.withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(color: statusColor),
                                    ),
                                    child: Text(
                                      statusText,
                                      style: TextStyle(
                                        color: statusColor,
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                ),
                              ),
                              DataTableCell(
                                  text: '${slab.rates.length} districts'),
                              DataTableCell(text: formatDate(slab.createdAt)),
                              TableCell(
                                child: Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      IconButton(
                                        icon: const Icon(Icons.visibility,
                                            size: 16),
                                        onPressed: () => _navigateToSlabForm(
                                          slab: slab,
                                          readOnly: true,
                                        ),
                                        tooltip: 'View Slab',
                                      ),
                                      IconButton(
                                        icon: const Icon(Icons.edit, size: 16),
                                        onPressed: () =>
                                            _navigateToSlabForm(slab: slab),
                                        tooltip: 'Edit Slab',
                                      ),
                                      IconButton(
                                        icon:
                                            const Icon(Icons.delete, size: 16),
                                        onPressed: () =>
                                            _showDeleteConfirmation(slab),
                                        tooltip: 'Delete Slab',
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          );
                        }).toList(),
                      ),
                    ],
                  ),
                ),
              );
            }),
          ),

          // Pagination Section
          Obx(() {
            if (slabListController.totalItems.value == 0) {
              return const SizedBox.shrink();
            }

            return Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: notifier.getBgColor,
                border: Border(
                  top: BorderSide(color: notifier.getfillborder),
                ),
              ),
              child: PaginationWidget(
                currentPage: slabListController.currentPage.value,
                totalPages: slabListController.totalPages,
                itemsPerPage: slabListController.itemsPerPage.value,
                onPageChanged: (page) => slabListController.goToPage(page),
                onItemsPerPageChanged: (count) =>
                    slabListController.changeItemsPerPage(count),
              ),
            );
          }),
        ],
      ),
    );
  }
}
