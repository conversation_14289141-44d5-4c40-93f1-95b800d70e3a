import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../controllers/trial_balance_controller.dart';
import '../widgets/trial_balance_form_widget.dart';
import '../widgets/trial_balance_table_widget.dart';
import '../widgets/trial_balance_summary_widget.dart';
import '../widgets/saved_reports_widget.dart';

/// Main Trial Balance screen
class TrialBalanceScreen extends StatelessWidget {
  const TrialBalanceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<TrialBalanceController>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Trial Balance'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 2,
        actions: [
          // Export button
          Obx(() => controller.currentReport.value != null
              ? IconButton(
                  icon: const Icon(Icons.download),
                  onPressed: () => _showExportOptions(context, controller),
                  tooltip: 'Export Report',
                )
              : const SizedBox.shrink()),

          // Save button
          Obx(() => controller.currentReport.value != null
              ? IconButton(
                  icon: controller.isSaving.value
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Icon(Icons.save),
                  onPressed: controller.isSaving.value
                      ? null
                      : controller.saveTrialBalanceReport,
                  tooltip: 'Save Report',
                )
              : const SizedBox.shrink()),

          // Refresh button
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => controller.generateTrialBalance(),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Form section
              Card(
                elevation: 2,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Generate Trial Balance',
                        style:
                            Theme.of(context).textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                      const SizedBox(height: 16),
                      const TrialBalanceFormWidget(),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Summary section (only show if report exists)
              if (controller.currentReport.value != null) ...[
                const TrialBalanceSummaryWidget(),
                const SizedBox(height: 16),
              ],

              // Table section (only show if report exists)
              if (controller.currentReport.value != null) ...[
                Card(
                  elevation: 2,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Trial Balance Report',
                              style: Theme.of(context)
                                  .textTheme
                                  .headlineSmall
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                            // Filter controls
                            Row(
                              children: [
                                // Search field
                                SizedBox(
                                  width: 200,
                                  child: TextField(
                                    decoration: const InputDecoration(
                                      hintText: 'Search accounts...',
                                      prefixIcon: Icon(Icons.search),
                                      border: OutlineInputBorder(),
                                      contentPadding: EdgeInsets.symmetric(
                                        horizontal: 12,
                                        vertical: 8,
                                      ),
                                    ),
                                    onChanged: (value) =>
                                        controller.searchQuery.value = value,
                                  ),
                                ),
                                const SizedBox(width: 8),

                                // Account type filter
                                Obx(() => DropdownButton<String>(
                                      value:
                                          controller.selectedAccountType.value,
                                      items: controller.accountTypeOptions
                                          .map((type) => DropdownMenuItem(
                                                value: type,
                                                child: Text(type),
                                              ))
                                          .toList(),
                                      onChanged: (value) {
                                        if (value != null) {
                                          controller.selectedAccountType.value =
                                              value;
                                        }
                                      },
                                    )),
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        const TrialBalanceTableWidget(),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
              ],

              // Saved reports section
              Card(
                elevation: 2,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Saved Reports',
                        style:
                            Theme.of(context).textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                      const SizedBox(height: 16),
                      const SavedReportsWidget(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  /// Show export options dialog
  void _showExportOptions(
      BuildContext context, TrialBalanceController controller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Options'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.picture_as_pdf),
              title: const Text('Export as PDF'),
              onTap: () {
                Navigator.of(context).pop();
                _exportToPDF(controller);
              },
            ),
            ListTile(
              leading: const Icon(Icons.table_chart),
              title: const Text('Export as Excel'),
              onTap: () {
                Navigator.of(context).pop();
                _exportToExcel(controller);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  /// Export to PDF
  void _exportToPDF(TrialBalanceController controller) async {
    if (controller.currentReport.value == null) {
      Get.snackbar('Error', 'No report available to export');
      return;
    }

    try {
      controller.setLoading(true);

      final pdfBytes = await controller.exportToPDF();
      final fileName =
          'trial_balance_${DateFormat('yyyyMMdd').format(controller.currentReport.value!.endDate)}.pdf';

      await controller.exportService.sharePDF(pdfBytes, fileName);

      Get.snackbar(
        'Success',
        'PDF exported successfully',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to export PDF: $e',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      controller.setLoading(false);
    }
  }

  /// Export to Excel
  void _exportToExcel(TrialBalanceController controller) async {
    if (controller.currentReport.value == null) {
      Get.snackbar('Error', 'No report available to export');
      return;
    }

    try {
      controller.setLoading(true);

      await controller.exportToExcel();

      Get.snackbar(
        'Success',
        'Excel file downloaded successfully',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to export Excel: $e',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      controller.setLoading(false);
    }
  }
}
