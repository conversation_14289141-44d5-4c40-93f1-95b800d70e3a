import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/models/locations/district_model.dart';
import 'package:logestics/features/locations/repositories/district_repository.dart';

class GetDistrictsUseCase {
  final DistrictRepository _districtRepository;

  GetDistrictsUseCase(this._districtRepository);

  /// Executes the use case to get all Districts.
  Future<Either<FailureObj, List<DistrictModel>>> call() async {
    try {
      return await _districtRepository.getDistricts();
    } catch (e) {
      return Left(FailureObj(
        code: 'unexpected-error',
        message: 'An unexpected error occurred.',
      ));
    }
  }
}
