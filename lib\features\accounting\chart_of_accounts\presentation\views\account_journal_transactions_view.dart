import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../../../core/utils/app_constants/styles/app_text_styles.dart';
import '../../../../../core/utils/widgets/pagination_widget.dart';
import '../../../../../models/finance/chart_of_accounts_model.dart';
import '../../../../../models/finance/journal_entry_model.dart';
import '../controllers/account_journal_transaction_controller.dart';
import '../../services/account_journal_transaction_service.dart';

class AccountJournalTransactionsView extends StatefulWidget {
  final ChartOfAccountsModel account;
  final String uid;

  const AccountJournalTransactionsView({
    super.key,
    required this.account,
    required this.uid,
  });

  @override
  State<AccountJournalTransactionsView> createState() =>
      _AccountJournalTransactionsViewState();
}

class _AccountJournalTransactionsViewState
    extends State<AccountJournalTransactionsView> {
  late AccountJournalTransactionController controller;
  DateTimeRange? selectedDateRange;

  @override
  void initState() {
    super.initState();
    controller = Get.find<AccountJournalTransactionController>();
    // Load transactions for this account
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.loadTransactionsForAccount(widget.account, widget.uid);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.account.accountName} - Transactions'),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => controller.refreshTransactions(),
          ),
        ],
      ),
      body: Column(
        children: [
          _buildHeader(),
          _buildFilters(),
          Expanded(child: _buildTransactionsList()),
          _buildPagination(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey[100],
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.account.accountName,
                      style: AppTextStyles.headingMedium,
                    ),
                    Text(
                      'Account #${widget.account.accountNumber}',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                    Text(
                      widget.account.accountCategory.displayName,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.blue[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              Obx(() => Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        'Current Balance',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      if (controller.isLoadingBalance.value)
                        const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      else
                        Text(
                          controller
                              .formatCurrency(controller.accountBalance.value),
                          style: AppTextStyles.headingMedium.copyWith(
                            color: controller.accountBalance.value >= 0
                                ? Colors.green[700]
                                : Colors.red[700],
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                    ],
                  )),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildDateRangeFilter(),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatusFilter(),
              ),
            ],
          ),
          if (controller.hasFiltersApplied) ...[
            const SizedBox(height: 8),
            _buildActiveFilters(),
          ],
        ],
      ),
    );
  }

  Widget _buildDateRangeFilter() {
    return OutlinedButton.icon(
      onPressed: _selectDateRange,
      icon: const Icon(Icons.date_range),
      label: Text(
        selectedDateRange == null
            ? 'Select Date Range'
            : '${DateFormat('MMM dd').format(selectedDateRange!.start)} - ${DateFormat('MMM dd, yyyy').format(selectedDateRange!.end)}',
      ),
    );
  }

  Widget _buildStatusFilter() {
    return PopupMenuButton<JournalEntryStatus>(
      child: OutlinedButton.icon(
        onPressed: null,
        icon: const Icon(Icons.filter_list),
        label: Obx(() => Text(
              controller.selectedStatuses.isEmpty
                  ? 'Filter by Status'
                  : '${controller.selectedStatuses.length} Status(es)',
            )),
      ),
      itemBuilder: (context) => JournalEntryStatus.values
          .map((status) => PopupMenuItem<JournalEntryStatus>(
                value: status,
                child: Obx(() => CheckboxListTile(
                      title: Text(status.displayName),
                      value: controller.selectedStatuses.contains(status),
                      onChanged: (checked) {
                        if (checked == true) {
                          controller.addStatusFilter(status);
                        } else {
                          controller.removeStatusFilter(status);
                        }
                        Navigator.pop(context);
                      },
                      dense: true,
                    )),
              ))
          .toList(),
    );
  }

  Widget _buildActiveFilters() {
    return Wrap(
      spacing: 8,
      children: [
        if (selectedDateRange != null)
          Chip(
            label: Text(
              '${DateFormat('MMM dd').format(selectedDateRange!.start)} - ${DateFormat('MMM dd').format(selectedDateRange!.end)}',
            ),
            onDeleted: () {
              setState(() {
                selectedDateRange = null;
              });
              controller.clearDateRange();
            },
          ),
        ...controller.selectedStatuses.map((status) => Chip(
              label: Text(status.displayName),
              onDeleted: () => controller.removeStatusFilter(status),
            )),
      ],
    );
  }

  Widget _buildTransactionsList() {
    return Obx(() {
      if (controller.isLoading.value) {
        return const Center(child: CircularProgressIndicator());
      }

      if (controller.isError.value) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error, size: 64, color: Colors.red[300]),
              const SizedBox(height: 16),
              Text(
                'Error loading transactions',
                style: AppTextStyles.headingMedium,
              ),
              const SizedBox(height: 8),
              Text(
                controller.errorMessage.value,
                style:
                    AppTextStyles.bodyMedium.copyWith(color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => controller.refreshTransactions(),
                child: const Text('Retry'),
              ),
            ],
          ),
        );
      }

      if (!controller.hasTransactions) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.receipt_long, size: 64, color: Colors.grey[300]),
              const SizedBox(height: 16),
              Text(
                'No transactions found',
                style: AppTextStyles.headingMedium,
              ),
              const SizedBox(height: 8),
              Text(
                'This account has no journal entry transactions yet.',
                style:
                    AppTextStyles.bodyMedium.copyWith(color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      }

      return _buildTransactionsTable();
    });
  }

  Widget _buildTransactionsTable() {
    final currentTransactions = controller.getCurrentPageTransactions();

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columns: const [
          DataColumn(label: Text('Date')),
          DataColumn(label: Text('Entry #')),
          DataColumn(label: Text('Description')),
          DataColumn(label: Text('Type')),
          DataColumn(label: Text('Amount')),
          DataColumn(label: Text('Balance')),
          DataColumn(label: Text('Status')),
        ],
        rows: currentTransactions.map((transaction) {
          return DataRow(
            cells: [
              DataCell(Text(controller.formatDate(transaction.entryDate))),
              DataCell(Text(transaction.entryNumber)),
              DataCell(
                SizedBox(
                  width: 200,
                  child: Text(
                    transaction.description,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                  ),
                ),
              ),
              DataCell(
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: controller
                        .getTransactionTypeColor(transaction)
                        .withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    controller.getTransactionType(transaction),
                    style: TextStyle(
                      color: controller.getTransactionTypeColor(transaction),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
              DataCell(
                Text(
                  controller.formatCurrency(transaction.amount.abs()),
                  style: TextStyle(
                    color: controller.getTransactionTypeColor(transaction),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              DataCell(
                Text(
                  controller.formatCurrency(transaction.runningBalance),
                  style: TextStyle(
                    color: transaction.runningBalance >= 0
                        ? Colors.green[700]
                        : Colors.red[700],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              DataCell(
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: controller
                        .getStatusColor(transaction.status)
                        .withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    controller.getStatusDisplayName(transaction.status),
                    style: TextStyle(
                      color: controller.getStatusColor(transaction.status),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ],
          );
        }).toList(),
      ),
    );
  }

  Widget _buildPagination() {
    return Obx(() => PaginationWidget(
          currentPage: controller.currentPage.value,
          totalPages: controller.totalPages,
          itemsPerPage: controller.itemsPerPage.value,
          totalItems: controller.totalItems.value,
          onPageChanged: controller.setCurrentPage,
          onItemsPerPageChanged: controller.setItemsPerPage,
          isLoading: controller.isLoadingPage.value,
        ));
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: selectedDateRange,
    );

    if (picked != null) {
      setState(() {
        selectedDateRange = picked;
      });
      controller.setDateRange(picked);
    }
  }
}
