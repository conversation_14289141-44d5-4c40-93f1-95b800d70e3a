import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:logestics/bindings/app_bindings.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/widgets/data_table_cell.dart';
import 'package:logestics/core/utils/widgets/pagination_widget.dart';
import 'package:logestics/features/finance/accounts/presentation/controllers/account_controller.dart';
import 'package:logestics/features/finance/deposit_categories/presentation/controllers/deposit_category_controller.dart';
import 'package:logestics/firebase_service/finance/deposit_firebase_service.dart';
import 'package:logestics/features/finance/deposits/repository/deposit_repository.dart';
import 'package:logestics/features/finance/deposits/presentation/controllers/deposit_controller.dart';
import 'package:logestics/features/finance/payers/presentation/payer_controller.dart';
import 'package:logestics/main.dart';
import 'package:logestics/models/finance/account_model.dart';
import 'package:logestics/models/finance/deposit_category_model.dart';
import 'package:logestics/models/finance/deposit_model.dart';
import 'package:logestics/models/finance/payer_model.dart';
import 'package:provider/provider.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:uuid/uuid.dart';
import 'package:logestics/features/finance/deposits/presentation/views/deposits_pdf_generation_dialog.dart';

class DepositsView extends StatefulWidget {
  const DepositsView({super.key});

  @override
  State<DepositsView> createState() => _DepositsViewState();
}

class _DepositsViewState extends State<DepositsView> {
  late final DepositController controller;

  @override
  void initState() {
    super.initState();
    // Initialize Firebase API
    final firebaseService = DepositFirebaseService();
    // Initialize repository with Firebase API
    final repository = DepositRepositoryImpl(firebaseService);
    // Initialize controller with repository
    AppBindings().dependencies();
    controller = Get.put(
        DepositController(
            depositRepository: repository,
            accountRepository: Get.find(),
            payerRepository: Get.find(),
            categoryRepository: Get.find()),
        permanent: true);

    // Ensure all required controllers are available for the add dialogs
    _initializeRequiredControllers();
  }

  /// Initialize all controllers needed for the add new dialogs
  void _initializeRequiredControllers() {
    try {
      // Try to get existing controllers or create them if they don't exist

      // Account Controller
      try {
        Get.find<AccountController>();
      } catch (e) {
        log('AccountController not found in deposits view, will handle gracefully');
      }

      // Payer Controller
      try {
        Get.find<PayerController>();
      } catch (e) {
        log('PayerController not found in deposits view, will handle gracefully');
      }

      // Deposit Category Controller - this one we know exists from our earlier setup
      try {
        Get.find<DepositCategoryController>();
      } catch (e) {
        log('DepositCategoryController not found in deposits view, will handle gracefully');
      }
    } catch (e) {
      log('Error initializing controllers: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    var width = Get.width;

    notifier = Provider.of(context, listen: true);

    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          child: Column(
            children: [
              SizedBox(
                height: width < 650 ? 55 : 40,
                width: width,
                child: width < 650
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Deposits',
                                overflow: TextOverflow.ellipsis,
                                style: AppTextStyles.titleStyle
                                    .copyWith(color: notifier.text),
                              ),
                              ElevatedButton.icon(
                                onPressed: () => _showPDFGenerationDialog(),
                                icon:
                                    const Icon(Icons.picture_as_pdf, size: 16),
                                label: const Text('PDF'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color(0xFF0f7bf4),
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 12, vertical: 6),
                                  textStyle: const TextStyle(fontSize: 12),
                                ),
                              ),
                            ],
                          ),
                          const Spacer(),
                        ],
                      )
                    : Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Deposits',
                            overflow: TextOverflow.ellipsis,
                            style: AppTextStyles.titleStyle
                                .copyWith(color: notifier.text),
                          ),
                          const Spacer(),
                          ElevatedButton.icon(
                            onPressed: () => _showPDFGenerationDialog(),
                            icon: const Icon(Icons.picture_as_pdf),
                            label: const Text('Export PDF'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF0f7bf4),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 8),
                            ),
                          ),
                        ],
                      ),
              ),
              const SizedBox(height: 20),
              SizedBox(
                height: 570,
                child: _buildDepositsList(),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDepositsList() {
    final controller = Get.find<DepositController>();

    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            color: notifier.getBgColor,
            borderRadius: BorderRadius.circular(10),
          ),
          padding: const EdgeInsets.symmetric(vertical: 15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 15),
                child: Get.width < 650
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          InkWell(
                            onTap: () => controller.openDrawer(),
                            child: Text(
                              'Add New Deposit',
                              style: AppTextStyles.addNewInvoiceStyle,
                            ),
                          ),
                          TextField(
                            controller: controller.searchController,
                            decoration: InputDecoration(
                              hintText: 'Search deposits...',
                              prefixIcon: const Icon(Icons.search),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                              filled: true,
                              fillColor: notifier.textFileColor,
                            ),
                          ),
                        ],
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          InkWell(
                            onTap: () => controller.openDrawer(),
                            child: Text(
                              'Add New Deposit',
                              style: AppTextStyles.addNewInvoiceStyle,
                            ),
                          ),
                          SizedBox(
                            width: Get.width < 850
                                ? Get.width / 2
                                : Get.width / 3.5,
                            child: TextField(
                              controller: controller.searchController,
                              decoration: InputDecoration(
                                hintText: 'Search deposits...',
                                prefixIcon: const Icon(Icons.search),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8.0),
                                ),
                                filled: true,
                                fillColor: notifier.textFileColor,
                              ),
                            ),
                          ),
                        ],
                      ),
              ),
              const SizedBox(height: 20),
              Expanded(
                child: SizedBox(
                  width: Get.width,
                  child: Obx(() {
                    if (controller.isLoading.value) {
                      return const Center(
                        child: CircularProgressIndicator(),
                      );
                    }

                    if (controller.filteredDeposits.isEmpty) {
                      return Center(
                        child: Text(
                          'No deposits found',
                          style: AppTextStyles.invoiceDataStyle.copyWith(
                            color: notifier.text,
                          ),
                        ),
                      );
                    }

                    return ListView(
                      shrinkWrap: true,
                      children: [
                        Table(
                          columnWidths: const {
                            0: FlexColumnWidth(0.5), // S.No
                            1: FlexColumnWidth(1.5), // Account
                            2: FlexColumnWidth(1.0), // Amount
                            3: FlexColumnWidth(1.0), // Date
                            4: FlexColumnWidth(1.5), // Payer
                            5: FlexColumnWidth(1.0), // Actions
                          },
                          border: TableBorder(
                            horizontalInside: BorderSide(
                              color: notifier.getfillborder,
                            ),
                          ),
                          children: [
                            TableRow(
                              decoration: BoxDecoration(
                                color: notifier.getHoverColor,
                              ),
                              children: [
                                DataTableCell(
                                  text: 'S.No',
                                  style:
                                      AppTextStyles.invoiceDataStyle.copyWith(
                                    color: notifier.text,
                                  ),
                                ),
                                DataTableCell(
                                  text: 'Account',
                                  style:
                                      AppTextStyles.invoiceDataStyle.copyWith(
                                    color: notifier.text,
                                  ),
                                ),
                                DataTableCell(
                                  text: 'Amount',
                                  style:
                                      AppTextStyles.invoiceDataStyle.copyWith(
                                    color: notifier.text,
                                  ),
                                ),
                                DataTableCell(
                                  text: 'Date',
                                  style:
                                      AppTextStyles.invoiceDataStyle.copyWith(
                                    color: notifier.text,
                                  ),
                                ),
                                DataTableCell(
                                  text: 'Payer',
                                  style:
                                      AppTextStyles.invoiceDataStyle.copyWith(
                                    color: notifier.text,
                                  ),
                                ),
                                DataTableCell(
                                  text: 'Actions',
                                  style:
                                      AppTextStyles.invoiceDataStyle.copyWith(
                                    color: notifier.text,
                                  ),
                                ),
                              ],
                            ),
                            ...controller.paginatedDeposits
                                .asMap()
                                .entries
                                .map((entry) {
                              final i = entry.key;
                              final deposit = entry.value;
                              return TableRow(
                                children: [
                                  DataTableCell(
                                    text: '${i + 1}',
                                    style:
                                        AppTextStyles.invoiceDataStyle.copyWith(
                                      color: notifier.text,
                                    ),
                                  ),
                                  DataTableCell(
                                    onTap: () => _showDepositDetails(deposit),
                                    text: deposit.accountName,
                                    style:
                                        AppTextStyles.invoiceDataStyle.copyWith(
                                      color: notifier.text,
                                      decoration: TextDecoration.underline,
                                    ),
                                  ),
                                  DataTableCell(
                                    text:
                                        'PKR ${deposit.amount.toStringAsFixed(2)}',
                                    style:
                                        AppTextStyles.invoiceDataStyle.copyWith(
                                      color: notifier.text,
                                    ),
                                  ),
                                  DataTableCell(
                                    text: DateFormat('MM/dd/yyyy')
                                        .format(deposit.createdAt),
                                    style:
                                        AppTextStyles.invoiceDataStyle.copyWith(
                                      color: notifier.text,
                                    ),
                                  ),
                                  DataTableCell(
                                    text: deposit.payerName,
                                    style:
                                        AppTextStyles.invoiceDataStyle.copyWith(
                                      color: notifier.text,
                                    ),
                                  ),
                                  DataTableActionsCell(
                                    menuItems: [
                                      DataTablePopupMenuItem(
                                        text: 'View',
                                        icon: Icons.visibility_outlined,
                                        onTap: () =>
                                            _showDepositDetails(deposit),
                                      ),
                                      DataTablePopupMenuItem(
                                        text: 'Delete',
                                        icon: Icons.delete_outline,
                                        isDanger: true,
                                        onTap: () => _confirmDelete(deposit),
                                      ),
                                    ],
                                  ),
                                ],
                              );
                            }),
                          ],
                        ),
                        const SizedBox(height: 20),
                        // Add pagination widget
                        Obx(() => PaginationWidget(
                              currentPage: controller.currentPage.value,
                              totalPages: controller.totalPages,
                              onPageChanged: controller.setCurrentPage,
                              itemsPerPage: controller.itemsPerPage.value,
                              onItemsPerPageChanged: controller.setItemsPerPage,
                            )),
                      ],
                    );
                  }),
                ),
              ),
            ],
          ),
        ),
        Obx(() {
          if (!controller.isDrawerOpen.value) return const SizedBox.shrink();

          return Positioned(
            right: 0,
            top: 0,
            bottom: 0,
            width: Get.width < 650 ? Get.width : 400,
            child: Container(
              decoration: BoxDecoration(
                color: notifier.getBgColor,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(10),
                    blurRadius: 10,
                    offset: const Offset(0, 0),
                  ),
                ],
              ),
              child: Form(
                key: controller.formKey,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Add New Deposit',
                            style: AppTextStyles.titleStyle.copyWith(
                              color: notifier.text,
                            ),
                          ),
                          IconButton(
                            onPressed: controller.closeDrawer,
                            icon: const Icon(Icons.close),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      _buildDropdownField<AccountModel>(
                        label: 'Account',
                        hint: 'Select Account',
                        value: controller.selectedAccount.value,
                        items: controller.accounts.map((account) {
                          return DropdownMenuItem<AccountModel>(
                            value: account,
                            child: Text(account.name),
                          );
                        }).toList(),
                        onChanged: (value) {
                          controller.setSelectedAccount(value);
                        },
                        validator: controller.validateAccount,
                        addNewAction: controller.openAddAccount,
                        isLoading: controller.isLoadingAccounts.value,
                      ),
                      const SizedBox(height: 16),
                      _buildTextField(
                        controller: controller.amountController,
                        label: 'Amount',
                        keyboardType: const TextInputType.numberWithOptions(
                            decimal: true),
                        validator: controller.validateAmount,
                      ),
                      // Add balance validation feedback
                      Obx(() {
                        if (controller
                            .balanceValidationMessage.value.isNotEmpty) {
                          return Container(
                            margin: const EdgeInsets.only(top: 8),
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: controller.hasInsufficientBalance.value
                                  ? Colors.red.withValues(alpha: 0.1)
                                  : Colors.green.withValues(alpha: 0.1),
                              border: Border.all(
                                color: controller.hasInsufficientBalance.value
                                    ? Colors.red
                                    : Colors.green,
                              ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  controller.hasInsufficientBalance.value
                                      ? Icons.error_outline
                                      : Icons.check_circle_outline,
                                  color: controller.hasInsufficientBalance.value
                                      ? Colors.red
                                      : Colors.green,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    controller.balanceValidationMessage.value,
                                    style: TextStyle(
                                      color: controller
                                              .hasInsufficientBalance.value
                                          ? Colors.red
                                          : Colors.green,
                                      fontSize: 12,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        }
                        return const SizedBox.shrink();
                      }),
                      const SizedBox(height: 16),
                      _buildDatePicker(
                        label: 'Date',
                        selectedDate: controller.selectedDate.value,
                        onDateSelected: controller.setDate,
                      ),
                      const SizedBox(height: 16),
                      _buildDropdownField<DepositCategoryModel>(
                        label: 'Category',
                        hint: 'Select Category',
                        value: controller.selectedCategory.value,
                        items: controller.categories.map((category) {
                          return DropdownMenuItem<DepositCategoryModel>(
                            value: category,
                            child: Text(category.name),
                          );
                        }).toList(),
                        onChanged: (value) {
                          controller.setSelectedCategory(value);
                        },
                        validator: controller.validateCategory,
                        addNewAction: controller.openAddCategory,
                        isLoading: controller.isLoadingCategories.value,
                      ),
                      const SizedBox(height: 16),
                      _buildDropdownField<PayerModel>(
                        label: 'Payer',
                        hint: 'Select Payer',
                        value: controller.selectedPayer.value,
                        items: controller.payers.map((payer) {
                          return DropdownMenuItem<PayerModel>(
                            value: payer,
                            child: Text(payer.name),
                          );
                        }).toList(),
                        onChanged: (value) {
                          controller.setSelectedPayer(value);
                        },
                        validator: controller.validatePayer,
                        addNewAction: controller.openAddPayer,
                        isLoading: controller.isLoadingPayers.value,
                      ),
                      const SizedBox(height: 16),
                      _buildTextField(
                        controller: controller.referenceController,
                        label: 'Reference Number',
                        validator: controller.validateReference,
                      ),
                      const SizedBox(height: 16),
                      // _buildFileUploader(controller),
                      const SizedBox(height: 16),
                      _buildTextField(
                        controller: controller.notesController,
                        label: 'Notes',
                        maxLines: 4,
                      ),
                      const SizedBox(height: 24),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: controller.createDeposit,
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text('Add Deposit'),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }),
      ],
    );
  }

  Future<void> _confirmDelete(DepositModel deposit) async {
    await Get.dialog(
      AlertDialog(
        title: const Text('Confirm Delete'),
        content: const Text(
            'Are you sure you want to delete this deposit? This will also affect the account balance.'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          Obx(() => TextButton(
                onPressed: controller.isDeleting.value
                    ? null
                    : () async {
                        Get.back(); // Close confirmation dialog

                        // Show loading snackbar
                        Get.showSnackbar(
                          const GetSnackBar(
                            title: 'Deleting...',
                            message: 'Please wait while we delete the deposit',
                            duration: Duration(seconds: 3),
                            isDismissible: false,
                          ),
                        );

                        // Delete deposit
                        await controller.deleteDeposit(deposit);

                        // Close loading snackbar
                        if (Get.isSnackbarOpen) {
                          Get.closeCurrentSnackbar();
                        }
                      },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: controller.isDeleting.value
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Text('Delete'),
              )),
        ],
      ),
    );
  }

  void _showDepositDetails(DepositModel deposit) {
    Get.dialog(
      Dialog(
        insetPadding: const EdgeInsets.all(20),
        child: Container(
          width: Get.width < 650 ? Get.width : 600,
          padding: const EdgeInsets.all(20),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Deposit Details',
                      style: AppTextStyles.titleStyle.copyWith(
                        color: notifier.text,
                      ),
                    ),
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                _buildDetailRow('Account', deposit.accountName),
                _buildDetailRow(
                    'Amount', 'PKR ${deposit.amount.toStringAsFixed(2)}'),
                _buildDetailRow(
                    'Date', DateFormat('MM/dd/yyyy').format(deposit.createdAt)),
                _buildDetailRow('Category', deposit.categoryName),
                _buildDetailRow('Payer', deposit.payerName),
                _buildDetailRow('Reference Number', deposit.referenceNumber),
                if (deposit.notes.isNotEmpty)
                  _buildDetailRow('Notes', deposit.notes),
                _buildDetailRow(
                  'Created At',
                  DateFormat('MMM dd, yyyy HH:mm').format(deposit.createdAt),
                ),
                const SizedBox(height: 32),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Get.back(),
                      child: const Text('Close'),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton.icon(
                      onPressed: () => _confirmDelete(deposit),
                      icon: const Icon(Icons.delete, size: 16),
                      label: const Text('Delete Deposit'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: notifier.text,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: notifier.text,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    TextInputType keyboardType = TextInputType.text,
    String? Function(String?)? validator,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: notifier.text,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          validator: validator,
          maxLines: maxLines,
          style: TextStyle(color: notifier.text),
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            filled: true,
            fillColor: notifier.textFileColor,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
        ),
      ],
    );
  }

  Widget _buildDropdownField<T>({
    required String label,
    required String hint,
    required T? value,
    required List<DropdownMenuItem<T>> items,
    required void Function(T?) onChanged,
    required String? Function(T?)? validator,
    required VoidCallback addNewAction,
    required bool isLoading,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: notifier.text,
              ),
            ),
            TextButton.icon(
              onPressed: () {
                // Call appropriate dialog based on the dropdown type
                if (label == 'Account') {
                  _showAddAccountDialog();
                } else if (label == 'Payer') {
                  _showAddPayerDialog();
                } else if (label == 'Category') {
                  _showAddCategoryDialog();
                }
              },
              icon: const Icon(Icons.add, size: 18, color: Colors.blue),
              label:
                  const Text('Add New', style: TextStyle(color: Colors.blue)),
              style: TextButton.styleFrom(
                padding: EdgeInsets.zero,
                minimumSize: const Size(50, 20),
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        isLoading
            ? Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 15),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                  color: notifier.textFileColor,
                ),
                child: const Row(
                  children: [
                    SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                    SizedBox(width: 12),
                    Text('Loading...'),
                  ],
                ),
              )
            : items.isEmpty
                ? Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 15),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(8),
                      color: notifier.textFileColor,
                    ),
                    child: Text('No ${label.toLowerCase()} available'),
                  )
                : Builder(
                    builder: (context) {
                      // Validate dropdown items and value to prevent assertion errors
                      final validItems = _validateDropdownItems(items);
                      final validValue =
                          _validateDropdownValue(value, validItems);

                      return DropdownButtonFormField<T>(
                        value: validValue,
                        hint: Text(hint),
                        style: TextStyle(color: notifier.text),
                        dropdownColor: notifier.getcardColor,
                        decoration: InputDecoration(
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          filled: true,
                          fillColor: notifier.textFileColor,
                          contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 12),
                        ),
                        items: validItems,
                        onChanged: isLoading ? null : onChanged,
                        validator: validator,
                      );
                    },
                  ),
      ],
    );
  }

  Widget _buildDatePicker({
    required String label,
    required DateTime selectedDate,
    required Function(DateTime) onDateSelected,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: notifier.text,
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: () async {
            final DateTime? picked = await showDatePicker(
              context: Get.context!,
              initialDate: selectedDate,
              firstDate: DateTime(2000),
              lastDate: DateTime(2101),
            );
            if (picked != null) {
              onDateSelected(picked);
            }
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 15),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(8),
              color: notifier.textFileColor,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  DateFormat('MM/dd/yyyy').format(selectedDate),
                  style: TextStyle(color: notifier.text),
                ),
                Icon(Icons.calendar_today, color: notifier.text),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // Widget _buildFileUploader(DepositController controller) {
  //   return Column(
  //     crossAxisAlignment: CrossAxisAlignment.start,
  //     children: [
  //       Row(
  //         children: [
  //           Text(
  //             'Upload File',
  //             style: TextStyle(
  //               fontWeight: FontWeight.bold,
  //               color: notifier.text,
  //             ),
  //           ),
  //           const SizedBox(width: 8),
  //           Text(
  //             '(Optional)',
  //             style: TextStyle(
  //               color: notifier.text.withAlpha(150),
  //               fontStyle: FontStyle.italic,
  //               fontSize: 12,
  //             ),
  //           ),
  //         ],
  //       ),
  //       const SizedBox(height: 8),
  // Obx(() {
  //   if (controller.selectedFileName.isNotEmpty) {
  //     return Container(
  //       padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
  //       decoration: BoxDecoration(
  //         border: Border.all(color: Colors.grey),
  //         borderRadius: BorderRadius.circular(8),
  //         color: notifier.textFileColor,
  //       ),
  //       child: Column(
  //         crossAxisAlignment: CrossAxisAlignment.start,
  //         children: [
  //           Row(
  //             children: [
  //               Expanded(
  //                 child: Text(
  //                   controller.selectedFileName.value,
  //                   style: TextStyle(color: notifier.text),
  //                   overflow: TextOverflow.ellipsis,
  //                 ),
  //               ),
  //               IconButton(
  //                 icon: const Icon(Icons.close,
  //                     color: Colors.red, size: 20),
  //                 onPressed: controller.removeFile,
  //               ),
  //             ],
  //           ),
  //           if (controller.selectedFileSize.value > 0)
  //             Text(
  //               'Size: ${(controller.selectedFileSize.value / 1024 / 1024).toStringAsFixed(2)} MB${kIsWeb ? " (Web)" : ""}',
  //               style: TextStyle(
  //                 color: notifier.text.withAlpha(70),
  //                 fontSize: 12,
  //               ),
  //             ),
  //           // Show image preview if it's an image file
  //           if (controller.isImage.value)
  //             Padding(
  //               padding: const EdgeInsets.only(top: 12.0),
  //               child: Column(
  //                 crossAxisAlignment: CrossAxisAlignment.start,
  //                 children: [
  //                   Text(
  //                     'Preview:',
  //                     style: TextStyle(
  //                       fontWeight: FontWeight.bold,
  //                       color: notifier.text.withAlpha(150),
  //                       fontSize: 12,
  //                     ),
  //                   ),
  //                   const SizedBox(height: 8),
  //                   Container(
  //                     constraints: BoxConstraints(
  //                       maxHeight: 200,
  //                       maxWidth: double.infinity,
  //                     ),
  //                     decoration: BoxDecoration(
  //                       border: Border.all(color: Colors.grey.shade300),
  //                       borderRadius: BorderRadius.circular(4),
  //                     ),
  //                     child: kIsWeb
  //                         ? controller.selectedFileBytes.value != null
  //                             ? Image.memory(
  //                                 Uint8List.fromList(controller
  //                                     .selectedFileBytes.value!),
  //                                 fit: BoxFit.contain,
  //                               )
  //                             : const Center(
  //                                 child: Text('No preview available'))
  //                         : controller.selectedFile.value != null
  //                             ? Image.file(
  //                                 controller.selectedFile.value!,
  //                                 fit: BoxFit.contain,
  //                               )
  //                             : const Center(
  //                                 child: Text('No preview available')),
  //                   ),
  //                 ],
  //               ),
  //             ),
  //         ],
  //       ),
  //     );
  //   } else {
  //     return Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         ElevatedButton.icon(
  //           onPressed: controller.pickFile,
  //           icon: const Icon(Icons.upload_file),
  //           label: const Text('Select File'),
  //           style: ElevatedButton.styleFrom(
  //             backgroundColor: Colors.grey.shade700,
  //             foregroundColor: Colors.white,
  //             padding: const EdgeInsets.symmetric(
  //               horizontal: 16,
  //               vertical: 12,
  //             ),
  //           ),
  //         ),
  //         const SizedBox(height: 8),
  //         Text(
  //           'Supported formats: PDF, DOC, DOCX, XLS, XLSX, JPG, JPEG, PNG\nMaximum file size: 10MB',
  //           style: TextStyle(
  //             color: notifier.text.withAlpha(70),
  //             fontSize: 12,
  //           ),
  //         ),
  //         if (kIsWeb)
  //           Padding(
  //             padding: const EdgeInsets.only(top: 8.0),
  //             child: Text(
  //               'Web browser file upload is enabled',
  //               style: TextStyle(
  //                 color: Colors.green,
  //                 fontSize: 12,
  //                 fontWeight: FontWeight.bold,
  //               ),
  //             ),
  //           ),
  //       ],
  //     );
  //   }
  // }),
  //     ],
  //   );
  // }

  void _showAddAccountDialog() {
    final nameController = TextEditingController();
    final numberController = TextEditingController();
    final bankNameController = TextEditingController();
    final branchCodeController = TextEditingController();
    final balanceController = TextEditingController();
    final formKey = GlobalKey<FormState>();

    Get.dialog(
      Dialog(
        insetPadding: const EdgeInsets.all(20),
        child: Container(
          width: Get.width < 650 ? Get.width : 500,
          padding: const EdgeInsets.all(20),
          child: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Add New Account',
                        style: AppTextStyles.titleStyle.copyWith(
                          color: notifier.text,
                        ),
                      ),
                      IconButton(
                        onPressed: () => Get.back(),
                        icon: const Icon(Icons.close),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  TextFormField(
                    controller: nameController,
                    decoration: InputDecoration(
                      labelText: 'Account Name',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      filled: true,
                      fillColor: notifier.textFileColor,
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter account name';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: numberController,
                    decoration: InputDecoration(
                      labelText: 'Account Number',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      filled: true,
                      fillColor: notifier.textFileColor,
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter account number';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: branchCodeController,
                    decoration: InputDecoration(
                      labelText: 'Branch Code',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      filled: true,
                      fillColor: notifier.textFileColor,
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter branch code';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: bankNameController,
                    decoration: InputDecoration(
                      labelText: 'Bank/Branch Address',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      filled: true,
                      fillColor: notifier.textFileColor,
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: balanceController,
                    decoration: InputDecoration(
                      labelText: 'Initial Balance',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      filled: true,
                      fillColor: notifier.textFileColor,
                    ),
                    keyboardType:
                        const TextInputType.numberWithOptions(decimal: true),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter initial balance';
                      }
                      if (double.tryParse(value) == null) {
                        return 'Please enter a valid number';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 24),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () async {
                        if (formKey.currentState!.validate()) {
                          // Show loading indicator
                          Get.dialog(
                            const Center(child: CircularProgressIndicator()),
                            barrierDismissible: false,
                          );

                          try {
                            // Extract validated data from form
                            final name = nameController.text.trim();
                            final accountNumber = numberController.text.trim();
                            final branchCode = branchCodeController.text.trim();
                            final branchAddress =
                                bankNameController.text.trim();
                            final initialBalanceText =
                                balanceController.text.trim();

                            // Parse balance
                            final initialBalance =
                                double.tryParse(initialBalanceText) ?? 0.0;

                            // Create account directly through deposit controller
                            log('Creating account directly through repository');

                            // Get Firebase Auth UID
                            final uid =
                                FirebaseAuth.instance.currentUser?.uid ?? '';
                            if (uid.isEmpty) {
                              throw Exception('User not authenticated');
                            }

                            // Generate a UUID
                            final accountId = const Uuid().v4();

                            // Create account model
                            final newAccount = AccountModel(
                              id: accountId,
                              name: name,
                              initialBalance: initialBalance,
                              accountNumber: accountNumber,
                              branchCode: branchCode,
                              branchAddress: branchAddress,
                              availableBalance: initialBalance,
                              createdAt: DateTime.now(),
                              uid: uid,
                            );

                            // Use the deposit controller's account repository
                            final result = await controller.accountRepository
                                .createAccount(newAccount);

                            // Handle result
                            result.fold(
                              (failure) {
                                // Close loading indicator
                                Get.back();

                                // Show error
                                SnackbarUtils.showError(
                                  AppStrings.errorS,
                                  'Failed to create account: ${failure.message}',
                                );
                              },
                              (success) async {
                                // Close loading indicator
                                Get.back();

                                // Close form dialog
                                Get.back();

                                // Notify user
                                SnackbarUtils.showSuccess(
                                  AppStrings.success,
                                  success.message,
                                );

                                // Refresh account list
                                await controller.fetchAccounts();
                              },
                            );
                          } catch (e) {
                            // Close loading indicator
                            Get.back();

                            log('Detailed error creating account: $e');

                            // Show error with more details
                            SnackbarUtils.showError(
                              AppStrings.errorS,
                              'Failed to create account: ${e.toString()}',
                            );
                          }
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('Add Account'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _showAddPayerDialog() {
    final nameController = TextEditingController();
    final phoneController = TextEditingController();
    final formKey = GlobalKey<FormState>();

    Get.dialog(
      Dialog(
        insetPadding: const EdgeInsets.all(20),
        child: Container(
          width: Get.width < 650 ? Get.width : 500,
          padding: const EdgeInsets.all(20),
          child: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Add New Payer',
                        style: AppTextStyles.titleStyle.copyWith(
                          color: notifier.text,
                        ),
                      ),
                      IconButton(
                        onPressed: () => Get.back(),
                        icon: const Icon(Icons.close),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  TextFormField(
                    controller: nameController,
                    decoration: InputDecoration(
                      labelText: 'Payer Name',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      filled: true,
                      fillColor: notifier.textFileColor,
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter payer name';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: phoneController,
                    decoration: InputDecoration(
                      labelText: 'Phone Number',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      filled: true,
                      fillColor: notifier.textFileColor,
                    ),
                    keyboardType: TextInputType.phone,
                  ),
                  const SizedBox(height: 24),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () async {
                        if (formKey.currentState!.validate()) {
                          // Show loading indicator
                          Get.dialog(
                            const Center(child: CircularProgressIndicator()),
                            barrierDismissible: false,
                          );

                          try {
                            // Extract validated data from form
                            final name = nameController.text.trim();
                            final phone = phoneController.text.trim();

                            // Create payer directly through deposit controller
                            log('Creating payer directly through repository');

                            // Get Firebase Auth UID
                            final uid =
                                FirebaseAuth.instance.currentUser?.uid ?? '';
                            if (uid.isEmpty) {
                              throw Exception('User not authenticated');
                            }

                            // Generate a UUID
                            final payerId = const Uuid().v4();

                            // Create payer model
                            final newPayer = PayerModel(
                              id: payerId,
                              name: name,
                              phoneNumber: phone,
                              createdAt: DateTime.now(),
                              uid: uid,
                            );

                            // Use the deposit controller's payer repository
                            final result = await controller.payerRepository
                                .createPayer(newPayer);

                            // Handle result
                            result.fold(
                              (failure) {
                                // Close loading indicator
                                Get.back();

                                // Show error
                                SnackbarUtils.showError(
                                  AppStrings.errorS,
                                  'Failed to create payer: ${failure.message}',
                                );
                              },
                              (success) async {
                                // Close loading indicator
                                Get.back();

                                // Close form dialog
                                Get.back();

                                // Notify user
                                SnackbarUtils.showSuccess(
                                  AppStrings.success,
                                  success.message,
                                );

                                // Refresh payer list
                                await controller.fetchPayers();
                              },
                            );
                          } catch (e) {
                            // Close loading indicator
                            Get.back();

                            log('Detailed error creating payer: $e');

                            // Show error with more details
                            SnackbarUtils.showError(
                              AppStrings.errorS,
                              'Failed to create payer: ${e.toString()}',
                            );
                          }
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('Add Payer'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _showAddCategoryDialog() {
    final nameController = TextEditingController();
    final descriptionController = TextEditingController();
    final formKey = GlobalKey<FormState>();

    Get.dialog(
      Dialog(
        insetPadding: const EdgeInsets.all(20),
        child: Container(
          width: Get.width < 650 ? Get.width : 500,
          padding: const EdgeInsets.all(20),
          child: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Add New Category',
                        style: AppTextStyles.titleStyle.copyWith(
                          color: notifier.text,
                        ),
                      ),
                      IconButton(
                        onPressed: () => Get.back(),
                        icon: const Icon(Icons.close),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  TextFormField(
                    controller: nameController,
                    decoration: InputDecoration(
                      labelText: 'Category Name',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      filled: true,
                      fillColor: notifier.textFileColor,
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter category name';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: descriptionController,
                    decoration: InputDecoration(
                      labelText: 'Description (Optional)',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      filled: true,
                      fillColor: notifier.textFileColor,
                    ),
                    maxLines: 3,
                  ),
                  const SizedBox(height: 24),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () async {
                        if (formKey.currentState!.validate()) {
                          // Show loading indicator
                          Get.dialog(
                            const Center(child: CircularProgressIndicator()),
                            barrierDismissible: false,
                          );

                          try {
                            // Extract validated data from form
                            final name = nameController.text.trim();
                            final description =
                                descriptionController.text.trim();

                            // Create category directly through deposit controller
                            log('Creating category directly through repository');

                            // Get Firebase Auth UID
                            final uid =
                                FirebaseAuth.instance.currentUser?.uid ?? '';
                            if (uid.isEmpty) {
                              throw Exception('User not authenticated');
                            }

                            // Generate a UUID
                            final categoryId = const Uuid().v4();

                            // Create category model
                            final newCategory = DepositCategoryModel(
                              id: categoryId,
                              name: name,
                              description: description,
                              createdAt: DateTime.now(),
                              uid: uid,
                            );

                            // Use the deposit controller's category repository
                            final result = await controller.categoryRepository
                                .addCategory(newCategory);

                            // Handle result
                            result.fold(
                              (failure) {
                                // Close loading indicator
                                Get.back();

                                // Show error
                                SnackbarUtils.showError(
                                  AppStrings.errorS,
                                  'Failed to create category: ${failure.message}',
                                );
                              },
                              (success) async {
                                // Close loading indicator
                                Get.back();

                                // Close form dialog
                                Get.back();

                                // Notify user
                                SnackbarUtils.showSuccess(
                                  AppStrings.success,
                                  'Category created successfully',
                                );

                                // Refresh category list
                                await controller.fetchCategories();
                              },
                            );
                          } catch (e) {
                            // Close loading indicator
                            Get.back();

                            log('Detailed error creating category: $e');

                            // Show error with more details
                            SnackbarUtils.showError(
                              AppStrings.errorS,
                              'Failed to create category: ${e.toString()}',
                            );
                          }
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('Add Category'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  List<DropdownMenuItem<T>> _validateDropdownItems<T>(
      List<DropdownMenuItem<T>> items) {
    // Remove duplicate items based on value equality
    final Map<String, DropdownMenuItem<T>> uniqueItems = {};

    for (final item in items) {
      String key;
      if (item.value is AccountModel) {
        key = (item.value as AccountModel).id;
      } else if (item.value is DepositCategoryModel) {
        key = (item.value as DepositCategoryModel).id;
      } else if (item.value is PayerModel) {
        key = (item.value as PayerModel).id;
      } else {
        key = item.value.toString();
      }

      uniqueItems[key] = item;
    }

    return uniqueItems.values.toList();
  }

  T? _validateDropdownValue<T>(T? value, List<DropdownMenuItem<T>> items) {
    if (value == null) return null;

    // Check if the value exists in the items list
    final exists = items.any((item) {
      if (value is AccountModel && item.value is AccountModel) {
        return (value as AccountModel).id == (item.value as AccountModel).id;
      } else if (value is DepositCategoryModel &&
          item.value is DepositCategoryModel) {
        return (value as DepositCategoryModel).id ==
            (item.value as DepositCategoryModel).id;
      } else if (value is PayerModel && item.value is PayerModel) {
        return (value as PayerModel).id == (item.value as PayerModel).id;
      } else {
        return item.value == value;
      }
    });

    // If value doesn't exist in items, return null to prevent assertion error
    if (!exists) {
      log('Warning: Selected value not found in dropdown items, clearing selection');
      return null;
    }

    return value;
  }

  /// Show PDF generation dialog
  void _showPDFGenerationDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const DepositsPDFGenerationDialog(),
    );
  }
}
