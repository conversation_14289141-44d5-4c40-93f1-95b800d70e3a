class ExpenseModel {
  final String id;
  final String title;
  final String accountId;
  final String accountName;
  final double amount;

  final String categoryId;
  final String categoryName;
  final String payeeId;
  final String payeeName;
  final String referenceNumber;
  final String notes;
  final DateTime createdAt;
  final String uid; // User ID who created this expense

  ExpenseModel({
    required this.id,
    required this.title,
    required this.accountId,
    required this.accountName,
    required this.amount,
    required this.categoryId,
    required this.categoryName,
    required this.payeeId,
    required this.payeeName,
    required this.referenceNumber,
    this.notes = '',
    required this.createdAt,
    this.uid = '', // Default empty string for backward compatibility
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'accountId': accountId,
      'accountName': accountName,
      'amount': amount,
      'categoryId': categoryId,
      'categoryName': categoryName,
      'payeeId': payeeId,
      'payeeName': payeeName,
      'referenceNumber': referenceNumber,
      'notes': notes,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'uid': uid,
    };
  }

  factory ExpenseModel.fromJson(Map<String, dynamic> json) {
    return ExpenseModel(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      accountId: json['accountId'] ?? '',
      accountName: json['accountName'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      categoryId: json['categoryId'] ?? '',
      categoryName: json['categoryName'] ?? '',
      payeeId: json['payeeId'] ?? '',
      payeeName: json['payeeName'] ?? '',
      referenceNumber: json['referenceNumber'] ?? '',
      notes: json['notes'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt'] ?? 0),
      uid: json['uid'] ?? '',
    );
  }

  // Create a copy of this model with updated fields
  ExpenseModel copyWith({
    String? id,
    String? title,
    String? accountId,
    String? accountName,
    double? amount,
    String? categoryId,
    String? categoryName,
    String? payeeId,
    String? payeeName,
    String? referenceNumber,
    String? notes,
    DateTime? createdAt,
    String? uid,
  }) {
    return ExpenseModel(
      id: id ?? this.id,
      title: title ?? this.title,
      accountId: accountId ?? this.accountId,
      accountName: accountName ?? this.accountName,
      amount: amount ?? this.amount,
      categoryId: categoryId ?? this.categoryId,
      categoryName: categoryName ?? this.categoryName,
      payeeId: payeeId ?? this.payeeId,
      payeeName: payeeName ?? this.payeeName,
      referenceNumber: referenceNumber ?? this.referenceNumber,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      uid: uid ?? this.uid,
    );
  }
}
