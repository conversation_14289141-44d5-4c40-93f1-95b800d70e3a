import 'dart:developer';
import 'package:either_dart/either.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/firebase_service/accounting/aged_reports_firebase_service.dart';
import 'package:logestics/models/finance/aged_report_model.dart';

/// Repository for Aged Reports (Receivables and Payables)
/// Follows the existing repository pattern used in other financial modules
class AgedReportsRepository {
  final AgedReportsFirebaseService _firebaseService;

  AgedReportsRepository({AgedReportsFirebaseService? firebaseService})
      : _firebaseService = firebaseService ?? AgedReportsFirebaseService();

  /// Generate Aged Receivables Report
  Future<Either<FailureObj, AgedReceivablesReport>> generateAgedReceivablesReport({
    required DateTime asOfDate,
    required String companyName,
    bool includeZeroBalances = false,
    List<String>? customerIds,
  }) async {
    try {
      log('Repository: Generating Aged Receivables Report');
      
      final result = await _firebaseService.generateAgedReceivablesReport(
        asOfDate: asOfDate,
        companyName: companyName,
        includeZeroBalances: includeZeroBalances,
        customerIds: customerIds,
      );

      return result.fold(
        (failure) {
          log('Repository: Failed to generate aged receivables report - ${failure.message}');
          return Left(failure);
        },
        (report) {
          log('Repository: Successfully generated aged receivables report with ${report.totalCustomers} customers');
          return Right(report);
        },
      );
    } catch (e) {
      log('Repository: Error generating aged receivables report: $e');
      return Left(FailureObj(
        code: 'REPOSITORY_ERROR',
        message: 'Repository error generating aged receivables report: $e',
      ));
    }
  }

  /// Generate Aged Payables Report
  Future<Either<FailureObj, AgedPayablesReport>> generateAgedPayablesReport({
    required DateTime asOfDate,
    required String companyName,
    bool includeZeroBalances = false,
    List<String>? vendorIds,
  }) async {
    try {
      log('Repository: Generating Aged Payables Report');
      
      final result = await _firebaseService.generateAgedPayablesReport(
        asOfDate: asOfDate,
        companyName: companyName,
        includeZeroBalances: includeZeroBalances,
        vendorIds: vendorIds,
      );

      return result.fold(
        (failure) {
          log('Repository: Failed to generate aged payables report - ${failure.message}');
          return Left(failure);
        },
        (report) {
          log('Repository: Successfully generated aged payables report with ${report.totalVendors} vendors');
          return Right(report);
        },
      );
    } catch (e) {
      log('Repository: Error generating aged payables report: $e');
      return Left(FailureObj(
        code: 'REPOSITORY_ERROR',
        message: 'Repository error generating aged payables report: $e',
      ));
    }
  }

  /// Save Aged Receivables Report
  Future<Either<FailureObj, String>> saveAgedReceivablesReport(
    AgedReceivablesReport report,
  ) async {
    try {
      log('Repository: Saving Aged Receivables Report');
      
      await _firebaseService.saveAgedReceivablesReport(report);
      
      log('Repository: Successfully saved aged receivables report');
      return Right('Aged Receivables Report saved successfully');
    } catch (e) {
      log('Repository: Error saving aged receivables report: $e');
      return Left(FailureObj(
        code: 'SAVE_ERROR',
        message: 'Failed to save aged receivables report: $e',
      ));
    }
  }

  /// Save Aged Payables Report
  Future<Either<FailureObj, String>> saveAgedPayablesReport(
    AgedPayablesReport report,
  ) async {
    try {
      log('Repository: Saving Aged Payables Report');
      
      await _firebaseService.saveAgedPayablesReport(report);
      
      log('Repository: Successfully saved aged payables report');
      return Right('Aged Payables Report saved successfully');
    } catch (e) {
      log('Repository: Error saving aged payables report: $e');
      return Left(FailureObj(
        code: 'SAVE_ERROR',
        message: 'Failed to save aged payables report: $e',
      ));
    }
  }

  /// Get saved Aged Receivables Reports
  Future<Either<FailureObj, List<AgedReceivablesReport>>> getSavedAgedReceivablesReports() async {
    try {
      log('Repository: Fetching saved Aged Receivables Reports');
      
      final reports = await _firebaseService.getSavedAgedReceivablesReports();
      
      log('Repository: Successfully fetched ${reports.length} aged receivables reports');
      return Right(reports);
    } catch (e) {
      log('Repository: Error fetching saved aged receivables reports: $e');
      return Left(FailureObj(
        code: 'FETCH_ERROR',
        message: 'Failed to fetch saved aged receivables reports: $e',
      ));
    }
  }

  /// Get saved Aged Payables Reports
  Future<Either<FailureObj, List<AgedPayablesReport>>> getSavedAgedPayablesReports() async {
    try {
      log('Repository: Fetching saved Aged Payables Reports');
      
      final reports = await _firebaseService.getSavedAgedPayablesReports();
      
      log('Repository: Successfully fetched ${reports.length} aged payables reports');
      return Right(reports);
    } catch (e) {
      log('Repository: Error fetching saved aged payables reports: $e');
      return Left(FailureObj(
        code: 'FETCH_ERROR',
        message: 'Failed to fetch saved aged payables reports: $e',
      ));
    }
  }

  /// Get customer list for filtering (from invoices)
  Future<Either<FailureObj, List<String>>> getCustomerList() async {
    try {
      log('Repository: Fetching customer list for filtering');
      
      // This would be implemented to get unique customer names from invoices
      // For now, return empty list as placeholder
      final customers = <String>[];
      
      log('Repository: Successfully fetched ${customers.length} customers');
      return Right(customers);
    } catch (e) {
      log('Repository: Error fetching customer list: $e');
      return Left(FailureObj(
        code: 'FETCH_ERROR',
        message: 'Failed to fetch customer list: $e',
      ));
    }
  }

  /// Get vendor list for filtering (from bills/payees)
  Future<Either<FailureObj, List<String>>> getVendorList() async {
    try {
      log('Repository: Fetching vendor list for filtering');
      
      // This would be implemented to get unique vendor names from bills/payees
      // For now, return empty list as placeholder
      final vendors = <String>[];
      
      log('Repository: Successfully fetched ${vendors.length} vendors');
      return Right(vendors);
    } catch (e) {
      log('Repository: Error fetching vendor list: $e');
      return Left(FailureObj(
        code: 'FETCH_ERROR',
        message: 'Failed to fetch vendor list: $e',
      ));
    }
  }

  /// Export Aged Receivables Report to Excel
  Future<Either<FailureObj, String>> exportAgedReceivablesToExcel(
    AgedReceivablesReport report,
  ) async {
    try {
      log('Repository: Exporting Aged Receivables Report to Excel');
      
      // This would be implemented using the existing Excel export patterns
      // For now, return success message as placeholder
      final fileName = 'aged_receivables_${DateTime.now().millisecondsSinceEpoch}.xlsx';
      
      log('Repository: Successfully exported aged receivables report to Excel');
      return Right(fileName);
    } catch (e) {
      log('Repository: Error exporting aged receivables report to Excel: $e');
      return Left(FailureObj(
        code: 'EXPORT_ERROR',
        message: 'Failed to export aged receivables report to Excel: $e',
      ));
    }
  }

  /// Export Aged Payables Report to Excel
  Future<Either<FailureObj, String>> exportAgedPayablesToExcel(
    AgedPayablesReport report,
  ) async {
    try {
      log('Repository: Exporting Aged Payables Report to Excel');
      
      // This would be implemented using the existing Excel export patterns
      // For now, return success message as placeholder
      final fileName = 'aged_payables_${DateTime.now().millisecondsSinceEpoch}.xlsx';
      
      log('Repository: Successfully exported aged payables report to Excel');
      return Right(fileName);
    } catch (e) {
      log('Repository: Error exporting aged payables report to Excel: $e');
      return Left(FailureObj(
        code: 'EXPORT_ERROR',
        message: 'Failed to export aged payables report to Excel: $e',
      ));
    }
  }

  /// Generate PDF for Aged Receivables Report
  Future<Either<FailureObj, String>> generateAgedReceivablesPDF(
    AgedReceivablesReport report,
  ) async {
    try {
      log('Repository: Generating Aged Receivables Report PDF');
      
      // This would be implemented using the existing PDF generation patterns
      // For now, return success message as placeholder
      final fileName = 'aged_receivables_${DateTime.now().millisecondsSinceEpoch}.pdf';
      
      log('Repository: Successfully generated aged receivables report PDF');
      return Right(fileName);
    } catch (e) {
      log('Repository: Error generating aged receivables report PDF: $e');
      return Left(FailureObj(
        code: 'PDF_ERROR',
        message: 'Failed to generate aged receivables report PDF: $e',
      ));
    }
  }

  /// Generate PDF for Aged Payables Report
  Future<Either<FailureObj, String>> generateAgedPayablesPDF(
    AgedPayablesReport report,
  ) async {
    try {
      log('Repository: Generating Aged Payables Report PDF');
      
      // This would be implemented using the existing PDF generation patterns
      // For now, return success message as placeholder
      final fileName = 'aged_payables_${DateTime.now().millisecondsSinceEpoch}.pdf';
      
      log('Repository: Successfully generated aged payables report PDF');
      return Right(fileName);
    } catch (e) {
      log('Repository: Error generating aged payables report PDF: $e');
      return Left(FailureObj(
        code: 'PDF_ERROR',
        message: 'Failed to generate aged payables report PDF: $e',
      ));
    }
  }
}
