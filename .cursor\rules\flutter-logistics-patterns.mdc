---
description:
globs:
alwaysApply: false
---
# Flutter Logistics Application Patterns

## State Management with GetX

This application uses GetX for state management. Controllers should follow these patterns:

### Controller Structure
- Controllers extend `GetxController`
- Use mixins like `AutoRefreshMixin` for common functionality
- Always implement `forceRefresh()` method for data updates
- Use `.obs` for reactive variables
- Call `update()` after state changes

### Example Controller Pattern
```dart
class ExampleController extends GetxController with AutoRefreshMixin {
  final repository = ExampleRepositoryImpl(ExampleFirebaseService());
  final items = <ItemModel>[].obs;
  final isLoading = false.obs;

  @override
  Future<void> forceRefresh() async {
    await loadItems();
  }

  Future<void> loadItems() async {
    isLoading.value = true;
    final result = await repository.getItems();
    result.fold(
      (failure) => SnackbarUtils.showError('Error', failure.message),
      (itemsList) => items.value = itemsList,
    );
    isLoading.value = false;
  }
}
```

## Repository Pattern

### Repository Implementation
- Use interface-based repositories with implementation classes
- Always return `Either<FailureObj, SuccessObj>` or `Either<FailureObj, List<Model>>`
- Firebase services handle the actual data operations

### File Structure Pattern
- `repositories/` - Contains repository interfaces and implementations
- `firebase_service/` - Contains Firebase-specific service classes
- `models/` - Contains data models with `toJson()` and `fromJson()` methods

## Financial Transaction Patterns

### Transaction Types
- **Deposits**: Money coming IN (broker fees, income)
- **Expenses**: Money going OUT (payments, munshiana fees, operational costs)
- **Account Transactions**: All account movements for tracking

### Financial Flow Implementation
```dart
// For money coming in (deposits)
final depositRepository = DepositRepositoryImpl(DepositFirebaseService());
final deposit = DepositModel(/* deposit data */);
await depositRepository.createDeposit(deposit);

// For money going out (expenses)
final expenseRepository = ExpenseRepositoryImpl(ExpenseFirebaseService(), AccountFirebaseService());
final expense = ExpenseModel(/* expense data */);
await expenseRepository.createExpense(expense);
```

### Account Balance Updates
- Repositories automatically handle account balance updates
- Never manually update account balances in controllers
- Always reload accounts after financial transactions

## Error Handling Patterns

### Using fold() Pattern
```dart
final result = await repository.someOperation();
result.fold(
  (failure) {
    log('Operation failed: ${failure.message}');
    SnackbarUtils.showError('Error Title', failure.message);
    return false;
  },
  (success) {
    log('Operation succeeded');
    SnackbarUtils.showSuccess('Success', success.message);
    return true;
  },
);
```

### Async Method Returns
- Return `Future<bool>` for operations that can succeed/fail
- Use try-catch blocks with proper error logging
- Show user-friendly error messages via SnackbarUtils

## Firebase Integration

### Field Naming Consistency
- Use `createdAt` for timestamps (not `dateTime`)
- Always include `uid` field for user-scoped data
- Use `orderBy('createdAt', descending: true)` for sorting

### Firebase Service Pattern
```dart
class ExampleFirebaseService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  String get _uid => FirebaseAuth.instance.currentUser?.uid ?? 'anonymous';

  Future<List<Model>> getItems() async {
    final snapshot = await _firestore
        .collection('items')
        .where('uid', isEqualTo: _uid)
        .orderBy('createdAt', descending: true)
        .get();
    
    return snapshot.docs.map((doc) => Model.fromJson(doc.data())).toList();
  }
}
```

## Voucher System Patterns

### Voucher Financial Flows
- **Broker Fees** (when outsource): Create as DEPOSIT → increases account balance
- **Munshiana Fees**: Create as EXPENSE → decreases account balance  
- **Payment Transactions**: Create as EXPENSE → decreases account balance

### Transaction Creation Flow
1. Save voucher first
2. Only create financial transactions if voucher save succeeds
3. Use proper repositories for each transaction type
4. Force refresh relevant controllers to update UI

## UI Refresh Patterns

### Auto-refresh After Operations
```dart
// Force refresh related controllers
try {
  if (Get.isRegistered<DepositController>()) {
    Get.find<DepositController>().forceRefresh();
  }
} catch (e) {
  log('Could not refresh controller: $e');
}
```

### Pagination and Filtering
- Use `PaginationMixin` for paginated lists
- Implement search/filter functionality
- Always update pagination after data changes

## Logging and Debugging

### Consistent Logging
- Use `log()` for debugging information
- Log operation start, success, and failure states
- Include relevant data in log messages
- Log account and transaction details for financial operations

### Example Logging Pattern
```dart
log('Creating transaction for account: ${account.name}');
log('Transaction amount: ${transaction.amount}');
log('Transaction result: ${result.isSuccess ? 'SUCCESS' : 'FAILED'}');
```

