import 'dart:developer';
import 'package:either_dart/either.dart';
import '../../../../core/shared_services/failure_obj.dart';
import '../../../../core/shared_services/success_obj.dart';
import '../../../../firebase_service/accounting/profit_loss_firebase_service.dart';
import '../../../../models/accounting/financial_report_models.dart';

/// Repository for Profit & Loss report operations
class ProfitLossRepository {
  final ProfitLossFirebaseService _firebaseService = ProfitLossFirebaseService();

  /// Generate Profit & Loss report
  Future<Either<FailureObj, ProfitLossReport>> generateProfitLoss({
    required DateTime startDate,
    required DateTime endDate,
    required String companyName,
    bool includeInactiveAccounts = false,
    bool includeZeroBalances = false,
  }) async {
    try {
      log('Repository: Generating P&L report');

      // Validate input parameters
      if (startDate.isAfter(endDate)) {
        return Left(FailureObj(
          code: 'invalid-date-range',
          message: 'Start date cannot be after end date',
        ));
      }

      if (companyName.trim().isEmpty) {
        return Left(FailureObj(
          code: 'invalid-company-name',
          message: 'Company name cannot be empty',
        ));
      }

      // Call Firebase service
      final result = await _firebaseService.generateProfitLoss(
        startDate: startDate,
        endDate: endDate,
        companyName: companyName,
        includeInactiveAccounts: includeInactiveAccounts,
        includeZeroBalances: includeZeroBalances,
      );

      return result.fold(
        (failure) {
          log('Repository: P&L generation failed: ${failure.message}');
          return Left(failure);
        },
        (report) {
          log('Repository: P&L generated successfully');
          return Right(report);
        },
      );
    } catch (e) {
      log('Repository: Error generating P&L: $e');
      return Left(FailureObj(
        code: 'repository-error',
        message: 'Repository error: $e',
      ));
    }
  }

  /// Save Profit & Loss report
  Future<Either<FailureObj, SuccessObj>> saveProfitLossReport(ProfitLossReport report) async {
    try {
      log('Repository: Saving P&L report');

      // Validate report
      if (report.reportId.trim().isEmpty) {
        return Left(FailureObj(
          code: 'invalid-report-id',
          message: 'Report ID cannot be empty',
        ));
      }

      if (report.companyName.trim().isEmpty) {
        return Left(FailureObj(
          code: 'invalid-company-name',
          message: 'Company name cannot be empty',
        ));
      }

      // Call Firebase service
      final result = await _firebaseService.saveProfitLossReport(report);

      return result.fold(
        (failure) {
          log('Repository: P&L save failed: ${failure.message}');
          return Left(failure);
        },
        (success) {
          log('Repository: P&L saved successfully');
          return Right(success);
        },
      );
    } catch (e) {
      log('Repository: Error saving P&L: $e');
      return Left(FailureObj(
        code: 'repository-error',
        message: 'Repository error: $e',
      ));
    }
  }

  /// Load saved Profit & Loss reports
  Future<Either<FailureObj, List<ProfitLossReport>>> getSavedProfitLossReports() async {
    try {
      log('Repository: Loading saved P&L reports');

      final result = await _firebaseService.getSavedProfitLossReports();

      return result.fold(
        (failure) {
          log('Repository: P&L load failed: ${failure.message}');
          return Left(failure);
        },
        (reports) {
          log('Repository: Loaded ${reports.length} P&L reports');
          return Right(reports);
        },
      );
    } catch (e) {
      log('Repository: Error loading P&L reports: $e');
      return Left(FailureObj(
        code: 'repository-error',
        message: 'Repository error: $e',
      ));
    }
  }

  /// Load specific Profit & Loss report by ID
  Future<Either<FailureObj, ProfitLossReport>> getProfitLossReport(String reportId) async {
    try {
      log('Repository: Loading P&L report: $reportId');

      // Validate report ID
      if (reportId.trim().isEmpty) {
        return Left(FailureObj(
          code: 'invalid-report-id',
          message: 'Report ID cannot be empty',
        ));
      }

      final result = await _firebaseService.getProfitLossReport(reportId);

      return result.fold(
        (failure) {
          log('Repository: P&L load failed: ${failure.message}');
          return Left(failure);
        },
        (report) {
          log('Repository: P&L loaded successfully');
          return Right(report);
        },
      );
    } catch (e) {
      log('Repository: Error loading P&L report: $e');
      return Left(FailureObj(
        code: 'repository-error',
        message: 'Repository error: $e',
      ));
    }
  }

  /// Delete Profit & Loss report
  Future<Either<FailureObj, SuccessObj>> deleteProfitLossReport(String reportId) async {
    try {
      log('Repository: Deleting P&L report: $reportId');

      // Validate report ID
      if (reportId.trim().isEmpty) {
        return Left(FailureObj(
          code: 'invalid-report-id',
          message: 'Report ID cannot be empty',
        ));
      }

      final result = await _firebaseService.deleteProfitLossReport(reportId);

      return result.fold(
        (failure) {
          log('Repository: P&L delete failed: ${failure.message}');
          return Left(failure);
        },
        (success) {
          log('Repository: P&L deleted successfully');
          return Right(success);
        },
      );
    } catch (e) {
      log('Repository: Error deleting P&L report: $e');
      return Left(FailureObj(
        code: 'repository-error',
        message: 'Repository error: $e',
      ));
    }
  }
}
