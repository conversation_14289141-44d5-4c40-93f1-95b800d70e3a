import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/widgets/data_table_cell.dart';
import 'package:logestics/core/utils/widgets/pagination_widget.dart';
import 'package:logestics/core/utils/widgets/loading_indicator.dart';
import 'package:logestics/features/finance/loans/presentation/controllers/loan_history_controller.dart';
import 'package:logestics/features/finance/loans/presentation/widgets/loan_history_filters.dart';
import 'package:logestics/features/finance/loans/presentation/widgets/loan_export_dialog.dart';
import 'package:logestics/features/home/<USER>/theme.dart';
import 'package:logestics/models/finance/loan_model.dart';

class LoanHistoryView extends StatelessWidget {
  final bool titleShow;

  const LoanHistoryView({super.key, this.titleShow = true});

  @override
  Widget build(BuildContext context) {
    final notifier = Provider.of<ColorNotifier>(context, listen: true);

    // Try to find the controller with error handling
    late LoanHistoryController controller;
    try {
      controller = Get.find<LoanHistoryController>();
    } catch (e) {
      return Scaffold(
        backgroundColor: notifier.mainBgColor,
        appBar: AppBar(
          title: Text('Loan History - Controller Error'),
          backgroundColor: notifier.getBgColor,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error, size: 64, color: Colors.red),
              SizedBox(height: 16),
              Text('Controller Error: $e'),
              SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => Get.back(),
                child: Text('Go Back'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: notifier.mainBgColor,
      body: Column(
        children: [
          // Navigation breadcrumb (fixed at top)
          if (titleShow) _buildNavigationRow(notifier),

          // Filters section
          LoanHistoryFilters(controller: controller),

          // Main content area
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: notifier.getBgColor,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Column(
                children: [
                  // Header with search and refresh
                  _buildHeader(notifier, controller),

                  // Main content area (table, loading, error, empty states)
                  Expanded(
                    child: Obx(() {
                      if (controller.isInitialLoading.value) {
                        return const Center(
                          child: LoadingIndicator(
                            type: LoadingIndicatorType.circular,
                            size: LoadingIndicatorSize.medium,
                            message: 'Loading loan history...',
                          ),
                        );
                      }

                      if (controller.hasError.value) {
                        return _buildErrorState(notifier, controller);
                      }

                      if (controller.filteredLoans.isEmpty) {
                        return _buildEmptyState(notifier, controller);
                      }

                      return _buildTableWithPagination(notifier, controller);
                    }),
                  ),

                  // Export button (always at bottom)
                  if (titleShow) _buildExportButton(notifier, controller),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationRow(ColorNotifier notifier) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          InkWell(
            onTap: () => Get.back(),
            child: Row(
              children: [
                Icon(
                  Icons.home,
                  size: 15,
                  color: const Color(0xFF0f7bf4),
                ),
                const SizedBox(width: 4),
                const Text(
                  AppStrings.dashboard,
                  style: AppTextStyles.navigationTextStyle,
                ),
              ],
            ),
          ),
          const Text(' > ', style: AppTextStyles.navigationTextStyle),
          const Text('Finance', style: AppTextStyles.navigationTextStyle),
          const Text(' > ', style: AppTextStyles.navigationTextStyle),
          const Text('Loans', style: AppTextStyles.navigationTextStyle),
          const Text(' > ', style: AppTextStyles.navigationTextStyle),
          Text(
            'Loan History',
            style: AppTextStyles.navigationTextStyle.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(
      ColorNotifier notifier, LoanHistoryController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: notifier.getHoverColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(10),
          topRight: Radius.circular(10),
        ),
      ),
      child: Row(
        children: [
          // Title
          Text(
            'Loan History',
            style: AppTextStyles.invoiceHeaderStyle.copyWith(
              color: notifier.text,
              fontSize: 18,
            ),
          ),
          const Spacer(),

          // Refresh button
          Obx(() => IconButton(
                onPressed: controller.isLoading.value
                    ? null
                    : () => controller.refreshData(),
                icon: controller.isRefreshing.value
                    ? SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(notifier.text),
                        ),
                      )
                    : Icon(Icons.refresh, color: notifier.text),
                tooltip: 'Refresh loan history',
              )),

          const SizedBox(width: 16),

          // Search bar
          SizedBox(
            width: 300,
            child: TextField(
              controller: controller.searchController,
              decoration: InputDecoration(
                hintText: 'Search loans...',
                hintStyle: TextStyle(color: notifier.text.withOpacity(0.6)),
                prefixIcon:
                    Icon(Icons.search, color: notifier.text.withOpacity(0.6)),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: notifier.text.withOpacity(0.3)),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: notifier.text.withOpacity(0.3)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFF0f7bf4)),
                ),
                filled: true,
                fillColor: notifier.textFileColor,
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              style: TextStyle(color: notifier.text),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(
      ColorNotifier notifier, LoanHistoryController controller) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.history,
            size: 64,
            color: notifier.text.withOpacity(0.3),
          ),
          const SizedBox(height: 16),
          Text(
            controller.searchQuery.value.isNotEmpty
                ? 'No loans found matching "${controller.searchQuery.value}"'
                : 'No loan history available',
            style: AppTextStyles.emptyStateStyle.copyWith(
              color: notifier.text.withOpacity(0.6),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            controller.searchQuery.value.isNotEmpty
                ? 'Try adjusting your search criteria'
                : 'Loan transactions will appear here once created',
            style: TextStyle(
              color: notifier.text.withOpacity(0.4),
              fontSize: 14,
            ),
          ),
          if (controller.searchQuery.value.isNotEmpty) ...[
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => controller.clearFilters(),
              child: const Text('Clear Filters'),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildErrorState(
      ColorNotifier notifier, LoanHistoryController controller) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red.withValues(alpha: 0.6),
          ),
          const SizedBox(height: 16),
          Text(
            'Failed to load loan history',
            style: AppTextStyles.emptyStateStyle.copyWith(
              color: notifier.text.withOpacity(0.8),
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            controller.errorMessage.value.isNotEmpty
                ? controller.errorMessage.value
                : 'Something went wrong while loading the data',
            style: TextStyle(
              color: notifier.text.withOpacity(0.6),
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton.icon(
                onPressed: () => controller.retryLoading(),
                icon: const Icon(Icons.refresh),
                label: const Text('Retry'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF0f7bf4),
                  foregroundColor: Colors.white,
                ),
              ),
              const SizedBox(width: 16),
              OutlinedButton(
                onPressed: () => controller.clearFilters(),
                child: const Text('Clear Filters'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildExportButton(
      ColorNotifier notifier, LoanHistoryController controller) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          InkWell(
            onTap: () => _showExportDialog(controller),
            child: Text(
              "Export Loans",
              style: AppTextStyles.addNewInvoiceStyle,
            ),
          ),
        ],
      ),
    );
  }

  void _showExportDialog(LoanHistoryController controller) {
    Get.dialog(
      LoanExportDialog(controller: controller),
      barrierDismissible: true,
    );
  }

  Widget _buildTableWithPagination(
      ColorNotifier notifier, LoanHistoryController controller) {
    return Column(
      children: [
        // Table content with refresh overlay
        Expanded(
          child: Stack(
            children: [
              LayoutBuilder(
                builder: (context, constraints) {
                  return _buildLoanTable(notifier, controller, constraints);
                },
              ),
              if (controller.isRefreshing.value)
                Container(
                  color: notifier.getBgColor.withValues(alpha: 0.8),
                  child: const Center(
                    child: LoadingIndicator(
                      type: LoadingIndicatorType.circular,
                      size: LoadingIndicatorSize.small,
                      message: 'Refreshing...',
                    ),
                  ),
                ),
            ],
          ),
        ),

        // Pagination controls
        const SizedBox(height: 20),
        Obx(() => PaginationWidget(
              currentPage: controller.currentPage.value,
              totalPages: controller.totalPages,
              itemsPerPage: controller.itemsPerPage.value,
              onPageChanged: (page) => controller.setCurrentPage(page),
              onItemsPerPageChanged: (count) =>
                  controller.setItemsPerPage(count),
            )),
        const SizedBox(height: 20),
      ],
    );
  }

  Widget _buildLoanTable(ColorNotifier notifier,
      LoanHistoryController controller, BoxConstraints constraints) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SizedBox(
        width: constraints.maxWidth < 1200 ? 1200 : constraints.maxWidth,
        child: SingleChildScrollView(
          child: Table(
            border: TableBorder(
              horizontalInside: BorderSide(color: notifier.getfillborder),
            ),
            columnWidths: const {
              0: FlexColumnWidth(1.5), // Borrower/Lender
              1: FlexColumnWidth(1.2), // Amount
              2: FlexColumnWidth(1.0), // Disbursement Date
              3: FlexColumnWidth(1.0), // Due Date
              4: FlexColumnWidth(1.0), // Status
              5: FlexColumnWidth(1.2), // Paid Amount
              6: FlexColumnWidth(1.2), // Remaining Balance
              7: FlexColumnWidth(1.0), // Loan Type
              8: FlexColumnWidth(1.5), // Remarks
              9: FlexColumnWidth(0.8), // Actions
            },
            children: [
              // Table header
              TableRow(
                decoration: BoxDecoration(color: notifier.getHoverColor),
                children: [
                  DataTableCell(
                    text: 'Borrower/Lender',
                    style: AppTextStyles.invoiceHeaderStyle
                        .copyWith(color: notifier.text),
                  ),
                  DataTableCell(
                    text: 'Loan Amount',
                    style: AppTextStyles.invoiceHeaderStyle
                        .copyWith(color: notifier.text),
                  ),
                  DataTableCell(
                    text: 'Disbursement Date',
                    style: AppTextStyles.invoiceHeaderStyle
                        .copyWith(color: notifier.text),
                  ),
                  DataTableCell(
                    text: 'Due Date',
                    style: AppTextStyles.invoiceHeaderStyle
                        .copyWith(color: notifier.text),
                  ),
                  DataTableCell(
                    text: 'Status',
                    style: AppTextStyles.invoiceHeaderStyle
                        .copyWith(color: notifier.text),
                  ),
                  DataTableCell(
                    text: 'Paid Amount',
                    style: AppTextStyles.invoiceHeaderStyle
                        .copyWith(color: notifier.text),
                  ),
                  DataTableCell(
                    text: 'Remaining Balance',
                    style: AppTextStyles.invoiceHeaderStyle
                        .copyWith(color: notifier.text),
                  ),
                  DataTableCell(
                    text: 'Loan Type',
                    style: AppTextStyles.invoiceHeaderStyle
                        .copyWith(color: notifier.text),
                  ),
                  DataTableCell(
                    text: 'Remarks',
                    style: AppTextStyles.invoiceHeaderStyle
                        .copyWith(color: notifier.text),
                  ),
                  DataTableCell(
                    text: 'Actions',
                    style: AppTextStyles.invoiceHeaderStyle
                        .copyWith(color: notifier.text),
                  ),
                ],
              ),
              // Table rows
              ...controller.paginatedLoans.map((loan) {
                return TableRow(
                  children: [
                    DataTableCell(
                      text: '${loan.requestedByName} → ${loan.requestedToName}',
                      style: TextStyle(color: notifier.text),
                    ),
                    DataTableCell(
                      text: controller.formatCurrency(loan.amount),
                      style: TextStyle(
                        color: notifier.text,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    DataTableCell(
                      text: loan.approvalDate != null
                          ? controller.formatDate(loan.approvalDate)
                          : '-',
                      style: TextStyle(color: notifier.text),
                    ),
                    DataTableCell(
                      text: controller.formatDate(loan.dueDate),
                      style: TextStyle(color: notifier.text),
                    ),
                    TableCell(
                      verticalAlignment: TableCellVerticalAlignment.middle,
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: controller.getDisplayStatusColor(loan),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            controller.getDisplayStatus(loan).toUpperCase(),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ),
                    DataTableCell(
                      text: controller
                          .formatCurrency(controller.getPaidAmount(loan)),
                      style: TextStyle(
                        color: Colors.green[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    DataTableCell(
                      text: controller
                          .formatCurrency(controller.getRemainingBalance(loan)),
                      style: TextStyle(
                        color: controller.getRemainingBalance(loan) > 0
                            ? Colors.red[700]
                            : Colors.green[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    DataTableCell(
                      text: _getLoanType(loan),
                      style: TextStyle(color: notifier.text),
                    ),
                    DataTableCell(
                      text: loan.notes ?? '-',
                      style: TextStyle(color: notifier.text),
                    ),
                    TableCell(
                      verticalAlignment: TableCellVerticalAlignment.middle,
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              icon:
                                  Icon(Icons.visibility, color: notifier.text),
                              onPressed: () => _showLoanDetails(loan),
                              tooltip: 'View Details',
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                );
              }),
            ],
          ),
        ),
      ),
    );
  }

  String _getLoanType(LoanModel loan) {
    if (loan.notes?.toLowerCase().contains('personal') ?? false) {
      return 'Personal';
    }
    if (loan.notes?.toLowerCase().contains('business') ?? false) {
      return 'Business';
    }
    if (loan.notes?.toLowerCase().contains('emergency') ?? false) {
      return 'Emergency';
    }
    if (loan.notes?.toLowerCase().contains('investment') ?? false) {
      return 'Investment';
    }
    if (loan.amount <= 100000) {
      return 'Personal';
    }
    return 'Business';
  }

  void _showLoanDetails(LoanModel loan) {
    Get.dialog(
      Dialog(
        child: Container(
          width: 600,
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Loan Details',
                style: AppTextStyles.invoiceHeaderStyle.copyWith(fontSize: 20),
              ),
              const SizedBox(height: 16),
              _buildDetailRow('Loan ID:', loan.id),
              _buildDetailRow('Borrower:', loan.requestedByName),
              _buildDetailRow('Lender:', loan.requestedToName),
              _buildDetailRow('Amount:',
                  NumberFormat.currency(symbol: 'PKR ').format(loan.amount)),
              _buildDetailRow('Request Date:',
                  DateFormat('dd/MM/yyyy').format(loan.requestDate)),
              _buildDetailRow(
                  'Due Date:', DateFormat('dd/MM/yyyy').format(loan.dueDate)),
              if (loan.approvalDate != null)
                _buildDetailRow('Approval Date:',
                    DateFormat('dd/MM/yyyy').format(loan.approvalDate!)),
              if (loan.repaymentDate != null)
                _buildDetailRow('Repayment Date:',
                    DateFormat('dd/MM/yyyy').format(loan.repaymentDate!)),
              _buildDetailRow('Status:', loan.status.toUpperCase()),
              if (loan.notes != null && loan.notes!.isNotEmpty)
                _buildDetailRow('Notes:', loan.notes!),
              if (loan.rejectionReason != null &&
                  loan.rejectionReason!.isNotEmpty)
                _buildDetailRow('Rejection Reason:', loan.rejectionReason!),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ElevatedButton(
                    onPressed: () => Get.back(),
                    child: const Text('Close'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 150,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}
