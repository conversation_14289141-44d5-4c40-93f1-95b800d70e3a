import 'dart:developer';
import 'package:logestics/features/slab/domain/usecases/get_active_slabs_for_district_use_case.dart';
import 'package:logestics/models/invoice_model.dart';

/// Service for calculating billing amounts using slab rates
class SlabRateCalculationService {
  final GetActiveSlabsForDistrictUseCase getActiveSlabsForDistrictUseCase;

  SlabRateCalculationService({
    required this.getActiveSlabsForDistrictUseCase,
  });

  /// Calculate billing amount for a single invoice using slab rates
  /// Returns the calculated amount or null if no applicable slab rate is found
  Future<double?> calculateInvoiceAmount({
    required InvoiceModel invoice,
    String rateType = 'hmtRate', // Default to HMT rate
  }) async {
    try {
      // Use belongsToDate, fallback to orderDate, then createdAt for rate calculation
      final invoiceDate =
          invoice.belongsToDate ?? invoice.orderDate ?? invoice.createdAt;

      // Get active slabs for the invoice's district and date
      final result = await getActiveSlabsForDistrictUseCase.call(
        districtId: invoice.districtId,
        date: invoiceDate,
      );

      return result.fold(
        (failure) {
          log('Error getting slab rates for invoice ${invoice.tasNumber}: ${failure.message}');
          return null;
        },
        (slabs) {
          if (slabs.isEmpty) {
            log('No active slab rates found for district ${invoice.districtName} on $invoiceDate');
            return null;
          }

          // Use the most recent slab (slabs are ordered by creation date)
          final slab = slabs.first;
          final rate = slab.getRateForDistrict(invoice.districtId);

          if (rate == null) {
            log('No rate found for district ${invoice.districtName} in slab ${slab.slabName}');
            return null;
          }

          // Calculate amount based on rate type
          double rateValue;
          switch (rateType.toLowerCase()) {
            case 'hmtrate':
            case 'hmt':
              rateValue = rate.hmtRate;
              break;
            case 'nonfuelrate':
            case 'nonfuel':
              rateValue = rate.nonFuelRate;
              break;
            default:
              // Check if it's a custom column
              final customValue = rate.getCustomValue(rateType);
              if (customValue != null && customValue is num) {
                rateValue = customValue.toDouble();
              } else {
                log('Unknown rate type: $rateType, falling back to HMT rate');
                rateValue = rate.hmtRate;
              }
          }

          // Calculate total amount: (numberOfBags × weightPerBag × distanceInKilometers × rate) / 1000
          // Dividing by 1000 to convert from kg to tons for rate calculation
          final totalWeightKg = invoice.numberOfBags * invoice.weightPerBag;
          final totalWeightTons = totalWeightKg / 1000;
          final calculatedAmount =
              totalWeightTons * invoice.distanceInKilometers * rateValue;

          log('Invoice ${invoice.tasNumber}: ${totalWeightTons.toStringAsFixed(2)} tons × ${invoice.distanceInKilometers} km × $rateValue rate = ${calculatedAmount.toStringAsFixed(2)}');

          return calculatedAmount;
        },
      );
    } catch (e) {
      log('Unexpected error calculating invoice amount: $e');
      return null;
    }
  }

  /// Calculate total billing amount for multiple invoices using slab rates
  /// Returns a map with total amount and breakdown by invoice
  Future<SlabCalculationResult> calculateBatchInvoiceAmounts({
    required List<InvoiceModel> invoices,
    String rateType = 'hmtRate',
    double fallbackRate = 0.1, // Fallback rate if no slab rate is found
  }) async {
    double totalAmount = 0.0;
    final Map<String, double> invoiceAmounts = {};
    final Map<String, String> calculationDetails = {};
    int slabRateUsedCount = 0;
    int fallbackRateUsedCount = 0;

    for (final invoice in invoices) {
      final slabAmount = await calculateInvoiceAmount(
        invoice: invoice,
        rateType: rateType,
      );

      double invoiceAmount;
      String calculationMethod;

      if (slabAmount != null) {
        invoiceAmount = slabAmount;
        calculationMethod = 'Slab Rate';
        slabRateUsedCount++;
      } else {
        // Fallback to the original calculation method
        invoiceAmount = invoice.numberOfBags *
            invoice.weightPerBag *
            invoice.distanceInKilometers *
            fallbackRate;
        calculationMethod = 'Fallback Rate';
        fallbackRateUsedCount++;

        log('Using fallback rate for invoice ${invoice.tasNumber}: ${invoiceAmount.toStringAsFixed(2)}');
      }

      totalAmount += invoiceAmount;
      invoiceAmounts[invoice.tasNumber] = invoiceAmount;
      calculationDetails[invoice.tasNumber] = calculationMethod;
    }

    log('Batch calculation complete: $slabRateUsedCount invoices used slab rates, $fallbackRateUsedCount used fallback rate');

    return SlabCalculationResult(
      totalAmount: totalAmount,
      invoiceAmounts: invoiceAmounts,
      calculationDetails: calculationDetails,
      slabRateUsedCount: slabRateUsedCount,
      fallbackRateUsedCount: fallbackRateUsedCount,
    );
  }

  /// Get available rate types for a specific district and date
  Future<List<String>> getAvailableRateTypes({
    required String districtId,
    required DateTime date,
  }) async {
    try {
      final result = await getActiveSlabsForDistrictUseCase.call(
        districtId: districtId,
        date: date,
      );

      return result.fold(
        (failure) => ['hmtRate', 'nonFuelRate'], // Default rate types
        (slabs) {
          if (slabs.isEmpty) return ['hmtRate', 'nonFuelRate'];

          final slab = slabs.first;
          final rate = slab.getRateForDistrict(districtId);

          if (rate == null) return ['hmtRate', 'nonFuelRate'];

          // Return standard rates plus custom columns
          final rateTypes = ['hmtRate', 'nonFuelRate'];
          rateTypes.addAll(rate.customColumns.keys);

          return rateTypes;
        },
      );
    } catch (e) {
      log('Error getting available rate types: $e');
      return ['hmtRate', 'nonFuelRate'];
    }
  }
}

/// Result class for batch slab calculations
class SlabCalculationResult {
  final double totalAmount;
  final Map<String, double> invoiceAmounts;
  final Map<String, String> calculationDetails;
  final int slabRateUsedCount;
  final int fallbackRateUsedCount;

  SlabCalculationResult({
    required this.totalAmount,
    required this.invoiceAmounts,
    required this.calculationDetails,
    required this.slabRateUsedCount,
    required this.fallbackRateUsedCount,
  });

  /// Get calculation summary
  String get summary {
    final total = slabRateUsedCount + fallbackRateUsedCount;
    return 'Total: ${totalAmount.toStringAsFixed(2)} | '
        'Slab rates: $slabRateUsedCount/$total | '
        'Fallback rates: $fallbackRateUsedCount/$total';
  }

  /// Check if all invoices used slab rates
  bool get allUsedSlabRates => fallbackRateUsedCount == 0;

  /// Check if any invoices used slab rates
  bool get anyUsedSlabRates => slabRateUsedCount > 0;
}
