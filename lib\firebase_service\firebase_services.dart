import 'package:cloud_firestore/cloud_firestore.dart';

class FirebaseServices {
  Future<int> getNextAutoIncrementValue({
    required String collectionName,
    required String fieldName,
  }) async {
    final collection = FirebaseFirestore.instance.collection(collectionName);

    // Query only the field we need and order in descending order with limit 1
    // This is more efficient than fetching all documents
    QuerySnapshot snapshot =
        await collection.orderBy(fieldName, descending: true).limit(1).get();

    if (snapshot.docs.isEmpty) {
      return 1;
    }

    final data = snapshot.docs.first.data() as Map<String, dynamic>;
    final currentMaxNumber = data[fieldName] as int? ?? 0;

    return currentMaxNumber + 1;
  }
}
