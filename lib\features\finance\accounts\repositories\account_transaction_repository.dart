import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/firebase_service/finance/account_transaction_firebase_service.dart';
import 'package:logestics/models/finance/account_transaction_model.dart';
import 'package:logestics/models/finance/paginated_transaction_result.dart';
import 'package:uuid/uuid.dart';

abstract class AccountTransactionRepository {
  Future<Either<FailureObj, List<AccountTransactionModel>>>
      getTransactionsForAccount(String accountId);
  Future<Either<FailureObj, List<AccountTransactionModel>>>
      getAllTransactions();

  // Pagination methods
  Future<Either<FailureObj, PaginatedTransactionResult>>
      getTransactionsForAccountPaginated({
    required String accountId,
    required int limit,
    QueryDocumentSnapshot? lastDocument,
  });
  Future<Either<FailureObj, int>> getTransactionCountForAccount(
      String accountId);
  Future<Either<FailureObj, AccountTransactionModel>> createTransaction(
      AccountTransactionModel transaction);
  Future<Either<FailureObj, SuccessObj>> createTransactions(
      List<AccountTransactionModel> transactions);
  Future<Either<FailureObj, SuccessObj>> deleteTransaction(
      String transactionId);
  Future<Either<FailureObj, SuccessObj>> updateTransaction(
      AccountTransactionModel transaction);

  // Convenience methods for specific transaction types
  Future<Either<FailureObj, AccountTransactionModel>> createDepositTransaction(
      {required String accountId,
      required String accountName,
      required double amount,
      required DateTime transactionDate,
      required String payerId,
      required String payerName,
      required String referenceId,
      required String referenceName,
      String description = ''});

  Future<Either<FailureObj, AccountTransactionModel>>
      createVoucherPaymentTransaction(
          {required String accountId,
          required String accountName,
          required double amount,
          required DateTime transactionDate,
          required String? payeeId,
          required String? payeeName,
          required String voucherId,
          required String voucherNumber,
          required String description,
          Map<String, dynamic>? metadata});

  Future<Either<FailureObj, AccountTransactionModel>>
      createBrokerFeesTransaction(
          {required String accountId,
          required String accountName,
          required double amount,
          required DateTime transactionDate,
          required String? payeeId,
          required String? payeeName,
          required String voucherId,
          required String voucherNumber,
          String? category});

  Future<Either<FailureObj, AccountTransactionModel>>
      createMunshianaTransaction(
          {required String accountId,
          required String accountName,
          required double amount,
          required DateTime transactionDate,
          required String? payeeId,
          required String? payeeName,
          required String voucherId,
          required String voucherNumber,
          String? category});
}

class AccountTransactionRepositoryImpl implements AccountTransactionRepository {
  final AccountTransactionFirebaseService firebaseService;

  AccountTransactionRepositoryImpl(this.firebaseService);

  @override
  Future<Either<FailureObj, List<AccountTransactionModel>>>
      getTransactionsForAccount(String accountId) async {
    try {
      log('Fetching transactions for account: $accountId');
      final transactions =
          await firebaseService.getTransactionsForAccount(accountId);
      return Right(transactions);
    } catch (e) {
      log('Error fetching transactions: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, List<AccountTransactionModel>>>
      getAllTransactions() async {
    try {
      log('Fetching all transactions');
      final transactions = await firebaseService.getAllTransactions();
      return Right(transactions);
    } catch (e) {
      log('Error fetching all transactions: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, AccountTransactionModel>> createTransaction(
      AccountTransactionModel transaction) async {
    try {
      log('Creating transaction: ${transaction.description}');
      final createdTransaction =
          await firebaseService.createTransaction(transaction);
      return Right(createdTransaction);
    } catch (e) {
      log('Error creating transaction: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> createTransactions(
      List<AccountTransactionModel> transactions) async {
    try {
      log('Creating ${transactions.length} transactions');
      await firebaseService.createTransactions(transactions);
      return Right(SuccessObj(message: 'Transactions created successfully'));
    } catch (e) {
      log('Error creating transactions: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> deleteTransaction(
      String transactionId) async {
    try {
      log('Deleting transaction: $transactionId');
      await firebaseService.deleteTransaction(transactionId);
      return Right(SuccessObj(message: 'Transaction deleted successfully'));
    } catch (e) {
      log('Error deleting transaction: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> updateTransaction(
      AccountTransactionModel transaction) async {
    try {
      log('Updating transaction: ${transaction.id}');
      await firebaseService.updateTransaction(transaction);
      return Right(SuccessObj(message: 'Transaction updated successfully'));
    } catch (e) {
      log('Error updating transaction: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, AccountTransactionModel>>
      createVoucherPaymentTransaction({
    required String accountId,
    required String accountName,
    required double amount,
    required DateTime transactionDate,
    required String? payeeId,
    required String? payeeName,
    required String voucherId,
    required String voucherNumber,
    required String description,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      log('Creating voucher payment transaction for voucher: $voucherNumber');
      final transaction = await firebaseService.createVoucherPaymentTransaction(
        accountId: accountId,
        accountName: accountName,
        amount: amount,
        transactionDate: transactionDate,
        payeeId: payeeId,
        payeeName: payeeName,
        voucherId: voucherId,
        voucherNumber: voucherNumber,
        description: description,
        metadata: metadata,
      );
      return Right(transaction);
    } catch (e) {
      log('Error creating voucher payment transaction: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, AccountTransactionModel>>
      createBrokerFeesTransaction({
    required String accountId,
    required String accountName,
    required double amount,
    required DateTime transactionDate,
    required String? payeeId,
    required String? payeeName,
    required String voucherId,
    required String voucherNumber,
    String? category,
  }) async {
    try {
      log('Creating broker fees transaction for voucher: $voucherNumber');
      final transaction = await firebaseService.createBrokerFeesTransaction(
        accountId: accountId,
        accountName: accountName,
        amount: amount,
        transactionDate: transactionDate,
        payeeId: payeeId,
        payeeName: payeeName,
        voucherId: voucherId,
        voucherNumber: voucherNumber,
        category: category ?? 'Broker Fees',
      );
      return Right(transaction);
    } catch (e) {
      log('Error creating broker fees transaction: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, AccountTransactionModel>>
      createMunshianaTransaction({
    required String accountId,
    required String accountName,
    required double amount,
    required DateTime transactionDate,
    required String? payeeId,
    required String? payeeName,
    required String voucherId,
    required String voucherNumber,
    String? category,
  }) async {
    try {
      log('Creating munshiana transaction for voucher: $voucherNumber');
      final transaction = await firebaseService.createMunshianaTransaction(
        accountId: accountId,
        accountName: accountName,
        amount: amount,
        transactionDate: transactionDate,
        payeeId: payeeId,
        payeeName: payeeName,
        voucherId: voucherId,
        voucherNumber: voucherNumber,
        category: category ?? 'Munshiana',
      );
      return Right(transaction);
    } catch (e) {
      log('Error creating munshiana transaction: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, AccountTransactionModel>> createDepositTransaction({
    required String accountId,
    required String accountName,
    required double amount,
    required DateTime transactionDate,
    required String payerId,
    required String payerName,
    required String referenceId,
    required String referenceName,
    String description = '',
  }) async {
    try {
      log('Creating deposit transaction: $amount for account $accountName');
      final transaction = AccountTransactionModel(
        id: const Uuid().v4(),
        accountId: accountId,
        accountName: accountName,
        amount: amount, // Positive amount for incoming deposit
        transactionDate: transactionDate,
        type: TransactionType.deposit,
        description:
            description.isNotEmpty ? description : 'Deposit from $payerName',
        payerId: payerId,
        payerName: payerName,
        referenceId: referenceId,
        referenceName: referenceName,
      );

      final createdTransaction =
          await firebaseService.createTransaction(transaction);
      return Right(createdTransaction);
    } catch (e) {
      log('Error creating deposit transaction: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, PaginatedTransactionResult>>
      getTransactionsForAccountPaginated({
    required String accountId,
    required int limit,
    QueryDocumentSnapshot? lastDocument,
  }) async {
    try {
      log('Fetching paginated transactions for account: $accountId, limit: $limit');
      final result = await firebaseService.getTransactionsForAccountPaginated(
        accountId: accountId,
        limit: limit,
        lastDocument: lastDocument,
      );
      return Right(result);
    } catch (e) {
      log('Error fetching paginated transactions: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, int>> getTransactionCountForAccount(
      String accountId) async {
    try {
      log('Fetching transaction count for account: $accountId');
      final count =
          await firebaseService.getTransactionCountForAccount(accountId);
      return Right(count);
    } catch (e) {
      log('Error fetching transaction count: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }
}
