import '../../models/finance/chart_of_accounts_model.dart';
import '../../firebase_service/accounting/chart_of_accounts_firebase_service.dart';

/// Service for managing configurable account mappings for different transaction types
class TransactionAccountMappingService {
  final ChartOfAccountsFirebaseService _chartOfAccountsService;

  TransactionAccountMappingService(this._chartOfAccountsService);

  /// Get default account mapping for expense transactions
  Future<ExpenseAccountMapping?> getExpenseAccountMapping(String uid) async {
    try {
      // Get expense account (operating expenses)
      final expenseAccount = await _getAccountByType(
        AccountType.operatingExpenses,
        'General Expenses',
      );

      // Get cash account
      final cashAccount = await _getAccountByType(
        AccountType.cash,
        'Cash',
      );

      if (expenseAccount == null || cashAccount == null) return null;

      return ExpenseAccountMapping(
        expenseAccount: expenseAccount,
        cashAccount: cashAccount,
      );
    } catch (e) {
      return null;
    }
  }

  /// Get default account mapping for bill transactions
  Future<BillAccountMapping?> getBillAccountMapping(String uid) async {
    try {
      // Get accounts receivable account
      final accountsReceivableAccount = await _getAccountByType(
        AccountType.accountsReceivable,
        'Accounts Receivable',
      );

      // Get service revenue account
      final serviceRevenueAccount = await _getAccountByType(
        AccountType.serviceRevenue,
        'Service Revenue',
      );

      if (accountsReceivableAccount == null || serviceRevenueAccount == null) {
        return null;
      }

      return BillAccountMapping(
        accountsReceivableAccount: accountsReceivableAccount,
        serviceRevenueAccount: serviceRevenueAccount,
      );
    } catch (e) {
      return null;
    }
  }

  /// Get default account mapping for voucher transactions
  Future<VoucherAccountMapping?> getVoucherAccountMapping(String uid) async {
    try {
      // Get broker fees expense account
      final brokerFeesAccount = await _getAccountByType(
        AccountType.operatingExpenses,
        'Broker Fees',
      );

      // Get munshiana expense account
      final munshianaAccount = await _getAccountByType(
        AccountType.operatingExpenses,
        'Munshiana',
      );

      // Get freight revenue account
      final freightRevenueAccount = await _getAccountByType(
        AccountType.serviceRevenue,
        'Freight Revenue',
      );

      // Get cash account
      final cashAccount = await _getAccountByType(
        AccountType.cash,
        'Cash',
      );

      if (brokerFeesAccount == null ||
          munshianaAccount == null ||
          freightRevenueAccount == null ||
          cashAccount == null) {
        return null;
      }

      return VoucherAccountMapping(
        brokerFeesAccount: brokerFeesAccount,
        munshianaAccount: munshianaAccount,
        freightRevenueAccount: freightRevenueAccount,
        cashAccount: cashAccount,
      );
    } catch (e) {
      return null;
    }
  }

  /// Get default account mapping for loan transactions
  Future<LoanAccountMapping?> getLoanAccountMapping(String uid) async {
    try {
      // Get loans receivable account (for money we lend out)
      final loansReceivableAccount = await _getAccountByType(
        AccountType.currentAssets,
        'Loans Receivable',
      );

      // Get loans payable account (for money we borrow)
      final loansPayableAccount = await _getAccountByType(
        AccountType.longTermLiabilities,
        'Loans Payable',
      );

      // Get cash account
      final cashAccount = await _getAccountByType(
        AccountType.cash,
        'Cash',
      );

      if (loansReceivableAccount == null ||
          loansPayableAccount == null ||
          cashAccount == null) {
        return null;
      }

      return LoanAccountMapping(
        loansReceivableAccount: loansReceivableAccount,
        loansPayableAccount: loansPayableAccount,
        cashAccount: cashAccount,
      );
    } catch (e) {
      return null;
    }
  }

  /// Get default account mapping for general account transactions
  Future<AccountTransactionMapping?> getAccountTransactionMapping(
      String uid) async {
    try {
      // Get accounts receivable account
      final accountsReceivableAccount = await _getAccountByType(
        AccountType.accountsReceivable,
        'Accounts Receivable',
      );

      // Get general expenses account
      final generalExpensesAccount = await _getAccountByType(
        AccountType.operatingExpenses,
        'General Expenses',
      );

      // Get miscellaneous expenses account
      final miscExpensesAccount = await _getAccountByType(
        AccountType.operatingExpenses,
        'Miscellaneous Expenses',
      );

      if (accountsReceivableAccount == null ||
          generalExpensesAccount == null ||
          miscExpensesAccount == null) {
        return null;
      }

      return AccountTransactionMapping(
        accountsReceivableAccount: accountsReceivableAccount,
        generalExpensesAccount: generalExpensesAccount,
        miscellaneousExpensesAccount: miscExpensesAccount,
      );
    } catch (e) {
      return null;
    }
  }

  // Helper method
  Future<ChartOfAccountsModel?> _getAccountByType(
    AccountType accountType,
    String preferredName,
  ) async {
    try {
      final accounts =
          await _chartOfAccountsService.getAccountsByType(accountType);
      // First try to find by preferred name and type
      try {
        return accounts.firstWhere(
          (account) => account.accountName
              .toLowerCase()
              .contains(preferredName.toLowerCase()),
        );
      } catch (e) {
        // If not found, get any account of the specified type
        try {
          return accounts.first;
        } catch (e) {
          return null;
        }
      }
    } catch (e) {
      return null;
    }
  }
}

/// Account mapping classes for different transaction types
class ExpenseAccountMapping {
  final ChartOfAccountsModel expenseAccount;
  final ChartOfAccountsModel cashAccount;

  ExpenseAccountMapping({
    required this.expenseAccount,
    required this.cashAccount,
  });
}

class BillAccountMapping {
  final ChartOfAccountsModel accountsReceivableAccount;
  final ChartOfAccountsModel serviceRevenueAccount;

  BillAccountMapping({
    required this.accountsReceivableAccount,
    required this.serviceRevenueAccount,
  });
}

class VoucherAccountMapping {
  final ChartOfAccountsModel brokerFeesAccount;
  final ChartOfAccountsModel munshianaAccount;
  final ChartOfAccountsModel freightRevenueAccount;
  final ChartOfAccountsModel cashAccount;

  VoucherAccountMapping({
    required this.brokerFeesAccount,
    required this.munshianaAccount,
    required this.freightRevenueAccount,
    required this.cashAccount,
  });
}

class LoanAccountMapping {
  final ChartOfAccountsModel loansReceivableAccount;
  final ChartOfAccountsModel loansPayableAccount;
  final ChartOfAccountsModel cashAccount;

  LoanAccountMapping({
    required this.loansReceivableAccount,
    required this.loansPayableAccount,
    required this.cashAccount,
  });
}

class AccountTransactionMapping {
  final ChartOfAccountsModel accountsReceivableAccount;
  final ChartOfAccountsModel generalExpensesAccount;
  final ChartOfAccountsModel miscellaneousExpensesAccount;

  AccountTransactionMapping({
    required this.accountsReceivableAccount,
    required this.generalExpensesAccount,
    required this.miscellaneousExpensesAccount,
  });
}
