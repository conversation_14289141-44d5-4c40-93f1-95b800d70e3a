# Firebase Composite Indexes Setup Guide

## Chart of Accounts Collection Indexes Required

The Chart of Accounts dashboard is failing because Firebase requires composite indexes for compound queries. Here are the exact indexes you need to create:

### Collection: `chart_of_accounts`

#### 1. **Primary Active Accounts Query Index**
- **Fields:**
  - `uid` (Ascending)
  - `isActive` (Ascending) 
  - `accountNumber` (Ascending)

#### 2. **All Accounts Query Index**
- **Fields:**
  - `uid` (Ascending)
  - `accountNumber` (Ascending)

#### 3. **Accounts by Category Query Index**
- **Fields:**
  - `uid` (Ascending)
  - `category` (Ascending)
  - `isActive` (Ascending)
  - `accountNumber` (Ascending)

#### 4. **Accounts by Type Query Index**
- **Fields:**
  - `uid` (Ascending)
  - `accountType` (Ascending)
  - `isActive` (Ascending)
  - `accountNumber` (Ascending)

#### 5. **Child Accounts Query Index**
- **Fields:**
  - `uid` (Ascending)
  - `parentAccountId` (Ascending)
  - `isActive` (Ascending)
  - `accountNumber` (Ascending)

#### 6. **Next Account Number Query Index**
- **Fields:**
  - `uid` (Ascending)
  - `category` (Ascending)
  - `accountNumber` (Descending)

#### 7. **Child Account Check Query Index**
- **Fields:**
  - `parentAccountId` (Ascending)
  - `uid` (Ascending)
  - `isActive` (Ascending)

## How to Create These Indexes

### Method 1: Firebase Console (Recommended)

1. **Go to Firebase Console**: https://console.firebase.google.com/
2. **Select your project**
3. **Navigate to Firestore Database**
4. **Click on "Indexes" tab**
5. **Click "Create Index"**
6. **For each index above:**
   - Collection ID: `chart_of_accounts`
   - Add fields in the exact order specified
   - Set ascending/descending as noted
   - Click "Create"

### Method 2: Automatic Index Creation

1. **Run the app and navigate to Chart of Accounts**
2. **Firebase will show error messages with direct links to create indexes**
3. **Click the provided links to auto-create indexes**
4. **Wait for indexes to build (can take several minutes)**

### Method 3: Firebase CLI (Advanced)

Create a `firestore.indexes.json` file:

```json
{
  "indexes": [
    {
      "collectionGroup": "chart_of_accounts",
      "queryScope": "COLLECTION",
      "fields": [
        {"fieldPath": "uid", "order": "ASCENDING"},
        {"fieldPath": "isActive", "order": "ASCENDING"},
        {"fieldPath": "accountNumber", "order": "ASCENDING"}
      ]
    },
    {
      "collectionGroup": "chart_of_accounts", 
      "queryScope": "COLLECTION",
      "fields": [
        {"fieldPath": "uid", "order": "ASCENDING"},
        {"fieldPath": "accountNumber", "order": "ASCENDING"}
      ]
    },
    {
      "collectionGroup": "chart_of_accounts",
      "queryScope": "COLLECTION", 
      "fields": [
        {"fieldPath": "uid", "order": "ASCENDING"},
        {"fieldPath": "category", "order": "ASCENDING"},
        {"fieldPath": "isActive", "order": "ASCENDING"},
        {"fieldPath": "accountNumber", "order": "ASCENDING"}
      ]
    },
    {
      "collectionGroup": "chart_of_accounts",
      "queryScope": "COLLECTION",
      "fields": [
        {"fieldPath": "uid", "order": "ASCENDING"},
        {"fieldPath": "accountType", "order": "ASCENDING"}, 
        {"fieldPath": "isActive", "order": "ASCENDING"},
        {"fieldPath": "accountNumber", "order": "ASCENDING"}
      ]
    },
    {
      "collectionGroup": "chart_of_accounts",
      "queryScope": "COLLECTION",
      "fields": [
        {"fieldPath": "uid", "order": "ASCENDING"},
        {"fieldPath": "parentAccountId", "order": "ASCENDING"},
        {"fieldPath": "isActive", "order": "ASCENDING"}, 
        {"fieldPath": "accountNumber", "order": "ASCENDING"}
      ]
    },
    {
      "collectionGroup": "chart_of_accounts",
      "queryScope": "COLLECTION",
      "fields": [
        {"fieldPath": "uid", "order": "ASCENDING"},
        {"fieldPath": "category", "order": "ASCENDING"},
        {"fieldPath": "accountNumber", "order": "DESCENDING"}
      ]
    },
    {
      "collectionGroup": "chart_of_accounts", 
      "queryScope": "COLLECTION",
      "fields": [
        {"fieldPath": "parentAccountId", "order": "ASCENDING"},
        {"fieldPath": "uid", "order": "ASCENDING"},
        {"fieldPath": "isActive", "order": "ASCENDING"}
      ]
    }
  ]
}
```

Then run: `firebase deploy --only firestore:indexes`

## Expected Results

After creating these indexes:
- ✅ Chart of Accounts dashboard will load without errors
- ✅ Real-time updates will work properly
- ✅ All filtering and sorting operations will function
- ✅ Account creation, editing, and deletion will work seamlessly

## Index Building Time

- **Small datasets** (< 1000 documents): 1-5 minutes
- **Medium datasets** (1000-10000 documents): 5-15 minutes  
- **Large datasets** (> 10000 documents): 15+ minutes

## Troubleshooting

If you still see errors after creating indexes:
1. **Wait for index building to complete** (check Firebase Console)
2. **Clear browser cache and restart the app**
3. **Check that all field names match exactly** (case-sensitive)
4. **Verify collection name is `chart_of_accounts`**
5. **Ensure you're using the correct Firebase project**

## Additional Indexes for Other Accounting Collections

You may also need similar indexes for:
- `journal_entries` collection
- `fiscal_periods` collection  
- `financial_reports` collection

Follow the same pattern for any compound queries in those collections.
