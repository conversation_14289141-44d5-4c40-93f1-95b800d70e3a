import 'dart:convert';
import 'dart:developer';
import 'package:logestics/firebase_service/backup_restore_firebase_service.dart';

/// Utility class for testing backup and restore functionality
class BackupTestUtils {
  static final BackupRestoreFirebaseService _service = BackupRestoreFirebaseService();

  /// Test the complete backup and restore cycle
  static Future<bool> testBackupRestoreCycle() async {
    try {
      log('Starting backup/restore cycle test...');

      // Step 1: Create a backup
      log('Step 1: Creating backup...');
      final backupResult = await _service.createBackup(
        onProgress: (progress) => log('Backup progress: $progress'),
      );

      if (backupResult.isLeft()) {
        log('Backup creation failed');
        return false;
      }

      final backupData = backupResult.getOrElse(() => {});
      log('Backup created successfully');

      // Step 2: Validate backup structure
      log('Step 2: Validating backup structure...');
      if (!_validateBackupStructure(backupData)) {
        log('Backup structure validation failed');
        return false;
      }
      log('Backup structure validation passed');

      // Step 3: Test JSON serialization/deserialization
      log('Step 3: Testing JSON serialization...');
      final jsonString = _service.backupToJson(backupData);
      final parseResult = _service.parseBackupJson(jsonString);
      
      if (parseResult.isLeft()) {
        log('JSON serialization test failed');
        return false;
      }
      log('JSON serialization test passed');

      // Step 4: Validate parsed data matches original
      log('Step 4: Validating data integrity...');
      final parsedData = parseResult.getOrElse(() => {});
      if (!_compareBackupData(backupData, parsedData)) {
        log('Data integrity validation failed');
        return false;
      }
      log('Data integrity validation passed');

      log('Backup/restore cycle test completed successfully!');
      return true;
    } catch (e) {
      log('Backup/restore cycle test failed with error: $e');
      return false;
    }
  }

  /// Validate the structure of backup data
  static bool _validateBackupStructure(Map<String, dynamic> backupData) {
    try {
      // Check required top-level keys
      if (!backupData.containsKey('metadata') || !backupData.containsKey('collections')) {
        log('Missing required top-level keys');
        return false;
      }

      // Validate metadata
      final metadata = backupData['metadata'] as Map<String, dynamic>?;
      if (metadata == null) {
        log('Invalid metadata structure');
        return false;
      }

      final requiredMetadataFields = ['version', 'createdAt', 'companyUid', 'totalCollections'];
      for (String field in requiredMetadataFields) {
        if (!metadata.containsKey(field)) {
          log('Missing required metadata field: $field');
          return false;
        }
      }

      // Validate collections
      final collections = backupData['collections'] as Map<String, dynamic>?;
      if (collections == null) {
        log('Invalid collections structure');
        return false;
      }

      // Validate each collection is a list
      for (String collectionName in collections.keys) {
        if (collections[collectionName] is! List) {
          log('Collection $collectionName is not a list');
          return false;
        }
      }

      return true;
    } catch (e) {
      log('Error validating backup structure: $e');
      return false;
    }
  }

  /// Compare two backup data objects for equality
  static bool _compareBackupData(Map<String, dynamic> original, Map<String, dynamic> parsed) {
    try {
      // Compare metadata
      final originalMetadata = original['metadata'] as Map<String, dynamic>;
      final parsedMetadata = parsed['metadata'] as Map<String, dynamic>;
      
      if (!_compareMaps(originalMetadata, parsedMetadata)) {
        log('Metadata comparison failed');
        return false;
      }

      // Compare collections
      final originalCollections = original['collections'] as Map<String, dynamic>;
      final parsedCollections = parsed['collections'] as Map<String, dynamic>;
      
      if (originalCollections.keys.length != parsedCollections.keys.length) {
        log('Collection count mismatch');
        return false;
      }

      for (String collectionName in originalCollections.keys) {
        if (!parsedCollections.containsKey(collectionName)) {
          log('Missing collection: $collectionName');
          return false;
        }

        final originalList = originalCollections[collectionName] as List;
        final parsedList = parsedCollections[collectionName] as List;
        
        if (originalList.length != parsedList.length) {
          log('Document count mismatch in collection: $collectionName');
          return false;
        }
      }

      return true;
    } catch (e) {
      log('Error comparing backup data: $e');
      return false;
    }
  }

  /// Compare two maps for equality
  static bool _compareMaps(Map<String, dynamic> map1, Map<String, dynamic> map2) {
    if (map1.keys.length != map2.keys.length) return false;
    
    for (String key in map1.keys) {
      if (!map2.containsKey(key)) return false;
      if (map1[key] != map2[key]) return false;
    }
    
    return true;
  }

  /// Test error scenarios
  static Future<bool> testErrorScenarios() async {
    try {
      log('Starting error scenario tests...');

      // Test 1: Invalid JSON parsing
      log('Test 1: Invalid JSON parsing...');
      final invalidJsonResult = _service.parseBackupJson('invalid json');
      if (invalidJsonResult.isRight()) {
        log('Invalid JSON test failed - should have returned error');
        return false;
      }
      log('Invalid JSON test passed');

      // Test 2: Invalid backup structure
      log('Test 2: Invalid backup structure...');
      final invalidStructure = {'invalid': 'structure'};
      final invalidJsonString = jsonEncode(invalidStructure);
      final invalidStructureResult = _service.parseBackupJson(invalidJsonString);
      
      if (invalidStructureResult.isRight()) {
        // This should pass parsing but fail validation during restore
        final restoreResult = await _service.restoreFromBackup(
          backupData: invalidStructure,
          overwriteExisting: false,
        );
        if (restoreResult.isRight()) {
          log('Invalid structure test failed - should have returned error');
          return false;
        }
      }
      log('Invalid structure test passed');

      log('Error scenario tests completed successfully!');
      return true;
    } catch (e) {
      log('Error scenario tests failed with error: $e');
      return false;
    }
  }

  /// Generate test filename
  static String generateTestFilename() {
    return _service.generateBackupFilename('TestCompany');
  }

  /// Test filename generation
  static bool testFilenameGeneration() {
    try {
      final filename = generateTestFilename();
      
      // Check filename format
      if (!filename.contains('TestCompany_Backup_')) {
        log('Filename format test failed');
        return false;
      }
      
      if (!filename.endsWith('.json')) {
        log('Filename extension test failed');
        return false;
      }
      
      log('Filename generation test passed: $filename');
      return true;
    } catch (e) {
      log('Filename generation test failed with error: $e');
      return false;
    }
  }

  /// Run all tests
  static Future<bool> runAllTests() async {
    log('=== Starting Backup/Restore System Tests ===');
    
    bool allTestsPassed = true;
    
    // Test filename generation
    if (!testFilenameGeneration()) {
      allTestsPassed = false;
    }
    
    // Test error scenarios
    if (!await testErrorScenarios()) {
      allTestsPassed = false;
    }
    
    // Test backup/restore cycle (only if we have data)
    try {
      if (!await testBackupRestoreCycle()) {
        log('Backup/restore cycle test failed (this may be expected if no data exists)');
        // Don't fail overall tests for this as it depends on having data
      }
    } catch (e) {
      log('Backup/restore cycle test skipped due to error: $e');
    }
    
    log('=== Backup/Restore System Tests Complete ===');
    log('Overall result: ${allTestsPassed ? 'PASSED' : 'FAILED'}');
    
    return allTestsPassed;
  }
}
