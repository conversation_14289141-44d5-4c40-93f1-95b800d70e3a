import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:logestics/features/finance/loans/presentation/controllers/loan_requests_controller.dart';
import 'package:logestics/features/home/<USER>/theme.dart';

class LoanRequestsFilters extends StatelessWidget {
  final LoanRequestsController controller;

  const LoanRequestsFilters({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    final notifier = Provider.of<ColorNotifier>(context, listen: true);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: notifier.getBgColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            offset: const Offset(0, 2),
            blurRadius: 4,
          ),
        ],
      ),
      child: Column(
        children: [
          // First row: Status and Date filters
          Row(
            children: [
              // Status Filter
              Expanded(
                flex: 2,
                child: _buildStatusFilter(notifier),
              ),
              const SizedBox(width: 16),

              // Date Filter
              Expanded(
                flex: 3,
                child: _buildDateFilter(notifier),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Second row: Search and Clear filters
          Row(
            children: [
              // Search
              Expanded(
                flex: 4,
                child: _buildSearchField(notifier),
              ),
              const SizedBox(width: 16),

              // Clear Filters Button
              _buildClearFiltersButton(notifier),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusFilter(ColorNotifier notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Status',
          style: TextStyle(
            color: notifier.text,
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        Obx(() => Container(
              decoration: BoxDecoration(
                border: Border.all(color: notifier.text.withValues(alpha: 0.3)),
                borderRadius: BorderRadius.circular(8),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  value: controller.selectedStatus.value,
                  isExpanded: true,
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  items: ['All', 'Pending', 'Approved', 'Rejected', 'Cancelled']
                      .map((status) => DropdownMenuItem(
                            value: status,
                            child: Text(
                              status,
                              style: TextStyle(color: notifier.text),
                            ),
                          ))
                      .toList(),
                  onChanged: (value) {
                    if (value != null) {
                      controller.filterByStatus(value);
                    }
                  },
                ),
              ),
            )),
      ],
    );
  }

  Widget _buildDateFilter(ColorNotifier notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Date Range',
          style: TextStyle(
            color: notifier.text,
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildDateFilterChip('Today', 'today', notifier),
            _buildDateFilterChip('This Week', 'week', notifier),
            _buildDateFilterChip('This Month', 'month', notifier),
            _buildDateFilterChip('Last 30 Days', 'last30days', notifier),
            _buildDateFilterChip('All', 'all', notifier),
          ],
        ),
      ],
    );
  }

  Widget _buildDateFilterChip(
      String label, String value, ColorNotifier notifier) {
    return Obx(() {
      final isSelected = controller.dateFilterType.value == value;
      return FilterChip(
        label: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : notifier.text,
            fontSize: 12,
          ),
        ),
        selected: isSelected,
        onSelected: (selected) {
          if (selected) {
            controller.filterByDateRange(value);
          }
        },
        selectedColor: const Color(0xFF0f7bf4),
        backgroundColor: notifier.getBgColor,
        side: BorderSide(
          color: isSelected
              ? const Color(0xFF0f7bf4)
              : notifier.text.withValues(alpha: 0.3),
        ),
      );
    });
  }

  Widget _buildSearchField(ColorNotifier notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Search',
          style: TextStyle(
            color: notifier.text,
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller.searchController,
          decoration: InputDecoration(
            hintText: 'Search by name, amount, status...',
            hintStyle: TextStyle(color: notifier.text.withValues(alpha: 0.5)),
            prefixIcon:
                Icon(Icons.search, color: notifier.text.withValues(alpha: 0.7)),
            suffixIcon: Obx(() => controller.searchQuery.value.isNotEmpty
                ? IconButton(
                    icon: Icon(Icons.clear,
                        color: notifier.text.withValues(alpha: 0.7)),
                    onPressed: () {
                      controller.searchController.clear();
                    },
                  )
                : const SizedBox.shrink()),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide:
                  BorderSide(color: notifier.text.withValues(alpha: 0.3)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide:
                  BorderSide(color: notifier.text.withValues(alpha: 0.3)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF0f7bf4)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
          ),
          style: TextStyle(color: notifier.text),
        ),
      ],
    );
  }

  Widget _buildClearFiltersButton(ColorNotifier notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Actions',
          style: TextStyle(
            color: notifier.text,
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        ElevatedButton.icon(
          onPressed: () => controller.clearFilters(),
          icon: const Icon(Icons.clear_all, size: 18),
          label: const Text('Clear Filters'),
          style: ElevatedButton.styleFrom(
            backgroundColor: notifier.getBgColor,
            foregroundColor: notifier.text,
            side: BorderSide(color: notifier.text.withValues(alpha: 0.3)),
            elevation: 0,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
        ),
      ],
    );
  }
}

class LoanRequestsStats extends StatelessWidget {
  final LoanRequestsController controller;

  const LoanRequestsStats({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    final notifier = Provider.of<ColorNotifier>(context, listen: true);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: notifier.getBgColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: notifier.text.withValues(alpha: 0.1)),
      ),
      child: Obx(() => Row(
            children: [
              _buildStatCard(
                'Total Incoming',
                controller.totalIncomingRequests.toString(),
                Icons.call_received,
                Colors.blue,
                notifier,
              ),
              const SizedBox(width: 16),
              _buildStatCard(
                'Pending Incoming',
                controller.pendingIncomingRequests.toString(),
                Icons.pending_actions,
                Colors.orange,
                notifier,
              ),
              const SizedBox(width: 16),
              _buildStatCard(
                'Total Outgoing',
                controller.totalOutgoingRequests.toString(),
                Icons.call_made,
                Colors.green,
                notifier,
              ),
              const SizedBox(width: 16),
              _buildStatCard(
                'Pending Outgoing',
                controller.pendingOutgoingRequests.toString(),
                Icons.schedule,
                Colors.amber,
                notifier,
              ),
            ],
          )),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
    ColorNotifier notifier,
  ) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: notifier.text,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: notifier.text.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
