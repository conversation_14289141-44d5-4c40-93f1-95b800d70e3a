# Journal Entry Firebase Indexing Fix

## Problem Summary
The Journal Entries screen is failing to load due to missing Firebase Firestore composite indexes. The error occurs when the system tries to execute compound queries that combine multiple fields.

## Identified Problematic Queries

### 1. Basic Journal Entry Loading
**Query**: `uid` + `orderBy(entryDate, descending: true)`
**Location**: `journal_entry_firebase_service.dart:105-106`
```dart
.where('uid', isEqualTo: _uid)
.orderBy('entryDate', descending: true)
```

### 2. Date Range Filtering
**Query**: `uid` + `entryDate` range + `orderBy(entryDate)`
**Location**: `journal_entry_firebase_service.dart:150-155`
```dart
.where('uid', isEqualTo: _uid)
.where('entryDate', isGreaterThanOrEqualTo: startDate.millisecondsSinceEpoch)
.where('entryDate', isLessThanOrEqualTo: endDate.millisecondsSinceEpoch)
.orderBy('entryDate', descending: true)
```

### 3. Account-Specific Journal Entries with Filters
**Query**: `uid` + `entryDate` range + `status` filter + `orderBy(entryDate)`
**Location**: `journal_entry_firebase_service.dart:420-440`
```dart
.where('uid', isEqualTo: uid)
.where('entryDate', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
.where('entryDate', isLessThanOrEqualTo: Timestamp.fromDate(endDate))
.where('status', whereIn: statusStrings)
.orderBy('entryDate', descending: true)
```

### 4. Source Transaction Queries
**Query**: `uid` + `sourceTransactionId` + `sourceTransactionType`
**Location**: `general_ledger_firebase_service.dart:318-326`
```dart
.where('uid', isEqualTo: uid)
.where('sourceTransactionId', isEqualTo: referenceId)
.where('sourceTransactionType', isEqualTo: referenceType)
```

### 5. Journal Entry Lines by Account
**Query**: `accountId` + `orderBy(createdAt)`
**Location**: `journal_entry_firebase_service.dart:370-371`
```dart
.where('accountId', isEqualTo: accountId)
.orderBy('createdAt', descending: true)
```

### 6. Journal Entry Lines by Journal Entry
**Query**: `journalEntryId` + `orderBy(createdAt)`
**Location**: `journal_entry_firebase_service.dart:117-118`
```dart
.where('journalEntryId', isEqualTo: doc.id)
.orderBy('createdAt')
```

## Required Firebase Indexes

### Collection: `journal_entries`

#### Index 1: Basic Journal Entries
```
Collection ID: journal_entries
Fields:
  - uid (Ascending)
  - entryDate (Descending)
```

#### Index 2: Date Range Queries
```
Collection ID: journal_entries
Fields:
  - uid (Ascending)
  - entryDate (Ascending)
```

#### Index 3: Status + Date Filtering
```
Collection ID: journal_entries
Fields:
  - uid (Ascending)
  - status (Ascending)
  - entryDate (Descending)
```

#### Index 4: Source Transaction Queries
```
Collection ID: journal_entries
Fields:
  - uid (Ascending)
  - sourceTransactionId (Ascending)
  - sourceTransactionType (Ascending)
```

#### Index 5: Complex Account Filtering
```
Collection ID: journal_entries
Fields:
  - uid (Ascending)
  - entryDate (Ascending)
  - status (Ascending)
```

### Collection: `journal_entry_lines`

#### Index 6: Lines by Account
```
Collection ID: journal_entry_lines
Fields:
  - accountId (Ascending)
  - createdAt (Descending)
```

#### Index 7: Lines by Journal Entry
```
Collection ID: journal_entry_lines
Fields:
  - journalEntryId (Ascending)
  - createdAt (Ascending)
```

## Firebase Console Commands

### Step 1: Access Firebase Console
1. Go to: https://console.firebase.google.com/project/logistics-abedb/firestore/indexes
2. Click **Create Index** for each index below

### Step 2: Create Journal Entry Indexes

#### Index 1: Basic Journal Entries
1. Collection ID: `journal_entries`
2. Add fields:
   - Field: `uid`, Order: `Ascending`
   - Field: `entryDate`, Order: `Descending`
3. Click **Create Index**

#### Index 2: Date Range Queries
1. Collection ID: `journal_entries`
2. Add fields:
   - Field: `uid`, Order: `Ascending`
   - Field: `entryDate`, Order: `Ascending`
3. Click **Create Index**

#### Index 3: Status + Date Filtering
1. Collection ID: `journal_entries`
2. Add fields:
   - Field: `uid`, Order: `Ascending`
   - Field: `status`, Order: `Ascending`
   - Field: `entryDate`, Order: `Descending`
3. Click **Create Index**

#### Index 4: Source Transaction Queries
1. Collection ID: `journal_entries`
2. Add fields:
   - Field: `uid`, Order: `Ascending`
   - Field: `sourceTransactionId`, Order: `Ascending`
   - Field: `sourceTransactionType`, Order: `Ascending`
3. Click **Create Index**

#### Index 5: Complex Account Filtering
1. Collection ID: `journal_entries`
2. Add fields:
   - Field: `uid`, Order: `Ascending`
   - Field: `entryDate`, Order: `Ascending`
   - Field: `status`, Order: `Ascending`
3. Click **Create Index**

### Step 3: Create Journal Entry Lines Indexes

#### Index 6: Lines by Account
1. Collection ID: `journal_entry_lines`
2. Add fields:
   - Field: `accountId`, Order: `Ascending`
   - Field: `createdAt`, Order: `Descending`
3. Click **Create Index**

#### Index 7: Lines by Journal Entry
1. Collection ID: `journal_entry_lines`
2. Add fields:
   - Field: `journalEntryId`, Order: `Ascending`
   - Field: `createdAt`, Order: `Ascending`
3. Click **Create Index**

## Expected Resolution

After creating these indexes:
✅ Journal Entries screen will load without errors
✅ Date range filtering will work properly
✅ Status-based filtering will function correctly
✅ Account-specific journal entry queries will execute successfully
✅ Real-time streams will work without fallback queries

## Testing Steps

1. **Create all indexes** in Firebase Console
2. **Wait for indexes to build** (status: Building → Enabled)
3. **Navigate to Journal Entries** screen in the app
4. **Test basic loading** - verify entries display
5. **Test date filtering** - apply date range filters
6. **Test status filtering** - filter by entry status
7. **Test account views** - view journal entries for specific accounts

## Monitoring

- Index build time: 5-15 minutes depending on data size
- All indexes must show "Enabled" status before testing
- Check browser console for any remaining indexing errors
