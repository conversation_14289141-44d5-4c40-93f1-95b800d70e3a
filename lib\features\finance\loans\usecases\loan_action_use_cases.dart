import 'package:either_dart/either.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/features/finance/loans/repositories/loan_repository.dart';

class ApproveLoanUseCase {
  final LoanRepository _loanRepository;

  ApproveLoanUseCase(this._loanRepository);

  Future<Either<FailureObj, SuccessObj>> execute(
      String loanId, String fromAccountId) {
    return _loanRepository.approveLoanRequest(loanId, fromAccountId);
  }
}

class RejectLoanUseCase {
  final LoanRepository _loanRepository;

  RejectLoanUseCase(this._loanRepository);

  Future<Either<FailureObj, SuccessObj>> execute(String loanId, String reason) {
    return _loanRepository.rejectLoanRequest(loanId, reason);
  }
}

class RepayLoanUseCase {
  final LoanRepository _loanRepository;

  RepayLoanUseCase(this._loanRepository);

  Future<Either<FailureObj, SuccessObj>> execute(
      String loanId, String fromAccountId) {
    return _loanRepository.repayLoan(loanId, fromAccountId);
  }
}
