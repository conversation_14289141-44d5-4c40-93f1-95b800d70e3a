import 'dart:developer';
import '../../models/finance/expense_model.dart';
import '../../models/finance/bill_model.dart';
import '../../models/voucher_model.dart';
import '../../models/finance/loan_model.dart';
import '../../models/finance/journal_entry_model.dart';
import '../../firebase_service/finance/expense_firebase_service.dart';
import '../../firebase_service/finance/bill_firebase_service.dart';
import '../../firebase_service/voucher/voucher_crud_firebase_service.dart';
import '../../firebase_service/finance/loan_firebase_service.dart';
import '../../firebase_service/accounting/general_ledger_firebase_service.dart';
import 'expense_journal_integration_service.dart';
import 'bill_journal_integration_service.dart';
import 'voucher_journal_integration_service.dart';
import 'loan_journal_integration_service.dart';

/// Service for synchronizing existing transactions with journal entries
/// Handles retroactive journal entry creation and maintains referential integrity
class TransactionSynchronizationService {
  final ExpenseFirebaseService _expenseService;
  final BillFirebaseService _billService;
  final VoucherCrudFirebaseService _voucherService;
  final LoanFirebaseService _loanService;
  final GeneralLedgerFirebaseService _generalLedgerService;
  final ExpenseJournalIntegrationService _expenseIntegrationService;
  final BillJournalIntegrationService _billIntegrationService;
  final VoucherJournalIntegrationService _voucherIntegrationService;
  final LoanJournalIntegrationService _loanIntegrationService;

  TransactionSynchronizationService(
    this._expenseService,
    this._billService,
    this._voucherService,
    this._loanService,
    this._generalLedgerService,
    this._expenseIntegrationService,
    this._billIntegrationService,
    this._voucherIntegrationService,
    this._loanIntegrationService,
  );

  /// Synchronize all existing transactions with journal entries
  Future<SynchronizationResult> synchronizeAllTransactions(String uid) async {
    log('Starting full transaction synchronization for user: $uid');

    final result = SynchronizationResult();

    try {
      // Synchronize expenses
      final expenseResult = await synchronizeExpenses(uid);
      result.addExpenseResult(expenseResult);

      // Synchronize bills
      final billResult = await synchronizeBills(uid);
      result.addBillResult(billResult);

      // Synchronize vouchers
      final voucherResult = await synchronizeVouchers(uid);
      result.addVoucherResult(voucherResult);

      // Synchronize loans
      final loanResult = await synchronizeLoans(uid);
      result.addLoanResult(loanResult);

      log('Full synchronization completed: ${result.totalProcessed} transactions processed');
      return result;
    } catch (e) {
      log('Error during full synchronization: $e');
      result.addError('Full synchronization failed: $e');
      return result;
    }
  }

  /// Synchronize existing expenses with journal entries
  Future<TransactionTypeSyncResult> synchronizeExpenses(String uid) async {
    log('Synchronizing expenses for user: $uid');

    try {
      final expenses = await _expenseService.getExpenses();

      if (expenses.isEmpty) {
        log('No expenses found for synchronization');
        return TransactionTypeSyncResult(
          transactionType: 'expense',
          totalFetched: 0,
          successCount: 0,
          failureCount: 0,
          skippedCount: 0,
          errors: [],
        );
      }

      final result = TransactionTypeSyncResult(transactionType: 'expense');
      result.totalFetched = expenses.length;

      for (final expense in expenses) {
        try {
          // Check if journal entries already exist
          final hasEntries =
              await _expenseIntegrationService.hasExistingJournalEntries(
            expense.id,
            uid,
          );

          if (hasEntries) {
            result.skippedCount++;
            continue;
          }

          // Process the expense
          final success = await _expenseIntegrationService
              .processExpenseTransaction(expense);

          if (success) {
            result.successCount++;
            log('Successfully synchronized expense: ${expense.id}');
          } else {
            result.failureCount++;
            result.errors.add('Failed to process expense: ${expense.id}');
          }
        } catch (e) {
          result.failureCount++;
          result.errors.add('Error processing expense ${expense.id}: $e');
        }
      }

      log('Expense synchronization completed: ${result.successCount}/${result.totalFetched} successful');
      return result;
    } catch (e) {
      log('Error synchronizing expenses: $e');
      return TransactionTypeSyncResult(
        transactionType: 'expense',
        totalFetched: 0,
        successCount: 0,
        failureCount: 0,
        skippedCount: 0,
        errors: ['Error synchronizing expenses: $e'],
      );
    }
  }

  /// Synchronize existing bills with journal entries
  Future<TransactionTypeSyncResult> synchronizeBills(String uid) async {
    log('Synchronizing bills for user: $uid');

    try {
      final bills = await _billService.getBills(uid: uid);

      if (bills.isEmpty) {
        log('No bills found for synchronization');
        return TransactionTypeSyncResult(
          transactionType: 'bill',
          totalFetched: 0,
          successCount: 0,
          failureCount: 0,
          skippedCount: 0,
          errors: [],
        );
      }

      final result = TransactionTypeSyncResult(transactionType: 'bill');
      result.totalFetched = bills.length;

      for (final bill in bills) {
        try {
          // Check if journal entries already exist
          final hasEntries =
              await _billIntegrationService.hasExistingJournalEntries(
            bill.billId,
            uid,
          );

          if (hasEntries) {
            result.skippedCount++;
            continue;
          }

          // Process the bill based on its status
          bool success = false;
          if (bill.billStatus == 'received' ||
              bill.billStatus == 'pending_billing') {
            success =
                await _billIntegrationService.processBillTransaction(bill);
          } else if (bill.billStatus == 'payment_received') {
            // Process both creation and payment
            success =
                await _billIntegrationService.processBillTransaction(bill);
            if (success) {
              success = await _billIntegrationService.processBillStatusUpdate(
                bill,
                'pending_billing',
                'payment_received',
              );
            }
          }

          if (success) {
            result.successCount++;
            log('Successfully synchronized bill: ${bill.billId}');
          } else {
            result.failureCount++;
            result.errors.add('Failed to process bill: ${bill.billId}');
          }
        } catch (e) {
          result.failureCount++;
          result.errors.add('Error processing bill ${bill.billId}: $e');
        }
      }

      log('Bill synchronization completed: ${result.successCount}/${result.totalFetched} successful');
      return result;
    } catch (e) {
      log('Error synchronizing bills: $e');
      return TransactionTypeSyncResult(
        transactionType: 'bill',
        totalFetched: 0,
        successCount: 0,
        failureCount: 0,
        skippedCount: 0,
        errors: ['Error synchronizing bills: $e'],
      );
    }
  }

  /// Synchronize existing vouchers with journal entries
  Future<TransactionTypeSyncResult> synchronizeVouchers(String uid) async {
    log('Synchronizing vouchers for user: $uid');

    try {
      final vouchersData =
          await _voucherService.getVouchersForCompany(uid: uid);

      if (vouchersData.isEmpty) {
        log('No vouchers found for synchronization');
        return TransactionTypeSyncResult(
          transactionType: 'voucher',
          totalFetched: 0,
          successCount: 0,
          failureCount: 0,
          skippedCount: 0,
          errors: [],
        );
      }

      // Convert Map data to VoucherModel objects
      final vouchers =
          vouchersData.map((data) => VoucherModel.fromJson(data)).toList();

      final result = TransactionTypeSyncResult(transactionType: 'voucher');
      result.totalFetched = vouchers.length;

      for (final voucher in vouchers) {
        try {
          // Check if journal entries already exist
          final hasEntries =
              await _voucherIntegrationService.hasExistingJournalEntries(
            voucher.voucherNumber,
            uid,
          );

          if (hasEntries) {
            result.skippedCount++;
            continue;
          }

          // Process the voucher
          final success = await _voucherIntegrationService
              .processVoucherTransaction(voucher, uid);

          if (success) {
            result.successCount++;
            log('Successfully synchronized voucher: ${voucher.voucherNumber}');
          } else {
            result.failureCount++;
            result.errors
                .add('Failed to process voucher: ${voucher.voucherNumber}');
          }
        } catch (e) {
          result.failureCount++;
          result.errors
              .add('Error processing voucher ${voucher.voucherNumber}: $e');
        }
      }

      log('Voucher synchronization completed: ${result.successCount}/${result.totalFetched} successful');
      return result;
    } catch (e) {
      log('Error synchronizing vouchers: $e');
      return TransactionTypeSyncResult(
        transactionType: 'voucher',
        totalFetched: 0,
        successCount: 0,
        failureCount: 0,
        skippedCount: 0,
        errors: ['Error synchronizing vouchers: $e'],
      );
    }
  }

  /// Synchronize existing loans with journal entries
  Future<TransactionTypeSyncResult> synchronizeLoans(String uid) async {
    log('Synchronizing loans for user: $uid');

    try {
      final loansResult = await _loanService.getLoanHistory();

      return loansResult.fold(
        (failure) {
          log('Failed to fetch loans: ${failure.message}');
          return TransactionTypeSyncResult(
            transactionType: 'loan',
            totalFetched: 0,
            successCount: 0,
            failureCount: 0,
            skippedCount: 0,
            errors: ['Failed to fetch loans: ${failure.message}'],
          );
        },
        (loans) async {
          final result = TransactionTypeSyncResult(transactionType: 'loan');
          result.totalFetched = loans.length;

          for (final loan in loans) {
            try {
              // Check if journal entries already exist
              final hasEntries =
                  await _loanIntegrationService.hasExistingJournalEntries(
                loan.id,
                uid,
              );

              if (hasEntries) {
                result.skippedCount++;
                continue;
              }

              // Process the loan based on its status
              bool success = false;
              if (loan.status == 'approved') {
                success = await _loanIntegrationService.processLoanDisbursement(
                    loan, uid);
              } else if (loan.status == 'repaid') {
                // Process both disbursement and repayment
                success = await _loanIntegrationService.processLoanDisbursement(
                    loan, uid);
                if (success) {
                  success = await _loanIntegrationService.processLoanRepayment(
                      loan, uid);
                }
              }

              if (success) {
                result.successCount++;
                log('Successfully synchronized loan: ${loan.id}');
              } else {
                result.failureCount++;
                result.errors.add('Failed to process loan: ${loan.id}');
              }
            } catch (e) {
              result.failureCount++;
              result.errors.add('Error processing loan ${loan.id}: $e');
            }
          }

          log('Loan synchronization completed: ${result.successCount}/${result.totalFetched} successful');
          return result;
        },
      );
    } catch (e) {
      log('Error synchronizing loans: $e');
      return TransactionTypeSyncResult(
        transactionType: 'loan',
        totalFetched: 0,
        successCount: 0,
        failureCount: 0,
        skippedCount: 0,
        errors: ['Error synchronizing loans: $e'],
      );
    }
  }

  /// Get synchronization status for a specific transaction type
  Future<SyncStatusResult> getSynchronizationStatus(
      String transactionType, String uid) async {
    try {
      switch (transactionType.toLowerCase()) {
        case 'expense':
          return await _getExpenseSyncStatus(uid);
        case 'bill':
          return await _getBillSyncStatus(uid);
        case 'voucher':
          return await _getVoucherSyncStatus(uid);
        case 'loan':
          return await _getLoanSyncStatus(uid);
        default:
          return SyncStatusResult(
            transactionType: transactionType,
            totalTransactions: 0,
            syncedTransactions: 0,
            unsyncedTransactions: 0,
            syncPercentage: 0.0,
          );
      }
    } catch (e) {
      log('Error getting synchronization status for $transactionType: $e');
      return SyncStatusResult(
        transactionType: transactionType,
        totalTransactions: 0,
        syncedTransactions: 0,
        unsyncedTransactions: 0,
        syncPercentage: 0.0,
      );
    }
  }

  Future<SyncStatusResult> _getExpenseSyncStatus(String uid) async {
    try {
      final expenses = await _expenseService.getExpenses();

      int syncedCount = 0;
      for (final expense in expenses) {
        final hasEntries = await _expenseIntegrationService
            .hasExistingJournalEntries(expense.id, uid);
        if (hasEntries) syncedCount++;
      }

      return SyncStatusResult(
        transactionType: 'expense',
        totalTransactions: expenses.length,
        syncedTransactions: syncedCount,
        unsyncedTransactions: expenses.length - syncedCount,
        syncPercentage:
            expenses.isNotEmpty ? (syncedCount / expenses.length) * 100 : 0.0,
      );
    } catch (e) {
      log('Error getting expense sync status: $e');
      return SyncStatusResult(
        transactionType: 'expense',
        totalTransactions: 0,
        syncedTransactions: 0,
        unsyncedTransactions: 0,
        syncPercentage: 0.0,
      );
    }
  }

  Future<SyncStatusResult> _getBillSyncStatus(String uid) async {
    try {
      final bills = await _billService.getBills(uid: uid);

      int syncedCount = 0;
      for (final bill in bills) {
        final hasEntries = await _billIntegrationService
            .hasExistingJournalEntries(bill.billId, uid);
        if (hasEntries) syncedCount++;
      }

      return SyncStatusResult(
        transactionType: 'bill',
        totalTransactions: bills.length,
        syncedTransactions: syncedCount,
        unsyncedTransactions: bills.length - syncedCount,
        syncPercentage:
            bills.isNotEmpty ? (syncedCount / bills.length) * 100 : 0.0,
      );
    } catch (e) {
      log('Error getting bill sync status: $e');
      return SyncStatusResult(
        transactionType: 'bill',
        totalTransactions: 0,
        syncedTransactions: 0,
        unsyncedTransactions: 0,
        syncPercentage: 0.0,
      );
    }
  }

  Future<SyncStatusResult> _getVoucherSyncStatus(String uid) async {
    try {
      final vouchersData =
          await _voucherService.getVouchersForCompany(uid: uid);

      int syncedCount = 0;
      for (final voucherData in vouchersData) {
        final voucher = VoucherModel.fromJson(voucherData);
        final hasEntries = await _voucherIntegrationService
            .hasExistingJournalEntries(voucher.voucherNumber, uid);
        if (hasEntries) syncedCount++;
      }

      return SyncStatusResult(
        transactionType: 'voucher',
        totalTransactions: vouchersData.length,
        syncedTransactions: syncedCount,
        unsyncedTransactions: vouchersData.length - syncedCount,
        syncPercentage: vouchersData.isNotEmpty
            ? (syncedCount / vouchersData.length) * 100
            : 0.0,
      );
    } catch (e) {
      log('Error getting voucher sync status: $e');
      return SyncStatusResult(
        transactionType: 'voucher',
        totalTransactions: 0,
        syncedTransactions: 0,
        unsyncedTransactions: 0,
        syncPercentage: 0.0,
      );
    }
  }

  Future<SyncStatusResult> _getLoanSyncStatus(String uid) async {
    try {
      final loansResult = await _loanService.getLoanHistory();
      return loansResult.fold(
        (failure) => SyncStatusResult(
          transactionType: 'loan',
          totalTransactions: 0,
          syncedTransactions: 0,
          unsyncedTransactions: 0,
          syncPercentage: 0.0,
        ),
        (loans) async {
          int syncedCount = 0;
          for (final loan in loans) {
            final hasEntries = await _loanIntegrationService
                .hasExistingJournalEntries(loan.id, uid);
            if (hasEntries) syncedCount++;
          }

          return SyncStatusResult(
            transactionType: 'loan',
            totalTransactions: loans.length,
            syncedTransactions: syncedCount,
            unsyncedTransactions: loans.length - syncedCount,
            syncPercentage:
                loans.isNotEmpty ? (syncedCount / loans.length) * 100 : 0.0,
          );
        },
      );
    } catch (e) {
      log('Error getting loan sync status: $e');
      return SyncStatusResult(
        transactionType: 'loan',
        totalTransactions: 0,
        syncedTransactions: 0,
        unsyncedTransactions: 0,
        syncPercentage: 0.0,
      );
    }
  }
}

/// Result classes for synchronization operations
class SynchronizationResult {
  final List<TransactionTypeSyncResult> results = [];
  final List<String> errors = [];

  void addExpenseResult(TransactionTypeSyncResult result) =>
      results.add(result);
  void addBillResult(TransactionTypeSyncResult result) => results.add(result);
  void addVoucherResult(TransactionTypeSyncResult result) =>
      results.add(result);
  void addLoanResult(TransactionTypeSyncResult result) => results.add(result);
  void addError(String error) => errors.add(error);

  int get totalProcessed =>
      results.fold(0, (sum, result) => sum + result.totalFetched);
  int get totalSuccessful =>
      results.fold(0, (sum, result) => sum + result.successCount);
  int get totalFailed =>
      results.fold(0, (sum, result) => sum + result.failureCount);
  int get totalSkipped =>
      results.fold(0, (sum, result) => sum + result.skippedCount);

  double get successRate =>
      totalProcessed > 0 ? (totalSuccessful / totalProcessed) * 100 : 0.0;
  bool get hasErrors =>
      errors.isNotEmpty || results.any((r) => r.errors.isNotEmpty);
  bool get isSuccessful => !hasErrors && totalFailed == 0;
}

class TransactionTypeSyncResult {
  final String transactionType;
  int totalFetched = 0;
  int successCount = 0;
  int failureCount = 0;
  int skippedCount = 0;
  final List<String> errors = [];

  TransactionTypeSyncResult({
    required this.transactionType,
    this.totalFetched = 0,
    this.successCount = 0,
    this.failureCount = 0,
    this.skippedCount = 0,
    List<String>? errors,
  }) {
    if (errors != null) this.errors.addAll(errors);
  }

  double get successRate =>
      totalFetched > 0 ? (successCount / totalFetched) * 100 : 0.0;
  bool get hasErrors => errors.isNotEmpty;
  bool get isSuccessful => !hasErrors && failureCount == 0;
}

class SyncStatusResult {
  final String transactionType;
  final int totalTransactions;
  final int syncedTransactions;
  final int unsyncedTransactions;
  final double syncPercentage;

  SyncStatusResult({
    required this.transactionType,
    required this.totalTransactions,
    required this.syncedTransactions,
    required this.unsyncedTransactions,
    required this.syncPercentage,
  });

  bool get isFullySynced => unsyncedTransactions == 0 && totalTransactions > 0;
  bool get hasUnsyncedTransactions => unsyncedTransactions > 0;
}
