import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/models/finance/account_model.dart';
import 'package:logestics/features/finance/accounts/repositories/account_repository.dart';
import 'package:uuid/uuid.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/core/utils/mixins/pagination_mixin.dart';
import 'package:logestics/core/utils/mixins/auto_refresh_mixin.dart';

class AccountController extends GetxController
    with PaginationMixin, AutoRefreshMixin {
  final AccountRepository repository;

  // Form controllers
  final nameController = TextEditingController();
  final initialBalanceController = TextEditingController();
  final accountNumberController = TextEditingController();
  final branchCodeController = TextEditingController();
  final branchAddressController = TextEditingController();

  // Observable variables
  final accounts = <AccountModel>[].obs;
  final isLoading = false.obs;
  final isDeleting = false.obs;
  final isDrawerOpen = false.obs;
  final searchQuery = ''.obs;
  final filteredAccounts = <AccountModel>[].obs;
  final hasError = false.obs;
  final errorMessage = ''.obs;

  final searchController = TextEditingController();
  final formKey = GlobalKey<FormState>();

  // Get current user's UID
  String get _uid => FirebaseAuth.instance.currentUser?.uid ?? '';

  AccountController({required this.repository});

  @override
  void onInit() {
    super.onInit();
    searchController.addListener(_onSearchChanged);
  }

  /// Implementation of AutoRefreshMixin.refreshData
  @override
  Future<void> refreshData() async {
    await loadAccounts();
  }

  Future<void> loadAccounts() async {
    if (isLoading.value) return;

    try {
      isLoading.value = true;
      hasError.value = false;
      errorMessage.value = '';

      final result = await repository.getAccounts();
      result.fold(
        (failure) {
          log('Failed to fetch accounts: ${failure.message}');
          hasError.value = true;
          errorMessage.value = failure.message;
          SnackbarUtils.showError(
              AppStrings.errorS, 'Failed to load accounts: ${failure.message}');
        },
        (accountsList) {
          accounts.value = accountsList;
          _filterAccounts();
        },
      );
    } catch (e) {
      log('Error loading accounts: $e');
      hasError.value = true;
      errorMessage.value = e.toString();
      SnackbarUtils.showError(AppStrings.errorS, 'Failed to load accounts');
    } finally {
      isLoading.value = false;
    }
  }

  /// Force refresh the accounts data
  @override
  Future<void> forceRefresh() async {
    await loadAccounts();
  }

  void _onSearchChanged() {
    searchQuery.value = searchController.text;
    _filterAccounts();
  }

  void _filterAccounts() {
    if (searchQuery.value.isEmpty) {
      filteredAccounts.value = accounts;
    } else {
      final query = searchQuery.value.toLowerCase();
      filteredAccounts.value = accounts.where((account) {
        return account.name.toLowerCase().contains(query) ||
            account.accountNumber.toLowerCase().contains(query) ||
            account.branchCode.toLowerCase().contains(query);
      }).toList();
    }
    setTotalItems(filteredAccounts.length);
  }

  List<AccountModel> get paginatedAccounts => paginateList(filteredAccounts);

  void openDrawer() {
    clearForm();
    isDrawerOpen.value = true;
  }

  void closeDrawer() {
    isDrawerOpen.value = false;
  }

  void clearForm() {
    nameController.clear();
    initialBalanceController.clear();
    accountNumberController.clear();
    branchCodeController.clear();
    branchAddressController.clear();
  }

  String? validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Name is required';
    }
    return null;
  }

  String? validateInitialBalance(String? value) {
    if (value == null || value.isEmpty) {
      return 'Initial balance is required';
    }
    if (double.tryParse(value) == null) {
      return 'Please enter a valid number';
    }
    return null;
  }

  String? validateAccountNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'Account number is required';
    }
    return null;
  }

  String? validateBranchCode(String? value) {
    if (value == null || value.isEmpty) {
      return 'Branch code is required';
    }
    return null;
  }

  Future<void> addAccount() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    if (isLoading.value) {
      return;
    }

    // Check if user is authenticated
    if (_uid.isEmpty) {
      SnackbarUtils.showError(
        AppStrings.errorS,
        'User not authenticated. Please login again.',
      );
      return;
    }

    isLoading.value = true;

    try {
      // Validate all required fields have non-null, non-empty values
      final name = nameController.text.trim();
      final accountNumber = accountNumberController.text.trim();
      final branchCode = branchCodeController.text.trim();
      final branchAddress = branchAddressController.text.trim();
      final balanceText = initialBalanceController.text.trim();

      if (name.isEmpty) {
        throw ArgumentError('Account name cannot be empty');
      }
      if (accountNumber.isEmpty) {
        throw ArgumentError('Account number cannot be empty');
      }
      if (branchCode.isEmpty) {
        throw ArgumentError('Branch code cannot be empty');
      }
      if (balanceText.isEmpty) {
        throw ArgumentError('Initial balance cannot be empty');
      }

      final initialBalance = double.tryParse(balanceText);
      if (initialBalance == null) {
        throw ArgumentError('Initial balance must be a valid number');
      }

      log('Creating account with uid: $_uid, name: $name, accountNumber: $accountNumber, branchCode: $branchCode');

      final accountId = const Uuid().v4();
      if (accountId.isEmpty) {
        throw Exception('Failed to generate account ID');
      }

      log('Generated account ID: $accountId');
      log('About to create AccountModel with:');
      log('  - id: $accountId');
      log('  - name: $name');
      log('  - initialBalance: $initialBalance');
      log('  - accountNumber: $accountNumber');
      log('  - branchCode: $branchCode');
      log('  - branchAddress: $branchAddress');
      log('  - availableBalance: $initialBalance');
      log('  - createdAt: ${DateTime.now()}');
      log('  - uid: $_uid');

      final newAccount = AccountModel(
        id: accountId,
        name: name,
        initialBalance: initialBalance,
        accountNumber: accountNumber,
        branchCode: branchCode,
        branchAddress: branchAddress,
        availableBalance: initialBalance,
        createdAt: DateTime.now(),
        uid: _uid, // Set current user's UID
      );

      log('AccountModel created successfully. Attempting toJson()...');

      final accountJson = newAccount.toJson();
      log('Account model toJson() successful: $accountJson');

      log('About to call repository.createAccount()...');

      final result = await repository.createAccount(newAccount);
      result.fold(
        (failure) => {
          log('Failed to create account: ${failure.message}'),
          SnackbarUtils.showError(AppStrings.error, failure.message),
        },
        (success) {
          log('Account created successfully in repository');
          accounts.add(newAccount);
          _filterAccounts();
          clearForm();
          closeDrawer();
          SnackbarUtils.showSuccess(AppStrings.success, success.message);
          // Force refresh to ensure pagination updates immediately
          _refreshAccountData();
        },
      );
    } catch (e, stackTrace) {
      log('Error adding account: $e');
      log('Stack trace: $stackTrace');
      SnackbarUtils.showError(AppStrings.error, 'Failed to add account: $e');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> deleteAccount(String accountId) async {
    if (isDeleting.value) {
      log('Delete already in progress after dialog, ignoring request');
      return;
    }

    log('Starting account deletion for ID: $accountId');
    isDeleting.value = true;

    try {
      final result = await repository.deleteAccount(accountId);
      result.fold(
        (failure) {
          log('Failed to delete account: ${failure.message}');
          SnackbarUtils.showError(AppStrings.errorS, failure.message);
        },
        (success) {
          log('Successfully deleted account: $accountId');
          accounts.removeWhere((account) => account.id == accountId);
          _filterAccounts();
          SnackbarUtils.showSuccess(AppStrings.success, success.message);
          // Force refresh to ensure pagination updates immediately
          _refreshAccountData();
        },
      );
    } catch (e) {
      log('Error deleting account: $e');
      SnackbarUtils.showError(AppStrings.error, 'Failed to delete account: $e');
    } finally {
      log('Resetting deletion state');
      isDeleting.value = false;
    }
  }

  Future<void> updateAccount(AccountModel account) async {
    if (isLoading.value) {
      return;
    }

    isLoading.value = true;

    try {
      final result = await repository.updateAccount(account);
      result.fold(
        (failure) => {
          log('Failed to update account: ${failure.message}'),
          SnackbarUtils.showError(AppStrings.error, failure.message),
        },
        (success) {
          final index = accounts.indexWhere((a) => a.id == account.id);
          if (index != -1) {
            accounts[index] = account;
            _filterAccounts();
          }
          SnackbarUtils.showSuccess(AppStrings.success, success.message);
          // Force refresh to ensure pagination updates immediately
          _refreshAccountData();
        },
      );
    } catch (e) {
      log('Error updating account: $e');
      SnackbarUtils.showError(AppStrings.error, 'Failed to update account: $e');
    } finally {
      isLoading.value = false;
    }
  }

  void _refreshAccountData() {
    // Immediately refresh the filtered list and pagination
    _filterAccounts();

    // Add a small delay to ensure any Firestore listeners have processed the change
    Future.delayed(const Duration(milliseconds: 500), () {
      // Force refresh the data from Firestore
      loadAccounts();
    });
  }

  @override
  void onClose() {
    nameController.dispose();
    initialBalanceController.dispose();
    accountNumberController.dispose();
    branchCodeController.dispose();
    branchAddressController.dispose();
    searchController.dispose();
    super.onClose();
  }
}
