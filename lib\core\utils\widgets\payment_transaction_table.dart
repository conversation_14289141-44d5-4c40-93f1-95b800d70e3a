import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:logestics/models/payment_transaction_model.dart';

class PaymentTransactionTable extends StatelessWidget {
  final List<PaymentTransactionModel> transactions;
  final double totalFreight;
  final double settledFreight;
  final Function(PaymentTransactionModel) onEdit;
  final Function(PaymentTransactionModel) onDelete;
  final bool Function(PaymentTransactionModel)? canEdit;
  final bool Function(PaymentTransactionModel)? canDelete;

  const PaymentTransactionTable({
    super.key,
    required this.transactions,
    required this.totalFreight,
    required this.settledFreight,
    required this.onEdit,
    required this.onDelete,
    this.canEdit,
    this.canDelete,
  });

  @override
  Widget build(BuildContext context) {
    final pendingAmount = totalFreight - settledFreight;
    final formatter = NumberFormat("#,##0.00", "en_US");

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Payment Summary
        Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.withAlpha(20),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildSummaryItem('Total Freight',
                  'PKR ${formatter.format(totalFreight)}', Colors.blue),
              _buildSummaryItem('Settled Amount',
                  'PKR ${formatter.format(settledFreight)}', Colors.green),
              _buildSummaryItem(
                  'Pending Amount',
                  'PKR ${formatter.format(pendingAmount)}',
                  pendingAmount > 0 ? Colors.red : Colors.green),
            ],
          ),
        ),

        // Table Header
        Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          decoration: BoxDecoration(
            color: Colors.grey.shade200,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(8),
              topRight: Radius.circular(8),
            ),
          ),
          child: Row(
            children: [
              _buildHeaderCell('Sr#', flex: 1),
              _buildHeaderCell('Date', flex: 2),
              _buildHeaderCell('Payment Method', flex: 2),
              _buildHeaderCell('Account/Card', flex: 3),
              _buildHeaderCell('Amount', flex: 2),
              _buildHeaderCell('Status', flex: 2),
              _buildHeaderCell('Actions', flex: 1),
            ],
          ),
        ),

        // Table Body
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(8),
              bottomRight: Radius.circular(8),
            ),
          ),
          child: transactions.isEmpty
              ? Container(
                  padding: const EdgeInsets.all(20),
                  alignment: Alignment.center,
                  child: const Text(
                    'No payment transactions yet',
                    style: TextStyle(
                      color: Colors.grey,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                )
              : ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: transactions.length,
                  separatorBuilder: (context, index) => Divider(
                    height: 1,
                    color: Colors.grey.shade300,
                  ),
                  itemBuilder: (context, index) {
                    final transaction = transactions[index];
                    return _buildTransactionRow(
                      context,
                      index + 1,
                      transaction,
                    );
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildSummaryItem(String label, String value, Color valueColor) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey.shade700,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: valueColor,
          ),
        ),
      ],
    );
  }

  Widget _buildHeaderCell(String text, {required int flex}) {
    return Expanded(
      flex: flex,
      child: Text(
        text,
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _buildTransactionRow(
    BuildContext context,
    int srNo,
    PaymentTransactionModel transaction,
  ) {
    final formatter = NumberFormat("#,##0.00", "en_US");

    // Format date
    final dateFormatter = DateFormat('dd MMM yyyy');
    final dateStr = dateFormatter.format(transaction.transactionDate);

    // Get payment method name
    String methodName = '';
    switch (transaction.method) {
      case PaymentMethod.cash:
        methodName = 'Cash';
        break;
      case PaymentMethod.check:
        methodName = 'Check';
        break;
      case PaymentMethod.accountTransfer:
        methodName = 'Account Transfer';
        break;
      case PaymentMethod.fuelCard:
        methodName = 'Fuel Card';
        break;
    }

    // Get status color
    Color statusColor;
    switch (transaction.status) {
      case PaymentStatus.paid:
        statusColor = Colors.green;
        break;
      case PaymentStatus.partial:
        statusColor = Colors.amber.shade700;
        break;
    }

    // Get account or card info
    String accountInfo = '';
    if (transaction.method == PaymentMethod.accountTransfer) {
      accountInfo = transaction.accountName ?? 'Unknown Account';
    } else if (transaction.method == PaymentMethod.check) {
      accountInfo =
          '${transaction.bankName ?? 'Bank'} - ${transaction.checkNumber ?? 'Unknown'}';
    } else if (transaction.method == PaymentMethod.fuelCard) {
      accountInfo =
          '${transaction.fuelCompany ?? 'Unknown'} - ${transaction.fuelCardNumber ?? 'Unknown Card'}';
      if (transaction.fuelLiters != null) {
        accountInfo += ' (${transaction.fuelLiters?.toStringAsFixed(2)} L)';
      }
    }

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      color: srNo % 2 == 0 ? Colors.grey.shade50 : Colors.white,
      child: Row(
        children: [
          // Sr#
          Expanded(
            flex: 1,
            child: Text(
              srNo.toString(),
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          // Date
          Expanded(
            flex: 2,
            child: Text(dateStr),
          ),
          // Payment Method
          Expanded(
            flex: 2,
            child: Text(methodName),
          ),
          // Account/Card
          Expanded(
            flex: 3,
            child: Text(
              accountInfo,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          // Amount
          Expanded(
            flex: 2,
            child: Text(
              'PKR ${formatter.format(transaction.amount)}',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          // Status
          Expanded(
            flex: 2,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
                  decoration: BoxDecoration(
                    color: statusColor.withAlpha(20),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: statusColor),
                  ),
                  child: Text(
                    transaction.status.toString().split('.').last.toUpperCase(),
                    style: TextStyle(
                      color: statusColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                if (transaction.status == PaymentStatus.paid) ...[
                  const SizedBox(width: 4),
                  Icon(
                    Icons.lock,
                    size: 14,
                    color: Colors.grey.shade600,
                  ),
                ],
              ],
            ),
          ),
          // Actions
          Expanded(
            flex: 1,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.edit,
                    size: 18,
                    color: (canEdit?.call(transaction) ?? true)
                        ? null
                        : Colors.grey.shade400,
                  ),
                  onPressed: (canEdit?.call(transaction) ?? true)
                      ? () => onEdit(transaction)
                      : null,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(maxWidth: 24),
                  tooltip: (canEdit?.call(transaction) ?? true)
                      ? 'Edit transaction'
                      : 'Cannot edit paid transactions',
                ),
                IconButton(
                  icon: Icon(
                    Icons.delete,
                    size: 18,
                    color: (canDelete?.call(transaction) ?? true)
                        ? null
                        : Colors.grey.shade400,
                  ),
                  onPressed: (canDelete?.call(transaction) ?? true)
                      ? () => onDelete(transaction)
                      : null,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(maxWidth: 24),
                  tooltip: (canDelete?.call(transaction) ?? true)
                      ? 'Delete transaction'
                      : 'Cannot delete paid transactions',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
