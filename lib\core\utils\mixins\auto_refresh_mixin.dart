import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'dart:async';

/// Mixin that provides auto-refresh functionality for controllers
/// This ensures data is always fresh when navigating between modules
mixin AutoRefreshMixin on GetxController {
  Timer? _refreshTimer;
  bool _isScreenActive = false;
  bool _hasInitialLoad = false;

  /// Override this method in your controller to define what data to refresh
  Future<void> refreshData();

  /// Optional: Override to define additional initialization logic
  Future<void> initializeData() async {
    if (!_hasInitialLoad) {
      await refreshData();
      _hasInitialLoad = true;
    }
  }

  @override
  void onInit() {
    super.onInit();
    // Initialize data when controller is first created
    WidgetsBinding.instance.addPostFrameCallback((_) {
      initializeData();
    });
  }

  @override
  void onReady() {
    super.onReady();
    _isScreenActive = true;
    // Refresh data when screen becomes ready
    if (_hasInitialLoad) {
      refreshData();
    }
  }

  @override
  void onClose() {
    _isScreenActive = false;
    _refreshTimer?.cancel();
    super.onClose();
  }

  /// Call this method when the screen becomes visible
  void onScreenVisible() {
    _isScreenActive = true;
    // Always refresh when screen becomes visible
    refreshData();
  }

  /// Call this method when the screen becomes hidden
  void onScreenHidden() {
    _isScreenActive = false;
  }

  /// Force refresh data immediately
  Future<void> forceRefresh() async {
    await refreshData();
  }

  /// Set up periodic refresh (optional)
  void startPeriodicRefresh({Duration interval = const Duration(minutes: 5)}) {
    _refreshTimer?.cancel();
    _refreshTimer = Timer.periodic(interval, (timer) {
      if (_isScreenActive) {
        refreshData();
      }
    });
  }

  /// Stop periodic refresh
  void stopPeriodicRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }

  /// Check if the controller is currently active
  bool get isScreenActive => _isScreenActive;
}
