import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';

import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';
import 'package:logestics/models/finance/bill_model.dart';
import 'package:logestics/models/invoice_model.dart';

class BillFirebaseService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final String _collectionPath = AppCollection.billsCollection;

  /// Creates a bill in Firestore and updates linked invoice statuses
  /// Uses transaction to prevent race conditions and ensure atomic operations
  Future<void> createBill({
    required String uid,
    required BillModel bill,
    required List<String> invoiceIds,
  }) async {
    try {
      log('Creating bill ${bill.billNumber} for company $uid');

      // Validate inputs
      if (uid.isEmpty) {
        throw ArgumentError('The "uid" field must not be null or empty.');
      }

      if (bill.billNumber.isEmpty) {
        throw ArgumentError(
            'The "billNumber" field must not be null or empty.');
      }

      if (invoiceIds.isEmpty) {
        throw ArgumentError('At least one invoice must be linked to the bill.');
      }

      // Use transaction for atomic operations and race condition prevention
      await _firestore.runTransaction((transaction) async {
        // Create bill document reference
        final billRef =
            _firestore.collection(_collectionPath).doc(bill.billNumber);

        // Check if bill with this number already exists
        final billSnapshot = await transaction.get(billRef);
        if (billSnapshot.exists) {
          throw Exception(
              'Bill with number ${bill.billNumber} already exists.');
        }

        // Verify all invoices exist and are not already billed
        for (final invoiceId in invoiceIds) {
          final invoiceRef = _firestore
              .collection(AppCollection.invoicesCollection)
              .doc(invoiceId);

          final invoiceSnapshot = await transaction.get(invoiceRef);
          if (!invoiceSnapshot.exists) {
            throw Exception('Invoice $invoiceId not found.');
          }

          final invoiceData = invoiceSnapshot.data()!;
          final currentStatus = invoiceData['invoiceStatus'] as String?;

          // Check if invoice is already billed
          if (currentStatus == 'Pending Billing' ||
              currentStatus == 'Payment Received') {
            throw Exception('Invoice $invoiceId is already billed or paid.');
          }
        }

        // Create the bill
        transaction.set(billRef, bill.toJson());

        // Update all linked invoices status to "Pending Billing"
        for (final invoiceId in invoiceIds) {
          final invoiceRef = _firestore
              .collection(AppCollection.invoicesCollection)
              .doc(invoiceId);

          transaction.update(invoiceRef, {
            'invoiceStatus': 'Pending Billing',
            'updatedAt': DateTime.now().toIso8601String(),
          });
        }
      }).timeout(
        const Duration(seconds: 20),
        onTimeout: () {
          throw TimeoutException('The operation timed out. Please try again.');
        },
      );

      log('Bill ${bill.billNumber} created successfully with ${invoiceIds.length} linked invoices');
    } on SocketException {
      log('No internet connection. Please check your network.');
      rethrow;
    } on TimeoutException {
      log('Timeout occurred while creating bill');
      rethrow;
    } on FirebaseException catch (e) {
      log('Firebase error: ${e.message}');
      rethrow;
    } catch (e) {
      log('Unexpected error creating bill: $e');
      rethrow;
    }
  }

  /// Updates a bill in Firestore
  Future<void> updateBill({
    required String uid,
    required BillModel bill,
  }) async {
    try {
      log('Updating bill ${bill.billNumber} for company $uid');

      // Validate inputs
      if (uid.isEmpty) {
        throw ArgumentError('The "uid" field must not be null or empty.');
      }

      if (bill.billNumber.isEmpty) {
        throw ArgumentError(
            'The "billNumber" field must not be null or empty.');
      }

      // Reference to the bill document
      final billRef =
          _firestore.collection(_collectionPath).doc(bill.billNumber);

      // Check if the bill exists and belongs to the current company
      final billDoc = await billRef.get();
      if (!billDoc.exists) {
        throw Exception('Bill not found');
      }

      final existingBill = BillModel.fromJson(billDoc.data()!);
      if (existingBill.companyUid != uid) {
        throw Exception('Bill does not belong to the current company');
      }

      // Update the bill with new updatedAt timestamp
      final updatedBill = bill.copyWith(updatedAt: DateTime.now());

      await billRef.update(updatedBill.toJson()).timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw TimeoutException('The operation timed out. Please try again.');
        },
      );

      log('Bill ${bill.billNumber} updated successfully');
    } on SocketException {
      log('No internet connection. Please check your network.');
      rethrow;
    } on TimeoutException {
      log('Timeout occurred while updating bill');
      rethrow;
    } on FirebaseException catch (e) {
      log('Firebase error: ${e.message}');
      rethrow;
    } catch (e) {
      log('Unexpected error updating bill: $e');
      rethrow;
    }
  }

  /// Updates bill status and linked invoice statuses
  Future<void> updateBillStatus({
    required String uid,
    required String billNumber,
    required String newStatus,
  }) async {
    try {
      log('Updating bill status for $billNumber to $newStatus');

      // Validate inputs
      if (uid.isEmpty) {
        throw ArgumentError('The "uid" field must not be null or empty.');
      }

      if (billNumber.isEmpty) {
        throw ArgumentError(
            'The "billNumber" field must not be null or empty.');
      }

      if (newStatus.isEmpty) {
        throw ArgumentError('The "newStatus" field must not be null or empty.');
      }

      // Get the bill document
      final billRef = _firestore.collection(_collectionPath).doc(billNumber);

      final billDoc = await billRef.get();
      if (!billDoc.exists) {
        throw Exception('Bill not found');
      }

      final bill = BillModel.fromJson(billDoc.data()!);
      if (bill.companyUid != uid) {
        throw Exception('Bill does not belong to the current company');
      }

      // Use a batch write for atomic operations
      final batch = _firestore.batch();

      // Update bill status
      batch.update(billRef, {
        'billStatus': newStatus,
        'updatedAt': DateTime.now().toIso8601String(),
        if (newStatus == 'Completed')
          'paidDate': DateTime.now().toIso8601String(),
      });

      // Update linked invoice statuses based on bill status
      final invoiceStatus =
          newStatus == 'Completed' ? 'Payment Received' : 'Pending Billing';

      for (final invoiceId in bill.linkedInvoiceIds) {
        final invoiceRef = _firestore
            .collection(AppCollection.invoicesCollection)
            .doc(invoiceId);

        batch.update(invoiceRef, {
          'invoiceStatus': invoiceStatus,
          'updatedAt': DateTime.now().toIso8601String(),
        });
      }

      // Commit the batch
      await batch.commit().timeout(
        const Duration(seconds: 15),
        onTimeout: () {
          throw TimeoutException('The operation timed out. Please try again.');
        },
      );

      log('Bill status updated to $newStatus with ${bill.linkedInvoiceIds.length} invoice statuses updated');
    } on SocketException {
      log('No internet connection. Please check your network.');
      rethrow;
    } on TimeoutException {
      log('Timeout occurred while updating bill status');
      rethrow;
    } on FirebaseException catch (e) {
      log('Firebase error: ${e.message}');
      rethrow;
    } catch (e) {
      log('Unexpected error updating bill status: $e');
      rethrow;
    }
  }

  /// Deletes a bill and reverts linked invoice statuses
  Future<void> deleteBill({
    required String uid,
    required String billNumber,
  }) async {
    try {
      log('Deleting bill $billNumber for company $uid');

      // Validate inputs
      if (uid.isEmpty) {
        throw ArgumentError('The "uid" field must not be null or empty.');
      }

      if (billNumber.isEmpty) {
        throw ArgumentError(
            'The "billNumber" field must not be null or empty.');
      }

      // Get the bill document first to get linked invoices
      final billRef = _firestore.collection(_collectionPath).doc(billNumber);

      final billDoc = await billRef.get();
      if (!billDoc.exists) {
        throw Exception('Bill not found');
      }

      final bill = BillModel.fromJson(billDoc.data()!);
      if (bill.companyUid != uid) {
        throw Exception('Bill does not belong to the current company');
      }

      // Use a batch write for atomic operations
      final batch = _firestore.batch();

      // Delete the bill
      batch.delete(billRef);

      // Revert linked invoice statuses back to "Received"
      for (final invoiceId in bill.linkedInvoiceIds) {
        final invoiceRef = _firestore
            .collection(AppCollection.invoicesCollection)
            .doc(invoiceId);

        batch.update(invoiceRef, {
          'invoiceStatus': 'Received',
          'updatedAt': DateTime.now().toIso8601String(),
        });
      }

      // Commit the batch
      await batch.commit().timeout(
        const Duration(seconds: 15),
        onTimeout: () {
          throw TimeoutException('The operation timed out. Please try again.');
        },
      );

      log('Bill $billNumber deleted successfully with ${bill.linkedInvoiceIds.length} invoice statuses reverted');
    } on SocketException {
      log('No internet connection. Please check your network.');
      rethrow;
    } on TimeoutException {
      log('Timeout occurred while deleting bill');
      rethrow;
    } on FirebaseException catch (e) {
      log('Firebase error: ${e.message}');
      rethrow;
    } catch (e) {
      log('Unexpected error deleting bill: $e');
      rethrow;
    }
  }

  /// Gets all bills for a company
  Future<List<BillModel>> getBills({required String uid}) async {
    try {
      log('Fetching bills for company $uid');

      // Remove orderBy to avoid composite index requirement
      // Sorting will be done in the application layer
      final snapshot = await _firestore
          .collection(_collectionPath)
          .where('companyUid', isEqualTo: uid)
          .get();

      final bills =
          snapshot.docs.map((doc) => BillModel.fromJson(doc.data())).toList();

      // Sort by bill date (newest first) in application
      bills.sort((a, b) => b.billDate.compareTo(a.billDate));

      log('Successfully fetched ${bills.length} bills');
      return bills;
    } catch (e) {
      log('Error fetching bills: $e');
      rethrow;
    }
  }

  /// Listens to real-time bill changes for a company
  Stream<List<DocumentChange>> listenToBills({required String uid}) {
    try {
      log('Setting up real-time listener for bills for company $uid');

      // Remove orderBy to avoid composite index requirement
      // Sorting will be done in the application layer
      return _firestore
          .collection(_collectionPath)
          .where('companyUid', isEqualTo: uid)
          .snapshots()
          .map((snapshot) => snapshot.docChanges);
    } catch (e) {
      log('Error setting up bills listener: $e');
      rethrow;
    }
  }

  /// Gets a single bill by bill number
  Future<BillModel?> getBillByNumber({
    required String uid,
    required String billNumber,
  }) async {
    try {
      log('Fetching bill $billNumber for company $uid');

      final doc =
          await _firestore.collection(_collectionPath).doc(billNumber).get();

      if (!doc.exists) {
        log('Bill $billNumber not found');
        return null;
      }

      final bill = BillModel.fromJson(doc.data()!);

      // Verify the bill belongs to the requesting company
      if (bill.companyUid != uid) {
        log('Bill $billNumber does not belong to company $uid');
        return null;
      }

      log('Successfully fetched bill $billNumber');
      return bill;
    } catch (e) {
      log('Error fetching bill $billNumber: $e');
      rethrow;
    }
  }

  /// Gets the next bill number for a company with proper year handling
  Future<int> getNextBillNumber({required String uid}) async {
    try {
      log('Getting next bill number for company $uid');

      final currentYear = DateTime.now().year;

      // Get all bills for this company in the current year
      final snapshot = await _firestore
          .collection(_collectionPath)
          .where('companyUid', isEqualTo: uid)
          .get();

      if (snapshot.docs.isEmpty) {
        log('No existing bills found, starting with bill number 1');
        return 1;
      }

      // Filter bills for current year and extract numeric parts
      final currentYearBills = <int>[];

      for (final doc in snapshot.docs) {
        try {
          final bill = BillModel.fromJson(doc.data());
          final billNumber = bill.billNumber;

          // Extract number from format BILL-YYYY-NNNN
          final parts = billNumber.split('-');
          if (parts.length == 3) {
            final billYear = int.tryParse(parts[1]);
            final billSequence = int.tryParse(parts[2]);

            // Only consider bills from current year
            if (billYear == currentYear && billSequence != null) {
              currentYearBills.add(billSequence);
            }
          }
        } catch (e) {
          log('Error parsing bill number for doc ${doc.id}: $e');
          continue;
        }
      }

      if (currentYearBills.isEmpty) {
        log('No bills found for current year $currentYear, starting with 1');
        return 1;
      }

      // Get the highest number and increment
      currentYearBills.sort();
      final lastNumber = currentYearBills.last;
      final nextNumber = lastNumber + 1;

      log('Next bill number for year $currentYear: $nextNumber (last was $lastNumber)');
      return nextNumber;
    } catch (e) {
      log('Error getting next bill number: $e');
      // Return 1 as fallback
      return 1;
    }
  }

  /// Generates a unique bill number with race condition protection
  /// Attempts to find an available bill number by checking multiple candidates
  Future<String> generateUniqueBillNumber({required String uid}) async {
    try {
      log('Generating unique bill number for company $uid');

      const maxAttempts = 10;

      for (int attempt = 0; attempt < maxAttempts; attempt++) {
        // Get next bill number
        final nextNumber = await getNextBillNumber(uid: uid);
        final candidateBillNumber =
            BillModel.generateBillNumber(nextNumber + attempt);

        // Check if this bill number is available
        final billRef =
            _firestore.collection(_collectionPath).doc(candidateBillNumber);
        final billSnapshot = await billRef.get();

        if (!billSnapshot.exists) {
          log('Generated unique bill number: $candidateBillNumber');
          return candidateBillNumber;
        }

        log('Bill number $candidateBillNumber already exists, trying next...');
      }

      // If all attempts failed, use timestamp-based fallback
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fallbackNumber =
          'BILL-${DateTime.now().year}-${timestamp.toString().substring(8)}';
      log('Using timestamp-based fallback bill number: $fallbackNumber');
      return fallbackNumber;
    } catch (e) {
      log('Error generating unique bill number: $e');
      // Ultimate fallback with timestamp
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      return 'BILL-${DateTime.now().year}-${timestamp.toString().substring(8)}';
    }
  }

  /// Gets bills filtered by status
  Future<List<BillModel>> getBillsByStatus({
    required String uid,
    required String status,
  }) async {
    try {
      log('Fetching bills with status $status for company $uid');

      final snapshot = await _firestore
          .collection(_collectionPath)
          .where('companyUid', isEqualTo: uid)
          .where('billStatus', isEqualTo: status)
          .orderBy('billDate', descending: true)
          .get();

      final bills =
          snapshot.docs.map((doc) => BillModel.fromJson(doc.data())).toList();

      log('Successfully fetched ${bills.length} bills with status $status');
      return bills;
    } catch (e) {
      log('Error fetching bills by status: $e');
      rethrow;
    }
  }

  /// Gets bills within a date range
  Future<List<BillModel>> getBillsByDateRange({
    required String uid,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      log('Fetching bills between $startDate and $endDate for company $uid');

      final snapshot = await _firestore
          .collection(_collectionPath)
          .where('companyUid', isEqualTo: uid)
          .where('billDate',
              isGreaterThanOrEqualTo: startDate.toIso8601String())
          .where('billDate', isLessThanOrEqualTo: endDate.toIso8601String())
          .orderBy('billDate', descending: true)
          .get();

      final bills =
          snapshot.docs.map((doc) => BillModel.fromJson(doc.data())).toList();

      log('Successfully fetched ${bills.length} bills in date range');
      return bills;
    } catch (e) {
      log('Error fetching bills by date range: $e');
      rethrow;
    }
  }

  /// Gets linked invoices for a bill
  Future<List<InvoiceModel>> getLinkedInvoices({
    required String uid,
    required List<String> invoiceIds,
  }) async {
    try {
      log('Fetching ${invoiceIds.length} linked invoices for company $uid');

      if (invoiceIds.isEmpty) {
        return [];
      }

      // Firestore 'in' queries are limited to 10 items, so we need to batch them
      final List<InvoiceModel> allInvoices = [];

      for (int i = 0; i < invoiceIds.length; i += 10) {
        final batch = invoiceIds.skip(i).take(10).toList();

        final snapshot = await _firestore
            .collection(AppCollection.invoicesCollection)
            .where('uid', isEqualTo: uid)
            .where('tasNumber', whereIn: batch)
            .get();

        final invoices = snapshot.docs
            .map((doc) => InvoiceModel.fromJson(doc.data()))
            .toList();

        allInvoices.addAll(invoices);
      }

      log('Successfully fetched ${allInvoices.length} linked invoices');
      return allInvoices;
    } catch (e) {
      log('Error fetching linked invoices: $e');
      rethrow;
    }
  }
}
