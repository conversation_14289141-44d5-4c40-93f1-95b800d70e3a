import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/widgets/header_secondary.dart';
import 'package:logestics/models/finance/fuel_expense_model.dart';
import 'package:logestics/features/finance/fuel_cards/presentation/controllers/fuel_expense_controller.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';

class FuelExpenseView extends StatefulWidget {
  const FuelExpenseView({super.key});

  @override
  State<FuelExpenseView> createState() => _FuelExpenseViewState();
}

class _FuelExpenseViewState extends State<FuelExpenseView> {
  late final FuelExpenseController controller;
  final formatter = NumberFormat("#,##0.00", "en_US");

  @override
  void initState() {
    super.initState();
    controller = Get.find<FuelExpenseController>();

    // Ensure data is loaded
    if (controller.fuelCards.isEmpty) {
      controller.loadFuelCards();
    }
  }

  @override
  Widget build(BuildContext context) {
    var width = Get.width;
    notifier = Provider.of(context, listen: true);

    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          child: Column(
            children: [
              SizedBox(
                height: width < 650 ? 55 : 40,
                width: width,
                child: width < 650
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Fuel Expenses',
                            overflow: TextOverflow.ellipsis,
                            style: AppTextStyles.titleStyle
                                .copyWith(color: notifier.text),
                          ),
                          const Spacer(),
                          HeaderSecondary(
                            option1: AppStrings.dashboard,
                            option2: AppStrings.system,
                            option3: 'Fuel Expenses',
                            notifier: notifier,
                          ),
                        ],
                      )
                    : Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Fuel Expenses',
                            overflow: TextOverflow.ellipsis,
                            style: AppTextStyles.titleStyle
                                .copyWith(color: notifier.text),
                          ),
                          const Spacer(),
                          HeaderSecondary(
                            option1: AppStrings.dashboard,
                            option2: AppStrings.system,
                            option3: 'Fuel Expenses',
                            notifier: notifier,
                          ),
                        ],
                      ),
              ),
              const SizedBox(height: 20),
              SizedBox(
                height: 570,
                child: _buildFuelExpenseTracker(),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFuelExpenseTracker() {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            color: notifier.getBgColor,
            borderRadius: BorderRadius.circular(10),
          ),
          padding: const EdgeInsets.symmetric(vertical: 15),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Left side - Form
              Expanded(
                flex: 1,
                child: Container(
                  padding: const EdgeInsets.all(16),
                  child: Form(
                    key: controller.formKey,
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Add Fuel Expense',
                            style: AppTextStyles.titleStyle.copyWith(
                              fontSize: 20,
                              color: notifier.text,
                            ),
                          ),
                          const SizedBox(height: 24),

                          // Trip ID
                          TextFormField(
                            controller: controller.tripIdController,
                            decoration: InputDecoration(
                              labelText: 'Trip ID',
                              hintText: 'Enter trip/freight ID',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                              filled: true,
                              fillColor: notifier.textFileColor,
                            ),
                            validator: controller.validateTripId,
                          ),
                          const SizedBox(height: 16),

                          // Freight Amount
                          TextFormField(
                            controller: controller.freightAmountController,
                            decoration: InputDecoration(
                              labelText: 'Freight Amount (PKR)',
                              hintText: 'Enter total freight amount',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                              filled: true,
                              fillColor: notifier.textFileColor,
                            ),
                            keyboardType: TextInputType.number,
                            validator: controller.validateFreightAmount,
                          ),
                          const SizedBox(height: 16),

                          // Other Expenses
                          TextFormField(
                            controller: controller.otherExpensesController,
                            decoration: InputDecoration(
                              labelText: 'Other Expenses (PKR)',
                              hintText: 'Enter other expenses (if any)',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                              filled: true,
                              fillColor: notifier.textFileColor,
                            ),
                            keyboardType: TextInputType.number,
                          ),
                          const SizedBox(height: 24),

                          Text(
                            'Fuel Card Details',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: notifier.text,
                            ),
                          ),
                          const SizedBox(height: 16),

                          // Step 1: Select Fuel Station
                          Obx(() => DropdownButtonFormField<String>(
                                decoration: InputDecoration(
                                  labelText: 'Select Fuel Station',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8.0),
                                  ),
                                  filled: true,
                                  fillColor: notifier.textFileColor,
                                ),
                                value: controller.selectedFuelStation.value,
                                items: controller.fuelStations.map((station) {
                                  return DropdownMenuItem<String>(
                                    value: station,
                                    child: Text(station),
                                  );
                                }).toList(),
                                onChanged: controller.selectFuelStation,
                                validator: controller.validateFuelStation,
                              )),
                          const SizedBox(height: 16),

                          // Step 2: Select Fuel Card
                          Obx(() => DropdownButtonFormField<String>(
                                decoration: InputDecoration(
                                  labelText: 'Select Fuel Card',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8.0),
                                  ),
                                  filled: true,
                                  fillColor: notifier.textFileColor,
                                ),
                                value: controller.selectedFuelCard.value?.id,
                                items: controller.filteredFuelCards.map((card) {
                                  return DropdownMenuItem<String>(
                                    value: card.id,
                                    child: Text(
                                      '${card.cardNumber} (${formatter.format(card.remainingCapacity)} L)',
                                    ),
                                  );
                                }).toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    final card = controller.filteredFuelCards
                                        .firstWhere((c) => c.id == value);
                                    controller.selectFuelCard(card);
                                  } else {
                                    controller.selectFuelCard(null);
                                  }
                                },
                                validator: (value) =>
                                    controller.validateFuelCard(value != null
                                        ? controller.filteredFuelCards
                                            .firstWhere(
                                                (card) => card.id == value)
                                        : null),
                              )),
                          const SizedBox(height: 16),

                          // Fuel Amount
                          TextFormField(
                            controller: controller.fuelAmountController,
                            decoration: InputDecoration(
                              labelText: 'Fuel Amount (Liters)',
                              hintText: 'Enter fuel amount in liters',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                              filled: true,
                              fillColor: notifier.textFileColor,
                              suffixText: 'L',
                            ),
                            keyboardType: TextInputType.number,
                            validator: controller.validateFuelAmount,
                          ),
                          const SizedBox(height: 24),

                          // Submit Button
                          Obx(() => SizedBox(
                                width: double.infinity,
                                child: ElevatedButton(
                                  onPressed: controller.isSaving.value
                                      ? null
                                      : controller.createFuelExpense,
                                  style: ElevatedButton.styleFrom(
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 16),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                  child: controller.isSaving.value
                                      ? const CircularProgressIndicator()
                                      : const Text('Save Expense'),
                                ),
                              )),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              // Right side - Summary
              Expanded(
                flex: 1,
                child: Container(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Expense Summary',
                        style: AppTextStyles.titleStyle.copyWith(
                          fontSize: 20,
                          color: notifier.text,
                        ),
                      ),
                      const SizedBox(height: 24),

                      // Fuel Cost Calculation
                      _buildSummaryCard(),

                      const SizedBox(height: 24),

                      // Recent Expenses
                      Expanded(
                        child: _buildRecentExpenses(),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),

        // Loading Indicator
        Obx(() {
          if (controller.isLoading.value) {
            return Container(
              color: Colors.black.withValues(alpha: 0.3),
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            );
          }
          return const SizedBox.shrink();
        }),
      ],
    );
  }

  Widget _buildSummaryCard() {
    return Obx(() {
      final fuelCost = controller.fuelCost.value;
      final freightAmount = controller.freightAmount.value;
      final otherExpenses = controller.otherExpenses.value;
      final profitOrLoss = controller.profitOrLoss.value;
      final fuelRate = controller.selectedFuelCard.value?.currentRate ?? 0.0;

      return Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Current Fuel Rate:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: notifier.text,
                    ),
                  ),
                  Text(
                    'PKR ${formatter.format(fuelRate)}/L',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                ],
              ),
              const Divider(),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Fuel Cost:',
                    style: TextStyle(
                      color: notifier.text,
                    ),
                  ),
                  Text(
                    'PKR ${formatter.format(fuelCost)}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.red[800],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Other Expenses:',
                    style: TextStyle(
                      color: notifier.text,
                    ),
                  ),
                  Text(
                    'PKR ${formatter.format(otherExpenses)}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.orange[800],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Freight Amount:',
                    style: TextStyle(
                      color: notifier.text,
                    ),
                  ),
                  Text(
                    'PKR ${formatter.format(freightAmount)}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.green[800],
                    ),
                  ),
                ],
              ),
              const Divider(),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Profit/Loss:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: notifier.text,
                    ),
                  ),
                  Text(
                    'PKR ${formatter.format(profitOrLoss)}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: profitOrLoss >= 0
                          ? Colors.green[800]
                          : Colors.red[800],
                    ),
                  ),
                ],
              ),

              // Validation warning
              if (fuelCost > freightAmount)
                Container(
                  margin: const EdgeInsets.only(top: 16),
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.red[100],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.red),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.warning, color: Colors.red[800]),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Fuel cost cannot exceed freight amount',
                          style: TextStyle(color: Colors.red[800]),
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildRecentExpenses() {
    return Obx(() {
      final expenses = controller.fuelExpenses;

      if (expenses.isEmpty) {
        return Center(
          child: Text(
            'No fuel expenses recorded yet',
            style: TextStyle(
              color: notifier.text.withValues(alpha: 0.6),
            ),
          ),
        );
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Recent Expenses',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: notifier.text,
            ),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: ListView.builder(
              itemCount: expenses.length,
              itemBuilder: (context, index) {
                final expense = expenses[index];
                return _buildExpenseListItem(expense);
              },
            ),
          ),
        ],
      );
    });
  }

  Widget _buildExpenseListItem(FuelExpenseModel expense) {
    final isProfit = expense.profitOrLoss >= 0;

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        title: Text(
          'Trip: ${expense.tripId}',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: notifier.text,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${expense.fuelAmount} L @ PKR ${formatter.format(expense.fuelRate)}/L',
              style: TextStyle(
                color: notifier.text.withValues(alpha: 0.7),
              ),
            ),
            Text(
              'Station: ${expense.fuelStationName}',
              style: TextStyle(
                color: notifier.text.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              'PKR ${formatter.format(expense.fuelCost)}',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.red[800],
              ),
            ),
            Text(
              isProfit
                  ? 'Profit: ${formatter.format(expense.profitOrLoss)}'
                  : 'Loss: ${formatter.format(expense.profitOrLoss.abs())}',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: isProfit ? Colors.green[800] : Colors.red[800],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
