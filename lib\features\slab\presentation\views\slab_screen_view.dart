import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/widgets/navigation/drawer_page_scaffold.dart';

import '../../../home/<USER>/drawer_controllers.dart';
import 'slab_list.dart';

class SlabScreenView extends StatefulWidget {
  const SlabScreenView({super.key});

  @override
  State<SlabScreenView> createState() => _SlabScreenViewState();
}

class _SlabScreenViewState extends State<SlabScreenView> {
  MainDrawerController mainDrawerController = Get.find<MainDrawerController>();

  @override
  Widget build(BuildContext context) {
    notifier = Provider.of(context, listen: true);

    return DrawerPageScaffold(
      pageTitle: "Slab Management",
      breadcrumbItems: const [AppStrings.system],
      body: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(height: 20),
            SizedBox(
              height: 570,
              child: const SlabList(titleShow: true),
            ),
          ],
        ),
      ),
    );
  }
}
