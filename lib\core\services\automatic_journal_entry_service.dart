import 'package:cloud_firestore/cloud_firestore.dart';
import '../../models/finance/journal_entry_model.dart';
import '../../models/finance/chart_of_accounts_model.dart';
import '../../models/finance/expense_model.dart';
import '../../models/finance/bill_model.dart';
import '../../models/voucher_model.dart';
import '../../models/finance/loan_model.dart';
import '../../models/finance/account_transaction_model.dart';
import '../../firebase_service/accounting/journal_entry_firebase_service.dart';
import '../../firebase_service/accounting/chart_of_accounts_firebase_service.dart';

/// Service for automatically generating journal entries from existing transactions
class AutomaticJournalEntryService {
  final JournalEntryFirebaseService _journalEntryService;
  final ChartOfAccountsFirebaseService _chartOfAccountsService;

  AutomaticJournalEntryService(
    this._journalEntryService,
    this._chartOfAccountsService,
  );

  /// Generate journal entry from expense transaction
  Future<JournalEntryModel?> generateExpenseJournalEntry({
    required ExpenseModel expense,
    required String uid,
    required String createdBy,
  }) async {
    try {
      // Get expense account (should be an expense account)
      final expenseAccount = await _getAccountByName(expense.categoryName, uid);
      if (expenseAccount == null) return null;

      // Get cash/bank account (should be an asset account)
      final cashAccount = await _getAccountByName(expense.accountName, uid);
      if (cashAccount == null) return null;

      // Create journal entry lines
      final lines = <JournalEntryLineModel>[
        // Debit expense account
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: expenseAccount.id,
          accountName: expenseAccount.accountName,
          accountNumber: expenseAccount.accountNumber,
          description: 'Expense: ${expense.title}',
          debitAmount: expense.amount,
          creditAmount: 0.0,
          createdAt: DateTime.now(),
        ),
        // Credit cash/bank account
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: cashAccount.id,
          accountName: cashAccount.accountName,
          accountNumber: cashAccount.accountNumber,
          description: 'Payment for: ${expense.title}',
          debitAmount: 0.0,
          creditAmount: expense.amount,
          createdAt: DateTime.now(),
        ),
      ];

      return JournalEntryModel(
        id: '',
        entryNumber: '', // Will be auto-generated
        entryDate: expense.createdAt,
        description: 'Expense: ${expense.title} - ${expense.payeeName}',
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: lines,
        totalDebits: expense.amount,
        totalCredits: expense.amount,
        referenceNumber: expense.referenceNumber,
        sourceTransactionId: expense.id,
        sourceTransactionType: 'expense',
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: uid,
      );
    } catch (e) {
      print('Error generating expense journal entry: $e');
      return null;
    }
  }

  /// Generate journal entry from bill transaction
  Future<JournalEntryModel?> generateBillJournalEntry({
    required BillModel bill,
    required String uid,
    required String createdBy,
  }) async {
    try {
      // Get accounts payable account
      final accountsPayableAccount = await _getAccountByType(
        AccountType.currentLiabilities,
        'Accounts Payable',
        uid,
      );
      if (accountsPayableAccount == null) return null;

      // Get revenue/service account
      final revenueAccount = await _getAccountByType(
        AccountType.serviceRevenue,
        'Service Revenue',
        uid,
      );
      if (revenueAccount == null) return null;

      // Create journal entry lines
      final lines = <JournalEntryLineModel>[
        // Debit accounts receivable (if bill represents revenue)
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: accountsPayableAccount.id,
          accountName: accountsPayableAccount.accountName,
          accountNumber: accountsPayableAccount.accountNumber,
          description: 'Bill: ${bill.billNumber}',
          debitAmount: bill.totalAmount,
          creditAmount: 0.0,
          createdAt: DateTime.now(),
        ),
        // Credit revenue account
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: revenueAccount.id,
          accountName: revenueAccount.accountName,
          accountNumber: revenueAccount.accountNumber,
          description: 'Revenue from bill: ${bill.billNumber}',
          debitAmount: 0.0,
          creditAmount: bill.totalAmount,
          createdAt: DateTime.now(),
        ),
      ];

      return JournalEntryModel(
        id: '',
        entryNumber: '', // Will be auto-generated
        entryDate: bill.billDate,
        description:
            'Bill: ${bill.billNumber} - ${bill.customerName ?? "Multiple Customers"}',
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: lines,
        totalDebits: bill.totalAmount,
        totalCredits: bill.totalAmount,
        referenceNumber: bill.billNumber,
        sourceTransactionId: bill.billId,
        sourceTransactionType: 'bill',
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: uid,
      );
    } catch (e) {
      print('Error generating bill journal entry: $e');
      return null;
    }
  }

  /// Generate journal entry from voucher transaction
  Future<List<JournalEntryModel>> generateVoucherJournalEntries({
    required VoucherModel voucher,
    required String uid,
    required String createdBy,
  }) async {
    final journalEntries = <JournalEntryModel>[];

    try {
      // 1. Broker fees journal entry
      if (voucher.brokerFees > 0) {
        final brokerEntry = await _generateBrokerFeesEntry(
          voucher: voucher,
          uid: uid,
          createdBy: createdBy,
        );
        if (brokerEntry != null) journalEntries.add(brokerEntry);
      }

      // 2. Munshiana fees journal entry
      if (voucher.munshianaFees > 0) {
        final munshianaEntry = await _generateMunshianaEntry(
          voucher: voucher,
          uid: uid,
          createdBy: createdBy,
        );
        if (munshianaEntry != null) journalEntries.add(munshianaEntry);
      }

      // 3. Net profit journal entry
      final netProfit =
          voucher.totalFreight - voucher.brokerFees - voucher.munshianaFees;
      if (netProfit != 0) {
        final netProfitEntry = await _generateNetProfitEntry(
          voucher: voucher,
          netProfit: netProfit,
          uid: uid,
          createdBy: createdBy,
        );
        if (netProfitEntry != null) journalEntries.add(netProfitEntry);
      }

      return journalEntries;
    } catch (e) {
      print('Error generating voucher journal entries: $e');
      return [];
    }
  }

  /// Generate journal entry from loan transaction
  Future<List<JournalEntryModel>> generateLoanJournalEntries({
    required LoanModel loan,
    required String transactionType, // 'disbursement' or 'repayment'
    required String uid,
    required String createdBy,
  }) async {
    final journalEntries = <JournalEntryModel>[];

    try {
      if (transactionType == 'disbursement') {
        // Loan disbursement entry
        final disbursementEntry = await _generateLoanDisbursementEntry(
          loan: loan,
          uid: uid,
          createdBy: createdBy,
        );
        if (disbursementEntry != null) journalEntries.add(disbursementEntry);
      } else if (transactionType == 'repayment') {
        // Loan repayment entry
        final repaymentEntry = await _generateLoanRepaymentEntry(
          loan: loan,
          uid: uid,
          createdBy: createdBy,
        );
        if (repaymentEntry != null) journalEntries.add(repaymentEntry);
      }

      return journalEntries;
    } catch (e) {
      print('Error generating loan journal entries: $e');
      return [];
    }
  }

  /// Generate journal entry from account transaction
  Future<JournalEntryModel?> generateAccountTransactionJournalEntry({
    required AccountTransactionModel transaction,
    required String uid,
    required String createdBy,
  }) async {
    try {
      // Get the account
      final account = await _getAccountByName(transaction.accountName, uid);
      if (account == null) return null;

      // Determine the offsetting account based on transaction type
      ChartOfAccountsModel? offsetAccount;
      String description = transaction.description;

      switch (transaction.type) {
        case TransactionType.deposit:
          // Credit cash account, debit accounts receivable or revenue
          offsetAccount = await _getAccountByType(
            AccountType.currentAssets,
            'Accounts Receivable',
            uid,
          );
          break;
        case TransactionType.expense:
          // Debit expense account, credit cash account
          offsetAccount = await _getAccountByType(
            AccountType.operatingExpenses,
            'General Expenses',
            uid,
          );
          break;
        case TransactionType.loan:
          // Handle loan transactions
          if (transaction.amount > 0) {
            // Loan received - debit cash, credit loan payable
            offsetAccount = await _getAccountByType(
              AccountType.longTermLiabilities,
              'Loans Payable',
              uid,
            );
          } else {
            // Loan payment - debit loan payable, credit cash
            offsetAccount = await _getAccountByType(
              AccountType.longTermLiabilities,
              'Loans Payable',
              uid,
            );
          }
          break;
        default:
          // For other transaction types, use a general account
          offsetAccount = await _getAccountByType(
            AccountType.operatingExpenses,
            'Miscellaneous Expenses',
            uid,
          );
      }

      if (offsetAccount == null) return null;

      // Create journal entry lines based on transaction amount
      final lines = <JournalEntryLineModel>[];

      if (transaction.amount > 0) {
        // Positive amount - money coming in
        lines.addAll([
          JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: account.id,
            accountName: account.accountName,
            accountNumber: account.accountNumber,
            description: description,
            debitAmount: transaction.amount,
            creditAmount: 0.0,
            createdAt: DateTime.now(),
          ),
          JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: offsetAccount.id,
            accountName: offsetAccount.accountName,
            accountNumber: offsetAccount.accountNumber,
            description: description,
            debitAmount: 0.0,
            creditAmount: transaction.amount,
            createdAt: DateTime.now(),
          ),
        ]);
      } else {
        // Negative amount - money going out
        final positiveAmount = transaction.amount.abs();
        lines.addAll([
          JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: offsetAccount.id,
            accountName: offsetAccount.accountName,
            accountNumber: offsetAccount.accountNumber,
            description: description,
            debitAmount: positiveAmount,
            creditAmount: 0.0,
            createdAt: DateTime.now(),
          ),
          JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: account.id,
            accountName: account.accountName,
            accountNumber: account.accountNumber,
            description: description,
            debitAmount: 0.0,
            creditAmount: positiveAmount,
            createdAt: DateTime.now(),
          ),
        ]);
      }

      final totalAmount = transaction.amount.abs();
      return JournalEntryModel(
        id: '',
        entryNumber: '', // Will be auto-generated
        entryDate: transaction.transactionDate,
        description: description,
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: lines,
        totalDebits: totalAmount,
        totalCredits: totalAmount,
        referenceNumber: transaction.referenceId,
        sourceTransactionId: transaction.id,
        sourceTransactionType: 'account_transaction',
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: uid,
      );
    } catch (e) {
      print('Error generating account transaction journal entry: $e');
      return null;
    }
  }

  // Helper methods
  Future<ChartOfAccountsModel?> _getAccountByName(
      String accountName, String uid) async {
    try {
      final accounts = await _chartOfAccountsService.getAccounts();
      return accounts.firstWhere(
        (account) =>
            account.accountName.toLowerCase() == accountName.toLowerCase(),
        orElse: () => accounts.first, // Fallback to first account
      );
    } catch (e) {
      return null;
    }
  }

  Future<ChartOfAccountsModel?> _getAccountByType(
    AccountType accountType,
    String preferredName,
    String uid,
  ) async {
    try {
      final accounts =
          await _chartOfAccountsService.getAccountsByType(accountType);
      // First try to find by preferred name and type
      try {
        return accounts.firstWhere(
          (account) => account.accountName
              .toLowerCase()
              .contains(preferredName.toLowerCase()),
        );
      } catch (e) {
        // If not found, get any account of the specified type
        try {
          return accounts.first;
        } catch (e) {
          return null;
        }
      }
    } catch (e) {
      return null;
    }
  }

  // Voucher-specific helper methods
  Future<JournalEntryModel?> _generateBrokerFeesEntry({
    required VoucherModel voucher,
    required String uid,
    required String createdBy,
  }) async {
    try {
      // Get broker fees expense account
      final brokerFeesAccount = await _getAccountByType(
        AccountType.operatingExpenses,
        'Broker Fees',
        uid,
      );
      if (brokerFeesAccount == null) return null;

      // Get cash/bank account for broker
      final brokerCashAccount =
          await _getAccountByName(voucher.brokerAccount, uid);
      if (brokerCashAccount == null) return null;

      final lines = <JournalEntryLineModel>[
        // Debit broker fees expense
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: brokerFeesAccount.id,
          accountName: brokerFeesAccount.accountName,
          accountNumber: brokerFeesAccount.accountNumber,
          description: 'Broker fees - Voucher ${voucher.voucherNumber}',
          debitAmount: voucher.brokerFees,
          creditAmount: 0.0,
          createdAt: DateTime.now(),
        ),
        // Credit cash account
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: brokerCashAccount.id,
          accountName: brokerCashAccount.accountName,
          accountNumber: brokerCashAccount.accountNumber,
          description: 'Broker payment - Voucher ${voucher.voucherNumber}',
          debitAmount: 0.0,
          creditAmount: voucher.brokerFees,
          createdAt: DateTime.now(),
        ),
      ];

      return JournalEntryModel(
        id: '',
        entryNumber: '',
        entryDate: voucher.belongsToDate ?? voucher.createdAt,
        description: 'Broker fees payment - Voucher ${voucher.voucherNumber}',
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: lines,
        totalDebits: voucher.brokerFees,
        totalCredits: voucher.brokerFees,
        referenceNumber: voucher.voucherNumber,
        sourceTransactionId: voucher.voucherNumber,
        sourceTransactionType: 'voucher_broker_fees',
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: uid,
      );
    } catch (e) {
      print('Error generating broker fees entry: $e');
      return null;
    }
  }

  Future<JournalEntryModel?> _generateMunshianaEntry({
    required VoucherModel voucher,
    required String uid,
    required String createdBy,
  }) async {
    try {
      // Get munshiana expense account
      final munshianaAccount = await _getAccountByType(
        AccountType.operatingExpenses,
        'Munshiana',
        uid,
      );
      if (munshianaAccount == null) return null;

      // Get cash/bank account for munshiana
      final munshianaCashAccount =
          await _getAccountByName(voucher.munshianaAccount, uid);
      if (munshianaCashAccount == null) return null;

      final lines = <JournalEntryLineModel>[
        // Debit munshiana expense
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: munshianaAccount.id,
          accountName: munshianaAccount.accountName,
          accountNumber: munshianaAccount.accountNumber,
          description: 'Munshiana fees - Voucher ${voucher.voucherNumber}',
          debitAmount: voucher.munshianaFees,
          creditAmount: 0.0,
          createdAt: DateTime.now(),
        ),
        // Credit cash account
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: munshianaCashAccount.id,
          accountName: munshianaCashAccount.accountName,
          accountNumber: munshianaCashAccount.accountNumber,
          description: 'Munshiana payment - Voucher ${voucher.voucherNumber}',
          debitAmount: 0.0,
          creditAmount: voucher.munshianaFees,
          createdAt: DateTime.now(),
        ),
      ];

      return JournalEntryModel(
        id: '',
        entryNumber: '',
        entryDate: voucher.belongsToDate ?? voucher.createdAt,
        description: 'Munshiana payment - Voucher ${voucher.voucherNumber}',
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: lines,
        totalDebits: voucher.munshianaFees,
        totalCredits: voucher.munshianaFees,
        referenceNumber: voucher.voucherNumber,
        sourceTransactionId: voucher.voucherNumber,
        sourceTransactionType: 'voucher_munshiana',
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: uid,
      );
    } catch (e) {
      print('Error generating munshiana entry: $e');
      return null;
    }
  }

  Future<JournalEntryModel?> _generateNetProfitEntry({
    required VoucherModel voucher,
    required double netProfit,
    required String uid,
    required String createdBy,
  }) async {
    try {
      // Get revenue account
      final revenueAccount = await _getAccountByType(
        AccountType.serviceRevenue,
        'Freight Revenue',
        uid,
      );
      if (revenueAccount == null) return null;

      // Get cash account (assuming company's main cash account)
      final cashAccount = await _getAccountByType(
        AccountType.cash,
        'Cash',
        uid,
      );
      if (cashAccount == null) return null;

      final lines = <JournalEntryLineModel>[];

      if (netProfit > 0) {
        // Profit - debit cash, credit revenue
        lines.addAll([
          JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: cashAccount.id,
            accountName: cashAccount.accountName,
            accountNumber: cashAccount.accountNumber,
            description: 'Net profit - Voucher ${voucher.voucherNumber}',
            debitAmount: netProfit,
            creditAmount: 0.0,
            createdAt: DateTime.now(),
          ),
          JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: revenueAccount.id,
            accountName: revenueAccount.accountName,
            accountNumber: revenueAccount.accountNumber,
            description: 'Freight revenue - Voucher ${voucher.voucherNumber}',
            debitAmount: 0.0,
            creditAmount: netProfit,
            createdAt: DateTime.now(),
          ),
        ]);
      } else {
        // Loss - debit expense, credit cash
        final lossAccount = await _getAccountByType(
          AccountType.operatingExpenses,
          'Freight Loss',
          uid,
        );
        if (lossAccount == null) return null;

        final lossAmount = netProfit.abs();
        lines.addAll([
          JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: lossAccount.id,
            accountName: lossAccount.accountName,
            accountNumber: lossAccount.accountNumber,
            description: 'Net loss - Voucher ${voucher.voucherNumber}',
            debitAmount: lossAmount,
            creditAmount: 0.0,
            createdAt: DateTime.now(),
          ),
          JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: cashAccount.id,
            accountName: cashAccount.accountName,
            accountNumber: cashAccount.accountNumber,
            description: 'Loss coverage - Voucher ${voucher.voucherNumber}',
            debitAmount: 0.0,
            creditAmount: lossAmount,
            createdAt: DateTime.now(),
          ),
        ]);
      }

      final totalAmount = netProfit.abs();
      return JournalEntryModel(
        id: '',
        entryNumber: '',
        entryDate: voucher.belongsToDate ?? voucher.createdAt,
        description:
            'Net ${netProfit > 0 ? "profit" : "loss"} - Voucher ${voucher.voucherNumber}',
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: lines,
        totalDebits: totalAmount,
        totalCredits: totalAmount,
        referenceNumber: voucher.voucherNumber,
        sourceTransactionId: voucher.voucherNumber,
        sourceTransactionType: 'voucher_net_profit',
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: uid,
      );
    } catch (e) {
      print('Error generating net profit entry: $e');
      return null;
    }
  }

  // Loan-specific helper methods
  Future<JournalEntryModel?> _generateLoanDisbursementEntry({
    required LoanModel loan,
    required String uid,
    required String createdBy,
  }) async {
    try {
      // Get cash account (money going out)
      final cashAccount = await _getAccountByName(loan.fromAccountName, uid);
      if (cashAccount == null) return null;

      // Get loan receivable account (money owed to us)
      final loanReceivableAccount = await _getAccountByType(
        AccountType.currentAssets,
        'Loans Receivable',
        uid,
      );
      if (loanReceivableAccount == null) return null;

      final lines = <JournalEntryLineModel>[
        // Debit loan receivable
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: loanReceivableAccount.id,
          accountName: loanReceivableAccount.accountName,
          accountNumber: loanReceivableAccount.accountNumber,
          description: 'Loan disbursed to ${loan.requestedByName}',
          debitAmount: loan.amount,
          creditAmount: 0.0,
          createdAt: DateTime.now(),
        ),
        // Credit cash account
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: cashAccount.id,
          accountName: cashAccount.accountName,
          accountNumber: cashAccount.accountNumber,
          description: 'Loan payment to ${loan.requestedByName}',
          debitAmount: 0.0,
          creditAmount: loan.amount,
          createdAt: DateTime.now(),
        ),
      ];

      return JournalEntryModel(
        id: '',
        entryNumber: '',
        entryDate: loan.approvalDate ?? DateTime.now(),
        description: 'Loan disbursement to ${loan.requestedByName}',
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: lines,
        totalDebits: loan.amount,
        totalCredits: loan.amount,
        referenceNumber: loan.id,
        sourceTransactionId: loan.id,
        sourceTransactionType: 'loan_disbursement',
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: uid,
      );
    } catch (e) {
      print('Error generating loan disbursement entry: $e');
      return null;
    }
  }

  Future<JournalEntryModel?> _generateLoanRepaymentEntry({
    required LoanModel loan,
    required String uid,
    required String createdBy,
  }) async {
    try {
      // Get cash account (money coming in)
      final cashAccount = await _getAccountByName(loan.toAccountName, uid);
      if (cashAccount == null) return null;

      // Get loan receivable account (reducing what's owed to us)
      final loanReceivableAccount = await _getAccountByType(
        AccountType.currentAssets,
        'Loans Receivable',
        uid,
      );
      if (loanReceivableAccount == null) return null;

      final lines = <JournalEntryLineModel>[
        // Debit cash account
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: cashAccount.id,
          accountName: cashAccount.accountName,
          accountNumber: cashAccount.accountNumber,
          description: 'Loan repayment from ${loan.requestedByName}',
          debitAmount: loan.amount,
          creditAmount: 0.0,
          createdAt: DateTime.now(),
        ),
        // Credit loan receivable
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: loanReceivableAccount.id,
          accountName: loanReceivableAccount.accountName,
          accountNumber: loanReceivableAccount.accountNumber,
          description: 'Loan repayment from ${loan.requestedByName}',
          debitAmount: 0.0,
          creditAmount: loan.amount,
          createdAt: DateTime.now(),
        ),
      ];

      return JournalEntryModel(
        id: '',
        entryNumber: '',
        entryDate: loan.repaymentDate ?? DateTime.now(),
        description: 'Loan repayment from ${loan.requestedByName}',
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: lines,
        totalDebits: loan.amount,
        totalCredits: loan.amount,
        referenceNumber: loan.id,
        sourceTransactionId: loan.id,
        sourceTransactionType: 'loan_repayment',
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: uid,
      );
    } catch (e) {
      print('Error generating loan repayment entry: $e');
      return null;
    }
  }
}
