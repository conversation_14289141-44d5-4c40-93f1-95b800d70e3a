class FromPlaceModel {
  final String placeId;
  final String fromPlace;
  final double kilometers;

  FromPlaceModel({
    required this.placeId,
    required this.fromPlace,
    required this.kilometers,
  });

  Map<String, dynamic> toJson() {
    return {
      'placeId': placeId,
      'fromPlace': fromPlace,
      'kilometers': kilometers,
    };
  }

  factory FromPlaceModel.fromJson(Map<String, dynamic> json) {
    return FromPlaceModel(
      placeId: json['placeId'] ?? '',
      fromPlace: json['fromPlace'] ?? '',
      kilometers: json['kilometers']?.toDouble() ?? 0.0,
    );
  }

  @override
  String toString() {
    return 'FromPlaceModel{placeId: $placeId, fromPlace: $fromPlace, kilometers: $kilometers}';
  }
}
