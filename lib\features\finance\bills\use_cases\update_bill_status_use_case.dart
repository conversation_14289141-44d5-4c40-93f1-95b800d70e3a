import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import '../repositories/bill_repository.dart';

class UpdateBillStatusUseCase {
  final BillRepository _billRepository;

  UpdateBillStatusUseCase(this._billRepository);

  /// Executes the use case to update a bill's status.
  Future<Either<FailureObj, SuccessObj>> call({
    required String uid,
    required String billId,
    required String newStatus,
  }) async {
    // Validate inputs
    if (uid.isEmpty) {
      return Left(FailureObj(
        code: 'invalid-company-id',
        message: 'Company ID cannot be empty.',
      ));
    }

    if (billId.isEmpty) {
      return Left(FailureObj(
        code: 'invalid-bill-id',
        message: 'Bill ID must not be empty.',
      ));
    }

    // Call the repository
    return await _billRepository.updateBillStatus(
      uid: uid,
      billId: billId,
      newStatus: newStatus,
    );
  }
}
