import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../controllers/financial_dashboard_controller.dart';

/// Widget displaying account balances summary
class AccountBalanceWidget extends StatelessWidget {
  const AccountBalanceWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<FinancialDashboardController>();
    final currencyFormatter =
        NumberFormat.currency(symbol: 'PKR ', decimalDigits: 0);

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Account Balances',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.refresh, size: 20),
                      onPressed: controller.refreshDashboard,
                      tooltip: 'Refresh Balances',
                    ),
                    IconButton(
                      icon: const Icon(Icons.list, size: 20),
                      onPressed: () =>
                          controller.navigateToReport('chart_of_accounts'),
                      tooltip: 'View All Accounts',
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            Obx(() {
              final balances = controller.accountBalances;
              if (balances.isEmpty) {
                return const Center(
                  child: Padding(
                    padding: EdgeInsets.all(32),
                    child: Column(
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('Loading account balances...'),
                      ],
                    ),
                  ),
                );
              }

              // Group balances by category
              final groupedBalances = <String, List<dynamic>>{};
              for (final balance in balances) {
                if (!groupedBalances.containsKey(balance.category)) {
                  groupedBalances[balance.category] = [];
                }
                groupedBalances[balance.category]!.add(balance);
              }

              return Column(
                children: [
                  // Category summaries
                  ...groupedBalances.entries.map((entry) {
                    final category = entry.key;
                    final categoryBalances = entry.value;
                    final totalBalance = categoryBalances.fold<double>(
                      0.0,
                      (sum, balance) => sum + balance.balance,
                    );

                    return _buildCategoryCard(
                      category,
                      totalBalance,
                      categoryBalances.length,
                      currencyFormatter,
                      context,
                      controller,
                    );
                  }).toList(),

                  const SizedBox(height: 16),

                  // Top accounts by balance
                  Text(
                    'Top Accounts by Balance',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                  const SizedBox(height: 8),

                  // Show top 5 accounts
                  ...() {
                    final sortedBalances = balances
                        .where((balance) => balance.balance.abs() > 0)
                        .toList();
                    sortedBalances.sort(
                        (a, b) => b.balance.abs().compareTo(a.balance.abs()));
                    return sortedBalances
                        .take(5)
                        .map((balance) => _buildAccountTile(
                              balance,
                              currencyFormatter,
                              context,
                              controller,
                            ));
                  }(),
                ],
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryCard(
    String category,
    double totalBalance,
    int accountCount,
    NumberFormat currencyFormatter,
    BuildContext context,
    FinancialDashboardController controller,
  ) {
    Color categoryColor;
    IconData categoryIcon;

    switch (category.toLowerCase()) {
      case 'assets':
        categoryColor = Colors.green;
        categoryIcon = Icons.account_balance_wallet;
        break;
      case 'liabilities':
        categoryColor = Colors.red;
        categoryIcon = Icons.credit_card;
        break;
      case 'equity':
        categoryColor = Colors.blue;
        categoryIcon = Icons.business;
        break;
      case 'revenue':
        categoryColor = Colors.teal;
        categoryIcon = Icons.trending_up;
        break;
      case 'expenses':
        categoryColor = Colors.orange;
        categoryIcon = Icons.trending_down;
        break;
      default:
        categoryColor = Colors.grey;
        categoryIcon = Icons.account_balance;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: categoryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: categoryColor.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(
            categoryIcon,
            color: categoryColor,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  category,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
                Text(
                  '$accountCount accounts',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                ),
              ],
            ),
          ),
          Text(
            currencyFormatter.format(totalBalance),
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: categoryColor,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountTile(
    dynamic balance,
    NumberFormat currencyFormatter,
    BuildContext context,
    FinancialDashboardController controller,
  ) {
    final isPositive = balance.balance >= 0;
    final balanceColor = isPositive ? Colors.green : Colors.red;

    return ListTile(
      dense: true,
      leading: CircleAvatar(
        radius: 16,
        backgroundColor: balanceColor.withOpacity(0.1),
        child: Text(
          balance.accountNumber.substring(0, 1),
          style: TextStyle(
            color: balanceColor,
            fontWeight: FontWeight.bold,
            fontSize: 12,
          ),
        ),
      ),
      title: Text(
        balance.accountName,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
      ),
      subtitle: Text(
        '${balance.accountNumber} • ${balance.accountType}',
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
      ),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            currencyFormatter.format(balance.balance),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: balanceColor,
                ),
          ),
          if (!balance.isActive)
            Text(
              'Inactive',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey,
                    fontStyle: FontStyle.italic,
                  ),
            ),
        ],
      ),
      onTap: () => controller.navigateToAccountDetails(balance.accountId),
    );
  }
}
