import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../core/utils/app_constants/firebase/collection_names.dart';
import '../../core/services/accounting_validation_service.dart';
import '../../models/finance/journal_entry_model.dart';
import '../firebase_services.dart';

class JournalEntryFirebaseService {
  late FirebaseFirestore _firestore;
  final FirebaseServices _firebaseServices = FirebaseServices();

  // Get current user's UID or return 'anonymous' if not authenticated
  String get _uid => FirebaseAuth.instance.currentUser?.uid ?? 'anonymous';

  JournalEntryFirebaseService() {
    _firestore = FirebaseFirestore.instance;
  }

  /// Validate double-entry rules before creating/updating
  ValidationResult validateDoubleEntry(JournalEntryModel journalEntry) {
    log('Validating journal entry: ${journalEntry.description}');

    // Use comprehensive validation service
    final validationResult =
        AccountingValidationService.validateJournalEntry(journalEntry);

    // Log validation results
    if (!validationResult.isValid) {
      log('Journal entry validation failed:');
      for (final error in validationResult.errors) {
        log('  ERROR: $error');
      }
    }

    if (validationResult.hasWarnings) {
      log('Journal entry validation warnings:');
      for (final warning in validationResult.warnings) {
        log('  WARNING: $warning');
      }
    }

    return validationResult;
  }

  /// Legacy method for backward compatibility
  bool validateDoubleEntryLegacy(JournalEntryModel journalEntry) {
    final result = validateDoubleEntry(journalEntry);
    return result.isValid;
  }

  /// Create a new journal entry with lines
  Future<void> createJournalEntry(JournalEntryModel journalEntry) async {
    log('Creating journal entry: ${journalEntry.description}');
    try {
      // Validate double-entry rules
      final validationResult = validateDoubleEntry(journalEntry);
      if (!validationResult.isValid) {
        final errorMessage = validationResult.errors.join('; ');
        throw Exception('Journal entry validation failed: $errorMessage');
      }

      // Use a batch to ensure atomicity
      final batch = _firestore.batch();

      // Create journal entry document
      final entryRef =
          _firestore.collection(AppCollection.journalEntriesCollection).doc();
      final entryId = entryRef.id;

      final entryData = journalEntry.toFirestore();
      entryData['id'] = entryId;
      entryData['uid'] = _uid;

      batch.set(entryRef, entryData);

      // Create journal entry lines
      for (final line in journalEntry.lines) {
        final lineRef = _firestore
            .collection(AppCollection.journalEntryLinesCollection)
            .doc();
        final lineId = lineRef.id;

        final lineData = line.toFirestore();
        lineData['id'] = lineId;
        lineData['journalEntryId'] = entryId;

        batch.set(lineRef, lineData);
      }

      await batch.commit();
      log('Successfully created journal entry: $entryId');
    } catch (e) {
      log('Error creating journal entry: $e');
      rethrow;
    }
  }

  /// Get all journal entries for the current user
  Future<List<JournalEntryModel>> getJournalEntries() async {
    log('🔍 [DEBUG] Starting to fetch journal entries from Firestore');
    log('🔍 [DEBUG] Current UID: $_uid');
    log('🔍 [DEBUG] Collection: ${AppCollection.journalEntriesCollection}');

    // First, let's check if there are ANY documents in the collection
    try {
      final allDocsSnapshot = await _firestore
          .collection(AppCollection.journalEntriesCollection)
          .limit(5)
          .get();
      log('🔍 [DEBUG] Total documents in collection (first 5): ${allDocsSnapshot.docs.length}');

      for (final doc in allDocsSnapshot.docs) {
        final data = doc.data();
        log('🔍 [DEBUG] Document ${doc.id}: uid=${data['uid']}, entryNumber=${data['entryNumber']}');
      }
    } catch (e) {
      log('❌ [DEBUG] Error checking all documents: $e');
    }

    try {
      final snapshot = await _firestore
          .collection(AppCollection.journalEntriesCollection)
          .where('uid', isEqualTo: _uid)
          .orderBy('entryDate', descending: true)
          .get();

      log('🔍 [DEBUG] Firestore query executed successfully');
      log('🔍 [DEBUG] Raw documents found: ${snapshot.docs.length}');

      final entries = <JournalEntryModel>[];

      for (final doc in snapshot.docs) {
        final entryData = doc.data();
        log('🔍 [DEBUG] Processing document: ${doc.id}');
        log('🔍 [DEBUG] Document data: $entryData');

        // Get lines for this journal entry
        try {
          final linesSnapshot = await _firestore
              .collection(AppCollection.journalEntryLinesCollection)
              .where('journalEntryId', isEqualTo: doc.id)
              .orderBy('createdAt')
              .get();

          log('🔍 [DEBUG] Lines found for entry ${doc.id}: ${linesSnapshot.docs.length}');

          final lines = linesSnapshot.docs
              .map((lineDoc) => JournalEntryLineModel.fromJson(lineDoc.data()))
              .toList();

          // Create journal entry with lines
          final entry = JournalEntryModel.fromJson({
            ...entryData,
            'id': doc.id,
            'lines': lines.map((line) => line.toJson()).toList(),
          });

          entries.add(entry);
          log('🔍 [DEBUG] Successfully added entry: ${entry.entryNumber}');
        } catch (lineError) {
          log('⚠️ [DEBUG] Error loading lines for entry ${doc.id}, using fallback: $lineError');

          // Fallback: Create entry without lines if line query fails
          final entry = JournalEntryModel.fromJson({
            ...entryData,
            'id': doc.id,
            'lines': [], // Empty lines as fallback
          });

          entries.add(entry);
          log('🔍 [DEBUG] Added entry with fallback (no lines): ${entry.entryNumber}');
        }
      }

      log('✅ [DEBUG] Successfully fetched ${entries.length} journal entries');
      return entries;
    } catch (e) {
      log('❌ [DEBUG] Error fetching journal entries: $e');

      // If compound query fails due to missing index, try fallback query
      if (e.toString().contains('index') ||
          e.toString().contains('FAILED_PRECONDITION')) {
        log('🔄 [DEBUG] Attempting fallback query without compound index...');
        return await _getFallbackJournalEntries();
      }

      rethrow;
    }
  }

  /// Fallback method to get journal entries without compound indexes
  Future<List<JournalEntryModel>> _getFallbackJournalEntries() async {
    try {
      log('🔄 [DEBUG] Using fallback query for journal entries');

      // Simple query with just uid filter
      final snapshot = await _firestore
          .collection(AppCollection.journalEntriesCollection)
          .where('uid', isEqualTo: _uid)
          .get();

      log('🔍 [DEBUG] Fallback query found ${snapshot.docs.length} documents');

      var entries = <JournalEntryModel>[];

      for (final doc in snapshot.docs) {
        try {
          final entryData = doc.data();

          // Simple lines query without orderBy
          final linesSnapshot = await _firestore
              .collection(AppCollection.journalEntryLinesCollection)
              .where('journalEntryId', isEqualTo: doc.id)
              .get();

          final lines = linesSnapshot.docs
              .map((lineDoc) => JournalEntryLineModel.fromJson(lineDoc.data()))
              .toList();

          // Sort lines in memory by createdAt
          lines.sort((a, b) => a.createdAt.compareTo(b.createdAt));

          final entry = JournalEntryModel.fromJson({
            ...entryData,
            'id': doc.id,
            'lines': lines.map((line) => line.toJson()).toList(),
          });

          entries.add(entry);
        } catch (e) {
          log('⚠️ [DEBUG] Error parsing journal entry document ${doc.id}: $e');
        }
      }

      // Sort entries in memory by entryDate (descending)
      entries.sort((a, b) => b.entryDate.compareTo(a.entryDate));

      log('✅ [DEBUG] Fallback query successful: ${entries.length} entries');
      return entries;
    } catch (e) {
      log('❌ [DEBUG] Fallback query also failed: $e');
      return [];
    }
  }

  /// Get journal entries by date range
  Future<List<JournalEntryModel>> getJournalEntriesByDateRange(
      DateTime startDate, DateTime endDate) async {
    log('Fetching journal entries by date range: $startDate to $endDate');
    try {
      final snapshot = await _firestore
          .collection(AppCollection.journalEntriesCollection)
          .where('uid', isEqualTo: _uid)
          .where('entryDate',
              isGreaterThanOrEqualTo: startDate.millisecondsSinceEpoch)
          .where('entryDate',
              isLessThanOrEqualTo: endDate.millisecondsSinceEpoch)
          .orderBy('entryDate', descending: true)
          .get();

      final entries = <JournalEntryModel>[];

      for (final doc in snapshot.docs) {
        final entryData = doc.data();

        // Get lines for this journal entry
        final linesSnapshot = await _firestore
            .collection(AppCollection.journalEntryLinesCollection)
            .where('journalEntryId', isEqualTo: doc.id)
            .orderBy('createdAt')
            .get();

        final lines = linesSnapshot.docs
            .map((lineDoc) => JournalEntryLineModel.fromJson(lineDoc.data()))
            .toList();

        // Create journal entry with lines
        final entry = JournalEntryModel.fromJson({
          ...entryData,
          'lines': lines.map((line) => line.toJson()).toList(),
        });

        entries.add(entry);
      }

      log('Successfully fetched ${entries.length} journal entries for date range');
      return entries;
    } catch (e) {
      log('Error fetching journal entries by date range: $e');

      // If compound query fails due to missing index, try fallback query
      if (e.toString().contains('index') ||
          e.toString().contains('FAILED_PRECONDITION')) {
        log('🔄 Attempting fallback date range query without compound index...');
        return await _getFallbackJournalEntriesByDateRange(startDate, endDate);
      }

      rethrow;
    }
  }

  /// Fallback method for date range queries without compound indexes
  Future<List<JournalEntryModel>> _getFallbackJournalEntriesByDateRange(
      DateTime startDate, DateTime endDate) async {
    try {
      log('🔄 Using fallback query for journal entries by date range');

      // Simple query with just uid filter
      final snapshot = await _firestore
          .collection(AppCollection.journalEntriesCollection)
          .where('uid', isEqualTo: _uid)
          .get();

      var entries = <JournalEntryModel>[];

      for (final doc in snapshot.docs) {
        try {
          final entryData = doc.data();
          final entryDate = (entryData['entryDate'] as Timestamp).toDate();

          // Filter by date range in memory
          if (entryDate.isAfter(startDate.subtract(const Duration(days: 1))) &&
              entryDate.isBefore(endDate.add(const Duration(days: 1)))) {
            // Simple lines query
            final linesSnapshot = await _firestore
                .collection(AppCollection.journalEntryLinesCollection)
                .where('journalEntryId', isEqualTo: doc.id)
                .get();

            final lines = linesSnapshot.docs
                .map(
                    (lineDoc) => JournalEntryLineModel.fromJson(lineDoc.data()))
                .toList();

            lines.sort((a, b) => a.createdAt.compareTo(b.createdAt));

            final entry = JournalEntryModel.fromJson({
              ...entryData,
              'id': doc.id,
              'lines': lines.map((line) => line.toJson()).toList(),
            });

            entries.add(entry);
          }
        } catch (e) {
          log('⚠️ Error parsing journal entry document ${doc.id}: $e');
        }
      }

      // Sort entries in memory by entryDate (descending)
      entries.sort((a, b) => b.entryDate.compareTo(a.entryDate));

      log('✅ Fallback date range query successful: ${entries.length} entries');
      return entries;
    } catch (e) {
      log('❌ Fallback date range query also failed: $e');
      return [];
    }
  }

  /// Get journal entry by ID
  Future<JournalEntryModel?> getJournalEntryById(String entryId) async {
    log('Fetching journal entry by ID: $entryId');
    try {
      final doc = await _firestore
          .collection(AppCollection.journalEntriesCollection)
          .doc(entryId)
          .get();

      if (!doc.exists) {
        log('Journal entry not found: $entryId');
        return null;
      }

      final entryData = doc.data() as Map<String, dynamic>;

      // Verify the entry belongs to the current user
      if (entryData['uid'] != _uid) {
        log('Journal entry does not belong to current user: $entryId');
        return null;
      }

      // Get lines for this journal entry
      final linesSnapshot = await _firestore
          .collection(AppCollection.journalEntryLinesCollection)
          .where('journalEntryId', isEqualTo: entryId)
          .orderBy('createdAt')
          .get();

      final lines = linesSnapshot.docs
          .map((lineDoc) => JournalEntryLineModel.fromJson(lineDoc.data()))
          .toList();

      // Create journal entry with lines
      final entry = JournalEntryModel.fromJson({
        ...entryData,
        'lines': lines.map((line) => line.toJson()).toList(),
      });

      log('Successfully fetched journal entry: ${entry.description}');
      return entry;
    } catch (e) {
      log('Error fetching journal entry by ID: $e');
      rethrow;
    }
  }

  /// Update journal entry status
  Future<void> updateJournalEntryStatus(
      String entryId, JournalEntryStatus status) async {
    log('Updating journal entry status: $entryId to ${status.displayName}');
    try {
      await _firestore
          .collection(AppCollection.journalEntriesCollection)
          .doc(entryId)
          .update({
        'status': status.name,
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      });

      log('Successfully updated journal entry status: $entryId');
    } catch (e) {
      log('Error updating journal entry status: $e');
      rethrow;
    }
  }

  /// Post a journal entry (change status from draft to posted)
  Future<void> postJournalEntry(String entryId) async {
    await updateJournalEntryStatus(entryId, JournalEntryStatus.posted);
  }

  /// Reverse a journal entry
  Future<void> reverseJournalEntry(
      String entryId, JournalEntryModel reversalEntry) async {
    log('Reversing journal entry: $entryId');
    try {
      final batch = _firestore.batch();

      // Update original entry status to reversed
      final originalEntryRef = _firestore
          .collection(AppCollection.journalEntriesCollection)
          .doc(entryId);

      batch.update(originalEntryRef, {
        'status': JournalEntryStatus.reversed.name,
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      });

      // Create reversal entry
      final reversalEntryRef =
          _firestore.collection(AppCollection.journalEntriesCollection).doc();
      final reversalEntryId = reversalEntryRef.id;

      final reversalData = reversalEntry.toJson();
      reversalData['id'] = reversalEntryId;
      reversalData['uid'] = _uid;
      reversalData['reversalOfEntryId'] = entryId;

      batch.set(reversalEntryRef, reversalData);

      // Create reversal entry lines
      for (final line in reversalEntry.lines) {
        final lineRef = _firestore
            .collection(AppCollection.journalEntryLinesCollection)
            .doc();
        final lineId = lineRef.id;

        final lineData = line.toJson();
        lineData['id'] = lineId;
        lineData['journalEntryId'] = reversalEntryId;

        batch.set(lineRef, lineData);
      }

      await batch.commit();
      log('Successfully reversed journal entry: $entryId');
    } catch (e) {
      log('Error reversing journal entry: $e');
      rethrow;
    }
  }

  /// Delete a journal entry (only drafts can be deleted)
  Future<void> deleteJournalEntry(String entryId) async {
    log('Deleting journal entry: $entryId');
    try {
      final batch = _firestore.batch();

      // Delete journal entry lines first
      final linesSnapshot = await _firestore
          .collection(AppCollection.journalEntryLinesCollection)
          .where('journalEntryId', isEqualTo: entryId)
          .get();

      for (final lineDoc in linesSnapshot.docs) {
        batch.delete(lineDoc.reference);
      }

      // Delete journal entry
      final entryRef = _firestore
          .collection(AppCollection.journalEntriesCollection)
          .doc(entryId);

      batch.delete(entryRef);

      await batch.commit();
      log('Successfully deleted journal entry: $entryId');
    } catch (e) {
      log('Error deleting journal entry: $e');
      rethrow;
    }
  }

  /// Get next journal entry number
  Future<String> getNextJournalEntryNumber() async {
    log('Getting next journal entry number');
    try {
      final nextNumber = await _firebaseServices.getNextAutoIncrementValue(
        collectionName: AppCollection.journalEntriesCollection,
        fieldName: 'entryNumber',
      );

      final entryNumber = 'JE${nextNumber.toString().padLeft(6, '0')}';
      log('Next journal entry number: $entryNumber');
      return entryNumber;
    } catch (e) {
      log('Error getting next journal entry number: $e');
      rethrow;
    }
  }

  /// Get journal entries by account
  Future<List<JournalEntryLineModel>> getJournalEntriesByAccount(
      String accountId) async {
    log('Fetching journal entries by account: $accountId');
    try {
      final snapshot = await _firestore
          .collection(AppCollection.journalEntryLinesCollection)
          .where('accountId', isEqualTo: accountId)
          .orderBy('createdAt', descending: true)
          .get();

      final lines = snapshot.docs
          .map((doc) => JournalEntryLineModel.fromFirestore(doc.data()))
          .toList();

      log('Successfully fetched ${lines.length} journal entry lines for account');
      return lines;
    } catch (e) {
      log('Error fetching journal entries by account: $e');
      rethrow;
    }
  }

  /// Get journal entries for account with pagination and filtering
  Future<List<JournalEntryModel>> getJournalEntriesForAccount({
    required String accountId,
    required String uid,
    int limit = 25,
    QueryDocumentSnapshot? lastDocument,
    DateTime? startDate,
    DateTime? endDate,
    List<JournalEntryStatus>? statusFilter,
  }) async {
    log('Fetching journal entries for account: $accountId with pagination');
    try {
      // First, get journal entry IDs that contain lines for this account
      Query lineQuery = _firestore
          .collection(AppCollection.journalEntryLinesCollection)
          .where('accountId', isEqualTo: accountId);

      final lineSnapshot = await lineQuery.get();
      final journalEntryIds = lineSnapshot.docs
          .map((doc) {
            final data = doc.data() as Map<String, dynamic>;
            return data['journalEntryId'] as String;
          })
          .toSet()
          .toList();

      if (journalEntryIds.isEmpty) {
        log('No journal entries found for account: $accountId');
        return [];
      }

      // Now get the actual journal entries
      Query entryQuery = _firestore
          .collection(AppCollection.journalEntriesCollection)
          .where('uid', isEqualTo: uid)
          .where(FieldPath.documentId, whereIn: journalEntryIds);

      // Apply date filters
      if (startDate != null) {
        entryQuery = entryQuery.where('entryDate',
            isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
      }
      if (endDate != null) {
        entryQuery = entryQuery.where('entryDate',
            isLessThanOrEqualTo: Timestamp.fromDate(endDate));
      }

      // Apply status filter
      if (statusFilter != null && statusFilter.isNotEmpty) {
        final statusStrings = statusFilter.map((s) => s.name).toList();
        entryQuery = entryQuery.where('status', whereIn: statusStrings);
      }

      // Apply ordering and pagination
      entryQuery = entryQuery.orderBy('entryDate', descending: true);

      if (lastDocument != null) {
        entryQuery = entryQuery.startAfterDocument(lastDocument);
      }

      entryQuery = entryQuery.limit(limit);

      final snapshot = await entryQuery.get();

      // Load journal entries with their lines from separate collection
      final entries = <JournalEntryModel>[];

      for (final doc in snapshot.docs) {
        final entryData = doc.data() as Map<String, dynamic>;

        // Get lines for this journal entry
        final linesSnapshot = await _firestore
            .collection(AppCollection.journalEntryLinesCollection)
            .where('journalEntryId', isEqualTo: doc.id)
            .orderBy('createdAt')
            .get();

        final lines = linesSnapshot.docs
            .map((lineDoc) => JournalEntryLineModel.fromJson(lineDoc.data()))
            .toList();

        // Create journal entry with lines
        final entry = JournalEntryModel.fromJson({
          ...entryData,
          'id': doc.id,
          'lines': lines.map((line) => line.toJson()).toList(),
        });

        entries.add(entry);
      }

      log('Successfully fetched ${entries.length} journal entries for account with ${entries.fold(0, (total, entry) => total + entry.lines.length)} total lines');
      return entries;
    } catch (e) {
      log('Error fetching journal entries for account: $e');
      rethrow;
    }
  }

  /// Calculate account balance from posted journal entries
  Future<double> calculateAccountBalance(String accountId) async {
    log('Calculating account balance for: $accountId');
    try {
      // Get all posted journal entry lines for this account
      final linesSnapshot = await _firestore
          .collection(AppCollection.journalEntryLinesCollection)
          .where('accountId', isEqualTo: accountId)
          .get();

      double totalDebits = 0.0;
      double totalCredits = 0.0;

      for (final lineDoc in linesSnapshot.docs) {
        final lineData = lineDoc.data();

        // Check if the parent journal entry is posted
        final journalEntryId = lineData['journalEntryId'];
        final entryDoc = await _firestore
            .collection(AppCollection.journalEntriesCollection)
            .doc(journalEntryId)
            .get();

        if (entryDoc.exists) {
          final entryData = entryDoc.data() as Map<String, dynamic>;
          final status =
              JournalEntryStatus.fromString(entryData['status'] ?? 'draft');

          // Only include posted entries in balance calculation
          if (status == JournalEntryStatus.posted) {
            final line = JournalEntryLineModel.fromFirestore(lineData);
            totalDebits += line.debitAmount;
            totalCredits += line.creditAmount;
          }
        }
      }

      final balance = totalDebits - totalCredits;
      log('Account balance calculated: Debits=$totalDebits, Credits=$totalCredits, Balance=$balance');
      return balance;
    } catch (e) {
      log('Error calculating account balance: $e');
      rethrow;
    }
  }

  /// Stream to listen for real-time updates to journal entries
  Stream<List<JournalEntryModel>> listenToJournalEntries() {
    try {
      return _firestore
          .collection(AppCollection.journalEntriesCollection)
          .where('uid', isEqualTo: _uid)
          .orderBy('entryDate', descending: true)
          .snapshots()
          .asyncMap((snapshot) async {
        final entries = <JournalEntryModel>[];

        for (final doc in snapshot.docs) {
          final entryData = doc.data();

          // Get lines for this journal entry
          final linesSnapshot = await _firestore
              .collection(AppCollection.journalEntryLinesCollection)
              .where('journalEntryId', isEqualTo: doc.id)
              .orderBy('createdAt')
              .get();

          final lines = linesSnapshot.docs
              .map((lineDoc) => JournalEntryLineModel.fromJson(lineDoc.data()))
              .toList();

          // Create journal entry with lines
          final entry = JournalEntryModel.fromJson({
            ...entryData,
            'lines': lines.map((line) => line.toJson()).toList(),
          });

          entries.add(entry);
        }

        return entries;
      });
    } catch (e) {
      log('Error listening to journal entries: $e', error: e);
      return Stream.value([]);
    }
  }
}
