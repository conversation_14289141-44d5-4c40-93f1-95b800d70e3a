import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/core/utils/mixins/pagination_mixin.dart';
import 'package:logestics/core/utils/mixins/auto_refresh_mixin.dart';
import 'package:logestics/models/finance/bill_model.dart';

import '../../../../company/presentation/conrollers/company_controller.dart';
import '../../use_cases/delete_bill_use_case.dart';
import '../../use_cases/update_bill_status_use_case.dart';
import '../../use_cases/get_bills_use_case.dart';

class BillsListController extends GetxController
    with PaginationMixin, AutoRefreshMixin {
  final DeleteBillUseCase deleteBillUseCase;
  final UpdateBillStatusUseCase updateBillStatusUseCase;
  final GetBillsUseCase getBillsUseCase;

  BillsListController({
    required this.deleteBillUseCase,
    required this.updateBillStatusUseCase,
    required this.getBillsUseCase,
  });

  late final CompanyController companyController;
  final selectedStatus = RxnString();
  final searchQuery = ''.obs;
  final filteredBills = <BillModel>[].obs;
  final allBills = <BillModel>[].obs;
  final searchController = TextEditingController();
  final isLoading = false.obs;
  final isInitialLoading = true.obs;
  final isRefreshing = false.obs;
  bool hasInitialized = false;

  // Date filtering
  final dateFilterType =
      'all'.obs; // all, today, yesterday, week, month, custom
  final selectedStartDate = DateTime.now().obs;
  final selectedEndDate = DateTime.now().obs;

  @override
  void onInit() {
    super.onInit();

    try {
      // Initialize CompanyController with error handling
      companyController = Get.find<CompanyController>();
      log('BillsListController: CompanyController initialized successfully');
    } catch (e) {
      log('BillsListController: Failed to initialize CompanyController: $e');
      SnackbarUtils.showError(
        'Error',
        'Failed to initialize company data. Please restart the app.',
      );
      return;
    }

    searchController.addListener(_onSearchChanged);
    _loadBills();
  }

  @override
  void onReady() {
    super.onReady();
    // Refresh data when controller is ready
    refreshData();
  }

  /// Check if any loading operation is in progress
  bool get isAnyLoading =>
      isLoading.value || isInitialLoading.value || isRefreshing.value;

  /// Implementation of AutoRefreshMixin.refreshData
  @override
  Future<void> refreshData() async {
    isRefreshing.value = true;
    isLoading.value = true;
    try {
      await _loadBills();
    } finally {
      // Ensure loading states are reset
      isLoading.value = false;
      isRefreshing.value = false;
      isInitialLoading.value = false;
    }
  }

  /// Method to handle when the bills screen becomes active
  void onScreenActivated() {
    // Reset initial loading state if we have data
    if (allBills.isNotEmpty) {
      isInitialLoading.value = false;
    }

    // Trigger filtering to ensure UI is updated
    _filterBills();
    update();
  }

  /// Manual refresh method for pull-to-refresh functionality
  Future<void> manualRefresh() async {
    await refreshData();
  }

  /// Validate that all required dependencies are available
  bool _validateDependencies() {
    try {
      // Check if CompanyController is available
      if (!Get.isRegistered<CompanyController>()) {
        log('BillsListController: CompanyController is not registered');
        SnackbarUtils.showError(
          'Error',
          'Company controller not available. Please restart the app.',
        );
        return false;
      }

      // Use cases are required final fields, so they should always be available
      log('BillsListController: All dependencies validated successfully');

      return true;
    } catch (e) {
      log('BillsListController: Dependency validation failed: $e');
      SnackbarUtils.showError(
        'Error',
        'Failed to validate dependencies: $e',
      );
      return false;
    }
  }

  /// Load bills from Firebase
  Future<void> _loadBills() async {
    log('BillsListController: Starting to load bills');

    // Validate dependencies before proceeding
    if (!_validateDependencies()) {
      log('BillsListController: Dependency validation failed, aborting load');
      return;
    }

    isLoading.value = true;
    try {
      final uid = companyController.currentUserId;
      log('BillsListController: Company UID = $uid');
      log('BillsListController: CompanyController instance = ${companyController.runtimeType}');
      log('BillsListController: CompanyController currentUserId = ${companyController.currentUserId}');

      if (uid.isEmpty) {
        log('BillsListController: Company UID is empty');
        SnackbarUtils.showError(
          'Error',
          'Company ID not found. Please try again.',
        );
        return;
      }

      log('BillsListController: Calling getBillsUseCase with UID: $uid');

      // Debug: Direct Firebase query to check if bills exist
      try {
        final directSnapshot =
            await FirebaseFirestore.instance.collection('bills').get();
        log('BillsListController: Direct query found ${directSnapshot.docs.length} total bills in database');

        final companyBills = await FirebaseFirestore.instance
            .collection('bills')
            .where('companyUid', isEqualTo: uid)
            .get();
        log('BillsListController: Direct query found ${companyBills.docs.length} bills for company $uid');

        for (final doc in companyBills.docs) {
          log('BillsListController: Direct query bill: ${doc.id} - ${doc.data()}');
        }
      } catch (e) {
        log('BillsListController: Direct query error: $e');
      }

      final bills = await getBillsUseCase.call(uid);
      log('BillsListController: Successfully fetched ${bills.length} bills');

      // Debug: Print bill details
      for (final bill in bills) {
        log('BillsListController: Bill ${bill.billNumber} - ${bill.billDate} - ${bill.billStatus}');
      }

      allBills.assignAll(bills);
      log('BillsListController: allBills now has ${allBills.length} bills');

      _filterBills();
      log('BillsListController: After filtering, filteredBills has ${filteredBills.length} bills');
      log('BillsListController: paginatedBills has ${paginatedBills.length} bills');
      log('BillsListController: Bills loaded and filtered successfully');
    } catch (e) {
      log('BillsListController: Error loading bills: $e');
      SnackbarUtils.showError(
        'Error',
        'Failed to load bills: $e',
      );
    } finally {
      isLoading.value = false;
      isInitialLoading.value = false;
      log('BillsListController: Loading state reset');
    }
  }

  void _onSearchChanged() {
    searchQuery.value = searchController.text;
    _filterBills();
  }

  void searchBills(String query) {
    // Only update searchQuery, don't modify controller.text to avoid text selection
    searchQuery.value = query;
    _filterBills();
  }

  void filterByStatus(String? status) {
    selectedStatus.value = status;
    _filterBills();
  }

  void setSelectedStatus(String? status) {
    selectedStatus.value = status;
    _filterBills();
  }

  void setDateFilter(String filterType) {
    dateFilterType.value = filterType;
    _filterBills();
  }

  void setCustomDateRange(DateTime startDate, DateTime endDate) {
    selectedStartDate.value = startDate;
    selectedEndDate.value = endDate;
    dateFilterType.value = 'custom';
    _filterBills();
  }

  void _filterBills() {
    log('BillsListController: Starting _filterBills with ${allBills.length} bills');
    List<BillModel> filtered = List.from(allBills);
    log('BillsListController: Initial filtered list has ${filtered.length} bills');

    // Apply search filter
    if (searchQuery.value.isNotEmpty) {
      log('BillsListController: Applying search filter: "${searchQuery.value}"');
      filtered = filtered.where((bill) {
        final query = searchQuery.value.toLowerCase();
        return bill.billNumber.toLowerCase().contains(query) ||
            (bill.customerName?.toLowerCase().contains(query) ?? false) ||
            (bill.notes?.toLowerCase().contains(query) ?? false);
      }).toList();
      log('BillsListController: After search filter: ${filtered.length} bills');
    }

    // Apply status filter
    if (selectedStatus.value != null && selectedStatus.value!.isNotEmpty) {
      log('BillsListController: Applying status filter: "${selectedStatus.value}"');
      filtered = filtered.where((bill) {
        final billStatus = bill.billStatus.toString().split('.').last;
        log('BillsListController: Bill ${bill.billNumber} status: "$billStatus" vs filter: "${selectedStatus.value}"');
        return billStatus == selectedStatus.value;
      }).toList();
      log('BillsListController: After status filter: ${filtered.length} bills');
    }

    // Apply date filter
    log('BillsListController: Applying date filter: "${dateFilterType.value}"');
    filtered = _applyDateFilter(filtered);
    log('BillsListController: After date filter: ${filtered.length} bills');

    // Sort by bill date (newest first)
    filtered.sort((a, b) => b.billDate.compareTo(a.billDate));

    filteredBills.assignAll(filtered);
    setTotalItems(filtered.length);
    log('BillsListController: Final filteredBills: ${filteredBills.length} bills');
  }

  List<BillModel> _applyDateFilter(List<BillModel> bills) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    switch (dateFilterType.value) {
      case 'today':
        return bills.where((bill) {
          final billDate = DateTime(
              bill.billDate.year, bill.billDate.month, bill.billDate.day);
          return billDate.isAtSameMomentAs(today);
        }).toList();

      case 'yesterday':
        final yesterday = today.subtract(const Duration(days: 1));
        return bills.where((bill) {
          final billDate = DateTime(
              bill.billDate.year, bill.billDate.month, bill.billDate.day);
          return billDate.isAtSameMomentAs(yesterday);
        }).toList();

      case 'week':
        final weekStart = today.subtract(Duration(days: today.weekday - 1));
        return bills
            .where((bill) => bill.billDate
                .isAfter(weekStart.subtract(const Duration(days: 1))))
            .toList();

      case 'month':
        final monthStart = DateTime(today.year, today.month, 1);
        return bills
            .where((bill) => bill.billDate
                .isAfter(monthStart.subtract(const Duration(days: 1))))
            .toList();

      case 'custom':
        final startDate = DateTime(selectedStartDate.value.year,
            selectedStartDate.value.month, selectedStartDate.value.day);
        final endDate = DateTime(selectedEndDate.value.year,
            selectedEndDate.value.month, selectedEndDate.value.day, 23, 59, 59);
        return bills
            .where((bill) =>
                bill.billDate.isAfter(
                    startDate.subtract(const Duration(milliseconds: 1))) &&
                bill.billDate
                    .isBefore(endDate.add(const Duration(milliseconds: 1))))
            .toList();

      default:
        return bills;
    }
  }

  /// Get paginated bills for display
  List<BillModel> get paginatedBills {
    return paginateList(filteredBills);
  }

  /// Clear all filters
  void clearFilters() {
    selectedStatus.value = null;
    searchQuery.value = '';
    searchController.clear();
    dateFilterType.value = 'all';
    _filterBills();
  }

  /// Delete a bill
  Future<void> deleteBill(String billNumber) async {
    try {
      log('Attempting to delete bill: $billNumber');
      final uid = companyController.currentUserId;
      if (uid.isEmpty) {
        SnackbarUtils.showError(
          'Error',
          'Company ID not found. Please try again.',
        );
        return;
      }

      log('Deleting bill $billNumber for company $uid');
      final result = await deleteBillUseCase.call(
        uid: uid,
        billId: billNumber, // Pass billNumber as billId parameter to use case
      );

      result.fold(
        (failure) => showErrorSnackbar(failure.message),
        (success) {
          SnackbarUtils.showSuccess(
            'Success',
            success.message,
          );
          // Refresh the bills list
          refreshData();
        },
      );
    } catch (e) {
      showErrorSnackbar('Failed to delete bill: $e');
    }
  }

  /// Update bill status
  Future<void> updateBillStatus({
    required String billId, // This parameter receives billNumber from UI
    required String newStatus,
  }) async {
    try {
      log('Attempting to update bill status: $billId to $newStatus');
      final uid = companyController.currentUserId;
      if (uid.isEmpty) {
        SnackbarUtils.showError(
          'Error',
          'Company ID not found. Please try again.',
        );
        return;
      }

      log('Updating bill $billId status to $newStatus for company $uid');
      final result = await updateBillStatusUseCase.call(
        uid: uid,
        billId: billId, // Pass billNumber as billId parameter to use case
        newStatus: newStatus,
      );

      result.fold(
        (failure) => showErrorSnackbar(failure.message),
        (success) {
          SnackbarUtils.showSuccess(
            'Success',
            success.message,
          );
          // Refresh the bills list
          refreshData();
        },
      );
    } catch (e) {
      showErrorSnackbar('Failed to update bill status: $e');
    }
  }

  /// Show confirmation dialog for bill deletion
  void showDeleteConfirmation(BillModel bill) {
    Get.dialog(
      AlertDialog(
        title: Text(AppStrings.deleteBillTitle),
        content: Text(AppStrings.confirmDeleteBill),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              deleteBill(
                  bill.billNumber); // Fixed: Use billNumber instead of billId
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  /// Show confirmation dialog for completing bill
  void showCompleteConfirmation(BillModel bill) {
    Get.dialog(
      AlertDialog(
        title: const Text('Complete Bill'),
        content: const Text(
          'Are you sure you want to mark this bill as completed? This will automatically update all linked invoices to "Payment Received" status.',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              updateBillStatus(
                billId:
                    bill.billNumber, // Fixed: Use billNumber instead of billId
                newStatus: BillStatus.completed,
              );
            },
            child: const Text('Complete'),
          ),
        ],
      ),
    );
  }

  void showErrorSnackbar(String errorMessage) {
    if (errorMessage.isNotEmpty) {
      SnackbarUtils.showError(
        "Error", // Title of the snackbar
        errorMessage, // Error message content
      );
    }
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }
}
