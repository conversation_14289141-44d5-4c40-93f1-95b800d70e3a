# Firebase Index Creation Checklist for Accounting System

## 🎯 Objective
Create 20 Firebase composite indexes to resolve all indexing errors in the accounting system.

## 📋 Pre-Requirements
- [ ] Access to Firebase Console: https://console.firebase.google.com/
- [ ] Navigate to your project: `logistics-a8edb`
- [ ] Go to Firestore Database → Indexes tab
- [ ] Have this checklist ready for reference

## 🏗️ Index Creation Checklist

### Chart of Accounts Collection (5 indexes)

#### ✅ Index 1: Primary Active Accounts
- [ ] Collection ID: `chart_of_accounts`
- [ ] Fields: `uid` (Ascending) → `isActive` (Ascending) → `accountNumber` (Ascending)
- [ ] Status: Building → Enabled

#### ✅ Index 2: All Accounts
- [ ] Collection ID: `chart_of_accounts`
- [ ] Fields: `uid` (Ascending) → `accountNumber` (Ascending)
- [ ] Status: Building → Enabled

#### ✅ Index 3: Category + Active Accounts
- [ ] Collection ID: `chart_of_accounts`
- [ ] Fields: `uid` (Ascending) → `category` (Ascending) → `isActive` (Ascending) → `accountNumber` (Ascending)
- [ ] Status: Building → Enabled

#### ✅ Index 4: Category + All Accounts
- [ ] Collection ID: `chart_of_accounts`
- [ ] Fields: `uid` (Ascending) → `category` (Ascending) → `accountNumber` (Ascending)
- [ ] Status: Building → Enabled

#### ✅ Index 5: Account Type + Active Accounts
- [ ] Collection ID: `chart_of_accounts`
- [ ] Fields: `uid` (Ascending) → `accountType` (Ascending) → `isActive` (Ascending) → `accountNumber` (Ascending)
- [ ] Status: Building → Enabled

### Journal Entries Collection (5 indexes)

#### ✅ Index 6: Journal Entries by Date (Descending)
- [ ] Collection ID: `journal_entries`
- [ ] Fields: `uid` (Ascending) → `entryDate` (Descending)
- [ ] Status: Building → Enabled

#### ✅ Index 7: Journal Entries Date Range
- [ ] Collection ID: `journal_entries`
- [ ] Fields: `uid` (Ascending) → `entryDate` (Ascending)
- [ ] Status: Building → Enabled

#### ✅ Index 8: Journal Entries with Status Filter
- [ ] Collection ID: `journal_entries`
- [ ] Fields: `uid` (Ascending) → `status` (Ascending) → `entryDate` (Descending)
- [ ] Status: Building → Enabled

#### ✅ Index 9: Source Transaction Queries
- [ ] Collection ID: `journal_entries`
- [ ] Fields: `uid` (Ascending) → `sourceTransactionId` (Ascending) → `sourceTransactionType` (Ascending)
- [ ] Status: Building → Enabled

#### ✅ Index 10: Complex Account Filtering
- [ ] Collection ID: `journal_entries`
- [ ] Fields: `uid` (Ascending) → `entryDate` (Ascending) → `status` (Ascending)
- [ ] Status: Building → Enabled

### Journal Entry Lines Collection (3 indexes)

#### ✅ Index 11: Journal Entry Lines by Account
- [ ] Collection ID: `journal_entry_lines`
- [ ] Fields: `accountId` (Ascending) → `createdAt` (Descending)
- [ ] Status: Building → Enabled

#### ✅ Index 12: Journal Entry Lines by Journal Entry
- [ ] Collection ID: `journal_entry_lines`
- [ ] Fields: `journalEntryId` (Ascending) → `createdAt` (Ascending)
- [ ] Status: Building → Enabled

#### ✅ Index 13: Journal Entry Lines with UID
- [ ] Collection ID: `journal_entry_lines`
- [ ] Fields: `accountId` (Ascending) → `uid` (Ascending)
- [ ] Status: Building → Enabled

### Financial Reports Collection (1 index)

#### ✅ Index 14: Financial Reports by Type and Date
- [ ] Collection ID: `financial_reports`
- [ ] Fields: `uid` (Ascending) → `reportType` (Ascending) → `generatedAt` (Descending)
- [ ] Status: Building → Enabled

### Fiscal Years Collection (1 index)

#### ✅ Index 15: Fiscal Years by Date
- [ ] Collection ID: `fiscal_years`
- [ ] Fields: `uid` (Ascending) → `startDate` (Descending)
- [ ] Status: Building → Enabled

### Fiscal Periods Collection (2 indexes)

#### ✅ Index 16: Fiscal Periods by Year and Date
- [ ] Collection ID: `fiscal_periods`
- [ ] Fields: `uid` (Ascending) → `fiscalYearId` (Ascending) → `startDate` (Ascending)
- [ ] Status: Building → Enabled

#### ✅ Index 17: Current Fiscal Period Query
- [ ] Collection ID: `fiscal_periods`
- [ ] Fields: `uid` (Ascending) → `startDate` (Ascending) → `endDate` (Ascending) → `status` (Ascending)
- [ ] Status: Building → Enabled

### Report Collections (3 indexes)

#### ✅ Index 18: Balance Sheet Reports by Date
- [ ] Collection ID: `balance_sheet_reports`
- [ ] Fields: `uid` (Ascending) → `generatedAt` (Descending)
- [ ] Status: Building → Enabled

#### ✅ Index 19: Aged Receivables Reports
- [ ] Collection ID: `aged_receivables_reports`
- [ ] Fields: `uid` (Ascending) → `generatedAt` (Descending)
- [ ] Status: Building → Enabled

#### ✅ Index 20: Aged Payables Reports
- [ ] Collection ID: `aged_payables_reports`
- [ ] Fields: `uid` (Ascending) → `generatedAt` (Descending)
- [ ] Status: Building → Enabled

## 🔍 Verification Steps

### After All Indexes Are Created:
- [ ] All 20 indexes show "Enabled" status
- [ ] Run test script: `dart test_accounting_system_comprehensive.dart`
- [ ] Test Chart of Accounts screen loads without errors
- [ ] Test Journal Entries screen loads without errors
- [ ] Test Trial Balance generates without errors
- [ ] Test Profit & Loss generates without errors
- [ ] Test Balance Sheet generates without errors
- [ ] Test General Ledger loads without errors
- [ ] Test Fiscal Periods loads without errors
- [ ] Test Aged Reports generate without errors
- [ ] Test Cash Flow Statement generates without errors

## ⏱️ Expected Timeline
- **Index Creation**: 5-15 minutes per index (depending on data size)
- **Total Time**: 1-5 hours for all 20 indexes
- **Testing**: 30 minutes after all indexes are enabled

## 🚨 Critical Notes
1. **Field Order Matters**: Add fields in the exact order specified
2. **Case Sensitivity**: Field names are case-sensitive
3. **Collection Names**: Must match exactly (no typos)
4. **Status Monitoring**: Wait for "Enabled" status before testing
5. **Parallel Creation**: You can create multiple indexes simultaneously

## 📞 Support
If any index fails to create or shows errors:
1. Check field names for typos
2. Verify collection names are correct
3. Ensure field order matches specification
4. Delete failed index and recreate
5. Check Firebase Console error messages
