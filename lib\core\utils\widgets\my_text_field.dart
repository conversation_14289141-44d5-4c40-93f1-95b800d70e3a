import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';

class MyTextFormField extends StatefulWidget {
  const MyTextFormField({
    super.key,
    this.titleText,
    required this.labelText,
    required this.hintText,
    this.suffixIcon,
    this.controller,
    this.textInputFormatter,
    this.enabled,
    this.validator,
    this.validatorMode = AutovalidateMode.onUnfocus,
    this.maxLength,
    this.onTap,
    this.onChanged,
    this.keyboardType,
    this.readOnly = false,
  });

  final String? titleText;
  final String labelText;
  final String hintText;
  //Todo make label and hint text required if necessary
  final Widget? suffixIcon;
  final TextEditingController? controller;
  final List<TextInputFormatter>? textInputFormatter;
  final bool? enabled;
  final String? Function(String?)? validator;
  final AutovalidateMode? validatorMode;
  final int? maxLength;
  final TextInputType? keyboardType;
  final Function()? onTap;
  final Function(String)? onChanged;
  final bool readOnly;
  @override
  State<MyTextFormField> createState() => _MyTextFormFieldState();
}

class _MyTextFormFieldState extends State<MyTextFormField> {
  @override
  Widget build(BuildContext context) {
    notifier = Provider.of(context, listen: true);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        widget.titleText == null
            ? Container()
            : Text(
                widget.titleText ?? "",
                style: TextStyle(
                  color: notifier.text,
                ),
                textAlign: TextAlign.start,
              ),
        widget.titleText == null ? Container() : const SizedBox(height: 7),
        Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: notifier.getfillborder,
            ),
            borderRadius: BorderRadius.circular(5),
          ),
          child: TextFormField(
            enabled: widget.enabled ?? true,
            readOnly: widget.readOnly,
            cursorColor: notifier.text,
            style: TextStyle(
              fontFamily: "Outfit",
              color: notifier.text,
            ),
            controller: widget.controller ?? TextEditingController(),
            inputFormatters: widget.textInputFormatter ?? [],
            decoration: InputDecoration(
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
              enabledBorder: UnderlineInputBorder(
                borderSide: BorderSide(
                  color: notifier.getfillborder,
                  width: 0,
                ),
                borderRadius: BorderRadius.circular(5),
              ),
              focusedBorder: UnderlineInputBorder(
                borderSide: const BorderSide(
                  color: Color(0xff0165FC),
                ),
                borderRadius: BorderRadius.circular(5),
              ),
              suffixIcon: widget.suffixIcon,
              hintText: widget.hintText,
              hintStyle: TextStyle(
                color: notifier.text,
              ),
              labelText: widget.labelText,
              labelStyle: const TextStyle(
                color: Colors.grey,
              ),
              floatingLabelStyle: const TextStyle(
                color: Color(0xff0165FC),
              ),
            ),
            validator: widget.validator,
            autovalidateMode: widget.validatorMode,
            maxLength: widget.maxLength,
            keyboardType: widget.keyboardType,
            onTap: widget.onTap,
            onChanged: widget.onChanged,
          ),
        ),
      ],
    );
  }
}
