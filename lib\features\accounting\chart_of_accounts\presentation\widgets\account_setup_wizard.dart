import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../../core/utils/snackbar_utils.dart';
import '../../../../../core/utils/app_constants/texts/app_strings.dart';
import '../../../../../firebase_service/accounting/chart_of_accounts_firebase_service.dart';
import '../../../../../models/finance/chart_of_accounts_model.dart';
import '../../repositories/chart_of_accounts_repository.dart';
import '../../services/account_setup_service.dart';
import '../controllers/chart_of_accounts_controller.dart';

class AccountSetupWizard extends StatefulWidget {
  const AccountSetupWizard({super.key});

  @override
  State<AccountSetupWizard> createState() => _AccountSetupWizardState();
}

class _AccountSetupWizardState extends State<AccountSetupWizard> {
  late AccountSetupService setupService;
  final isLoading = false.obs;
  final setupProgress = 0.0.obs;
  final isSetupComplete = false.obs;

  @override
  void initState() {
    super.initState();
    final firebaseService = ChartOfAccountsFirebaseService();
    final repository = ChartOfAccountsRepositoryImpl(firebaseService);
    setupService = AccountSetupService(repository);
    _checkSetupStatus();
  }

  Future<void> _checkSetupStatus() async {
    final isComplete = await setupService.isDefaultSetupComplete();
    final progress = await setupService.getSetupProgress();

    isSetupComplete.value = isComplete;
    setupProgress.value = progress;
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (isSetupComplete.value) {
        return _buildSetupCompleteView();
      }

      return _buildSetupWizardView();
    });
  }

  Widget _buildSetupWizardView() {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.account_tree,
              size: 64,
              color: Theme.of(context).primaryColor,
            ),
            const SizedBox(height: 16),
            Text(
              'Welcome to Chart of Accounts',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Set up your accounting foundation with predefined account categories',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            _buildSetupOptions(),
            const SizedBox(height: 24),
            Obx(() => setupProgress.value > 0
                ? _buildProgressIndicator()
                : const SizedBox.shrink()),
            const SizedBox(height: 16),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildSetupOptions() {
    return Column(
      children: [
        _buildSetupOption(
          icon: Icons.auto_awesome,
          title: 'Quick Setup',
          description: 'Create standard accounts for logistics business',
          categories: [
            'Assets (Cash, Vehicles, Equipment)',
            'Liabilities (Loans, Payables)',
            'Equity (Owner\'s Investment)',
            'Revenue (Service, Freight)',
            'Expenses (Fuel, Maintenance, Salaries)',
          ],
        ),
        const SizedBox(height: 16),
        _buildSetupOption(
          icon: Icons.build,
          title: 'Manual Setup',
          description: 'Start with empty chart and add accounts manually',
          categories: [
            'Create accounts as needed',
            'Full control over account structure',
            'Customize for your specific business',
          ],
          onTap: _startManualSetup,
        ),
      ],
    );
  }

  Widget _buildSetupOption({
    required IconData icon,
    required String title,
    required String description,
    required List<String> categories,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              description,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 12),
            ...categories.map((category) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    children: [
                      Icon(
                        Icons.check_circle_outline,
                        size: 16,
                        color: Colors.green[600],
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          category,
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ),
                    ],
                  ),
                )),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Setup Progress',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
            ),
            Text(
              '${(setupProgress.value * 100).toInt()}%',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).primaryColor,
                  ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: setupProgress.value,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(
            Theme.of(context).primaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Obx(() => Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: isLoading.value ? null : _skipSetup,
                child: const Text('Skip Setup'),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: ElevatedButton(
                onPressed: isLoading.value ? null : _runQuickSetup,
                child: isLoading.value
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Text('Quick Setup'),
              ),
            ),
          ],
        ));
  }

  Widget _buildSetupCompleteView() {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.check_circle,
              size: 64,
              color: Colors.green[600],
            ),
            const SizedBox(height: 16),
            Text(
              'Chart of Accounts Ready!',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.green[700],
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Your accounting foundation is set up and ready to use',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            _buildAccountSummary(),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.arrow_forward),
                label: const Text('Continue to Chart of Accounts'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountSummary() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green[200]!),
      ),
      child: Column(
        children: AccountCategory.values.map((category) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              children: [
                Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color: category.color,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    category.displayName,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
                Icon(
                  Icons.check,
                  size: 16,
                  color: Colors.green[600],
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  Future<void> _runQuickSetup() async {
    try {
      isLoading.value = true;

      final success = await setupService.setupDefaultChartOfAccounts();

      if (success) {
        SnackbarUtils.showSuccess(
          AppStrings.success,
          'Default chart of accounts created successfully!',
        );
        await _checkSetupStatus();
      } else {
        SnackbarUtils.showError(
          AppStrings.errorS,
          'Failed to set up default accounts. Please try again.',
        );
      }
    } catch (e) {
      SnackbarUtils.showError(
        AppStrings.errorS,
        'An error occurred during setup: $e',
      );
    } finally {
      isLoading.value = false;
    }
  }

  void _startManualSetup() {
    // Close the setup wizard and open the account creation form
    Navigator.of(context).pop();
    final controller = Get.find<ChartOfAccountsController>();
    controller.openDrawer();
  }

  void _skipSetup() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Skip Setup?'),
        content: const Text(
          'You can always set up default accounts later from the Chart of Accounts screen. '
          'Are you sure you want to skip the setup?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: const Text('Skip'),
          ),
        ],
      ),
    );
  }
}
