import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';
import 'package:logestics/models/finance/deposit_category_model.dart';

class DepositCategoryFirebaseService {
  static const String _collectionPath =
      AppCollection.depositCategoriesCollection;
  final FirebaseFirestore _firestore;

  // Get current user's UID or return 'anonymous' if not authenticated
  String get _uid => FirebaseAuth.instance.currentUser?.uid ?? 'anonymous';

  DepositCategoryFirebaseService([FirebaseFirestore? firestore])
      : _firestore = firestore ?? FirebaseFirestore.instance;

  Future<DepositCategoryModel> createCategory(
      String name, String description) async {
    try {
      final newCategory = DepositCategoryModel(
        id: '',
        name: name,
        description: description,
        createdAt: DateTime.now(),
        uid: _uid, // Add current user's UID
      );

      final docRef = await _firestore
          .collection(_collectionPath)
          .add(newCategory.toJson());
      final createdCategory = newCategory.copyWith(id: docRef.id);

      await docRef.update({'id': docRef.id});

      return createdCategory;
    } catch (e) {
      log('Error creating deposit category: $e', error: e);
      rethrow;
    }
  }

  Future<List<DepositCategoryModel>> getCategories() async {
    try {
      final snapshot = await _firestore
          .collection(_collectionPath)
          .where('uid', isEqualTo: _uid) // Filter by current user's UID
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => DepositCategoryModel.fromJson(doc.data()))
          .toList();
    } catch (e) {
      log('Error getting deposit categories: $e', error: e);
      return [];
    }
  }

  /// Stream to listen for real-time updates to categories
  Stream<List<DepositCategoryModel>> listenToCategories() {
    try {
      return _firestore
          .collection(_collectionPath)
          .where('uid', isEqualTo: _uid) // Filter by current user's UID
          .orderBy('createdAt', descending: true)
          .snapshots()
          .map((snapshot) => snapshot.docs
              .map((doc) => DepositCategoryModel.fromJson(doc.data()))
              .toList());
    } catch (e) {
      log('Error listening to deposit categories: $e', error: e);
      return Stream.value([]);
    }
  }

  /// Stream to listen for document changes (for optimistic updates)
  Stream<List<DocumentChange>> listenToCategoryChanges() {
    try {
      return _firestore
          .collection(_collectionPath)
          .where('uid', isEqualTo: _uid) // Filter by current user's UID
          .orderBy('createdAt', descending: true)
          .snapshots()
          .map((snapshot) => snapshot.docChanges);
    } catch (e) {
      log('Error listening to deposit category changes: $e', error: e);
      return Stream.value([]);
    }
  }

  Future<bool> checkCategoryExists(String name) async {
    log('Checking if deposit category exists: $name');
    try {
      final snapshot = await _firestore
          .collection(_collectionPath)
          .where('name', isEqualTo: name)
          .where('uid',
              isEqualTo: _uid) // Only check the current user's categories
          .limit(1)
          .get();

      final exists = snapshot.docs.isNotEmpty;
      log('Category existence check result: $exists');
      return exists;
    } catch (e) {
      log('Error checking if deposit category exists: $e');
      rethrow;
    }
  }

  Future<void> deleteCategory(String id) async {
    try {
      // First check if the category belongs to the current user
      final categoryDoc =
          await _firestore.collection(_collectionPath).doc(id).get();

      if (!categoryDoc.exists) {
        throw Exception('Category not found');
      }

      final categoryData = categoryDoc.data() as Map<String, dynamic>;
      if (categoryData['uid'] != _uid) {
        throw Exception('You do not have permission to delete this category');
      }

      await _firestore.collection(_collectionPath).doc(id).delete();
    } catch (e) {
      log('Error deleting deposit category: $e', error: e);
      rethrow;
    }
  }

  Future<DepositCategoryModel> updateCategory(
      DepositCategoryModel category) async {
    try {
      // First check if the category belongs to the current user
      final categoryDoc =
          await _firestore.collection(_collectionPath).doc(category.id).get();

      if (!categoryDoc.exists) {
        throw Exception('Category not found');
      }

      final categoryData = categoryDoc.data() as Map<String, dynamic>;
      if (categoryData['uid'] != _uid) {
        throw Exception('You do not have permission to update this category');
      }

      // Ensure we preserve the original UID
      final updatedCategory = category.copyWith(uid: categoryData['uid']);

      await _firestore
          .collection(_collectionPath)
          .doc(category.id)
          .update(updatedCategory.toJson());

      return updatedCategory;
    } catch (e) {
      log('Error updating deposit category: $e', error: e);
      rethrow;
    }
  }

  Future<DepositCategoryModel?> getCategoryById(String id) async {
    try {
      final doc = await _firestore.collection(_collectionPath).doc(id).get();

      if (!doc.exists) {
        log('Deposit category not found: $id');
        return null;
      }

      final data = doc.data() as Map<String, dynamic>;

      // Verify that the category belongs to the current user
      if (data['uid'] != _uid) {
        log('User does not have permission to access this category');
        return null;
      }

      return DepositCategoryModel.fromJson(data);
    } catch (e) {
      log('Error getting deposit category by ID: $e', error: e);
      return null;
    }
  }
}
