import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/features/finance/expenses/repositories/expense_repository.dart';
import 'package:logestics/features/finance/accounts/repositories/account_repository.dart';
import 'package:logestics/features/finance/payees/repositories/payee_repository.dart';
import 'package:logestics/features/finance/expense_categories/repositories/expense_category_repository.dart';
import 'package:logestics/features/finance/expenses/presentation/controllers/expenses_pdf_generation_controller.dart';
import 'package:logestics/services/pdf_generation_service.dart';
import 'package:logestics/firebase_service/firebase_auth_service.dart';
import 'package:logestics/firebase_service/finance/company_firebase_service.dart';

import 'package:logestics/features/home/<USER>/theme.dart';
import 'package:provider/provider.dart';

class ExpensesPDFGenerationDialog extends StatefulWidget {
  const ExpensesPDFGenerationDialog({super.key});

  @override
  State<ExpensesPDFGenerationDialog> createState() =>
      _ExpensesPDFGenerationDialogState();
}

class _ExpensesPDFGenerationDialogState
    extends State<ExpensesPDFGenerationDialog> {
  late ExpensesPDFGenerationController controller;
  late String controllerTag;

  @override
  void initState() {
    super.initState();

    // Generate unique tag for controller
    controllerTag = 'expenses_pdf_${DateTime.now().millisecondsSinceEpoch}';

    // Get repositories from dependency injection
    final expenseRepository = Get.find<ExpenseRepository>();
    final accountRepository = Get.find<AccountRepository>();
    final payeeRepository = Get.find<PayeeRepository>();
    final categoryRepository = Get.find<ExpenseCategoryRepository>();

    // Initialize controller with dependency injection
    controller = Get.put(
      ExpensesPDFGenerationController(
        expenseRepository: expenseRepository,
        accountRepository: accountRepository,
        payeeRepository: payeeRepository,
        categoryRepository: categoryRepository,
        pdfService: Get.find<PDFGenerationService>(),
        authService: Get.find<FirebaseAuthService>(),
        companyService: Get.find<CompanyFirebaseService>(),
      ),
      tag: controllerTag,
    );
  }

  @override
  void dispose() {
    Get.delete<ExpensesPDFGenerationController>(tag: controllerTag);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ColorNotifier>(
      builder: (context, notifier, child) {
        return Dialog(
          backgroundColor: notifier.getcardColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            width: MediaQuery.of(context).size.width * 0.9,
            height: MediaQuery.of(context).size.height * 0.9,
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Generate Expenses Report',
                      style: AppTextStyles.titleStyle.copyWith(
                        color: notifier.text,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: Icon(Icons.close, color: notifier.text),
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // Content
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Information message
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.blue.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                                color: Colors.blue.withValues(alpha: 0.3)),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.info_outline,
                                  color: Colors.blue, size: 20),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  'Date range is required for PDF generation. All other filters are optional and will be applied if selected.',
                                  style: TextStyle(
                                    color: Colors.blue.shade700,
                                    fontSize: 13,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 16),

                        // User Status Section
                        _buildUserStatusSection(notifier),

                        const SizedBox(height: 24),

                        // Filter Section
                        _buildFilterSection(notifier),

                        const SizedBox(height: 24),

                        // Date Range Section
                        _buildDateRangeSection(notifier),

                        const SizedBox(height: 24),

                        // Amount Range Section
                        _buildAmountRangeSection(notifier),

                        const SizedBox(height: 24),

                        // Custom Title Section
                        _buildCustomTitleSection(notifier),

                        const SizedBox(height: 32),

                        // Summary Section
                        _buildSummarySection(notifier),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                // Action Buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // Clear Filters Button
                    TextButton(
                      onPressed: controller.clearFilters,
                      child: Text(
                        'Clear Filters',
                        style: TextStyle(color: notifier.text),
                      ),
                    ),

                    const SizedBox(width: 16),

                    // Share PDF Button
                    Obx(() => ElevatedButton.icon(
                          onPressed: !controller.canGeneratePDF
                              ? null
                              : controller.sharePDF,
                          icon: controller.isLoading.value
                              ? SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white),
                                  ),
                                )
                              : const Icon(Icons.share),
                          label: Text(controller.isLoading.value
                              ? 'Generating...'
                              : 'Export PDF'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 24, vertical: 12),
                          ),
                        )),

                    const SizedBox(width: 16),

                    // Generate PDF Button
                    Obx(() => ElevatedButton.icon(
                          onPressed: !controller.canGeneratePDF
                              ? null
                              : controller.generatePDF,
                          icon: controller.isLoading.value
                              ? SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white),
                                  ),
                                )
                              : const Icon(Icons.picture_as_pdf),
                          label: Text(controller.isLoading.value
                              ? 'Generating...'
                              : 'Generate PDF'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 24, vertical: 12),
                          ),
                        )),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildUserStatusSection(ColorNotifier notifier) {
    return Obx(() {
      if (controller.isLoadingUser.value) {
        return Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.orange.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
          ),
          child: Row(
            children: [
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Loading user information...',
                  style: TextStyle(
                    color: Colors.orange.shade700,
                    fontSize: 13,
                  ),
                ),
              ),
            ],
          ),
        );
      }

      if (controller.userLoadError.value != null) {
        return Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.red.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.error_outline, color: Colors.red, size: 20),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'User Information Error',
                      style: TextStyle(
                        color: Colors.red.shade700,
                        fontSize: 13,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                controller.userLoadError.value!,
                style: TextStyle(
                  color: Colors.red.shade600,
                  fontSize: 12,
                ),
              ),
              const SizedBox(height: 8),
              ElevatedButton.icon(
                onPressed: controller.retryLoadUser,
                icon: Icon(Icons.refresh, size: 16),
                label: Text('Retry'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  minimumSize: Size(0, 32),
                ),
              ),
            ],
          ),
        );
      }

      if (controller.currentUser.value != null) {
        return Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.green.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
          ),
          child: Row(
            children: [
              Icon(Icons.check_circle_outline, color: Colors.green, size: 20),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'User: ${controller.currentUser.value!.companyName}',
                  style: TextStyle(
                    color: Colors.green.shade700,
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );
      }

      return const SizedBox.shrink();
    });
  }

  Widget _buildFilterSection(ColorNotifier notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Filters',
              style: AppTextStyles.titleStyle.copyWith(
                color: notifier.text,
                fontSize: 16,
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
              ),
              child: Text(
                'Optional',
                style: TextStyle(
                  color: Colors.blue,
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Account Filter
        _buildAccountFilter(notifier),
        const SizedBox(height: 16),

        // Payee Filter
        _buildPayeeFilter(notifier),
        const SizedBox(height: 16),

        // Category Filter
        _buildCategoryFilter(notifier),
      ],
    );
  }

  Widget _buildAccountFilter(ColorNotifier notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Accounts',
          style: TextStyle(
            color: notifier.text,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Obx(() => Wrap(
              spacing: 8,
              runSpacing: 8,
              children: controller.accounts.map((account) {
                final isSelected =
                    controller.selectedAccounts.contains(account);
                return FilterChip(
                  label: Text(account.name),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected) {
                      controller.selectedAccounts.add(account);
                    } else {
                      controller.selectedAccounts.remove(account);
                    }
                    controller.applyFilters();
                  },
                  backgroundColor: notifier.getBgColor,
                  selectedColor: Colors.blue.withValues(alpha: 0.2),
                  checkmarkColor: Colors.blue,
                  labelStyle: TextStyle(
                    color: isSelected ? Colors.blue : notifier.text,
                  ),
                );
              }).toList(),
            )),
      ],
    );
  }

  Widget _buildPayeeFilter(ColorNotifier notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Payees',
          style: TextStyle(
            color: notifier.text,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Obx(() => Wrap(
              spacing: 8,
              runSpacing: 8,
              children: controller.payees.map((payee) {
                final isSelected = controller.selectedPayees.contains(payee);
                return FilterChip(
                  label: Text(payee.name),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected) {
                      controller.selectedPayees.add(payee);
                    } else {
                      controller.selectedPayees.remove(payee);
                    }
                    controller.applyFilters();
                  },
                  backgroundColor: notifier.getBgColor,
                  selectedColor: Colors.green.withValues(alpha: 0.2),
                  checkmarkColor: Colors.green,
                  labelStyle: TextStyle(
                    color: isSelected ? Colors.green : notifier.text,
                  ),
                );
              }).toList(),
            )),
      ],
    );
  }

  Widget _buildCategoryFilter(ColorNotifier notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Categories',
          style: TextStyle(
            color: notifier.text,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Obx(() => Wrap(
              spacing: 8,
              runSpacing: 8,
              children: controller.categories.map((category) {
                final isSelected =
                    controller.selectedCategories.contains(category);
                return FilterChip(
                  label: Text(category.name),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected) {
                      controller.selectedCategories.add(category);
                    } else {
                      controller.selectedCategories.remove(category);
                    }
                    controller.applyFilters();
                  },
                  backgroundColor: notifier.getBgColor,
                  selectedColor: Colors.orange.withValues(alpha: 0.2),
                  checkmarkColor: Colors.orange,
                  labelStyle: TextStyle(
                    color: isSelected ? Colors.orange : notifier.text,
                  ),
                );
              }).toList(),
            )),
      ],
    );
  }

  Widget _buildDateRangeSection(ColorNotifier notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Date Range',
              style: AppTextStyles.titleStyle.copyWith(
                color: notifier.text,
                fontSize: 16,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              '*',
              style: TextStyle(
                color: Colors.red,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
              ),
              child: Text(
                'Required',
                style: TextStyle(
                  color: Colors.red,
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            // Start Date
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Start Date',
                    style: TextStyle(
                      color: notifier.text,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Obx(() => InkWell(
                        onTap: () async {
                          final date = await showDatePicker(
                            context: context,
                            initialDate:
                                controller.startDate.value ?? DateTime.now(),
                            firstDate: DateTime(2020),
                            lastDate: DateTime.now(),
                          );
                          if (date != null) {
                            controller.startDate.value = date;
                            controller.applyFilters();
                          }
                        },
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            border: Border.all(
                                color: notifier.text.withValues(alpha: 0.3)),
                            borderRadius: BorderRadius.circular(8),
                            color: notifier.getBgColor,
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.calendar_today,
                                  color: notifier.text.withValues(alpha: 0.6),
                                  size: 16),
                              const SizedBox(width: 8),
                              Text(
                                controller.startDate.value != null
                                    ? DateFormat('dd/MM/yyyy')
                                        .format(controller.startDate.value!)
                                    : 'Select Date',
                                style: TextStyle(color: notifier.text),
                              ),
                            ],
                          ),
                        ),
                      )),
                ],
              ),
            ),

            const SizedBox(width: 16),

            // End Date
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'End Date',
                    style: TextStyle(
                      color: notifier.text,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Obx(() => InkWell(
                        onTap: () async {
                          final date = await showDatePicker(
                            context: context,
                            initialDate:
                                controller.endDate.value ?? DateTime.now(),
                            firstDate: DateTime(2020),
                            lastDate: DateTime.now(),
                          );
                          if (date != null) {
                            controller.endDate.value = date;
                            controller.applyFilters();
                          }
                        },
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            border: Border.all(
                                color: notifier.text.withValues(alpha: 0.3)),
                            borderRadius: BorderRadius.circular(8),
                            color: notifier.getBgColor,
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.calendar_today,
                                  color: notifier.text.withValues(alpha: 0.6),
                                  size: 16),
                              const SizedBox(width: 8),
                              Text(
                                controller.endDate.value != null
                                    ? DateFormat('dd/MM/yyyy')
                                        .format(controller.endDate.value!)
                                    : 'Select Date',
                                style: TextStyle(color: notifier.text),
                              ),
                            ],
                          ),
                        ),
                      )),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAmountRangeSection(ColorNotifier notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Amount Range',
              style: AppTextStyles.titleStyle.copyWith(
                color: notifier.text,
                fontSize: 16,
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
              ),
              child: Text(
                'Optional',
                style: TextStyle(
                  color: Colors.blue,
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            // Minimum Amount
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Minimum Amount',
                    style: TextStyle(
                      color: notifier.text,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: controller.minAmountController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      hintText: 'Enter minimum amount',
                      hintStyle: TextStyle(
                          color: notifier.text.withValues(alpha: 0.5)),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                            color: notifier.text.withValues(alpha: 0.3)),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                            color: notifier.text.withValues(alpha: 0.3)),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: Colors.blue),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 12),
                    ),
                    style: TextStyle(color: notifier.text),
                  ),
                ],
              ),
            ),

            const SizedBox(width: 16),

            // Maximum Amount
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Maximum Amount',
                    style: TextStyle(
                      color: notifier.text,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: controller.maxAmountController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      hintText: 'Enter maximum amount',
                      hintStyle: TextStyle(
                          color: notifier.text.withValues(alpha: 0.5)),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                            color: notifier.text.withValues(alpha: 0.3)),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                            color: notifier.text.withValues(alpha: 0.3)),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: Colors.blue),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 12),
                    ),
                    style: TextStyle(color: notifier.text),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCustomTitleSection(ColorNotifier notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Custom Title',
              style: AppTextStyles.titleStyle.copyWith(
                color: notifier.text,
                fontSize: 16,
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
              ),
              child: Text(
                'Optional',
                style: TextStyle(
                  color: Colors.blue,
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        TextField(
          controller: controller.customTitleController,
          decoration: InputDecoration(
            hintText: 'Enter custom title for the report',
            hintStyle: TextStyle(color: notifier.text.withValues(alpha: 0.5)),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide:
                  BorderSide(color: notifier.text.withValues(alpha: 0.3)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide:
                  BorderSide(color: notifier.text.withValues(alpha: 0.3)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.blue),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
          ),
          style: TextStyle(color: notifier.text),
        ),
      ],
    );
  }

  Widget _buildSummarySection(ColorNotifier notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Summary',
          style: AppTextStyles.titleStyle.copyWith(
            color: notifier.text,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 16),
        Obx(() => Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: notifier.getBgColor,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: notifier.text.withValues(alpha: 0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.blue, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'Filtered Results',
                        style: TextStyle(
                          color: notifier.text,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  if (controller.isLoadingData.value) ...[
                    Row(
                      children: [
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                        const SizedBox(width: 8),
                        Text('Loading expenses data...',
                            style: TextStyle(color: notifier.text)),
                      ],
                    ),
                  ] else ...[
                    Text(
                      'Total Expenses: ${controller.filteredExpenses.length}',
                      style: TextStyle(color: notifier.text),
                    ),
                    Text(
                      'Total Amount: PKR ${NumberFormat('#,##0.00').format(controller.filteredExpenses.fold(0.0, (sum, e) => sum + e.amount))}',
                      style: TextStyle(
                        color: notifier.text,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    if (controller.filteredExpenses.isEmpty &&
                        !controller.isLoadingData.value) ...[
                      const SizedBox(height: 8),
                      Text(
                        'No expenses match the current filters. Please adjust your criteria.',
                        style: TextStyle(
                          color: Colors.orange,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ],
                ],
              ),
            )),
      ],
    );
  }
}
