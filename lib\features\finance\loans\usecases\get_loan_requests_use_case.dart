import 'package:either_dart/either.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/models/finance/loan_model.dart';
import 'package:logestics/features/finance/loans/repositories/loan_repository.dart';

class GetIncomingLoanRequestsUseCase {
  final LoanRepository _loanRepository;

  GetIncomingLoanRequestsUseCase(this._loanRepository);

  Future<Either<FailureObj, List<LoanModel>>> execute() {
    return _loanRepository.getIncomingLoanRequests();
  }
}

class GetOutgoingLoanRequestsUseCase {
  final LoanRepository _loanRepository;

  GetOutgoingLoanRequestsUseCase(this._loanRepository);

  Future<Either<FailureObj, List<LoanModel>>> execute() {
    return _loanRepository.getOutgoingLoanRequests();
  }
}
