import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';
import '../Controllers/drawer_controllers.dart';
import '../drawer/drawer.dart';
import 'appbar/app_bar.dart';

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key});

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  MainDrawerController mainDrawerController = Get.put(MainDrawerController());

  @override
  void initState() {
    super.initState();
  }

  /// Check if the current screen is Chart of Accounts
  bool _isChartOfAccountsScreen() {
    final currentScreen = mainDrawerController.currentScreen;
    return currentScreen != null &&
        currentScreen.runtimeType.toString().contains('ChartOfAccountsView');
  }

  /// Build the content area with conditional scrolling
  Widget _buildContentArea(Widget screen) {
    if (_isChartOfAccountsScreen()) {
      // Chart of Accounts needs bounded height constraints, not SingleChildScrollView
      return Container(
        decoration: const BoxDecoration(),
        child: screen,
      );
    } else {
      // Other screens can use SingleChildScrollView
      return SingleChildScrollView(
        child: Container(
          decoration: const BoxDecoration(),
          child: screen,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    notifier = Provider.of(context, listen: true);
    return LayoutBuilder(
      builder: (context, constraints) => GetBuilder<MainDrawerController>(
        builder: (mainDrawerController) {
          if (Get.width < 950) {
            return Scaffold(
              appBar: PreferredSize(
                preferredSize: Size.fromHeight(Get.width < 800 ? 120 : 50),
                child: const AppBarView(),
              ),
              backgroundColor: notifier.mainBgColor,
              resizeToAvoidBottomInset: true,
              drawer: SizedBox(
                width: Get.width < 450
                    ? Get.width / 1.4
                    : Get.width < 600
                        ? Get.width / 2
                        : Get.width < 750
                            ? Get.width / 2.7
                            : Get.width / 3.5,
                child: const MyDrawer(),
              ),
              body: Container(
                margin: const EdgeInsets.all(10),
                width: Get.width,
                child: _buildContentArea(mainDrawerController.currentScreen!),
              ),
            );
          } else if (Get.width < 1200) {
            return Scaffold(
              backgroundColor: notifier.mainBgColor,
              resizeToAvoidBottomInset: true,
              /* appBar: AppBar(leading: Builder(
              builder: (context) {
                return IconButton(onPressed: (){
                  Scaffold.of(context).openDrawer();
                }, icon: const Icon(Icons.line_weight_rounded));
              }
            ),),*/
              drawer: SizedBox(
                width: Get.width < 1050 ? Get.width / 3.5 : Get.width / 4.3,
                child: const MyDrawer(),
              ),
              // drawerScrimColor: notifier.text,

              body: Row(
                children: [
                  Expanded(
                    child: Container(
                      margin: const EdgeInsets.all(15),
                      decoration: const BoxDecoration(),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              color: notifier.getBgColor,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Center(
                              child: Row(
                                children: [
                                  Builder(builder: (context) {
                                    return IconButton(
                                        onPressed: () {
                                          Scaffold.of(context).openDrawer();
                                        },
                                        icon: const Icon(
                                            Icons.line_weight_rounded));
                                  }),
                                  const AppBarView(),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(height: 20),
                          StatefulBuilder(builder: (context, setState) {
                            return Expanded(
                              child: _buildContentArea(
                                  mainDrawerController.currentScreen!),
                            );
                          }),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          } else {
            return Scaffold(
              resizeToAvoidBottomInset: true,
              backgroundColor: notifier.mainBgColor,
              body: Row(
                children: [
                  SizedBox(width: Get.width / 6, child: const MyDrawer()),
                  Expanded(
                    child: Container(
                      margin: const EdgeInsets.all(15),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: const Center(
                              child: AppBarView(),
                            ),
                          ),
                          const SizedBox(height: 20),
                          StatefulBuilder(builder: (context, setState) {
                            return Expanded(
                              child: _buildContentArea(
                                  mainDrawerController.currentScreen!),
                            );
                          }),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          }
        },
      ),
    );
  }
}
