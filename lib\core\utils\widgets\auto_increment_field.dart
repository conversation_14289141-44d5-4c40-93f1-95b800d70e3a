import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:logestics/firebase_service/firebase_services.dart';
import '../app_constants/styles/app_text_styles.dart';

class AutoIncrementField extends StatefulWidget {
  final String labelText;
  final String hintText;
  final String collectionName;
  final String documentId;
  final TextEditingController controller;
  final String? Function(String?)? validator;

  const AutoIncrementField({
    super.key,
    required this.labelText,
    required this.hintText,
    required this.collectionName,
    required this.documentId,
    required this.controller,
    this.validator,
  });

  @override
  State<AutoIncrementField> createState() => _AutoIncrementFieldState();
}

class _AutoIncrementFieldState extends State<AutoIncrementField> {
  final firebaseServices = FirebaseServices();
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _fetchNextNumber();
  }

  Future<void> _fetchNextNumber() async {
    try {
      final nextNumber = await firebaseServices.getNextAutoIncrementValue(
        collectionName: widget.collectionName,
        // the field Name can be invoice number or voucher number or region number
        fieldName: widget.documentId,
      );
      widget.controller.text = nextNumber.toString();
    } catch (e) {
      log('Error fetching next number: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: widget.controller,
      readOnly: true,
      style: AppTextStyles.inputTextStyle,
      decoration: InputDecoration(
        labelText: widget.labelText,
        hintText: widget.hintText,
        suffixIcon: _isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: Padding(
                  padding: EdgeInsets.all(8.0),
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              )
            : const Icon(Icons.lock_outline),
        border: const OutlineInputBorder(),
      ),
      validator: widget.validator,
    );
  }
}
