import 'dart:developer';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:excel/excel.dart';
import 'package:logestics/models/asset/asset_audit_model.dart';
import 'package:universal_html/html.dart' as html;

class AuditTrailExportController extends GetxController {
  final isLoading = false.obs;

  /// Export audit trail to Excel
  Future<void> exportAuditTrailToExcel(List<AssetAuditModel> auditEntries) async {
    if (auditEntries.isEmpty) {
      Get.snackbar(
        'No Data',
        'No audit entries to export',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 2),
      );
      return;
    }

    isLoading.value = true;

    try {
      // Generate filename
      final dateFormat = DateFormat('yyyy-MM-dd');
      final fileName = 'Audit_Trail_Export_${dateFormat.format(DateTime.now())}.xlsx';

      log('Generating Excel file: $fileName with ${auditEntries.length} audit entries');

      // Create Excel workbook
      final excel = Excel.createExcel();
      final sheet = excel['Audit Trail Report'];

      // Remove default sheet if it exists
      if (excel.sheets.containsKey('Sheet1')) {
        excel.delete('Sheet1');
      }

      int currentRow = 0;

      // Title
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
          .value = TextCellValue('ASSET AUDIT TRAIL REPORT');
      sheet.merge(
        CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow),
        CellIndex.indexByColumnRow(columnIndex: 7, rowIndex: currentRow),
      );
      currentRow++;

      // Report generation date
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
          .value = TextCellValue('Generated on: ${DateFormat('MMM dd, yyyy HH:mm').format(DateTime.now())}');
      currentRow++;

      // Summary section
      currentRow++;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
          .value = TextCellValue('SUMMARY');
      currentRow++;

      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
          .value = TextCellValue('Total Audit Entries: ${auditEntries.length}');
      currentRow++;

      // Action breakdown
      final actionBreakdown = <String, int>{};
      for (final entry in auditEntries) {
        actionBreakdown[entry.actionDisplayText] = 
            (actionBreakdown[entry.actionDisplayText] ?? 0) + 1;
      }

      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
          .value = TextCellValue('ACTION BREAKDOWN');
      currentRow++;

      for (final entry in actionBreakdown.entries) {
        sheet
            .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
            .value = TextCellValue('${entry.key}: ${entry.value} entries');
        currentRow++;
      }

      // User activity breakdown
      final userBreakdown = <String, int>{};
      for (final entry in auditEntries) {
        userBreakdown[entry.userName] = (userBreakdown[entry.userName] ?? 0) + 1;
      }

      currentRow++;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
          .value = TextCellValue('USER ACTIVITY BREAKDOWN');
      currentRow++;

      for (final entry in userBreakdown.entries) {
        sheet
            .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
            .value = TextCellValue('${entry.key}: ${entry.value} entries');
        currentRow++;
      }

      // Detailed audit entries
      currentRow += 2;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
          .value = TextCellValue('DETAILED AUDIT ENTRIES');
      currentRow++;

      // Headers
      final headers = [
        'Timestamp',
        'Action',
        'Asset Name',
        'User Name',
        'Field Changes',
        'Notes',
        'IP Address',
        'Device Info'
      ];

      for (int i = 0; i < headers.length; i++) {
        sheet
            .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: currentRow))
            .value = TextCellValue(headers[i]);
      }
      currentRow++;

      // Data rows
      for (final entry in auditEntries) {
        final rowData = [
          DateFormat('MMM dd, yyyy HH:mm:ss').format(entry.timestamp),
          entry.actionDisplayText,
          entry.assetName,
          entry.userName,
          entry.formattedFieldChanges.isNotEmpty ? entry.formattedFieldChanges : 'No changes',
          entry.notes.isNotEmpty ? entry.notes : 'No notes',
          entry.ipAddress.isNotEmpty ? entry.ipAddress : 'Unknown',
          entry.deviceInfo.isNotEmpty ? entry.deviceInfo : 'Unknown',
        ];

        for (int i = 0; i < rowData.length; i++) {
          sheet
              .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: currentRow))
              .value = TextCellValue(rowData[i]);
        }
        currentRow++;
      }

      // Generate Excel bytes
      final excelBytes = excel.encode();
      if (excelBytes == null) {
        throw Exception('Failed to generate Excel file');
      }

      // Download file for web
      if (kIsWeb) {
        await _downloadExcelWeb(Uint8List.fromList(excelBytes), fileName);
      } else {
        // For mobile/desktop, you would save to file system
        throw UnimplementedError('Mobile/Desktop Excel download not implemented');
      }

      log('Excel file generated successfully: $fileName');

      // Show success message
      Get.snackbar(
        'Success',
        'Excel file "$fileName" has been downloaded',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
      );
    } catch (e) {
      log('Error generating Excel file: $e');
      Get.snackbar(
        'Error',
        'Failed to generate Excel file: $e',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// Download Excel file for web platform
  Future<void> _downloadExcelWeb(Uint8List bytes, String fileName) async {
    try {
      final blob = html.Blob([bytes]);
      final url = html.Url.createObjectUrlFromBlob(blob);
      final anchor = html.document.createElement('a') as html.AnchorElement
        ..href = url
        ..style.display = 'none'
        ..download = fileName;
      html.document.body?.children.add(anchor);

      // Trigger download
      anchor.click();

      // Clean up
      html.document.body?.children.remove(anchor);
      html.Url.revokeObjectUrl(url);

      log('Excel file download triggered for web: $fileName');
    } catch (e) {
      log('Error downloading Excel file on web: $e');
      rethrow;
    }
  }

  /// Export filtered audit trail with date range
  Future<void> exportFilteredAuditTrail({
    required List<AssetAuditModel> auditEntries,
    String? actionFilter,
    String? userFilter,
    String? assetFilter,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    if (auditEntries.isEmpty) {
      Get.snackbar(
        'No Data',
        'No audit entries match the current filters',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 2),
      );
      return;
    }

    isLoading.value = true;

    try {
      // Generate filename with filter info
      final dateFormat = DateFormat('yyyy-MM-dd');
      var fileName = 'Filtered_Audit_Trail_${dateFormat.format(DateTime.now())}';
      
      if (actionFilter?.isNotEmpty == true) {
        fileName += '_${actionFilter!.replaceAll(' ', '_')}';
      }
      if (userFilter?.isNotEmpty == true) {
        fileName += '_${userFilter!.replaceAll(' ', '_')}';
      }
      if (assetFilter?.isNotEmpty == true) {
        fileName += '_${assetFilter!.replaceAll(' ', '_')}';
      }
      
      fileName += '.xlsx';

      log('Generating filtered Excel file: $fileName with ${auditEntries.length} audit entries');

      // Create Excel workbook
      final excel = Excel.createExcel();
      final sheet = excel['Filtered Audit Trail'];

      // Remove default sheet if it exists
      if (excel.sheets.containsKey('Sheet1')) {
        excel.delete('Sheet1');
      }

      int currentRow = 0;

      // Title
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
          .value = TextCellValue('FILTERED ASSET AUDIT TRAIL REPORT');
      sheet.merge(
        CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow),
        CellIndex.indexByColumnRow(columnIndex: 7, rowIndex: currentRow),
      );
      currentRow++;

      // Report generation date
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
          .value = TextCellValue('Generated on: ${DateFormat('MMM dd, yyyy HH:mm').format(DateTime.now())}');
      currentRow++;

      // Filter information
      currentRow++;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
          .value = TextCellValue('APPLIED FILTERS');
      currentRow++;

      if (actionFilter?.isNotEmpty == true) {
        sheet
            .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
            .value = TextCellValue('Action: $actionFilter');
        currentRow++;
      }

      if (userFilter?.isNotEmpty == true) {
        sheet
            .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
            .value = TextCellValue('User: $userFilter');
        currentRow++;
      }

      if (assetFilter?.isNotEmpty == true) {
        sheet
            .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
            .value = TextCellValue('Asset: $assetFilter');
        currentRow++;
      }

      if (startDate != null) {
        sheet
            .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
            .value = TextCellValue('Start Date: ${DateFormat('MMM dd, yyyy').format(startDate)}');
        currentRow++;
      }

      if (endDate != null) {
        sheet
            .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
            .value = TextCellValue('End Date: ${DateFormat('MMM dd, yyyy').format(endDate)}');
        currentRow++;
      }

      // Summary
      currentRow++;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
          .value = TextCellValue('Total Filtered Entries: ${auditEntries.length}');
      currentRow++;

      // Detailed entries (same as regular export)
      currentRow += 2;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
          .value = TextCellValue('DETAILED AUDIT ENTRIES');
      currentRow++;

      // Headers
      final headers = [
        'Timestamp',
        'Action',
        'Asset Name',
        'User Name',
        'Field Changes',
        'Notes',
        'IP Address',
        'Device Info'
      ];

      for (int i = 0; i < headers.length; i++) {
        sheet
            .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: currentRow))
            .value = TextCellValue(headers[i]);
      }
      currentRow++;

      // Data rows
      for (final entry in auditEntries) {
        final rowData = [
          DateFormat('MMM dd, yyyy HH:mm:ss').format(entry.timestamp),
          entry.actionDisplayText,
          entry.assetName,
          entry.userName,
          entry.formattedFieldChanges.isNotEmpty ? entry.formattedFieldChanges : 'No changes',
          entry.notes.isNotEmpty ? entry.notes : 'No notes',
          entry.ipAddress.isNotEmpty ? entry.ipAddress : 'Unknown',
          entry.deviceInfo.isNotEmpty ? entry.deviceInfo : 'Unknown',
        ];

        for (int i = 0; i < rowData.length; i++) {
          sheet
              .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: currentRow))
              .value = TextCellValue(rowData[i]);
        }
        currentRow++;
      }

      // Generate Excel bytes
      final excelBytes = excel.encode();
      if (excelBytes == null) {
        throw Exception('Failed to generate Excel file');
      }

      // Download file for web
      if (kIsWeb) {
        await _downloadExcelWeb(Uint8List.fromList(excelBytes), fileName);
      } else {
        // For mobile/desktop, you would save to file system
        throw UnimplementedError('Mobile/Desktop Excel download not implemented');
      }

      log('Filtered Excel file generated successfully: $fileName');

      // Show success message
      Get.snackbar(
        'Success',
        'Filtered Excel file "$fileName" has been downloaded',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
      );
    } catch (e) {
      log('Error generating filtered Excel file: $e');
      Get.snackbar(
        'Error',
        'Failed to generate filtered Excel file: $e',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
      );
    } finally {
      isLoading.value = false;
    }
  }
}
