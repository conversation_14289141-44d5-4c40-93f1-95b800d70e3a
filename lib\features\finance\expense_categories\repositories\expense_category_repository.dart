import 'dart:developer';

import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/firebase_service/finance/expense_category_firebase_service.dart';
import 'package:logestics/models/finance/expense_category_model.dart';

abstract class ExpenseCategoryRepository {
  // Get all expense categories
  Future<Either<FailureObj, List<ExpenseCategoryModel>>> getExpenseCategories();

  // Create a new expense category
  Future<Either<FailureObj, SuccessObj>> createExpenseCategory(
      ExpenseCategoryModel category);

  // Update an existing expense category
  Future<Either<FailureObj, SuccessObj>> updateExpenseCategory(
      ExpenseCategoryModel category);

  // Delete an expense category
  Future<Either<FailureObj, SuccessObj>> deleteExpenseCategory(
      String categoryId);
}

class ExpenseCategoryRepositoryImpl implements ExpenseCategoryRepository {
  final ExpenseCategoryFirebaseService _firebaseService;

  ExpenseCategoryRepositoryImpl(this._firebaseService);

  @override
  Future<Either<FailureObj, List<ExpenseCategoryModel>>>
      getExpenseCategories() async {
    try {
      log('Fetching all expense categories');
      final categories = await _firebaseService.getExpenseCategories();
      return Right(categories);
    } catch (e) {
      log('Error fetching expense categories: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> createExpenseCategory(
      ExpenseCategoryModel category) async {
    try {
      log('Creating expense category in repository: ${category.name}');
      await _firebaseService.createExpenseCategory(
          name: category.name, description: category.description);
      return Right(
          SuccessObj(message: 'Expense category created successfully'));
    } catch (e) {
      log('Error creating expense category: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> updateExpenseCategory(
      ExpenseCategoryModel category) async {
    try {
      log('Updating expense category: ${category.id}');
      await _firebaseService.updateExpenseCategory(category);
      return Right(
          SuccessObj(message: 'Expense category updated successfully'));
    } catch (e) {
      log('Error updating expense category: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> deleteExpenseCategory(
      String categoryId) async {
    try {
      log('Deleting expense category: $categoryId');
      await _firebaseService.deleteExpenseCategory(categoryId);
      return Right(
          SuccessObj(message: 'Expense category deleted successfully'));
    } catch (e) {
      log('Error deleting expense category: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }
}
