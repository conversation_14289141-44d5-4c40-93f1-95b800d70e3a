import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';
import '../../controllers/dashboard_data_controller.dart';
import '../../widgets/total_invoices_widget.dart';
import '../../widgets/voucher_financial_insights_widget.dart';
import '../../widgets/recent_invoices_widget.dart';
import '../../widgets/asset_insights_widget.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  late DashboardDataController dashboardController;

  @override
  void initState() {
    super.initState();
    try {
      // Get the dashboard controller from dependency injection
      dashboardController = Get.find<DashboardDataController>();

      // Add a small delay to ensure controller is fully initialized
      WidgetsBinding.instance.addPostFrameCallback((_) {
        dashboardController.loadDashboardData();
      });
    } catch (e) {
      // Fallback: create controller manually
      dashboardController = Get.put(DashboardDataController());

      WidgetsBinding.instance.addPostFrameCallback((_) {
        dashboardController.loadDashboardData();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    notifier = Provider.of(context, listen: true);

    // Always return content without Scaffold when used in drawer system
    // The parent MyHomePage already provides the Scaffold structure
    return Container(
      color: notifier.getBgColor,
      child: RefreshIndicator(
        onRefresh: () async {
          await dashboardController.refreshData();
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Welcome header
              _buildWelcomeHeader(),
              const SizedBox(height: 24),

              // Dashboard widgets
              _buildDashboardContent(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Dashboard',
          style: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: notifier.text,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Real-time insights for your logistics business',
          style: TextStyle(
            fontSize: 16,
            color: notifier.text.withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }

  Widget _buildDashboardContent() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Responsive layout based on screen width
        if (constraints.maxWidth > 1200) {
          // Desktop layout - 3 columns
          return _buildDesktopLayout();
        } else if (constraints.maxWidth > 800) {
          // Tablet layout - 2 columns
          return _buildTabletLayout();
        } else {
          // Mobile layout - 1 column
          return _buildMobileLayout();
        }
      },
    );
  }

  Widget _buildDesktopLayout() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left column
        Expanded(
          flex: 1,
          child: Column(
            children: [
              const TotalInvoicesWidget(),
              const SizedBox(height: 20),
              const AssetInsightsWidget(),
              const SizedBox(height: 20),
              const RecentInvoicesWidget(),
            ],
          ),
        ),
        const SizedBox(width: 20),

        // Right column
        Expanded(
          flex: 1,
          child: const VoucherFinancialInsightsWidget(),
        ),
      ],
    );
  }

  Widget _buildTabletLayout() {
    return Column(
      children: [
        // Top row
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(child: const TotalInvoicesWidget()),
            const SizedBox(width: 20),
            Expanded(child: const AssetInsightsWidget()),
          ],
        ),
        const SizedBox(height: 20),

        // Middle row
        const VoucherFinancialInsightsWidget(),
        const SizedBox(height: 20),

        // Bottom row
        const RecentInvoicesWidget(),
      ],
    );
  }

  Widget _buildMobileLayout() {
    return Column(
      children: [
        const TotalInvoicesWidget(),
        const SizedBox(height: 20),
        const AssetInsightsWidget(),
        const SizedBox(height: 20),
        const VoucherFinancialInsightsWidget(),
        const SizedBox(height: 20),
        const RecentInvoicesWidget(),
      ],
    );
  }

  @override
  void dispose() {
    // Clean up controller if needed
    super.dispose();
  }
}
