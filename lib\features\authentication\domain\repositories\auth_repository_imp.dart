import 'dart:async';
import 'dart:io';

import 'package:either_dart/either.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/firebase_service/firebase_auth_service.dart';
import '../../../../core/utils/app_constants/texts/app_strings.dart';
import '../../../../models/auth_failer.dart';
import 'auth_repository.dart';

class AuthRepositoryImpl implements AuthRepository {
  final FirebaseAuthService firebaseAuthService;

  AuthRepositoryImpl(this.firebaseAuthService);

  @override
  Future<Either<AuthFailure, UserCredential>> login(
      String email, String password) async {
    try {
      // Call the authentication service for login
      var userCredential = await firebaseAuthService
          .login(email: email, password: password)
          .timeout(
            const Duration(seconds: 10),
            onTimeout: () => throw TimeoutException(AppStrings.timeout),
          );
      return Right(userCredential);
    } on FirebaseAuthException catch (e) {
      // Firebase specific error handling
      switch (e.code) {
        case 'user-not-found':
          return Left(AuthFailure(
              field: 'email', code: e.code, message: AppStrings.noUserFound));
        case 'wrong-password':
          return Left(AuthFailure(
              field: 'password',
              code: e.code,
              message: AppStrings.incorrectPassword));
        case 'invalid-email':
          return Left(AuthFailure(
              field: 'email', code: e.code, message: AppStrings.invalidEmail));
        case 'user-disabled':
          return Left(AuthFailure(
              field: 'email', code: e.code, message: AppStrings.userDisabled));
        case 'too-many-requests':
          return Left(
              AuthFailure(code: e.code, message: AppStrings.tooManyRequests));
        case 'network-request-failed':
          return Left(
              AuthFailure(code: e.code, message: AppStrings.networkError));
        case 'invalid-credential':
          return Left(
              AuthFailure(code: e.code, message: AppStrings.invalidCredential));
        default:
          return Left(AuthFailure(
              code: e.code,
              message: "${AppStrings.authError} ${e.code} ${e.message}"));
      }
    } on TimeoutException catch (e) {
      // Handle timeout error
      return Left(
          AuthFailure(message: "${AppStrings.timeoutError} ${e.message}"));
    } on SocketException {
      // Handle no internet connection
      return Left(AuthFailure(message: AppStrings.noInternet));
    } catch (e) {
      // Generic error handling
      return Left(AuthFailure(
          message: "${AppStrings.unexpectedError} ${e.toString()}"));
    }
  }

  @override
  Future<Either<AuthFailure, void>> sendPasswordResetEmail(String email) async {
    try {
      // Send the password reset email
      await FirebaseAuth.instance.sendPasswordResetEmail(email: email).timeout(
            const Duration(seconds: 10),
            onTimeout: () => throw TimeoutException(AppStrings.timeout),
          );

      return const Right(null); // Success
    } on FirebaseAuthException catch (e) {
      // Handle Firebase-specific errors
      switch (e.code) {
        case 'invalid-email':
          return Left(AuthFailure(
            field: 'email',
            message: AppStrings.invalidEmail,
          ));
        case 'user-not-found':
          return Left(
            AuthFailure(
              field: 'email',
              message: AppStrings.noUserFound,
            ),
          );
        case 'user-disabled':
          return Left(AuthFailure(
              field: 'email', code: e.code, message: AppStrings.userDisabled));
        case 'too-many-requests':
          return Left(
              AuthFailure(code: e.code, message: AppStrings.tooManyRequests));
        case 'network-request-failed':
          return Left(
              AuthFailure(code: e.code, message: AppStrings.networkError));
        case 'invalid-credential':
          return Left(
              AuthFailure(code: e.code, message: AppStrings.invalidCredential));
        default:
          return Left(AuthFailure(
            message: "${AppStrings.authError} ${e.code} ${e.message}",
          ));
      }
    } on TimeoutException catch (e) {
      // Handle timeout error
      return Left(AuthFailure(
        message: "${AppStrings.timeoutError} ${e.message}",
      ));
    } on SocketException {
      // Handle no internet connection
      return Left(AuthFailure(
        message: AppStrings.noInternet,
      ));
    } catch (e) {
      // Generic error handling
      return Left(AuthFailure(
        message: "${AppStrings.unexpectedError} ${e.toString()}",
      ));
    }
  }

  @override
  Future<void> enableLocalPersistence(persistence) async {
    await firebaseAuthService.enableLocalPersistence(persistence: persistence);
  }

  @override
  Future<Either<AuthFailure, void>> signOut() async {
    try {
      await firebaseAuthService.signOut(); // Call to FirebaseAuthService
      return const Right(null); // Sign out successful
    } on TimeoutException catch (e) {
      return Left(
          AuthFailure(message: "Timeout occurred: ${e.message ?? "unknown"}"));
    } catch (e) {
      return Left(AuthFailure(message: "Unexpected error: ${e.toString()}"));
    }
  }
}
