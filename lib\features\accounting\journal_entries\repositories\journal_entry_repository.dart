import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dartz/dartz.dart';
import '../../../../core/shared_services/failure_obj.dart';
import '../../../../core/shared_services/success_obj.dart';
import '../../../../core/utils/app_constants/texts/app_strings.dart';
import '../../../../firebase_service/accounting/journal_entry_firebase_service.dart';
import '../../../../models/finance/journal_entry_model.dart';

abstract class JournalEntryRepository {
  Future<Either<FailureObj, List<JournalEntryModel>>> getJournalEntries();
  Future<Either<FailureObj, List<JournalEntryModel>>>
      getJournalEntriesByDateRange(DateTime startDate, DateTime endDate);
  Future<Either<FailureObj, JournalEntryModel?>> getJournalEntryById(
      String entryId);
  Future<Either<FailureObj, SuccessObj>> createJournalEntry(
      JournalEntryModel journalEntry);
  Future<Either<FailureObj, SuccessObj>> updateJournalEntryStatus(
      String entryId, JournalEntryStatus status);
  Future<Either<FailureObj, SuccessObj>> postJournalEntry(String entryId);
  Future<Either<FailureObj, SuccessObj>> reverseJournalEntry(
      String entryId, JournalEntryModel reversalEntry);
  Future<Either<FailureObj, SuccessObj>> deleteJournalEntry(String entryId);
  Future<Either<FailureObj, String>> getNextJournalEntryNumber();
  Future<Either<FailureObj, List<JournalEntryLineModel>>>
      getJournalEntriesByAccount(String accountId);
  Future<Either<FailureObj, List<JournalEntryModel>>>
      getJournalEntriesForAccount({
    required String accountId,
    required String uid,
    int limit = 25,
    QueryDocumentSnapshot? lastDocument,
    DateTime? startDate,
    DateTime? endDate,
    List<JournalEntryStatus>? statusFilter,
  });
  Future<Either<FailureObj, double>> calculateAccountBalance(String accountId);
  Stream<List<JournalEntryModel>> listenToJournalEntries();
}

class JournalEntryRepositoryImpl implements JournalEntryRepository {
  final JournalEntryFirebaseService _firebaseService;

  JournalEntryRepositoryImpl(this._firebaseService);

  @override
  Future<Either<FailureObj, SuccessObj>> createJournalEntry(
      JournalEntryModel journalEntry) async {
    try {
      log('Creating journal entry in repository: ${journalEntry.description}');
      await _firebaseService.createJournalEntry(journalEntry);
      return Right(SuccessObj(message: 'Journal entry created successfully'));
    } catch (e) {
      log('Error creating journal entry: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, List<JournalEntryModel>>>
      getJournalEntries() async {
    try {
      log('🔍 [REPO DEBUG] Starting to fetch journal entries from repository');
      final entries = await _firebaseService.getJournalEntries();
      log('🔍 [REPO DEBUG] Firebase service returned ${entries.length} entries');

      for (int i = 0; i < entries.length && i < 3; i++) {
        log('🔍 [REPO DEBUG] Entry $i: ${entries[i].entryNumber} - ${entries[i].description}');
      }

      return Right(entries);
    } catch (e) {
      log('❌ [REPO DEBUG] Error fetching journal entries: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, List<JournalEntryModel>>>
      getJournalEntriesByDateRange(DateTime startDate, DateTime endDate) async {
    try {
      log('Fetching journal entries by date range from repository: $startDate to $endDate');
      final entries = await _firebaseService.getJournalEntriesByDateRange(
          startDate, endDate);
      return Right(entries);
    } catch (e) {
      log('Error fetching journal entries by date range: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, JournalEntryModel?>> getJournalEntryById(
      String entryId) async {
    try {
      log('Fetching journal entry by ID from repository: $entryId');
      final entry = await _firebaseService.getJournalEntryById(entryId);
      return Right(entry);
    } catch (e) {
      log('Error fetching journal entry by ID: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> updateJournalEntryStatus(
      String entryId, JournalEntryStatus status) async {
    try {
      log('Updating journal entry status in repository: $entryId to ${status.displayName}');
      await _firebaseService.updateJournalEntryStatus(entryId, status);
      return Right(
          SuccessObj(message: 'Journal entry status updated successfully'));
    } catch (e) {
      log('Error updating journal entry status: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> postJournalEntry(
      String entryId) async {
    try {
      log('Posting journal entry in repository: $entryId');
      await _firebaseService.postJournalEntry(entryId);
      return Right(SuccessObj(message: 'Journal entry posted successfully'));
    } catch (e) {
      log('Error posting journal entry: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> reverseJournalEntry(
      String entryId, JournalEntryModel reversalEntry) async {
    try {
      log('Reversing journal entry in repository: $entryId');
      await _firebaseService.reverseJournalEntry(entryId, reversalEntry);
      return Right(SuccessObj(message: 'Journal entry reversed successfully'));
    } catch (e) {
      log('Error reversing journal entry: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> deleteJournalEntry(
      String entryId) async {
    try {
      log('Deleting journal entry in repository: $entryId');
      await _firebaseService.deleteJournalEntry(entryId);
      return Right(SuccessObj(message: 'Journal entry deleted successfully'));
    } catch (e) {
      log('Error deleting journal entry: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, String>> getNextJournalEntryNumber() async {
    try {
      log('Getting next journal entry number from repository');
      final nextNumber = await _firebaseService.getNextJournalEntryNumber();
      return Right(nextNumber);
    } catch (e) {
      log('Error getting next journal entry number: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, List<JournalEntryLineModel>>>
      getJournalEntriesByAccount(String accountId) async {
    try {
      log('Fetching journal entries by account from repository: $accountId');
      final lines =
          await _firebaseService.getJournalEntriesByAccount(accountId);
      return Right(lines);
    } catch (e) {
      log('Error fetching journal entries by account: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, List<JournalEntryModel>>>
      getJournalEntriesForAccount({
    required String accountId,
    required String uid,
    int limit = 25,
    QueryDocumentSnapshot? lastDocument,
    DateTime? startDate,
    DateTime? endDate,
    List<JournalEntryStatus>? statusFilter,
  }) async {
    try {
      log('Fetching journal entries for account from repository: $accountId');
      final entries = await _firebaseService.getJournalEntriesForAccount(
        accountId: accountId,
        uid: uid,
        limit: limit,
        lastDocument: lastDocument,
        startDate: startDate,
        endDate: endDate,
        statusFilter: statusFilter,
      );
      return Right(entries);
    } catch (e) {
      log('Error fetching journal entries for account: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, double>> calculateAccountBalance(
      String accountId) async {
    try {
      log('Calculating account balance from repository: $accountId');
      final balance = await _firebaseService.calculateAccountBalance(accountId);
      return Right(balance);
    } catch (e) {
      log('Error calculating account balance: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Stream<List<JournalEntryModel>> listenToJournalEntries() {
    try {
      log('Setting up real-time listener for journal entries');
      return _firebaseService.listenToJournalEntries();
    } catch (e) {
      log('Error setting up journal entries listener: $e');
      return Stream.value([]);
    }
  }
}
