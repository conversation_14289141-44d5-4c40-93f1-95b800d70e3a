import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:logestics/firebase_service/firebase_auth_service.dart';

class AuthMiddleware extends GetMiddleware {
  @override
  RouteSettings? redirect(String? route) {
    try {
      final authService = Get.find<FirebaseAuthService>();

      // If user is not logged in, redirect to Login page
      if (authService.currentUser == null) {
        return const RouteSettings(name: '/login');
      }
      return null; // Allow access
    } catch (e) {
      // If there's any error with the auth service, redirect to login
      return const RouteSettings(name: '/login');
    }
  }
}
