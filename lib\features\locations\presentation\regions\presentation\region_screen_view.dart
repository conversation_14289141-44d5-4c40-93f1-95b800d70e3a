import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/bindings/app_bindings.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/widgets/header_secondary.dart';
import 'package:logestics/features/home/<USER>/drawer_controllers.dart';
import 'package:logestics/features/locations/presentation/regions/presentation/region_list.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';

class RegionsScreenView extends StatefulWidget {
  const RegionsScreenView({super.key});

  @override
  State<RegionsScreenView> createState() => _RegionsScreenViewState();
}

class _RegionsScreenViewState extends State<RegionsScreenView> {
  MainDrawerController mainDrawerController = Get.put(MainDrawerController());

  @override
  void initState() {
    super.initState();
    // Apply bindings here to ensure they're only applied once when the screen is created
    AppBindings().dependencies();
  }

  @override
  Widget build(BuildContext context) {
    var width = Get.width;

    notifier = Provider.of(context, listen: true);

    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          child: Column(
            children: [
              SizedBox(
                height: width < 650 ? 55 : 40,
                width: width,
                child: width < 650
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            AppStrings.regions,
                            overflow: TextOverflow.ellipsis,
                            style: AppTextStyles.titleStyle
                                .copyWith(color: notifier.text),
                          ),
                          const Spacer(),
                          HeaderSecondary(
                            option1: AppStrings.dashboard,
                            option2: AppStrings.system,
                            option3: AppStrings.regions,
                            notifier: notifier,
                          ),
                        ],
                      )
                    : Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            AppStrings.regions,
                            overflow: TextOverflow.ellipsis,
                            style: AppTextStyles.titleStyle
                                .copyWith(color: notifier.text),
                          ),
                          const Spacer(),
                          HeaderSecondary(
                            option1: AppStrings.dashboard,
                            option2: AppStrings.system,
                            option3: AppStrings.regions,
                            notifier: notifier,
                          ),
                        ],
                      ),
              ),
              const SizedBox(height: 20),
              SizedBox(
                height: 570,
                child: RegionList(),
              ),
            ],
          ),
        );
      },
    );
  }
}
