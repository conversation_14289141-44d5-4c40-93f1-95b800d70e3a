import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/features/authentication/presentation/screens/login_screen/components/back_colors.dart';
import 'package:logestics/features/authentication/presentation/controllers/signup_controller.dart';

import '../../../../../core/utils/app_constants/assets/app_assets.dart';
import '../../../../../core/utils/helpers/responsive_helper.dart';
import 'components/signup_component.dart';

class SignupScreen extends GetView<SignupController> {
  const SignupScreen({super.key});

  @override
  Widget build(BuildContext context) {
    var screenWidth = MediaQuery.of(context).size.width;
    return SafeArea(
      child: Scaffold(
        body: Stack(
          fit: StackFit.expand,
          children: [
            BackColors(),
            Center(
              child: Container(
                width: 900,
                height: 600, // Slightly taller to accommodate more fields
                padding: EdgeInsets.symmetric(horizontal: 40, vertical: 40),
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                          color: Colors.black.withAlpha(10),
                          offset: Offset(2, 2),
                          blurRadius: 10),
                      BoxShadow(
                          color: Colors.black.withAlpha(10),
                          offset: Offset(-2, -2),
                          blurRadius: 10)
                    ]),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: !Responsive.isTablet(context)
                      ? CrossAxisAlignment.start
                      : CrossAxisAlignment.center,
                  children: [
                    if (screenWidth > 770)
                      Expanded(child: Image.asset(AppAssets.onBoardingPng1)),
                    if (Responsive.isTablet(context))
                      SizedBox(
                        width: 60,
                      ),
                    Expanded(child: const SignupComponent()),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
