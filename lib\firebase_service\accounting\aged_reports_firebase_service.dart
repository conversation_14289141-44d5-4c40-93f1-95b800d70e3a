import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:either_dart/either.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/models/finance/aged_report_model.dart';
import 'package:logestics/models/invoice_model.dart';
import 'package:logestics/models/finance/bill_model.dart';

/// Firebase service for Aged Reports (Receivables and Payables)
class AgedReportsFirebaseService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final String _uid = FirebaseAuth.instance.currentUser?.uid ?? 'anonymous';

  /// Generate Aged Receivables Report
  Future<Either<FailureObj, AgedReceivablesReport>>
      generateAgedReceivablesReport({
    required DateTime asOfDate,
    required String companyName,
    bool includeZeroBalances = false,
    List<String>? customerIds,
  }) async {
    try {
      log('Generating Aged Receivables Report as of: ${asOfDate.toIso8601String()}');

      // Get all invoices for the company
      Query invoiceQuery = _firestore
          .collection(AppCollection.invoicesCollection)
          .where('uid', isEqualTo: _uid)
          .where('createdAt',
              isLessThanOrEqualTo: asOfDate.millisecondsSinceEpoch);

      // Add customer filter if specified
      if (customerIds != null && customerIds.isNotEmpty) {
        invoiceQuery = invoiceQuery.where('customerName', whereIn: customerIds);
      }

      final invoiceSnapshot = await invoiceQuery.get();
      final invoices = invoiceSnapshot.docs
          .map((doc) =>
              InvoiceModel.fromJson(doc.data() as Map<String, dynamic>))
          .toList();

      log('Found ${invoices.length} invoices for aged receivables calculation');

      // Create aged receivable entries
      final List<AgedReceivableEntry> entries = [];

      for (final invoice in invoices) {
        // For now, assume invoices are unpaid (in a real system, you'd check payment records)
        // This is a simplified implementation - you would need to track payments
        final outstandingAmount = _calculateInvoiceOutstanding(invoice);

        if (!includeZeroBalances && outstandingAmount <= 0) {
          continue;
        }

        final daysOutstanding = asOfDate.difference(invoice.createdAt).inDays;
        final agingPeriod =
            AgedReceivableEntry.calculateAgingPeriod(daysOutstanding);

        final entry = AgedReceivableEntry(
          customerId: invoice.customerName, // Using customer name as ID for now
          customerName: invoice.customerName,
          invoiceId: invoice.tasNumber,
          invoiceNumber: invoice.invoiceNumber.toString(),
          invoiceDate: invoice.createdAt,
          dueDate: invoice.belongsToDate, // Using belongs to date as due date
          totalAmount: outstandingAmount,
          paidAmount: 0.0, // Simplified - would need payment tracking
          outstandingAmount: outstandingAmount,
          daysOutstanding: daysOutstanding,
          agingPeriod: agingPeriod,
          status: invoice.invoiceStatus,
          notes: 'Invoice ${invoice.invoiceNumber}',
        );

        entries.add(entry);
      }

      log('Created ${entries.length} aged receivable entries');

      // Create the report
      final report = AgedReceivablesReport.fromEntries(
        reportId: 'AR_${DateTime.now().millisecondsSinceEpoch}',
        asOfDate: asOfDate,
        companyName: companyName,
        companyUid: _uid,
        entries: entries,
      );

      log('Generated Aged Receivables Report with ${report.totalCustomers} customers and total outstanding: ${report.grandTotalOutstanding}');

      return Right(report);
    } catch (e, stackTrace) {
      log('Error generating aged receivables report: $e');
      log('Stack trace: $stackTrace');
      return Left(FailureObj(
        code: 'ERROR',
        message: 'Failed to generate aged receivables report: $e',
      ));
    }
  }

  /// Generate Aged Payables Report
  Future<Either<FailureObj, AgedPayablesReport>> generateAgedPayablesReport({
    required DateTime asOfDate,
    required String companyName,
    bool includeZeroBalances = false,
    List<String>? vendorIds,
  }) async {
    try {
      log('Generating Aged Payables Report as of: ${asOfDate.toIso8601String()}');

      // Get all bills for the company
      Query billQuery = _firestore
          .collection(AppCollection.billsCollection)
          .where('companyUid', isEqualTo: _uid)
          .where('createdAt',
              isLessThanOrEqualTo: asOfDate.millisecondsSinceEpoch);

      final billSnapshot = await billQuery.get();
      final bills = billSnapshot.docs
          .map((doc) => BillModel.fromJson(doc.data() as Map<String, dynamic>))
          .toList();

      log('Found ${bills.length} bills for aged payables calculation');

      // Create aged payable entries
      final List<AgedPayableEntry> entries = [];

      for (final bill in bills) {
        // For now, assume bills are unpaid (in a real system, you'd check payment records)
        final outstandingAmount = _calculateBillOutstanding(bill);

        if (!includeZeroBalances && outstandingAmount <= 0) {
          continue;
        }

        final daysOutstanding = asOfDate.difference(bill.billDate).inDays;
        final agingPeriod =
            AgedReceivableEntry.calculateAgingPeriod(daysOutstanding);

        final entry = AgedPayableEntry(
          vendorId: bill.customerName ??
              'Unknown', // Using customer name as vendor for now
          vendorName: bill.customerName ?? 'Unknown Vendor',
          billId: bill.billId,
          billNumber: bill.billNumber,
          billDate: bill.billDate,
          dueDate: bill.dueDate,
          totalAmount: bill.totalAmount,
          paidAmount: 0.0, // Simplified - would need payment tracking
          outstandingAmount: outstandingAmount,
          daysOutstanding: daysOutstanding,
          agingPeriod: agingPeriod,
          status: bill.billStatus,
          notes: bill.notes,
        );

        entries.add(entry);
      }

      log('Created ${entries.length} aged payable entries');

      // Create the report
      final report = AgedPayablesReport.fromEntries(
        reportId: 'AP_${DateTime.now().millisecondsSinceEpoch}',
        asOfDate: asOfDate,
        companyName: companyName,
        companyUid: _uid,
        entries: entries,
      );

      log('Generated Aged Payables Report with ${report.totalVendors} vendors and total outstanding: ${report.grandTotalOutstanding}');

      return Right(report);
    } catch (e, stackTrace) {
      log('Error generating aged payables report: $e');
      log('Stack trace: $stackTrace');
      return Left(FailureObj(
        code: 'ERROR',
        message: 'Failed to generate aged payables report: $e',
      ));
    }
  }

  /// Save Aged Receivables Report
  Future<void> saveAgedReceivablesReport(AgedReceivablesReport report) async {
    try {
      log('Saving Aged Receivables Report: ${report.reportId}');
      await _firestore
          .collection(AppCollection.agedReceivablesReportsCollection)
          .doc(report.reportId)
          .set(report.toMap());
      log('Aged Receivables Report saved successfully');
    } catch (e) {
      log('Error saving aged receivables report: $e');
      throw Exception('Failed to save aged receivables report: $e');
    }
  }

  /// Save Aged Payables Report
  Future<void> saveAgedPayablesReport(AgedPayablesReport report) async {
    try {
      log('Saving Aged Payables Report: ${report.reportId}');
      await _firestore
          .collection(AppCollection.agedPayablesReportsCollection)
          .doc(report.reportId)
          .set(report.toMap());
      log('Aged Payables Report saved successfully');
    } catch (e) {
      log('Error saving aged payables report: $e');
      throw Exception('Failed to save aged payables report: $e');
    }
  }

  /// Get saved Aged Receivables Reports
  Future<List<AgedReceivablesReport>> getSavedAgedReceivablesReports() async {
    try {
      log('Fetching saved Aged Receivables Reports');
      final snapshot = await _firestore
          .collection(AppCollection.agedReceivablesReportsCollection)
          .where('companyUid', isEqualTo: _uid)
          .orderBy('generatedAt', descending: true)
          .get();

      final reports = snapshot.docs
          .map((doc) => AgedReceivablesReport.fromMap(doc.data()))
          .toList();

      log('Found ${reports.length} saved aged receivables reports');
      return reports;
    } catch (e) {
      log('Error fetching saved aged receivables reports: $e');
      return [];
    }
  }

  /// Get saved Aged Payables Reports
  Future<List<AgedPayablesReport>> getSavedAgedPayablesReports() async {
    try {
      log('Fetching saved Aged Payables Reports');
      final snapshot = await _firestore
          .collection(AppCollection.agedPayablesReportsCollection)
          .where('companyUid', isEqualTo: _uid)
          .orderBy('generatedAt', descending: true)
          .get();

      final reports = snapshot.docs
          .map((doc) => AgedPayablesReport.fromMap(doc.data()))
          .toList();

      log('Found ${reports.length} saved aged payables reports');
      return reports;
    } catch (e) {
      log('Error fetching saved aged payables reports: $e');
      return [];
    }
  }

  /// Helper method to calculate invoice outstanding amount
  /// In a real system, this would check payment records
  double _calculateInvoiceOutstanding(InvoiceModel invoice) {
    // Simplified calculation - in reality, you'd subtract payments
    // For now, we'll use a basic calculation based on invoice data
    final totalWeight = invoice.numberOfBags * invoice.weightPerBag;
    // Assuming a rate per kg - this would come from your billing logic
    const double ratePerKg = 50.0; // Example rate
    return totalWeight * ratePerKg;
  }

  /// Helper method to calculate bill outstanding amount
  /// In a real system, this would check payment records
  double _calculateBillOutstanding(BillModel bill) {
    // For bills, we have the total amount directly
    return bill.totalAmount;
  }
}
