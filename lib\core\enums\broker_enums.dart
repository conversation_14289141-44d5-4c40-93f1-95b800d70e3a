/// Enum for broker types in vouchers
enum BrokerType {
  own('Own'),
  outsource('Outsource');

  const BrokerType(this.value);
  final String value;

  /// Get BrokerType from string value
  static BrokerType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'own':
        return BrokerType.own;
      case 'outsource':
        return BrokerType.outsource;
      default:
        throw ArgumentError('Invalid broker type: $value');
    }
  }

  /// Get BrokerType from string value with null safety
  static BrokerType? fromStringOrNull(String? value) {
    if (value == null || value.isEmpty) return null;
    try {
      return fromString(value);
    } catch (e) {
      return null;
    }
  }

  /// Get all broker type values as strings
  static List<String> get allValues =>
      BrokerType.values.map((e) => e.value).toList();
}

/// Enum for voucher status
enum VoucherStatus {
  pending('Pending'),
  completed('Completed'),
  cancelled('Cancelled');

  const VoucherStatus(this.value);
  final String value;

  /// Get VoucherStatus from string value
  static VoucherStatus fromString(String value) {
    switch (value.toLowerCase()) {
      case 'pending':
        return VoucherStatus.pending;
      case 'completed':
        return VoucherStatus.completed;
      case 'cancelled':
        return VoucherStatus.cancelled;
      default:
        throw ArgumentError('Invalid voucher status: $value');
    }
  }

  /// Get VoucherStatus from string value with null safety
  static VoucherStatus? fromStringOrNull(String? value) {
    if (value == null || value.isEmpty) return null;
    try {
      return fromString(value);
    } catch (e) {
      return null;
    }
  }

  /// Get all voucher status values as strings
  static List<String> get allValues =>
      VoucherStatus.values.map((e) => e.value).toList();
}

/// Enum for accounting transaction types
enum AccountingTransactionType {
  deposit('Deposit'),
  expense('Expense'),
  debit('Debit');

  const AccountingTransactionType(this.value);
  final String value;

  /// Get AccountingTransactionType from string value
  static AccountingTransactionType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'deposit':
        return AccountingTransactionType.deposit;
      case 'expense':
        return AccountingTransactionType.expense;
      case 'debit':
        return AccountingTransactionType.debit;
      default:
        throw ArgumentError('Invalid accounting transaction type: $value');
    }
  }

  /// Get AccountingTransactionType from string value with null safety
  static AccountingTransactionType? fromStringOrNull(String? value) {
    if (value == null || value.isEmpty) return null;
    try {
      return fromString(value);
    } catch (e) {
      return null;
    }
  }

  /// Get all accounting transaction type values as strings
  static List<String> get allValues =>
      AccountingTransactionType.values.map((e) => e.value).toList();
}

/// Helper class for broker accounting rules
class BrokerAccountingRules {
  /// Get the accounting transaction type for a broker type
  static AccountingTransactionType getTransactionType(BrokerType brokerType) {
    switch (brokerType) {
      case BrokerType.own:
        return AccountingTransactionType
            .deposit; // Own broker = deposit+ (money coming IN)
      case BrokerType.outsource:
        return AccountingTransactionType
            .expense; // Outsource broker = expense- (money going OUT)
    }
  }

  /// Get the accounting description for a broker type
  static String getAccountingDescription(BrokerType brokerType) {
    switch (brokerType) {
      case BrokerType.own:
        return 'Own broker fees - adds to account and deposit history';
      case BrokerType.outsource:
        return 'Outsource broker fees - subtracts from account and goes to expense section';
    }
  }
}

/// Helper class for munshiana accounting rules
class MunshianaAccountingRules {
  /// Munshiana is always a deposit (money coming IN)
  static AccountingTransactionType get transactionType =>
      AccountingTransactionType.deposit;

  /// Get the accounting description for munshiana
  static String get accountingDescription =>
      'Munshiana payment - adds to account and deposit history';
}
