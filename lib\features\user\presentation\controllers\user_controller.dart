import 'dart:developer';

import 'package:get/get.dart';
import 'package:logestics/firebase_service/firebase_auth_service.dart';
import 'package:logestics/firebase_service/finance/company_firebase_service.dart';
import 'package:logestics/models/user_model.dart';

class UserController extends GetxController {
  final FirebaseAuthService _authService = Get.find<FirebaseAuthService>();
  final CompanyFirebaseService _companyService = CompanyFirebaseService();

  // Observable current user
  final Rx<UserModel?> currentUser = Rx<UserModel?>(null);
  final RxBool isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeCurrentUser();
  }

  void _initializeCurrentUser() {
    final authUser = _authService.currentUser;
    if (authUser != null) {
      _loadUserDetails();
    }
  }

  Future<void> _loadUserDetails() async {
    final authUser = _authService.currentUser;
    if (authUser == null) return;

    isLoading.value = true;

    try {
      // Try to get user details from Firestore
      final result = await _companyService.getUserById(authUser.uid);

      result.fold(
        (failure) {
          // If user not found in Firestore, create basic user from auth
          log('User not found in Firestore, using auth data: ${failure.message}');
          _createUserFromAuth(authUser);
        },
        (userModel) {
          // User found in Firestore
          currentUser.value = userModel;
          log('User loaded from Firestore: ${userModel.companyName}');
        },
      );
    } catch (e) {
      log('Error loading user details: $e');
      _createUserFromAuth(authUser);
    } finally {
      isLoading.value = false;
    }
  }

  void _createUserFromAuth(authUser) {
    // Create basic user model from Firebase Auth
    currentUser.value = UserModel(
      uid: authUser.uid,
      email: authUser.email ?? '',
      companyName:
          authUser.displayName ?? _extractNameFromEmail(authUser.email ?? ''),
      phoneNumber: authUser.phoneNumber ?? '',
    );
    log('Created user from auth: ${currentUser.value?.companyName}');
  }

  String _extractNameFromEmail(String email) {
    if (email.isEmpty) return 'User';

    // Extract name from email (before @)
    final name = email.split('@').first;

    // Capitalize first letter and replace dots/underscores with spaces
    return name
        .replaceAll(RegExp(r'[._]'), ' ')
        .split(' ')
        .map((word) => word.isNotEmpty
            ? '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}'
            : '')
        .join(' ')
        .trim();
  }

  // Getters for easy access
  String get userName => currentUser.value?.companyName ?? 'User';
  String get userEmail => currentUser.value?.email ?? '';
  String get userPhone => currentUser.value?.phoneNumber ?? '';
  String get userId => currentUser.value?.uid ?? '';

  // Get user initials for avatar
  String get userInitials {
    final name = userName;
    if (name.isEmpty) return 'U';

    final words = name.split(' ');
    if (words.length >= 2) {
      return '${words[0][0]}${words[1][0]}'.toUpperCase();
    } else {
      return name.substring(0, 1).toUpperCase();
    }
  }

  // Get user role (simplified for now)
  String get userRole {
    // For now, return 'Admin' for all users
    // This can be enhanced later with proper role management
    return 'Admin';
  }

  // Refresh user data
  Future<void> refreshUserData() async {
    await _loadUserDetails();
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _authService.signOut();
      currentUser.value = null;
    } catch (e) {
      log('Error signing out: $e');
    }
  }
}
