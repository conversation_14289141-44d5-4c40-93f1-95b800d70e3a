import 'dart:developer';
import 'dart:async';
import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';
import 'package:logestics/models/slab/slab_model.dart';

class SlabFirebaseService {
  late FirebaseFirestore _firestore;

  SlabFirebaseService() {
    _firestore = FirebaseFirestore.instance;
  }

  /// Creates a slab in Firestore (shared across all companies)
  Future<void> createSlab({
    required SlabModel slab,
  }) async {
    try {
      // Create a reference to the Firestore document
      final slabRef =
          _firestore.collection(AppCollection.slabsCollection).doc(slab.slabId);

      // Check if document with this slab ID already exists
      final docSnapshot = await slabRef.get();
      if (docSnapshot.exists) {
        throw Exception("Slab with ID ${slab.slabId} already exists.");
      }

      // Save the document with a timeout of 10 seconds
      await slabRef.set(slab.toJson()).timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw TimeoutException("The operation timed out. Please try again.");
        },
      );

      log("Slab ${slab.slabId} saved successfully.");
    } on SocketException {
      log("No internet connection. Please check your network.");
      rethrow;
    } on TimeoutException {
      log("Timeout now You are on Offline mode");
      rethrow;
    } on FirebaseException catch (e) {
      log("Firebase error: ${e.message}");
      rethrow;
    } catch (e) {
      log("Unexpected error: $e");
      rethrow;
    }
  }

  /// Retrieves all slabs (shared across all companies)
  Future<List<SlabModel>> getSlabs() async {
    try {
      log('Fetching all slabs');

      final querySnapshot = await _firestore
          .collection(AppCollection.slabsCollection)
          .orderBy('createdAt', descending: true)
          .get()
          .timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw TimeoutException("The operation timed out. Please try again.");
        },
      );

      final slabs = querySnapshot.docs
          .map((doc) => SlabModel.fromJson(doc.data()))
          .toList();

      log('Successfully fetched ${slabs.length} slabs');
      return slabs;
    } on SocketException {
      log("No internet connection. Please check your network.");
      rethrow;
    } on TimeoutException {
      log("Timeout now You are on Offline mode");
      rethrow;
    } on FirebaseException catch (e) {
      log("Firebase error: ${e.message}");
      rethrow;
    } catch (e) {
      log("Unexpected error: $e");
      rethrow;
    }
  }

  /// Updates an existing slab in Firestore (shared across all companies)
  Future<void> updateSlab({
    required SlabModel slab,
  }) async {
    try {
      // Ensure the slabId field exists
      if (slab.slabId.isEmpty) {
        throw ArgumentError('The "slabId" field must not be null or empty.');
      }

      // Reference to the slab document
      final slabRef =
          _firestore.collection(AppCollection.slabsCollection).doc(slab.slabId);

      // Check if the slab exists
      final slabDoc = await slabRef.get();
      if (!slabDoc.exists) {
        throw Exception('Slab not found');
      }

      final existingSlab = SlabModel.fromJson(slabDoc.data()!);

      // Update the slab while preserving creation date
      final updatedSlab = SlabModel(
        slabId: slab.slabId,
        slabName: slab.slabName,
        startDate: slab.startDate,
        expiryDate: slab.expiryDate,
        createdAt: existingSlab.createdAt, // Keep original creation date
        isActive: slab.isActive,
        rates: slab.rates,
      );

      await slabRef.update(updatedSlab.toJson()).timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw TimeoutException("The operation timed out. Please try again.");
        },
      );

      log("Slab ${slab.slabId} updated successfully.");
    } on SocketException {
      log("No internet connection. Please check your network.");
      rethrow;
    } on TimeoutException {
      log("Timeout now You are on Offline mode");
      rethrow;
    } on FirebaseException catch (e) {
      log("Firebase error: ${e.message}");
      rethrow;
    } catch (e) {
      log("Unexpected error: $e");
      rethrow;
    }
  }

  /// Deletes a slab from Firestore (shared across all companies)
  Future<void> deleteSlab({
    required String slabId,
  }) async {
    try {
      if (slabId.isEmpty) {
        throw ArgumentError('The "slabId" field must not be null or empty.');
      }

      final slabRef =
          _firestore.collection(AppCollection.slabsCollection).doc(slabId);

      // Check if the slab exists
      final slabDoc = await slabRef.get();
      if (!slabDoc.exists) {
        throw Exception('Slab not found');
      }

      await slabRef.delete().timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw TimeoutException("The operation timed out. Please try again.");
        },
      );

      log("Slab $slabId deleted successfully.");
    } on SocketException {
      log("No internet connection. Please check your network.");
      rethrow;
    } on TimeoutException {
      log("Timeout now You are on Offline mode");
      rethrow;
    } on FirebaseException catch (e) {
      log("Firebase error: ${e.message}");
      rethrow;
    } catch (e) {
      log("Unexpected error: $e");
      rethrow;
    }
  }

  /// Get active slabs for a specific district and date (shared across all companies)
  Future<List<SlabModel>> getActiveSlabsForDistrict({
    required String districtId,
    required DateTime date,
  }) async {
    try {
      log('Fetching active slabs for district: $districtId, date: $date');

      final querySnapshot = await _firestore
          .collection(AppCollection.slabsCollection)
          .where('isActive', isEqualTo: true)
          .get()
          .timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw TimeoutException("The operation timed out. Please try again.");
        },
      );

      final allSlabs = querySnapshot.docs
          .map((doc) => SlabModel.fromJson(doc.data()))
          .toList();

      // Filter slabs that are valid for the given date and have rates for the district
      final validSlabs = allSlabs.where((slab) {
        return slab.isValidForDate(date) &&
            slab.getRateForDistrict(districtId) != null;
      }).toList();

      log('Found ${validSlabs.length} valid slabs for district $districtId on $date');
      return validSlabs;
    } on SocketException {
      log("No internet connection. Please check your network.");
      rethrow;
    } on TimeoutException {
      log("Timeout now You are on Offline mode");
      rethrow;
    } on FirebaseException catch (e) {
      log("Firebase error: ${e.message}");
      rethrow;
    } catch (e) {
      log("Unexpected error: $e");
      rethrow;
    }
  }
}
