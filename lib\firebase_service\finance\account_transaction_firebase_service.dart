import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';
import 'package:logestics/models/finance/account_transaction_model.dart';
import 'package:logestics/models/finance/paginated_transaction_result.dart';
import 'package:uuid/uuid.dart';

class AccountTransactionFirebaseService {
  late FirebaseFirestore _firestore;
  static const String _collectionPath = AppCollection.transactionsCollection;

  // Get current user's UID or return 'anonymous' if not authenticated
  String get _uid => FirebaseAuth.instance.currentUser?.uid ?? 'anonymous';

  AccountTransactionFirebaseService() {
    _firestore = FirebaseFirestore.instance;
  }

  // Create a new transaction
  Future<AccountTransactionModel> createTransaction(
      AccountTransactionModel transaction) async {
    try {
      final String id =
          transaction.id.isEmpty ? const Uuid().v4() : transaction.id;

      // Ensure the transaction has an ID and UID
      final transactionWithId = transaction.copyWith(id: id, uid: _uid);

      // Convert to Map
      final Map<String, dynamic> data = transactionWithId.toJson();

      // Add to Firestore
      await _firestore.collection(_collectionPath).doc(id).set(data);

      log('Created account transaction: $id');
      return transactionWithId;
    } catch (e) {
      log('Error creating account transaction: $e');
      rethrow;
    }
  }

  // Create a new transaction for external company (cross-company operations)
  Future<AccountTransactionModel> createTransactionForExternalCompany(
      AccountTransactionModel transaction, String externalCompanyUid) async {
    try {
      if (externalCompanyUid.isEmpty) {
        throw ArgumentError('External company UID cannot be empty');
      }

      final String id =
          transaction.id.isEmpty ? const Uuid().v4() : transaction.id;

      // Ensure the transaction has an ID and the external company's UID
      final transactionWithId =
          transaction.copyWith(id: id, uid: externalCompanyUid);

      // Convert to Map
      final Map<String, dynamic> data = transactionWithId.toJson();

      // Add to Firestore with external company's UID
      await _firestore.collection(_collectionPath).doc(id).set(data);

      log('Created cross-company account transaction: $id for company: $externalCompanyUid');
      return transactionWithId;
    } catch (e) {
      log('Error creating cross-company account transaction: $e');
      rethrow;
    }
  }

  // Get all transactions for a specific account (user-scoped) - Legacy method
  Future<List<AccountTransactionModel>> getTransactionsForAccount(
      String accountId) async {
    try {
      final snapshot = await _firestore
          .collection(_collectionPath)
          .where('accountId', isEqualTo: accountId)
          .where('uid', isEqualTo: _uid) // Filter by current user's UID
          .orderBy('transactionDate', descending: true)
          .get();

      final transactions = snapshot.docs.map((doc) {
        return AccountTransactionModel.fromJson(doc.data());
      }).toList();

      log('Retrieved ${transactions.length} transactions for account $accountId');
      return transactions;
    } catch (e) {
      log('Error getting transactions for account: $e');
      rethrow;
    }
  }

  // Get paginated transactions for a specific account (user-scoped)
  Future<PaginatedTransactionResult> getTransactionsForAccountPaginated({
    required String accountId,
    required int limit,
    QueryDocumentSnapshot? lastDocument,
  }) async {
    try {
      Query query = _firestore
          .collection(_collectionPath)
          .where('accountId', isEqualTo: accountId)
          .where('uid', isEqualTo: _uid) // Filter by current user's UID
          .orderBy('transactionDate', descending: true)
          .limit(limit);

      // Add pagination cursor if provided
      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      final snapshot = await query.get();

      final transactions = snapshot.docs.map((doc) {
        return AccountTransactionModel.fromJson(
            doc.data() as Map<String, dynamic>);
      }).toList();

      // Get the last document for next page cursor
      QueryDocumentSnapshot? nextPageCursor;
      if (snapshot.docs.isNotEmpty && transactions.length == limit) {
        nextPageCursor = snapshot.docs.last;
      }

      log('Retrieved ${transactions.length} paginated transactions for account $accountId');

      return PaginatedTransactionResult(
        transactions: transactions,
        nextPageCursor: nextPageCursor,
        hasNextPage: nextPageCursor != null,
      );
    } catch (e) {
      log('Error getting paginated transactions for account: $e');
      rethrow;
    }
  }

  // Get total count of transactions for an account (for pagination info)
  Future<int> getTransactionCountForAccount(String accountId) async {
    try {
      final snapshot = await _firestore
          .collection(_collectionPath)
          .where('accountId', isEqualTo: accountId)
          .where('uid', isEqualTo: _uid) // Filter by current user's UID
          .get();

      log('Total transaction count for account $accountId: ${snapshot.docs.length}');
      return snapshot.docs.length;
    } catch (e) {
      log('Error getting transaction count for account: $e');
      rethrow;
    }
  }

  // Get all transactions (user-scoped)
  Future<List<AccountTransactionModel>> getAllTransactions() async {
    try {
      final snapshot = await _firestore
          .collection(_collectionPath)
          .where('uid', isEqualTo: _uid) // Filter by current user's UID
          .orderBy('transactionDate', descending: true)
          .get();

      final transactions = snapshot.docs.map((doc) {
        return AccountTransactionModel.fromJson(doc.data());
      }).toList();

      log('Retrieved ${transactions.length} transactions');
      return transactions;
    } catch (e) {
      log('Error getting all transactions: $e');
      rethrow;
    }
  }

  // Create multiple transactions in a batch
  Future<void> createTransactions(
      List<AccountTransactionModel> transactions) async {
    try {
      final batch = _firestore.batch();

      for (final transaction in transactions) {
        final String id =
            transaction.id.isEmpty ? const Uuid().v4() : transaction.id;
        final doc = _firestore.collection(_collectionPath).doc(id);
        batch.set(doc, transaction.copyWith(id: id, uid: _uid).toJson());
      }

      await batch.commit();
      log('Created ${transactions.length} transactions in batch');
    } catch (e) {
      log('Error creating transactions in batch: $e');
      rethrow;
    }
  }

  // Create multiple transactions in a batch preserving original UIDs (for cross-company transactions)
  Future<void> createCrossCompanyTransactions(
      List<AccountTransactionModel> transactions) async {
    try {
      final batch = _firestore.batch();

      for (final transaction in transactions) {
        final String id =
            transaction.id.isEmpty ? const Uuid().v4() : transaction.id;
        final doc = _firestore.collection(_collectionPath).doc(id);
        // Preserve the original UID for cross-company transactions
        batch.set(doc, transaction.copyWith(id: id).toJson());
      }

      await batch.commit();
      log('Created ${transactions.length} cross-company transactions in batch');
    } catch (e) {
      log('Error creating cross-company transactions in batch: $e');
      rethrow;
    }
  }

  // Delete a transaction (with user permission check)
  Future<void> deleteTransaction(String transactionId) async {
    try {
      // First check if the transaction belongs to the current user
      final transactionDoc =
          await _firestore.collection(_collectionPath).doc(transactionId).get();

      if (!transactionDoc.exists) {
        throw Exception('Transaction not found');
      }

      final transactionData = transactionDoc.data() as Map<String, dynamic>;
      if (transactionData['uid'] != _uid) {
        throw Exception(
            'You do not have permission to delete this transaction');
      }

      await _firestore.collection(_collectionPath).doc(transactionId).delete();
      log('Deleted transaction: $transactionId');
    } catch (e) {
      log('Error deleting transaction: $e');
      rethrow;
    }
  }

  // Update an existing transaction (with user permission check)
  Future<void> updateTransaction(AccountTransactionModel transaction) async {
    try {
      if (transaction.id.isEmpty) {
        throw Exception('Cannot update transaction without an ID');
      }

      // First check if the transaction belongs to the current user
      final transactionDoc = await _firestore
          .collection(_collectionPath)
          .doc(transaction.id)
          .get();

      if (!transactionDoc.exists) {
        throw Exception('Transaction not found');
      }

      final transactionData = transactionDoc.data() as Map<String, dynamic>;
      if (transactionData['uid'] != _uid) {
        throw Exception(
            'You do not have permission to update this transaction');
      }

      // Preserve the original UID
      final updatedTransaction =
          transaction.copyWith(uid: transactionData['uid']);

      // Convert to Map
      final Map<String, dynamic> data = updatedTransaction.toJson();

      // Update in Firestore
      await _firestore
          .collection(_collectionPath)
          .doc(transaction.id)
          .update(data);

      log('Updated transaction: ${transaction.id}');
    } catch (e) {
      log('Error updating transaction: $e');
      rethrow;
    }
  }

  // Create a voucher payment transaction (convenience method)
  Future<AccountTransactionModel> createVoucherPaymentTransaction({
    required String accountId,
    required String accountName,
    required double amount,
    required DateTime transactionDate,
    String? payeeId,
    String? payeeName,
    required String voucherId,
    required String voucherNumber,
    required String description,
    Map<String, dynamic>? metadata,
  }) async {
    final transaction = AccountTransactionModel(
      id: const Uuid().v4(),
      accountId: accountId,
      accountName: accountName,
      amount: amount,
      transactionDate: transactionDate,
      type: TransactionType.voucherPayment,
      description: description,
      payeeId: payeeId,
      payeeName: payeeName,
      voucherId: voucherId,
      voucherNumber: voucherNumber,
      referenceId: voucherId,
      referenceName: voucherNumber,
      metadata: metadata,
      uid: _uid, // Ensure user ID is set
    );

    return await createTransaction(transaction);
  }

  // Create a broker fees transaction (convenience method)
  Future<AccountTransactionModel> createBrokerFeesTransaction({
    required String accountId,
    required String accountName,
    required double amount,
    required DateTime transactionDate,
    String? payeeId,
    String? payeeName,
    required String voucherId,
    required String voucherNumber,
    String? category,
  }) async {
    final transaction = AccountTransactionModel(
      id: const Uuid().v4(),
      accountId: accountId,
      accountName: accountName,
      amount: amount,
      transactionDate: transactionDate,
      type: TransactionType.brokerFees,
      description: 'Broker fees for voucher $voucherNumber',
      payeeId: payeeId,
      payeeName: payeeName,
      voucherId: voucherId,
      voucherNumber: voucherNumber,
      referenceId: voucherId,
      referenceName: voucherNumber,
      category: category,
      uid: _uid, // Ensure user ID is set
    );

    return await createTransaction(transaction);
  }

  // Create a munshiana transaction (convenience method)
  Future<AccountTransactionModel> createMunshianaTransaction({
    required String accountId,
    required String accountName,
    required double amount,
    required DateTime transactionDate,
    String? payeeId,
    String? payeeName,
    required String voucherId,
    required String voucherNumber,
    String? category,
  }) async {
    final transaction = AccountTransactionModel(
      id: const Uuid().v4(),
      accountId: accountId,
      accountName: accountName,
      amount: amount,
      transactionDate: transactionDate,
      type: TransactionType.munshiana,
      description: 'Munshiana payment for voucher $voucherNumber',
      payeeId: payeeId,
      payeeName: payeeName,
      voucherId: voucherId,
      voucherNumber: voucherNumber,
      referenceId: voucherId,
      referenceName: voucherNumber,
      category: category,
      uid: _uid, // Ensure user ID is set
    );

    return await createTransaction(transaction);
  }

  /// Stream to listen for real-time updates to transactions
  Stream<List<AccountTransactionModel>> listenToTransactions() {
    try {
      return _firestore
          .collection(_collectionPath)
          .where('uid', isEqualTo: _uid) // Filter by current user's UID
          .orderBy('transactionDate', descending: true)
          .snapshots()
          .map((snapshot) => snapshot.docs
              .map((doc) => AccountTransactionModel.fromJson(doc.data()))
              .toList());
    } catch (e) {
      log('Error listening to transactions: $e', error: e);
      return Stream.value([]);
    }
  }
}
