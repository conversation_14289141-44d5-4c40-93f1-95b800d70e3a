import 'package:logestics/models/locations/from_place_model.dart';

class StationModel {
  String stationId;
  final String stationName;
  final String zoneId;
  final String zoneName;
  final String regionId;
  final String regionName;
  final String regionCode;
  final String districtId;
  final String districtName;

  final DateTime createdAt;
  final List<FromPlaceModel> places;

  StationModel({
    required this.zoneId,
    required this.zoneName,
    required this.regionId,
    required this.regionName,
    required this.regionCode,
    required this.districtId,
    required this.districtName,
    required this.stationId,
    required this.stationName,
    required this.createdAt,
    required this.places,
  });

  StationModel copyWith({
    List<FromPlaceModel>? places,
  }) {
    return StationModel(
      stationId: stationId,
      stationName: stationName,
      zoneId: zoneId,
      zoneName: zoneName,
      regionId: regionId,
      regionName: regionName,
      regionCode: regionCode,
      districtId: districtId,
      districtName: districtName,
      createdAt: createdAt,
      places: places ?? this.places,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'zoneId': zoneId,
      'zoneName': zoneName,
      'regionId': regionId,
      'regionName': regionName,
      'regionCode': regionCode,
      'districtId': districtId,
      'districtName': districtName,
      'stationId': stationId,
      'stationName': stationName,
      'createdAt': createdAt.toIso8601String(),
      'places': places.map((place) => place.toJson()).toList(),
    };
  }

  factory StationModel.fromJson(Map<String, dynamic> json) {
    return StationModel(
      zoneId: json['zoneId'] ?? '',
      zoneName: json['zoneName'] ?? '',
      regionId: json['regionId'] ?? '',
      regionName: json['regionName'] ?? '',
      regionCode: json['regionCode'] ?? '',
      districtId: json['districtId'] ?? '',
      districtName: json['districtName'] ?? '',
      stationId: json['stationId'] ?? '',
      stationName: json['stationName'] ?? '',
      createdAt: DateTime.parse(json['createdAt']),
      places: List<FromPlaceModel>.from(
          json['places']?.map((place) => FromPlaceModel.fromJson(place)) ?? []),
    );
  }

  @override
  String toString() {
    return 'StationModel{zoneId: $zoneId, zoneName: $zoneName, regionId: $regionId, regionName: $regionName, regionCode: $regionCode, districtId: $districtId, districtName: $districtName, stationId: $stationId, stationName: $stationName, createdAt: $createdAt, places: $places}';
  }
}
