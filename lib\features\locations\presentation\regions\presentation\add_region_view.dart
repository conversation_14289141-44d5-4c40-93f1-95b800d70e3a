import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/widgets/custom_button.dart';
import 'package:logestics/core/utils/widgets/my_text_field.dart';
import 'package:logestics/core/utils/widgets/my_dropdown_field.dart';
import 'package:logestics/features/locations/domain/usecases/region_use_case/create_region_use_case.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';

import '../controller/add_region_controller.dart';

class AddRegionView extends StatefulWidget {
  const AddRegionView({super.key});

  @override
  State<AddRegionView> createState() => _AddRegionViewState();
}

class _AddRegionViewState extends State<AddRegionView> {
  late AddRegionController controller;

  @override
  void initState() {
    super.initState();

    controller = Get.put(AddRegionController(
        createRegionUseCase: CreateRegionUseCase(Get.find())));

    // Refresh zones when the view is opened
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.refreshZones();
    });
  }

  @override
  Widget build(BuildContext context) {
    notifier = Provider.of(context, listen: false);
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 600;
    final isMediumScreen = size.width < 1200 && size.width >= 600;

    return Form(
      key: controller.formKey,
      child: Scaffold(
        backgroundColor: notifier.getBgColor,
        body: Dialog(
          backgroundColor: notifier.getBgColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          child: Container(
            width: size.width * 0.9,
            height: size.height * 0.9,
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              spacing: 16,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        AppStrings.addNewRegion,
                        overflow: TextOverflow.ellipsis,
                        style: AppTextStyles.titleStyle
                            .copyWith(color: notifier.text),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: Icon(Icons.close, color: notifier.text),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
                Divider(thickness: 5),
                Expanded(
                  child: SingleChildScrollView(
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        return Wrap(
                          spacing: 16,
                          runSpacing: 16,
                          children: [
                            // Zone Dropdown
                            buildFormField(
                              constraints,
                              isSmallScreen,
                              isMediumScreen,
                              Obx(
                                () => controller.isLoadingZones.value
                                    ? Center(child: CircularProgressIndicator())
                                    : MyDropdownFormField(
                                        titletext: AppStrings.selectZone,
                                        hinttext: AppStrings.selectZoneHint,
                                        items: controller.zones
                                            .map((zone) => zone.zoneName)
                                            .toList(),
                                        initalValue: controller
                                            .selectedZone.value?.zoneName,
                                        onChanged: (value) {
                                          if (value != null) {
                                            final selectedZone =
                                                controller.zones.firstWhere(
                                              (zone) => zone.zoneName == value,
                                              orElse: () =>
                                                  controller.zones.first,
                                            );
                                            controller
                                                .setSelectedZone(selectedZone);
                                          }
                                        },
                                        validator: controller.validateZone,
                                        validatorMode:
                                            AutovalidateMode.onUserInteraction,
                                      ),
                              ),
                            ),

                            // Region Name Field
                            buildFormField(
                              constraints,
                              isSmallScreen,
                              isMediumScreen,
                              MyTextFormField(
                                titleText: AppStrings.regionName,
                                labelText: AppStrings.regionName,
                                hintText: AppStrings.regionNameHint,
                                controller: controller.regionNameController,
                                validator: controller.validateRegionName,
                              ),
                            ),

                            // Region Code Field
                            buildFormField(
                              constraints,
                              isSmallScreen,
                              isMediumScreen,
                              MyTextFormField(
                                titleText: "Region Code",
                                labelText: "Region Code",
                                hintText: "Enter region code",
                                controller: controller.regionCodeController,
                                validator: controller.validateRegionCode,
                                keyboardType: TextInputType.number,
                                textInputFormatter: [
                                  FilteringTextInputFormatter.digitsOnly
                                ],
                              ),
                            ),

                            // Buttons
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Obx(
                                  () => CustomButton.danger(
                                    isDisabled: controller.isLoading.value,
                                    onPressed: () => Get.back(),
                                    text: AppStrings.cancel,
                                    minimumSize:
                                        Size(isSmallScreen ? 100 : 130, 50),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Obx(
                                  () => CustomButton.primary(
                                    onPressed: () => controller.saveRegion(),
                                    isLoading: controller.isLoading.value,
                                    text: AppStrings.save,
                                    minimumSize:
                                        Size(isSmallScreen ? 100 : 130, 50),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

Widget buildFormField(
  BoxConstraints constraints,
  bool isSmallScreen,
  bool isMediumScreen,
  Widget field,
) {
  double width = isSmallScreen
      ? constraints.maxWidth
      : isMediumScreen
          ? (constraints.maxWidth - 16) / 2
          : (constraints.maxWidth - 32) / 3;
  return SizedBox(
    width: width,
    child: field,
  );
}
