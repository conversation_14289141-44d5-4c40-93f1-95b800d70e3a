import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/features/finance/check_usage/presentation/controllers/check_usage_controller.dart';
import 'package:logestics/main.dart';
import 'package:logestics/models/finance/check_usage_model.dart';
import 'package:provider/provider.dart';

class ActiveChecksView extends StatelessWidget {
  const ActiveChecksView({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<CheckUsageController>();
    var width = Get.width;
    notifier = Provider.of(context, listen: true);

    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          child: Column(
            children: [
              SizedBox(
                height: width < 650 ? 55 : 40,
                width: width,
                child: width < 650
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Active Checks',
                            overflow: TextOverflow.ellipsis,
                            style: AppTextStyles.titleStyle
                                .copyWith(color: notifier.text),
                          ),
                          const Spacer(),
                        ],
                      )
                    : Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Active Checks',
                            overflow: TextOverflow.ellipsis,
                            style: AppTextStyles.titleStyle
                                .copyWith(color: notifier.text),
                          ),
                          const Spacer(),
                        ],
                      ),
              ),
              const SizedBox(height: 20),
              SizedBox(
                height: 570,
                child: _buildActiveChecksList(controller),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildActiveChecksList(CheckUsageController controller) {
    return Container(
      decoration: BoxDecoration(
        color: notifier.getBgColor,
        borderRadius: BorderRadius.circular(10),
      ),
      padding: const EdgeInsets.symmetric(vertical: 15),
      child: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        if (controller.filteredActiveChecks.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.receipt_long_outlined,
                  size: 64,
                  color: Colors.grey,
                ),
                SizedBox(height: 16),
                Text(
                  'No active checks found',
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.grey,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Active checks will appear here when you use check payments',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: controller.filteredActiveChecks.length,
          itemBuilder: (context, index) {
            final check = controller.filteredActiveChecks[index];
            return _buildActiveCheckCard(check, controller);
          },
        );
      }),
    );
  }
}

Widget _buildActiveCheckCard(
    CheckUsageModel check, CheckUsageController controller) {
  final isExpired =
      check.expiryDate != null && DateTime.now().isAfter(check.expiryDate!);

  return Card(
    elevation: 3,
    margin: const EdgeInsets.only(bottom: 16),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(12),
      side: BorderSide(
        color: isExpired ? Colors.red : Colors.transparent,
        width: isExpired ? 2 : 0,
      ),
    ),
    child: Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  'Check #${check.checkNumber}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Chip(
                label: Text(check.status.toUpperCase()),
                backgroundColor: _getStatusColor(check.status),
                labelStyle: const TextStyle(color: Colors.white),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Amount: PKR ${check.amount.toStringAsFixed(2)}',
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            'Bank: ${check.bankName}',
            style: const TextStyle(fontSize: 14),
          ),
          const SizedBox(height: 4),
          Text(
            'Account: ${check.accountName}',
            style: const TextStyle(fontSize: 14),
          ),
          const SizedBox(height: 4),
          Text(
            'Payee: ${check.payeeName}',
            style: const TextStyle(fontSize: 14),
          ),
          const SizedBox(height: 8),
          Text(
            'Voucher: ${check.voucherNumber}',
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 4),
          Text(
            'Issue Date: ${_formatDate(check.issueDate)}',
            style: const TextStyle(fontSize: 14),
          ),
          if (check.expiryDate != null) ...[
            const SizedBox(height: 4),
            Text(
              'Expiry Date: ${_formatDate(check.expiryDate!)}',
              style: TextStyle(
                fontSize: 14,
                color: isExpired ? Colors.red : null,
                fontWeight: isExpired ? FontWeight.bold : null,
              ),
            ),
          ],
          const SizedBox(height: 4),
          Text(
            'Used Date: ${_formatDate(check.usageDate)}',
            style: const TextStyle(fontSize: 14),
          ),
          if (check.checkType == 'other' &&
              check.externalCompanyName != null) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.amber.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.amber.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.business, color: Colors.amber.shade700, size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Cross-company check from ${check.externalCompanyName}',
                      style: TextStyle(
                        color: Colors.amber.shade700,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
          if (check.notes.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              'Notes: ${check.notes}',
              style: const TextStyle(fontSize: 14),
            ),
          ],
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              if (check.status == 'issued') ...[
                TextButton.icon(
                  onPressed: () => _showUpdateStatusDialog(check, controller),
                  icon: const Icon(Icons.update),
                  label: const Text('Update Status'),
                ),
                const SizedBox(width: 8),
              ],
              ElevatedButton.icon(
                onPressed: () => _showCheckDetails(check),
                icon: const Icon(Icons.info_outline),
                label: const Text('Details'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    ),
  );
}

Color _getStatusColor(String status) {
  switch (status.toLowerCase()) {
    case 'issued':
      return Colors.orange;
    case 'cleared':
      return Colors.green;
    case 'bounced':
      return Colors.red;
    case 'cancelled':
      return Colors.grey;
    default:
      return Colors.blue;
  }
}

String _formatDate(DateTime date) {
  return '${date.day}/${date.month}/${date.year}';
}

void _showUpdateStatusDialog(
    CheckUsageModel check, CheckUsageController controller) {
  Get.dialog(
    AlertDialog(
      title: Text('Update Check Status'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text('Check #${check.checkNumber}'),
          const SizedBox(height: 16),
          Text('Current Status: ${check.status.toUpperCase()}'),
          const SizedBox(height: 16),
          const Text('Select new status:'),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: const Text('Cancel'),
        ),
        if (check.status != 'cleared')
          TextButton(
            onPressed: () {
              controller.updateCheckStatus(check.id, 'cleared');
              Get.back();
            },
            child: const Text('Mark as Cleared'),
          ),
        if (check.status != 'bounced')
          TextButton(
            onPressed: () {
              controller.updateCheckStatus(check.id, 'bounced');
              Get.back();
            },
            child: const Text('Mark as Bounced'),
          ),
        if (check.status != 'cancelled')
          TextButton(
            onPressed: () {
              controller.updateCheckStatus(check.id, 'cancelled');
              Get.back();
            },
            child: const Text('Mark as Cancelled'),
          ),
      ],
    ),
  );
}

void _showCheckDetails(CheckUsageModel check) {
  Get.dialog(
    AlertDialog(
      title: Text('Check #${check.checkNumber}'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('Amount', 'PKR ${check.amount.toStringAsFixed(2)}'),
            _buildDetailRow('Bank', check.bankName),
            _buildDetailRow('Account', check.accountName),
            _buildDetailRow('Payee', check.payeeName),
            _buildDetailRow('Voucher', check.voucherNumber),
            _buildDetailRow('Status', check.status.toUpperCase()),
            _buildDetailRow('Type', check.checkType.toUpperCase()),
            if (check.externalCompanyName != null)
              _buildDetailRow('External Company', check.externalCompanyName!),
            _buildDetailRow('Issue Date', _formatDate(check.issueDate)),
            if (check.expiryDate != null)
              _buildDetailRow('Expiry Date', _formatDate(check.expiryDate!)),
            _buildDetailRow('Usage Date', _formatDate(check.usageDate)),
            if (check.notes.isNotEmpty) _buildDetailRow('Notes', check.notes),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: const Text('Close'),
        ),
      ],
    ),
  );
}

Widget _buildDetailRow(String label, String value) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 4),
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            '$label:',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        Expanded(
          child: Text(value),
        ),
      ],
    ),
  );
}
