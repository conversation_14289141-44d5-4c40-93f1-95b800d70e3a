import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/features/slab/repositories/slab_repository.dart';
import 'package:logestics/models/slab/slab_model.dart';

class GetActiveSlabsForDistrictUseCase {
  final SlabRepository repository;

  GetActiveSlabsForDistrictUseCase(this.repository);

  Future<Either<FailureObj, List<SlabModel>>> call({
    required String districtId,
    required DateTime date,
  }) async {
    return await repository.getActiveSlabsForDistrict(
      districtId: districtId,
      date: date,
    );
  }
}
