import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/widgets/header_secondary.dart';
import 'package:logestics/firebase_service/finance/account_transaction_firebase_service.dart';
import 'package:logestics/models/finance/account_model.dart';
import 'package:logestics/features/finance/accounts/presentation/controllers/account_controller.dart';
import 'package:logestics/features/finance/accounts/presentation/controllers/account_transaction_controller.dart';
import 'package:logestics/features/finance/accounts/presentation/views/account_transactions_view.dart';
import 'package:logestics/features/home/<USER>/drawer_controllers.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';
import 'package:logestics/features/finance/accounts/repositories/account_repository.dart';
import 'package:logestics/firebase_service/finance/account_firebase_service.dart';
import 'package:logestics/features/finance/accounts/repositories/account_transaction_repository.dart';

class AccountsView extends StatefulWidget {
  const AccountsView({super.key});

  @override
  State<AccountsView> createState() => _AccountsViewState();
}

class _AccountsViewState extends State<AccountsView> {
  MainDrawerController mainDrawerController = Get.put(MainDrawerController());
  late final AccountController accountController;

  @override
  void initState() {
    super.initState();
    // Initialize Firebase Service
    final firebaseService = AccountFirebaseService();
    // Initialize repository with Firebase Service
    final repository = AccountRepositoryImpl(firebaseService);
    // Initialize controller with repository
    accountController = Get.put(AccountController(repository: repository));
    // Load data immediately when the view is created
    _loadData();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Reload data when dependencies change (e.g., when returning to this screen)
    _loadData();
  }

  Future<void> _loadData() async {
    if (!accountController.isLoading.value) {
      await accountController.loadAccounts();
    }
  }

  @override
  void didUpdateWidget(AccountsView oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Refresh data when widget is updated (e.g., when navigating back to this screen)
    _refreshData();
  }

  /// Refresh data when screen becomes visible
  void _refreshData() {
    if (accountController.isScreenActive) {
      accountController.onScreenVisible();
    }
  }

  @override
  Widget build(BuildContext context) {
    var width = Get.width;
    notifier = Provider.of(context, listen: true);

    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          child: Column(
            children: [
              SizedBox(
                height: width < 650 ? 55 : 40,
                width: width,
                child: width < 650
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Accounts',
                            overflow: TextOverflow.ellipsis,
                            style: AppTextStyles.titleStyle
                                .copyWith(color: notifier.text),
                          ),
                          const Spacer(),
                          HeaderSecondary(
                            option1: AppStrings.dashboard,
                            option2: AppStrings.system,
                            option3: 'Accounts',
                            notifier: notifier,
                          ),
                        ],
                      )
                    : Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Accounts',
                            overflow: TextOverflow.ellipsis,
                            style: AppTextStyles.titleStyle
                                .copyWith(color: notifier.text),
                          ),
                          const Spacer(),
                          Row(
                            children: [
                              IconButton(
                                onPressed: () async {
                                  await accountController.forceRefresh();
                                },
                                icon: Icon(
                                  Icons.refresh,
                                  color: notifier.text,
                                ),
                                tooltip: 'Refresh Data',
                              ),
                              const SizedBox(width: 8),
                              HeaderSecondary(
                                option1: AppStrings.dashboard,
                                option2: AppStrings.system,
                                option3: 'Accounts',
                                notifier: notifier,
                              ),
                            ],
                          ),
                        ],
                      ),
              ),
              const SizedBox(height: 20),
              SizedBox(
                height: 570,
                child: RefreshIndicator(
                  onRefresh: () async {
                    await accountController.forceRefresh();
                  },
                  child: _buildAccountsList(),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAccountsList() {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            color: notifier.getBgColor,
            borderRadius: BorderRadius.circular(10),
          ),
          padding: const EdgeInsets.symmetric(vertical: 15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 15),
                child: Get.width < 650
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          InkWell(
                            onTap: () => accountController.openDrawer(),
                            child: Text(
                              'Add New Account',
                              style: AppTextStyles.addNewInvoiceStyle,
                            ),
                          ),
                          TextField(
                            controller: accountController.searchController,
                            decoration: InputDecoration(
                              hintText: 'Search accounts...',
                              prefixIcon: const Icon(Icons.search),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                              filled: true,
                              fillColor: notifier.textFileColor,
                            ),
                          ),
                        ],
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          InkWell(
                            onTap: () => accountController.openDrawer(),
                            child: Text(
                              'Add New Account',
                              style: AppTextStyles.addNewInvoiceStyle,
                            ),
                          ),
                          SizedBox(
                            width: Get.width < 850
                                ? Get.width / 2
                                : Get.width / 3.5,
                            child: TextField(
                              controller: accountController.searchController,
                              decoration: InputDecoration(
                                hintText: 'Search accounts...',
                                prefixIcon: const Icon(Icons.search),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8.0),
                                ),
                                filled: true,
                                fillColor: notifier.textFileColor,
                              ),
                            ),
                          ),
                        ],
                      ),
              ),
              const SizedBox(height: 20),
              Expanded(
                child: SizedBox(
                  width: Get.width,
                  child: Obx(() {
                    if (accountController.isLoading.value) {
                      return const Center(
                        child: CircularProgressIndicator(),
                      );
                    }

                    if (accountController.hasError.value) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.error_outline,
                              size: 48,
                              color: Colors.red[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'Failed to load accounts',
                              style: TextStyle(
                                fontSize: 18,
                                color: notifier.text.withOpacity(0.7),
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              accountController.errorMessage.value,
                              style: TextStyle(
                                fontSize: 14,
                                color: notifier.text.withOpacity(0.5),
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton.icon(
                              onPressed: _loadData,
                              icon: const Icon(Icons.refresh),
                              label: const Text('Try Again'),
                            ),
                          ],
                        ),
                      );
                    }

                    return _buildAccountsTable();
                  }),
                ),
              ),
            ],
          ),
        ),
        Obx(() {
          if (!accountController.isDrawerOpen.value) {
            return const SizedBox.shrink();
          }

          return Positioned(
            right: 0,
            top: 0,
            bottom: 0,
            width: Get.width < 650 ? Get.width : 400,
            child: Container(
              decoration: BoxDecoration(
                color: notifier.getBgColor,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(10),
                    blurRadius: 10,
                    offset: const Offset(0, 0),
                  ),
                ],
              ),
              child: Form(
                key: accountController.formKey,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Add New Account',
                            style: AppTextStyles.titleStyle.copyWith(
                              color: notifier.text,
                            ),
                          ),
                          IconButton(
                            onPressed: accountController.closeDrawer,
                            icon: const Icon(Icons.close),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      TextFormField(
                        controller: accountController.nameController,
                        decoration: InputDecoration(
                          labelText: 'Name',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          filled: true,
                          fillColor: notifier.textFileColor,
                        ),
                        validator: accountController.validateName,
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: accountController.initialBalanceController,
                        decoration: InputDecoration(
                          labelText: 'Initial Balance',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          filled: true,
                          fillColor: notifier.textFileColor,
                        ),
                        keyboardType: TextInputType.number,
                        validator: accountController.validateInitialBalance,
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: accountController.accountNumberController,
                        decoration: InputDecoration(
                          labelText: 'Account Number',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          filled: true,
                          fillColor: notifier.textFileColor,
                        ),
                        validator: accountController.validateAccountNumber,
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: accountController.branchCodeController,
                        decoration: InputDecoration(
                          labelText: 'Branch Code',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          filled: true,
                          fillColor: notifier.textFileColor,
                        ),
                        validator: accountController.validateBranchCode,
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: accountController.branchAddressController,
                        decoration: InputDecoration(
                          labelText: 'Branch Address',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          filled: true,
                          fillColor: notifier.textFileColor,
                        ),
                      ),
                      const SizedBox(height: 24),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: accountController.addAccount,
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text('Add Account'),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }),
      ],
    );
  }

  Widget _buildAccountsTable() {
    return Obx(() {
      if (accountController.isLoading.value) {
        return const Center(child: CircularProgressIndicator());
      }

      if (accountController.filteredAccounts.isEmpty) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.account_balance_wallet_outlined,
                  size: 48,
                  color: notifier.text.withValues(alpha: 0.5),
                ),
                const SizedBox(height: 16),
                Text(
                  'No accounts found',
                  style: TextStyle(
                    fontSize: 18,
                    color: notifier.text.withValues(alpha: 0.7),
                  ),
                ),
                if (accountController.searchQuery.isNotEmpty)
                  TextButton(
                    onPressed: () {
                      accountController.searchController.text = '';
                    },
                    child: const Text('Clear search'),
                  ),
              ],
            ),
          ),
        );
      }

      return Container(
        margin: const EdgeInsets.only(top: 20),
        decoration: BoxDecoration(
          color: notifier.getcardColor,
          borderRadius: BorderRadius.circular(10),
        ),
        child: DataTable(
          columnSpacing: 20,
          headingRowColor: WidgetStateProperty.all(
            notifier.getHoverColor,
          ),
          dataRowMaxHeight: 80,
          dataRowMinHeight: 60,
          columns: [
            DataColumn(
              label: Text(
                'Account Name',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: notifier.text,
                ),
              ),
            ),
            DataColumn(
              label: Text(
                'Account Number',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: notifier.text,
                ),
              ),
            ),
            DataColumn(
              label: Text(
                'Branch Code',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: notifier.text,
                ),
              ),
            ),
            DataColumn(
              label: Text(
                'Available Balance',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: notifier.text,
                ),
              ),
            ),
            DataColumn(
              label: Text(
                'Actions',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: notifier.text,
                ),
              ),
            ),
          ],
          rows: accountController.filteredAccounts.map((account) {
            return DataRow(
              onSelectChanged: (selected) {
                if (selected == true) {
                  _navigateToTransactions(account);
                }
              },
              cells: [
                DataCell(
                  Text(
                    account.name,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: notifier.text,
                    ),
                  ),
                ),
                DataCell(
                  Text(
                    account.accountNumber,
                    style: TextStyle(color: notifier.text),
                  ),
                ),
                DataCell(
                  Text(
                    account.branchCode,
                    style: TextStyle(color: notifier.text),
                  ),
                ),
                DataCell(
                  Text(
                    '₨ ${account.availableBalance.toStringAsFixed(2)}',
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      color: account.availableBalance >= 0
                          ? Colors.green[700]
                          : Colors.red[700],
                    ),
                  ),
                ),
                DataCell(
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: Icon(Icons.edit, color: notifier.text),
                        onPressed: () => _showAccountDetails(account),
                        tooltip: 'Edit Account',
                      ),
                      IconButton(
                        icon: Icon(Icons.delete, color: Colors.red[400]),
                        onPressed: () => _confirmDelete(account),
                        tooltip: 'Delete Account',
                      ),
                    ],
                  ),
                ),
              ],
            );
          }).toList(),
        ),
      );
    });
  }

  Future<void> _confirmDelete(AccountModel account) {
    return Get.dialog(
      AlertDialog(
        title: const Text('Confirm Delete'),
        content: const Text('Are you sure you want to delete this account?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          Obx(() => TextButton(
                onPressed: accountController.isDeleting.value
                    ? null // Disable button while deleting
                    : () async {
                        Get.back(); // Close confirmation dialog
                        await accountController.deleteAccount(account.id);
                      },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: accountController.isDeleting.value
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Text('Delete'),
              )),
        ],
      ),
    );
  }

  void _showAccountDetails(AccountModel account) {
    accountController.nameController.text = account.name;
    accountController.initialBalanceController.text =
        account.initialBalance.toString();
    accountController.accountNumberController.text = account.accountNumber;
    accountController.branchCodeController.text = account.branchCode;
    accountController.branchAddressController.text = account.branchAddress;

    Get.dialog(
      Dialog(
        insetPadding: const EdgeInsets.all(20),
        child: Container(
          width: Get.width < 650 ? Get.width : 500,
          padding: const EdgeInsets.all(20),
          child: Form(
            key: accountController.formKey,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Account Details',
                        style: AppTextStyles.titleStyle.copyWith(
                          color: notifier.text,
                        ),
                      ),
                      IconButton(
                        onPressed: () => Get.back(),
                        icon: const Icon(Icons.close),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  TextFormField(
                    controller: accountController.nameController,
                    decoration: InputDecoration(
                      labelText: 'Name',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      filled: true,
                      fillColor: notifier.textFileColor,
                    ),
                    validator: accountController.validateName,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: accountController.initialBalanceController,
                    decoration: InputDecoration(
                      labelText: 'Initial Balance',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      filled: true,
                      fillColor: notifier.textFileColor,
                    ),
                    enabled: false, // Initial balance can't be changed
                    keyboardType: TextInputType.number,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: accountController.accountNumberController,
                    decoration: InputDecoration(
                      labelText: 'Account Number',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      filled: true,
                      fillColor: notifier.textFileColor,
                    ),
                    validator: accountController.validateAccountNumber,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: accountController.branchCodeController,
                    decoration: InputDecoration(
                      labelText: 'Branch Code',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      filled: true,
                      fillColor: notifier.textFileColor,
                    ),
                    validator: accountController.validateBranchCode,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: accountController.branchAddressController,
                    decoration: InputDecoration(
                      labelText: 'Branch Address',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      filled: true,
                      fillColor: notifier.textFileColor,
                    ),
                  ),
                  const SizedBox(height: 24),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        if (accountController.formKey.currentState!
                            .validate()) {
                          final updatedAccount = AccountModel(
                            id: account.id,
                            name: accountController.nameController.text,
                            initialBalance: account.initialBalance,
                            accountNumber:
                                accountController.accountNumberController.text,
                            branchCode:
                                accountController.branchCodeController.text,
                            branchAddress:
                                accountController.branchAddressController.text,
                            availableBalance: account.availableBalance,
                            createdAt: account.createdAt,
                          );
                          accountController.updateAccount(updatedAccount);
                          Get.back();
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('Update Account'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _navigateToTransactions(AccountModel account) {
    // Initialize Firebase Service
    final transactionFirebaseService = AccountTransactionFirebaseService();

    // Initialize repository with Firebase Service
    final transactionRepository =
        AccountTransactionRepositoryImpl(transactionFirebaseService);

    // Initialize controller with repository
    final transactionController =
        AccountTransactionController(repository: transactionRepository);

    // Navigate to the transactions view
    Get.to(() => AccountTransactionsView(
          account: account,
          controller: transactionController,
        ));
  }
}
