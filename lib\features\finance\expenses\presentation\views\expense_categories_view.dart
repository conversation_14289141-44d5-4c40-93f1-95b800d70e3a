import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/widgets/header_secondary.dart';
import 'package:logestics/features/finance/expense_categories/repositories/expense_category_repository.dart';
import 'package:logestics/firebase_service/finance/expense_category_firebase_service.dart';
import 'package:logestics/models/finance/expense_category_model.dart';
import 'package:logestics/features/finance/expenses/presentation/controllers/expense_category_controller.dart';
import 'package:logestics/features/home/<USER>/drawer_controllers.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';

class ExpenseCategoriesView extends StatefulWidget {
  const ExpenseCategoriesView({super.key});

  @override
  State<ExpenseCategoriesView> createState() => _ExpenseCategoriesViewState();
}

class _ExpenseCategoriesViewState extends State<ExpenseCategoriesView> {
  MainDrawerController mainDrawerController = Get.put(MainDrawerController());
  late final ExpensesCategoryController categoryController;

  @override
  void initState() {
    super.initState();
    // Initialize Controllers
    final categoryRepository =
        ExpenseCategoryRepositoryImpl(ExpenseCategoryFirebaseService());
    categoryController =
        Get.put(ExpensesCategoryController(repository: categoryRepository));
  }

  @override
  Widget build(BuildContext context) {
    var width = Get.width;
    notifier = Provider.of(context, listen: true);

    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          child: Column(
            children: [
              SizedBox(
                height: width < 650 ? 55 : 40,
                width: width,
                child: width < 650
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Expense Categories',
                            overflow: TextOverflow.ellipsis,
                            style: AppTextStyles.titleStyle
                                .copyWith(color: notifier.text),
                          ),
                          const Spacer(),
                          HeaderSecondary(
                            option1: AppStrings.dashboard,
                            option2: AppStrings.system,
                            option3: 'Categories',
                            notifier: notifier,
                          ),
                        ],
                      )
                    : Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Expense Categories',
                            overflow: TextOverflow.ellipsis,
                            style: AppTextStyles.titleStyle
                                .copyWith(color: notifier.text),
                          ),
                          const Spacer(),
                          HeaderSecondary(
                            option1: AppStrings.dashboard,
                            option2: AppStrings.system,
                            option3: 'Categories',
                            notifier: notifier,
                          ),
                        ],
                      ),
              ),
              const SizedBox(height: 20),
              SizedBox(
                height: 570,
                child: _buildCategoriesList(),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCategoriesList() {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            color: notifier.getBgColor,
            borderRadius: BorderRadius.circular(10),
          ),
          padding: const EdgeInsets.symmetric(vertical: 15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 15),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    InkWell(
                      onTap: () => categoryController.openDrawer(),
                      child: Text(
                        'Add New Category',
                        style: AppTextStyles.addNewInvoiceStyle,
                      ),
                    ),
                    SizedBox(
                      width: Get.width < 850 ? Get.width / 2 : Get.width / 3.5,
                      child: TextField(
                        controller: categoryController.searchController,
                        decoration: InputDecoration(
                          hintText: 'Search categories...',
                          prefixIcon: const Icon(Icons.search),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          filled: true,
                          fillColor: notifier.textFileColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              Expanded(
                child: SizedBox(
                  width: Get.width,
                  child: Obx(() {
                    if (categoryController.isLoading.value) {
                      return const Center(
                        child: CircularProgressIndicator(),
                      );
                    }

                    return _buildCategoriesTable();
                  }),
                ),
              ),
            ],
          ),
        ),
        // Add/Edit Category Drawer
        Obx(() {
          if (!categoryController.isDrawerOpen.value) {
            return const SizedBox.shrink();
          }

          return Positioned(
            right: 0,
            top: 0,
            bottom: 0,
            width: Get.width < 650 ? Get.width : 400,
            child: Container(
              decoration: BoxDecoration(
                color: notifier.getBgColor,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(10),
                    blurRadius: 10,
                    offset: const Offset(0, 0),
                  ),
                ],
              ),
              child: Form(
                key: categoryController.formKey,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Obx(() => Text(
                                categoryController.editingCategory.value == null
                                    ? 'Add New Category'
                                    : 'Edit Category',
                                style: AppTextStyles.titleStyle.copyWith(
                                  color: notifier.text,
                                ),
                              )),
                          IconButton(
                            onPressed: categoryController.closeDrawer,
                            icon: const Icon(Icons.close),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      TextFormField(
                        controller: categoryController.nameController,
                        decoration: InputDecoration(
                          labelText: 'Category Name',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          filled: true,
                          fillColor: notifier.textFileColor,
                        ),
                        validator: categoryController.validateName,
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: categoryController.descriptionController,
                        decoration: InputDecoration(
                          labelText: 'Description',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          filled: true,
                          fillColor: notifier.textFileColor,
                        ),
                        maxLines: 3,
                      ),
                      const SizedBox(height: 24),
                      Obx(() => SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: categoryController.isLoading.value
                                  ? null
                                  : categoryController.saveCategory,
                              style: ElevatedButton.styleFrom(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: categoryController.isLoading.value
                                  ? const CircularProgressIndicator()
                                  : const Text('Save Category'),
                            ),
                          )),
                    ],
                  ),
                ),
              ),
            ),
          );
        }),
      ],
    );
  }

  Widget _buildCategoriesTable() {
    return Obx(() {
      final filteredCategories = categoryController.filteredCategories;

      if (filteredCategories.isEmpty) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.category,
                  size: 48,
                  color: notifier.text.withValues(alpha: 0.5),
                ),
                const SizedBox(height: 16),
                Text(
                  'No categories found',
                  style: TextStyle(
                    fontSize: 18,
                    color: notifier.text.withValues(alpha: 0.7),
                  ),
                ),
                if (categoryController.searchQuery.isNotEmpty)
                  TextButton(
                    onPressed: () {
                      categoryController.searchController.text = '';
                    },
                    child: const Text('Clear search'),
                  ),
              ],
            ),
          ),
        );
      }

      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 15),
        decoration: BoxDecoration(
          color: notifier.getcardColor,
          borderRadius: BorderRadius.circular(10),
        ),
        child: DataTable(
          columnSpacing: 20,
          headingRowColor: WidgetStateProperty.all(
            notifier.getHoverColor,
          ),
          dataRowMaxHeight: 80,
          dataRowMinHeight: 60,
          columns: [
            DataColumn(
              label: Text(
                'Name',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: notifier.text,
                ),
              ),
            ),
            DataColumn(
              label: Text(
                'Description',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: notifier.text,
                ),
              ),
            ),
            DataColumn(
              label: Text(
                'Actions',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: notifier.text,
                ),
              ),
            ),
          ],
          rows: filteredCategories.map((category) {
            return DataRow(
              cells: [
                DataCell(
                  Text(
                    category.name,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: notifier.text,
                    ),
                  ),
                ),
                DataCell(
                  Text(
                    category.description,
                    style: TextStyle(color: notifier.text),
                  ),
                ),
                DataCell(
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: Icon(Icons.edit, color: Colors.blue[400]),
                        onPressed: () =>
                            categoryController.openDrawer(category: category),
                        tooltip: 'Edit Category',
                      ),
                      IconButton(
                        icon: Icon(Icons.delete, color: Colors.red[400]),
                        onPressed: () => _confirmDelete(category),
                        tooltip: 'Delete Category',
                      ),
                    ],
                  ),
                ),
              ],
            );
          }).toList(),
        ),
      );
    });
  }

  Future<void> _confirmDelete(ExpenseCategoryModel category) {
    return Get.dialog(
      AlertDialog(
        title: const Text('Confirm Delete'),
        content: const Text('Are you sure you want to delete this category?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Get.back();
              Get.dialog(
                const Center(
                  child: CircularProgressIndicator(),
                ),
                barrierDismissible: false,
              );
              await categoryController.deleteCategory(category.id);
              Get.back();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
