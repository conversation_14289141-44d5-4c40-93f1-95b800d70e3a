// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:logestics/features/finance/accounts/presentation/controllers/account_transaction_controller.dart';
import 'package:logestics/models/finance/account_transaction_model.dart';
import 'package:logestics/models/finance/expense_category_model.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';

class TransactionCategoryDialog extends StatefulWidget {
  final AccountTransactionController controller;
  final AccountTransactionModel transaction;
  final Function(bool) onComplete;

  const TransactionCategoryDialog({
    super.key,
    required this.controller,
    required this.transaction,
    required this.onComplete,
  });

  @override
  State<TransactionCategoryDialog> createState() =>
      _TransactionCategoryDialogState();
}

class _TransactionCategoryDialogState extends State<TransactionCategoryDialog> {
  late TextEditingController searchController;
  late ExpenseCategoryModel? selectedCategory;
  bool isUpdating = false;

  @override
  void initState() {
    super.initState();
    searchController = TextEditingController();
    // Initialize selected category if transaction has one
    selectedCategory =
        widget.controller.getCategoryForTransaction(widget.transaction);
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }

  // Filter categories based on search text
  List<ExpenseCategoryModel> _getFilteredCategories() {
    final categories = widget.controller.categories;
    if (searchController.text.isEmpty) {
      return categories;
    }
    return categories.where((category) {
      return category.name
              .toLowerCase()
              .contains(searchController.text.toLowerCase()) ||
          category.description
              .toLowerCase()
              .contains(searchController.text.toLowerCase());
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    // Get global notifier for theme
    notifier = Provider.of(context, listen: true);

    return AlertDialog(
      backgroundColor: notifier.getBgColor,
      title: Column(
        children: [
          Row(
            children: [
              Icon(Icons.category, color: Colors.teal),
              const SizedBox(width: 8),
              Text(
                'Select Transaction Category',
                style: TextStyle(color: notifier.text),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Current category display
          if (selectedCategory != null)
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.teal.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.teal.withValues(alpha: 0.5)),
              ),
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.teal, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Selected Category:',
                          style: TextStyle(
                            color: notifier.text.withValues(alpha: 0.7),
                            fontSize: 12,
                          ),
                        ),
                        Text(
                          selectedCategory!.name,
                          style: TextStyle(
                            color: notifier.text,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    icon: Icon(Icons.clear, color: Colors.red),
                    onPressed: () {
                      setState(() {
                        selectedCategory = null;
                      });
                    },
                    tooltip: 'Clear selection',
                  ),
                ],
              ),
            ),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Search field
            TextField(
              controller: searchController,
              decoration: InputDecoration(
                prefixIcon: Icon(Icons.search,
                    color: notifier.text.withValues(alpha: 0.7)),
                hintText: 'Search categories...',
                hintStyle:
                    TextStyle(color: notifier.text.withValues(alpha: 0.5)),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide:
                      BorderSide(color: notifier.text.withValues(alpha: 0.2)),
                ),
                filled: true,
                fillColor: notifier.getcardColor,
              ),
              style: TextStyle(color: notifier.text),
              onChanged: (_) => setState(() {}), // Rebuild on search change
            ),
            const SizedBox(height: 16),

            // Categories list
            SizedBox(
              height: 300,
              child: widget.controller.isCategoriesLoading.value
                  ? const Center(child: CircularProgressIndicator())
                  : _buildCategoriesList(),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            widget.onComplete(false);
            Navigator.pop(context);
          },
          child: Text(
            'Cancel',
            style: TextStyle(color: Colors.grey),
          ),
        ),
        ElevatedButton(
          onPressed: isUpdating ? null : () => _updateTransactionCategory(),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.teal,
            foregroundColor: Colors.white,
          ),
          child: isUpdating
              ? SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text('Update Category'),
        ),
      ],
    );
  }

  Widget _buildCategoriesList() {
    final filteredCategories = _getFilteredCategories();

    if (filteredCategories.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.category_outlined,
              size: 48,
              color: notifier.text.withValues(alpha: 0.3),
            ),
            const SizedBox(height: 16),
            Text(
              'No categories found',
              style: TextStyle(
                color: notifier.text.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: filteredCategories.length,
      itemBuilder: (context, index) {
        final category = filteredCategories[index];
        final isSelected = selectedCategory?.id == category.id;

        return Card(
          color: isSelected
              ? Colors.teal.withValues(alpha: 0.1)
              : notifier.getcardColor,
          margin: const EdgeInsets.symmetric(vertical: 4),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: isSelected
                ? BorderSide(color: Colors.teal, width: 2)
                : BorderSide.none,
          ),
          child: ListTile(
            leading: isSelected
                ? Icon(Icons.check_circle, color: Colors.teal)
                : Icon(Icons.category,
                    color: notifier.text.withValues(alpha: 0.7)),
            title: Text(
              category.name,
              style: TextStyle(
                color: notifier.text,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
            subtitle: category.description.isNotEmpty
                ? Text(
                    category.description,
                    style: TextStyle(
                      color: notifier.text.withValues(alpha: 0.7),
                      fontSize: 12,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  )
                : null,
            onTap: () {
              setState(() {
                selectedCategory = category;
              });
            },
          ),
        );
      },
    );
  }

  Future<void> _updateTransactionCategory() async {
    setState(() {
      isUpdating = true;
    });

    try {
      if (selectedCategory == null) {
        // Clear category
        final success = await widget.controller.updateTransactionCategory(
          widget.transaction.id,
          '',
          '',
        );
        widget.onComplete(success);
      } else {
        // Update with selected category
        final success = await widget.controller.updateTransactionCategory(
          widget.transaction.id,
          selectedCategory!.id,
          selectedCategory!.name,
        );
        widget.onComplete(success);
      }

      Navigator.pop(context);
    } catch (e) {
      // Handle error
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error updating category: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          isUpdating = false;
        });
      }
    }
  }
}
