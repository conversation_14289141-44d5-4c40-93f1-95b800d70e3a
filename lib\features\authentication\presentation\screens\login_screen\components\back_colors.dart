import 'dart:ui';
import 'package:flutter/material.dart';

import '../../../../../../core/utils/constants/constants.dart';
import '../../../../../../core/utils/helpers/responsive_helper.dart';

class BackColors extends StatelessWidget {
  const BackColors({super.key});
  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.sizeOf(context);
    return Container(
      color: Colors.white,
      margin: EdgeInsets.only(top: 30),
      child: <PERSON>ack(
        children: [
          Positioned(
              top: 100,
              child: Container(
                height: size.height * 0.5,
                width: size.width * 0.5,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(30),
                    gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.centerRight,
                        colors: [
                          lightOrange.withAlpha(00),
                          lightOrange.withAlpha(10),
                          lightOrange.withAlpha(20),
                          lightOrange.withAlpha(30),
                          lightOrange.withAlpha(40),
                          lightOrange.withAlpha(40),
                          lightOrange.withAlpha(30),
                          lightOrange.withAlpha(20),
                          lightOrange.withAlpha(10),
                          lightOrange.withAlpha(0),
                        ])),
              )),
          Positioned(
              top: 100,
              right: -50,
              child: Container(
                height: size.height * 0.5,
                width: size.width * 0.3,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(30),
                    gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.centerRight,
                        colors: [
                          lightOrange.withAlpha(00),
                          lightOrange.withAlpha(10),
                          lightOrange.withAlpha(20),
                          lightOrange.withAlpha(30),
                          lightOrange.withAlpha(20),
                          lightOrange.withAlpha(10),
                          lightOrange.withAlpha(0),
                        ])),
              )),
          Positioned(
              bottom: 100,
              right: -50,
              child: Container(
                height: size.height * 0.5,
                width: size.width * 0.6,
                decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.centerRight,
                        colors: [
                          lightAccentBlue.withAlpha(00),
                          lightAccentBlue.withAlpha(10),
                          lightAccentBlue.withAlpha(20),
                          lightAccentBlue.withAlpha(30),
                          lightAccentBlue.withAlpha(40),
                          lightAccentBlue.withAlpha(40),
                          lightAccentBlue.withAlpha(30),
                          lightAccentBlue.withAlpha(20),
                          lightAccentBlue.withAlpha(10),
                          lightAccentBlue.withAlpha(0),
                        ])),
              )),
          Positioned(
              bottom: -30,
              child: Container(
                height: size.height * 0.3,
                width: size.width * 0.6,
                decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.centerRight,
                        colors: [
                          lightAccentBlue.withAlpha(00),
                          lightAccentBlue.withAlpha(10),
                          lightAccentBlue.withAlpha(20),
                          lightAccentBlue.withAlpha(30),
                          lightAccentBlue.withAlpha(40),
                          lightAccentBlue.withAlpha(40),
                          lightAccentBlue.withAlpha(30),
                          lightAccentBlue.withAlpha(20),
                          lightAccentBlue.withAlpha(10),
                          lightAccentBlue.withAlpha(0),
                        ])),
              )),
          Positioned(
              bottom: 1,
              left: 1,
              top: !Responsive.isTablet(context) ? 100 : 200,
              right: 1,
              child: Container(
                decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.centerRight,
                        colors: [
                          Colors.pinkAccent.withAlpha(00),
                          Colors.pinkAccent.withAlpha(10),
                          Colors.pinkAccent.withAlpha(20),
                          Colors.pinkAccent.withAlpha(30),
                          Colors.pinkAccent.withAlpha(40),
                          Colors.pinkAccent.withAlpha(40),
                          Colors.pinkAccent.withAlpha(30),
                          Colors.pinkAccent.withAlpha(20),
                          Colors.pinkAccent.withAlpha(10),
                          Colors.pinkAccent.withAlpha(0),
                        ])),
              )),
          Positioned(
              bottom: 1,
              right: 1,
              child: Container(
                height: size.height * 0.3,
                width: size.width * 0.6,
                decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.centerRight,
                        colors: [
                          Colors.greenAccent.withAlpha(00),
                          Colors.greenAccent.withAlpha(10),
                          Colors.greenAccent.withAlpha(20),
                          Colors.greenAccent.withAlpha(30),
                          Colors.greenAccent.withAlpha(40),
                          Colors.greenAccent.withAlpha(40),
                          Colors.greenAccent.withAlpha(30),
                          Colors.greenAccent.withAlpha(20),
                          Colors.greenAccent.withAlpha(10),
                          Colors.greenAccent.withAlpha(0),
                        ])),
              )),
          Positioned.fill(
              child: BackdropFilter(
            filter: ImageFilter.blur(sigmaY: 30, sigmaX: 30),
            child: SizedBox(),
          )),
        ],
      ),
    );
  }
}
