import 'package:flutter/material.dart';

// Widget buildFormField(
//   BoxConstraints constraints,
//   bool isSmallScreen,
//   bool isMediumScreen,
//   Widget field,
// ) {
//   double width = isSmallScreen
//       ? constraints.maxWidth
//       : isMediumScreen
//           ? (constraints.maxWidth - 16) / 2
//           : (constraints.maxWidth - 32) / 3;
//   return SizedBox(
//     width: width,
//     child: field,
//   );
// }

class FormFieldWidget extends StatelessWidget {
  final BoxConstraints constraints;
  final bool isSmallScreen;
  final bool isMediumScreen;
  final Widget field;

  const FormFieldWidget({
    super.key,
    required this.constraints,
    required this.isSmallScreen,
    required this.isMediumScreen,
    required this.field,
  });

  @override
  Widget build(BuildContext context) {
    double width = isSmallScreen
        ? constraints.maxWidth
        : isMediumScreen
            ? (constraints.maxWidth) / 2.8
            : (constraints.maxWidth) / 2.5;
    return SizedBox(
      width: width,
      child: field,
    );
  }
}
