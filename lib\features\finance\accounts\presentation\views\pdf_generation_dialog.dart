import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../../../../../core/utils/app_constants/styles/app_text_styles.dart';
import '../../../../../features/home/<USER>/theme.dart';
import '../../../../../models/finance/account_model.dart';
import '../../../../../models/finance/account_transaction_model.dart';
import '../controllers/pdf_generation_controller.dart';

class PDFGenerationDialog extends StatefulWidget {
  final AccountModel? preSelectedAccount;
  final DateTimeRange? initialDateRange;

  const PDFGenerationDialog({
    super.key,
    this.preSelectedAccount,
    this.initialDateRange,
  });

  @override
  State<PDFGenerationDialog> createState() => _PDFGenerationDialogState();
}

class _PDFGenerationDialogState extends State<PDFGenerationDialog> {
  late PDFGenerationController controller;

  @override
  void initState() {
    super.initState();
    controller = Get.find<PDFGenerationController>();

    // Set pre-selected account if provided
    if (widget.preSelectedAccount != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        controller.setSelectedAccount(widget.preSelectedAccount!);
      });
    }

    // Set initial date range if provided
    if (widget.initialDateRange != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        controller.setDateRange(
          widget.initialDateRange!.start,
          widget.initialDateRange!.end,
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final notifier = Provider.of<ColorNotifier>(context);

    return Dialog(
      backgroundColor: notifier.getcardColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: 800,
          maxHeight: MediaQuery.of(context).size.height * 0.9,
        ),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.8,
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Generate Account Statement PDF',
                    style: AppTextStyles.titleStyle.copyWith(
                      color: notifier.text,
                      fontSize: 20,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Get.back(),
                    icon: Icon(Icons.close, color: notifier.text),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Content
              Flexible(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Account Selection Section
                      _buildAccountSelectionSection(notifier),

                      const SizedBox(height: 24),

                      // Date Range Section
                      _buildDateRangeSection(notifier),

                      const SizedBox(height: 24),

                      // Customization Section
                      _buildCustomizationSection(notifier),

                      const SizedBox(height: 24),

                      // Transaction Preview
                      _buildTransactionPreview(notifier),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Action Buttons
              _buildActionButtons(notifier),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAccountSelectionSection(ColorNotifier notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Account Selection',
          style: AppTextStyles.titleStyle.copyWith(
            color: notifier.text,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 12),

        // Account Type Filter
        Row(
          children: [
            Expanded(
              flex: 2,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Account Type',
                    style: TextStyle(
                      color: notifier.text,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Obx(() => Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        decoration: BoxDecoration(
                          border: Border.all(
                              color: notifier.text.withValues(alpha: 0.3)),
                          borderRadius: BorderRadius.circular(8),
                          color: notifier.getBgColor,
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<String>(
                            value: controller.selectedAccountType.value,
                            isExpanded: true,
                            dropdownColor: notifier.getcardColor,
                            style: TextStyle(color: notifier.text),
                            items: controller.accountTypes.map((type) {
                              return DropdownMenuItem(
                                value: type,
                                child: Text(type),
                              );
                            }).toList(),
                            onChanged: (value) {
                              if (value != null) {
                                controller.selectedAccountType.value = value;
                              }
                            },
                          ),
                        ),
                      )),
                ],
              ),
            ),
            const SizedBox(width: 16),

            // Specific Account
            Expanded(
              flex: 3,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Specific Account',
                    style: TextStyle(
                      color: notifier.text,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Obx(() => Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        decoration: BoxDecoration(
                          border: Border.all(
                              color: notifier.text.withValues(alpha: 0.3)),
                          borderRadius: BorderRadius.circular(8),
                          color: notifier.getBgColor,
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<AccountModel>(
                            value: controller.selectedAccount.value,
                            isExpanded: true,
                            hint: Text(
                              'Select Account',
                              style: TextStyle(
                                  color: notifier.text.withValues(alpha: 0.6)),
                            ),
                            dropdownColor: notifier.getcardColor,
                            style: TextStyle(color: notifier.text),
                            items: controller.filteredAccounts.map((account) {
                              return DropdownMenuItem(
                                value: account,
                                child: Text(
                                  account.name,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              );
                            }).toList(),
                            onChanged: (account) {
                              if (account != null) {
                                controller.setSelectedAccount(account);
                              }
                            },
                          ),
                        ),
                      )),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDateRangeSection(ColorNotifier notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Date Range',
          style: AppTextStyles.titleStyle.copyWith(
            color: notifier.text,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            // Start Date
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Start Date',
                    style: TextStyle(
                      color: notifier.text,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Obx(() => InkWell(
                        onTap: () => _selectStartDate(context),
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            border: Border.all(
                                color: notifier.text.withValues(alpha: 0.3)),
                            borderRadius: BorderRadius.circular(8),
                            color: notifier.getBgColor,
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.calendar_today,
                                  color: notifier.text.withValues(alpha: 0.6),
                                  size: 16),
                              const SizedBox(width: 8),
                              Text(
                                controller.startDate.value != null
                                    ? DateFormat('dd/MM/yyyy')
                                        .format(controller.startDate.value!)
                                    : 'Select Date',
                                style: TextStyle(color: notifier.text),
                              ),
                            ],
                          ),
                        ),
                      )),
                ],
              ),
            ),

            const SizedBox(width: 16),

            // End Date
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'End Date',
                    style: TextStyle(
                      color: notifier.text,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Obx(() => InkWell(
                        onTap: () => _selectEndDate(context),
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            border: Border.all(
                                color: notifier.text.withValues(alpha: 0.3)),
                            borderRadius: BorderRadius.circular(8),
                            color: notifier.getBgColor,
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.calendar_today,
                                  color: notifier.text.withValues(alpha: 0.6),
                                  size: 16),
                              const SizedBox(width: 8),
                              Text(
                                controller.endDate.value != null
                                    ? DateFormat('dd/MM/yyyy')
                                        .format(controller.endDate.value!)
                                    : 'Select Date',
                                style: TextStyle(color: notifier.text),
                              ),
                            ],
                          ),
                        ),
                      )),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCustomizationSection(ColorNotifier notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'PDF Customization (Optional)',
          style: AppTextStyles.titleStyle.copyWith(
            color: notifier.text,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 12),

        // Company Name
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Company Name',
              style: TextStyle(
                color: notifier.text,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: controller.companyNameController,
              style: TextStyle(color: notifier.text),
              decoration: InputDecoration(
                hintText: 'Enter company name',
                hintStyle:
                    TextStyle(color: notifier.text.withValues(alpha: 0.6)),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide:
                      BorderSide(color: notifier.text.withValues(alpha: 0.3)),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide:
                      BorderSide(color: notifier.text.withValues(alpha: 0.3)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.blue),
                ),
                filled: true,
                fillColor: notifier.getBgColor,
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Company Address
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Company Address',
              style: TextStyle(
                color: notifier.text,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: controller.companyAddressController,
              style: TextStyle(color: notifier.text),
              maxLines: 2,
              decoration: InputDecoration(
                hintText: 'Enter company address',
                hintStyle:
                    TextStyle(color: notifier.text.withValues(alpha: 0.6)),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide:
                      BorderSide(color: notifier.text.withValues(alpha: 0.3)),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide:
                      BorderSide(color: notifier.text.withValues(alpha: 0.3)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.blue),
                ),
                filled: true,
                fillColor: notifier.getBgColor,
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Custom Title
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Custom Report Title',
              style: TextStyle(
                color: notifier.text,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: controller.customTitleController,
              style: TextStyle(color: notifier.text),
              decoration: InputDecoration(
                hintText:
                    'Enter custom title (default: Account Statement Report)',
                hintStyle:
                    TextStyle(color: notifier.text.withValues(alpha: 0.6)),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide:
                      BorderSide(color: notifier.text.withValues(alpha: 0.3)),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide:
                      BorderSide(color: notifier.text.withValues(alpha: 0.3)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.blue),
                ),
                filled: true,
                fillColor: notifier.getBgColor,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTransactionPreview(ColorNotifier notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Transaction Preview',
              style: AppTextStyles.titleStyle.copyWith(
                color: notifier.text,
                fontSize: 16,
              ),
            ),
            // Date range indicator
            Obx(() {
              if (controller.startDate.value != null &&
                  controller.endDate.value != null) {
                final dateFormat = DateFormat('dd/MM/yyyy');
                return Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                    border:
                        Border.all(color: Colors.blue.withValues(alpha: 0.3)),
                  ),
                  child: Text(
                    '${dateFormat.format(controller.startDate.value!)} - ${dateFormat.format(controller.endDate.value!)}',
                    style: TextStyle(
                      color: Colors.blue,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }
              return const SizedBox.shrink();
            }),
          ],
        ),
        const SizedBox(height: 12),
        Obx(() {
          if (controller.selectedAccount.value == null) {
            return Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: notifier.text.withValues(alpha: 0.3)),
                borderRadius: BorderRadius.circular(8),
                color: notifier.getBgColor,
              ),
              child: Center(
                child: Text(
                  'Please select an account to preview transactions',
                  style: TextStyle(
                    color: notifier.text.withValues(alpha: 0.6),
                    fontSize: 14,
                  ),
                ),
              ),
            );
          }

          if (controller.isLoading.value) {
            return Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: notifier.text.withValues(alpha: 0.3)),
                borderRadius: BorderRadius.circular(8),
                color: notifier.getBgColor,
              ),
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            );
          }

          final transactions = controller.filteredTransactions;

          if (transactions.isEmpty) {
            return Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: notifier.text.withValues(alpha: 0.3)),
                borderRadius: BorderRadius.circular(8),
                color: notifier.getBgColor,
              ),
              child: Center(
                child: Text(
                  'No transactions found for the selected period',
                  style: TextStyle(
                    color: notifier.text.withValues(alpha: 0.6),
                    fontSize: 14,
                  ),
                ),
              ),
            );
          }

          // Calculate totals
          final totalCredits = transactions
              .where((t) => t.amount > 0)
              .fold(0.0, (sum, t) => sum + t.amount);

          final totalDebits = transactions
              .where((t) => t.amount < 0)
              .fold(0.0, (sum, t) => sum + t.amount.abs());

          final currencyFormat =
              NumberFormat.currency(symbol: 'PKR ', decimalDigits: 2);

          return Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: notifier.text.withValues(alpha: 0.3)),
              borderRadius: BorderRadius.circular(8),
              color: notifier.getBgColor,
            ),
            child: Column(
              children: [
                // Filtering Status Indicator
                if (controller.startDate.value != null &&
                    controller.endDate.value != null &&
                    controller.transactions.isNotEmpty)
                  Container(
                    width: double.infinity,
                    padding:
                        const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                    margin: const EdgeInsets.only(bottom: 12),
                    decoration: BoxDecoration(
                      color:
                          transactions.length < controller.transactions.length
                              ? Colors.orange.withValues(alpha: 0.1)
                              : Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(
                        color:
                            transactions.length < controller.transactions.length
                                ? Colors.orange.withValues(alpha: 0.3)
                                : Colors.green.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          transactions.length < controller.transactions.length
                              ? Icons.filter_alt
                              : Icons.check_circle_outline,
                          size: 16,
                          color: transactions.length <
                                  controller.transactions.length
                              ? Colors.orange
                              : Colors.green,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            transactions.length < controller.transactions.length
                                ? 'Showing ${transactions.length} of ${controller.transactions.length} transactions (filtered by date range)'
                                : 'Showing all ${transactions.length} transactions for selected period',
                            style: TextStyle(
                              color: transactions.length <
                                      controller.transactions.length
                                  ? Colors.orange.shade700
                                  : Colors.green.shade700,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                // Summary Row
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    Column(
                      children: [
                        Text(
                          'Transactions',
                          style: TextStyle(
                            color: notifier.text.withValues(alpha: 0.7),
                            fontSize: 12,
                          ),
                        ),
                        Text(
                          '${transactions.length}',
                          style: TextStyle(
                            color: notifier.text,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        // Show filtered vs total if filtering is applied
                        if (controller.startDate.value != null &&
                            controller.endDate.value != null &&
                            controller.transactions.isNotEmpty)
                          Text(
                            'of ${controller.transactions.length} total',
                            style: TextStyle(
                              color: notifier.text.withValues(alpha: 0.5),
                              fontSize: 10,
                            ),
                          ),
                      ],
                    ),
                    Column(
                      children: [
                        Text(
                          'Total Credits',
                          style: TextStyle(
                            color: notifier.text.withValues(alpha: 0.7),
                            fontSize: 12,
                          ),
                        ),
                        Text(
                          currencyFormat.format(totalCredits),
                          style: const TextStyle(
                            color: Colors.green,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    Column(
                      children: [
                        Text(
                          'Total Debits',
                          style: TextStyle(
                            color: notifier.text.withValues(alpha: 0.7),
                            fontSize: 12,
                          ),
                        ),
                        Text(
                          currencyFormat.format(totalDebits),
                          style: const TextStyle(
                            color: Colors.red,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),

                // Transaction List Preview
                if (transactions.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  Divider(color: notifier.text.withValues(alpha: 0.2)),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Icon(
                        Icons.list_alt,
                        size: 16,
                        color: notifier.text.withValues(alpha: 0.7),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Transaction Preview (First 5)',
                        style: TextStyle(
                          color: notifier.text.withValues(alpha: 0.7),
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Container(
                    constraints: const BoxConstraints(maxHeight: 200),
                    child: ListView.builder(
                      shrinkWrap: true,
                      itemCount:
                          transactions.length > 5 ? 5 : transactions.length,
                      itemBuilder: (context, index) {
                        final transaction = transactions[index];
                        final isCredit =
                            transaction.type == TransactionType.deposit;

                        return Container(
                          padding: const EdgeInsets.symmetric(
                              vertical: 6, horizontal: 8),
                          margin: const EdgeInsets.only(bottom: 4),
                          decoration: BoxDecoration(
                            color: notifier.getBgColor,
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(
                              color: notifier.text.withValues(alpha: 0.1),
                            ),
                          ),
                          child: Row(
                            children: [
                              // Date
                              SizedBox(
                                width: 70,
                                child: Text(
                                  DateFormat('dd/MM')
                                      .format(transaction.transactionDate),
                                  style: TextStyle(
                                    color: notifier.text.withValues(alpha: 0.6),
                                    fontSize: 11,
                                  ),
                                ),
                              ),
                              // Description
                              Expanded(
                                child: Text(
                                  transaction.description,
                                  style: TextStyle(
                                    color: notifier.text,
                                    fontSize: 11,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              // Amount
                              Text(
                                currencyFormat.format(transaction.amount),
                                style: TextStyle(
                                  color: isCredit ? Colors.green : Colors.red,
                                  fontSize: 11,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                  if (transactions.length > 5)
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        '... and ${transactions.length - 5} more transactions',
                        style: TextStyle(
                          color: notifier.text.withValues(alpha: 0.5),
                          fontSize: 11,
                          fontStyle: FontStyle.italic,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                ],
              ],
            ),
          );
        }),
      ],
    );
  }

  Widget _buildActionButtons(ColorNotifier notifier) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        // Reset Button
        TextButton(
          onPressed: () {
            controller.resetForm();
          },
          child: Text(
            'Reset',
            style: TextStyle(color: notifier.text.withValues(alpha: 0.7)),
          ),
        ),

        const SizedBox(width: 12),

        // Cancel Button
        TextButton(
          onPressed: () => Get.back(),
          child: Text(
            'Cancel',
            style: TextStyle(color: notifier.text.withValues(alpha: 0.7)),
          ),
        ),

        const SizedBox(width: 12),

        // Share Button
        Obx(() => ElevatedButton.icon(
              onPressed: controller.isLoading.value
                  ? null
                  : () {
                      controller.sharePDF();
                    },
              icon: controller.isLoading.value
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.share),
              label: Text(
                  controller.isLoading.value ? 'Generating...' : 'Share PDF'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
            )),

        const SizedBox(width: 12),

        // Generate Button
        Obx(() => ElevatedButton.icon(
              onPressed: controller.isLoading.value
                  ? null
                  : () {
                      controller.generatePDF();
                    },
              icon: controller.isLoading.value
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.picture_as_pdf),
              label: Text(controller.isLoading.value
                  ? 'Generating...'
                  : 'Generate PDF'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            )),
      ],
    );
  }

  Future<void> _selectStartDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: controller.startDate.value ??
          DateTime.now().subtract(const Duration(days: 30)),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      controller.setDateRange(
          picked, controller.endDate.value ?? DateTime.now());
    }
  }

  Future<void> _selectEndDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: controller.endDate.value ?? DateTime.now(),
      firstDate: controller.startDate.value ?? DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      controller.setDateRange(
          controller.startDate.value ??
              DateTime.now().subtract(const Duration(days: 30)),
          picked);
    }
  }
}
