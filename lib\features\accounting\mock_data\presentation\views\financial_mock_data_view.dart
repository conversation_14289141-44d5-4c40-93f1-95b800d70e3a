import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../../core/utils/widgets/navigation/drawer_page_scaffold.dart';
import '../controllers/financial_mock_data_controller.dart';

class FinancialMockDataView extends StatelessWidget {
  const FinancialMockDataView({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(FinancialMockDataController());

    return DrawerPageScaffold(
      pageTitle: 'Financial Mock Data Management',
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.data_usage,
                          size: 32,
                          color: Theme.of(context).primaryColor,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Financial System Mock Data',
                                style:
                                    Theme.of(context).textTheme.headlineSmall,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'Generate comprehensive test data for the financial system including Chart of Accounts, Journal Entries, and General Ledger transactions.',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.copyWith(
                                      color: Colors.grey[600],
                                    ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Progress Section
            Obx(() {
              if (controller.isGenerating.value) {
                return Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Generating Mock Data...',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 12),
                        LinearProgressIndicator(
                          value: controller.progress.value,
                          backgroundColor: Colors.grey[300],
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Theme.of(context).primaryColor,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          controller.currentStep.value,
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '${(controller.progress.value * 100).toInt()}% Complete',
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Colors.grey[600],
                                  ),
                        ),
                      ],
                    ),
                  ),
                );
              }
              return const SizedBox.shrink();
            }),

            // Action Cards
            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 1.2,
                children: [
                  // Generate Mock Data Card
                  _buildActionCard(
                    context: context,
                    title: 'Generate Mock Data',
                    subtitle: 'Create comprehensive test data',
                    icon: Icons.add_circle_outline,
                    color: Colors.green,
                    onTap: () => controller.generateMockData(),
                    isEnabled: !controller.isGenerating.value,
                  ),

                  // Clear All Data Card
                  _buildActionCard(
                    context: context,
                    title: 'Clear All Data',
                    subtitle: 'Remove all financial data',
                    icon: Icons.delete_outline,
                    color: Colors.red,
                    onTap: () => controller.clearAllData(),
                    isEnabled: !controller.isGenerating.value,
                  ),

                  // View Chart of Accounts Card
                  _buildActionCard(
                    context: context,
                    title: 'Chart of Accounts',
                    subtitle: 'View account structure',
                    icon: Icons.account_tree,
                    color: Colors.blue,
                    onTap: () => Get.toNamed('/chart-of-accounts'),
                    isEnabled: true,
                  ),

                  // View Financial Reports Card
                  _buildActionCard(
                    context: context,
                    title: 'Financial Reports',
                    subtitle: 'Access all reports',
                    icon: Icons.assessment,
                    color: Colors.purple,
                    onTap: () => Get.toNamed('/financial-dashboard'),
                    isEnabled: true,
                  ),
                ],
              ),
            ),

            // Data Overview Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Mock Data Includes:',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 12),
                    _buildDataItem(
                      icon: Icons.account_balance_wallet,
                      title: 'Chart of Accounts',
                      description:
                          '20+ accounts across all categories (Assets, Liabilities, Equity, Revenue, Expenses)',
                    ),
                    const SizedBox(height: 8),
                    _buildDataItem(
                      icon: Icons.receipt_long,
                      title: 'Journal Entries',
                      description:
                          '200+ realistic transactions over the last 3 months',
                    ),
                    const SizedBox(height: 8),
                    _buildDataItem(
                      icon: Icons.timeline,
                      title: 'General Ledger',
                      description:
                          'Complete transaction history with running balances',
                    ),
                    const SizedBox(height: 8),
                    _buildDataItem(
                      icon: Icons.analytics,
                      title: 'Financial Reports',
                      description:
                          'Data for Balance Sheet, P&L, Trial Balance, Cash Flow, and Aged Reports',
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionCard({
    required BuildContext context,
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
    required bool isEnabled,
  }) {
    return Card(
      elevation: isEnabled ? 2 : 1,
      child: InkWell(
        onTap: isEnabled ? onTap : null,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 48,
                color: isEnabled ? color : Colors.grey,
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: isEnabled ? null : Colors.grey,
                    ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: isEnabled ? Colors.grey[600] : Colors.grey,
                    ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDataItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 20,
          color: Colors.grey[600],
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                description,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
