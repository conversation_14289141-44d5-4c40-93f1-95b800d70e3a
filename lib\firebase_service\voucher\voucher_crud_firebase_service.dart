import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';

class VoucherCrudFirebaseService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Creates a voucher in Firestore using flat structure.
  Future<void> createVoucherToFirebase({
    required String uid,
    required Map<String, dynamic> voucher,
  }) async {
    try {
      // Add company ID to voucher for filtering
      voucher['uid'] = uid;
      voucher['createdAt'] = DateTime.now().millisecondsSinceEpoch;

      // Create a reference to the Firestore document in flat structure
      final voucherRef = _firestore
          .collection(AppCollection.vouchersCollection)
          .doc(voucher['voucherNumber']);

      // Check if document with this voucher number already exists
      final docSnapshot = await voucherRef.get();
      if (docSnapshot.exists) {
        throw Exception(
            "Voucher with number ${voucher['voucherNumber']} already exists.");
      }

      // Save the document with a timeout of 10 seconds
      await voucherRef.set(voucher).timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw TimeoutException("The operation timed out. Please try again.");
        },
      );

      log(
        "Voucher ${voucher['voucherNumber']} saved successfully for company $uid.",
      );
    } on SocketException {
      log("No internet connection. Please check your network.");
      rethrow;
    } on TimeoutException {
      log("Timeout now You are on Offline mode");
      rethrow;
    } on FirebaseException catch (e) {
      log("Firebase error: ${e.message}");
      rethrow;
    } catch (e) {
      log("Unexpected error: $e");
      rethrow;
    }
  }

  /// Gets all vouchers for a specific company
  Future<List<Map<String, dynamic>>> getVouchersForCompany({
    required String uid,
  }) async {
    try {
      final querySnapshot = await _firestore
          .collection(AppCollection.vouchersCollection)
          .where('uid', isEqualTo: uid)
          .orderBy('createdAt', descending: true)
          .get();

      log('Fetched ${querySnapshot.docs.length} vouchers for company $uid');
      return querySnapshot.docs
          .map((doc) => {...doc.data(), 'id': doc.id})
          .toList();
    } catch (e) {
      log('Error getting vouchers for company: $e');
      rethrow;
    }
  }

  /// Gets a single voucher by voucher number
  Future<Map<String, dynamic>?> getVoucherByNumber({
    required String uid,
    required String voucherNumber,
  }) async {
    try {
      log('Fetching voucher $voucherNumber for company $uid');

      final doc = await _firestore
          .collection(AppCollection.vouchersCollection)
          .doc(voucherNumber)
          .get();

      if (!doc.exists) {
        log('Voucher $voucherNumber not found');
        return null;
      }

      final voucherData = doc.data() as Map<String, dynamic>;

      // Verify the voucher belongs to the requesting company
      if (voucherData['uid'] != uid) {
        log('Voucher $voucherNumber does not belong to company $uid');
        return null;
      }

      log('Successfully fetched voucher $voucherNumber');
      return voucherData;
    } catch (e) {
      log('Error fetching voucher $voucherNumber: $e');
      rethrow;
    }
  }

  /// Listens to vouchers for a specific company
  Stream<List<DocumentChange>> listenToVouchers({required String uid}) {
    return _firestore
        .collection(AppCollection.vouchersCollection)
        .where('uid', isEqualTo: uid)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) => snapshot.docChanges,
        );
  }

  /// Updates an existing voucher in Firestore using flat structure.
  Future<void> updateVoucherToFirebase({
    required String uid,
    required Map<String, dynamic> voucher,
  }) async {
    try {
      // Ensure the `voucherNumber` field exists in the voucher data
      if (voucher['voucherNumber'] == null ||
          (voucher['voucherNumber'] as String).isEmpty) {
        throw ArgumentError(
            'The "voucherNumber" field must not be null or empty.');
      }

      // Reference to the voucher document in flat structure
      final voucherRef = _firestore
          .collection(AppCollection.vouchersCollection)
          .doc(voucher['voucherNumber']);

      // Check if the voucher belongs to the current company
      final voucherDoc = await voucherRef.get();
      if (!voucherDoc.exists) {
        throw Exception('Voucher not found');
      }

      final voucherData = voucherDoc.data() as Map<String, dynamic>;
      if (voucherData['uid'] != uid) {
        throw Exception('You do not have permission to update this voucher');
      }

      // Preserve the original company ID and add updated date time
      voucher['uid'] = voucherData['uid'];
      voucher['updatedAt'] = DateTime.now().millisecondsSinceEpoch;

      // Update the voucher with a timeout of 10 seconds
      await voucherRef.update(voucher).timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw TimeoutException("Now you are on offline Mode");
        },
      );

      log(
        "Voucher ${voucher['voucherNumber']} updated successfully for company $uid.",
      );
    } on SocketException {
      log("No internet connection. Please check your network.");
      rethrow;
    } on TimeoutException catch (e) {
      log("Timeout error: ${e.message}");
      rethrow;
    } on FirebaseException catch (e) {
      if (e.code == 'voucher-not-found') {
        log("Voucher not found: ${e.message}");
      } else {
        log("Firebase error: ${e.message}");
      }
      rethrow;
    } on ArgumentError catch (e) {
      log("Argument error: ${e.message}");
      rethrow;
    } catch (e) {
      log("Unexpected error: $e");
      rethrow;
    }
  }

  /// Deletes an existing voucher in Firestore using flat structure.
  Future<void> deleteVoucherFromFirebase({
    required String uid,
    required String voucherNumber,
  }) async {
    try {
      // Ensure the `voucherNumber` is not null or empty
      if (voucherNumber.isEmpty) {
        throw ArgumentError(
            'The "Voucher number" field must not be null or empty.');
      }

      // Reference to the voucher document in flat structure
      final voucherRef = _firestore
          .collection(AppCollection.vouchersCollection)
          .doc(voucherNumber);

      // Check if the voucher belongs to the current company
      final voucherDoc = await voucherRef.get();
      if (!voucherDoc.exists) {
        throw Exception('Voucher not found');
      }

      final voucherData = voucherDoc.data() as Map<String, dynamic>;
      if (voucherData['uid'] != uid) {
        throw Exception('You do not have permission to delete this voucher');
      }

      // Delete the voucher with a timeout of 10 seconds
      await voucherRef.delete().timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw TimeoutException("The operation timed out. Please try again.");
        },
      );

      log(
        "Voucher $voucherNumber deleted successfully for company $uid.",
      );
    } on SocketException {
      log("No internet connection. Please check your network.");
      rethrow;
    } on TimeoutException catch (e) {
      log("Timeout error: ${e.message}");
      throw Exception('TimeOut you are on offline mode ');
    } on FirebaseException catch (e) {
      if (e.code == 'not-found') {
        log("Voucher not found: ${e.message}");
      } else {
        log("Firebase error: ${e.message}");
      }
      rethrow;
    } on ArgumentError catch (e) {
      log("Argument error: ${e.message}");
      rethrow;
    } catch (e) {
      log("Unexpected error: $e");
      rethrow;
    }
  }

  /// Gets the highest voucher number for a company
  Future<int> getHighestVoucherNumber({required String uid}) async {
    try {
      final querySnapshot = await _firestore
          .collection(AppCollection.vouchersCollection)
          .where('uid', isEqualTo: uid)
          .orderBy('voucherNumber', descending: true)
          .limit(1)
          .get();

      if (querySnapshot.docs.isEmpty) {
        return 0; // Start from 1 for the first voucher
      }

      final highestVoucher = querySnapshot.docs.first.data();
      // Try to parse the voucher number if it's a string with prefix
      final voucherNumber = highestVoucher['voucherNumber'];
      if (voucherNumber is String) {
        // Extract number from string like "V001" -> 1
        final numberPart = voucherNumber.replaceAll(RegExp(r'[^0-9]'), '');
        return int.tryParse(numberPart) ?? 0;
      }
      return (voucherNumber as int?) ?? 0;
    } catch (e) {
      log('Error getting highest voucher number: $e');
      rethrow;
    }
  }
}
