import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/widgets/data_table_cell.dart';
import 'package:logestics/features/finance/loans/presentation/controllers/loan_history_controller.dart';
import 'package:logestics/features/finance/loans/presentation/controllers/loan_export_controller.dart';
import 'package:logestics/features/home/<USER>/theme.dart';

class LoanExportDialog extends StatelessWidget {
  final LoanHistoryController controller;

  const LoanExportDialog({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    final notifier = Provider.of<ColorNotifier>(context, listen: true);
    final exportController = Get.put(LoanExportController(controller));

    return Dialog(
      backgroundColor: notifier.getBgColor,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        width: Get.width * 0.8,
        height: Get.height * 0.8,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            _buildHeader(notifier),
            const SizedBox(height: 24),

            // Date range selection
            _buildDateRangeSection(notifier, exportController),
            const SizedBox(height: 24),

            // Preview section
            Expanded(
              child: _buildPreviewSection(notifier, exportController),
            ),

            // Action buttons
            const SizedBox(height: 24),
            _buildActionButtons(notifier, exportController),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(ColorNotifier notifier) {
    return Row(
      children: [
        Icon(
          Icons.file_download,
          color: notifier.text,
          size: 24,
        ),
        const SizedBox(width: 12),
        Text(
          'Export Loan History',
          style: AppTextStyles.invoiceHeaderStyle.copyWith(
            color: notifier.text,
            fontSize: 20,
          ),
        ),
        const Spacer(),
        IconButton(
          onPressed: () => Get.back(),
          icon: Icon(Icons.close, color: notifier.text),
        ),
      ],
    );
  }

  Widget _buildDateRangeSection(ColorNotifier notifier, LoanExportController exportController) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Date Range',
          style: AppTextStyles.invoiceHeaderStyle.copyWith(
            color: notifier.text,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            // Start Date
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Start Date',
                    style: TextStyle(
                      color: notifier.text,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Obx(() => InkWell(
                        onTap: () => exportController.selectStartDate(),
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                          decoration: BoxDecoration(
                            border: Border.all(color: notifier.text.withOpacity(0.3)),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.calendar_today, size: 16, color: notifier.text.withOpacity(0.6)),
                              const SizedBox(width: 8),
                              Text(
                                DateFormat('dd/MM/yyyy').format(exportController.startDate.value),
                                style: TextStyle(color: notifier.text),
                              ),
                            ],
                          ),
                        ),
                      )),
                ],
              ),
            ),
            const SizedBox(width: 16),
            // End Date
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'End Date',
                    style: TextStyle(
                      color: notifier.text,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Obx(() => InkWell(
                        onTap: () => exportController.selectEndDate(),
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                          decoration: BoxDecoration(
                            border: Border.all(color: notifier.text.withOpacity(0.3)),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.calendar_today, size: 16, color: notifier.text.withOpacity(0.6)),
                              const SizedBox(width: 8),
                              Text(
                                DateFormat('dd/MM/yyyy').format(exportController.endDate.value),
                                style: TextStyle(color: notifier.text),
                              ),
                            ],
                          ),
                        ),
                      )),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPreviewSection(ColorNotifier notifier, LoanExportController exportController) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Preview',
              style: AppTextStyles.invoiceHeaderStyle.copyWith(
                color: notifier.text,
                fontSize: 16,
              ),
            ),
            Obx(() => Text(
                  '${exportController.filteredLoans.length} loans found',
                  style: TextStyle(
                    color: notifier.text.withOpacity(0.7),
                    fontSize: 14,
                  ),
                )),
          ],
        ),
        const SizedBox(height: 12),

        // Preview table
        Expanded(
          child: Obx(() {
            if (exportController.filteredLoans.isEmpty) {
              return Container(
                decoration: BoxDecoration(
                  border: Border.all(color: notifier.text.withOpacity(0.2)),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.history,
                        size: 48,
                        color: Colors.grey.shade400,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No loans found for selected date range',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }

            return Container(
              decoration: BoxDecoration(
                border: Border.all(color: notifier.text.withOpacity(0.2)),
                borderRadius: BorderRadius.circular(8),
              ),
              child: SingleChildScrollView(
                child: Table(
                  border: TableBorder.all(
                    color: notifier.isDark ? Colors.grey.shade700 : Colors.grey.shade300,
                    width: 1,
                  ),
                  columnWidths: const {
                    0: FlexColumnWidth(0.8), // SN.
                    1: FlexColumnWidth(1.5), // Borrower/Lender
                    2: FlexColumnWidth(1.2), // Amount
                    3: FlexColumnWidth(1.0), // Request Date
                    4: FlexColumnWidth(1.0), // Due Date
                    5: FlexColumnWidth(1.0), // Status
                    6: FlexColumnWidth(1.2), // Loan Type
                  },
                  children: [
                    // Table header
                    TableRow(
                      decoration: BoxDecoration(color: notifier.getHoverColor),
                      children: [
                        DataTableCell(text: 'SN.', style: AppTextStyles.invoiceHeaderStyle.copyWith(color: notifier.text)),
                        DataTableCell(text: 'Borrower/Lender', style: AppTextStyles.invoiceHeaderStyle.copyWith(color: notifier.text)),
                        DataTableCell(text: 'Amount', style: AppTextStyles.invoiceHeaderStyle.copyWith(color: notifier.text)),
                        DataTableCell(text: 'Request Date', style: AppTextStyles.invoiceHeaderStyle.copyWith(color: notifier.text)),
                        DataTableCell(text: 'Due Date', style: AppTextStyles.invoiceHeaderStyle.copyWith(color: notifier.text)),
                        DataTableCell(text: 'Status', style: AppTextStyles.invoiceHeaderStyle.copyWith(color: notifier.text)),
                        DataTableCell(text: 'Loan Type', style: AppTextStyles.invoiceHeaderStyle.copyWith(color: notifier.text)),
                      ],
                    ),
                    // Table rows (show first 10 for preview)
                    ...exportController.filteredLoans.take(10).toList().asMap().entries.map((entry) {
                      final index = entry.key;
                      final loan = entry.value;
                      return TableRow(
                        children: [
                          DataTableCell(text: (index + 1).toString()),
                          DataTableCell(text: '${loan.requestedByName} → ${loan.requestedToName}'),
                          DataTableCell(text: 'PKR ${loan.amount.toStringAsFixed(2)}'),
                          DataTableCell(text: DateFormat('dd/MM/yyyy').format(loan.requestDate)),
                          DataTableCell(text: DateFormat('dd/MM/yyyy').format(loan.dueDate)),
                          DataTableCell(text: loan.status.toUpperCase()),
                          DataTableCell(text: _getLoanType(loan)),
                        ],
                      );
                    }),
                    // Show "and X more..." if there are more than 10 loans
                    if (exportController.filteredLoans.length > 10)
                      TableRow(
                        children: [
                          TableCell(
                            child: Container(
                              padding: const EdgeInsets.all(12),
                              child: Center(
                                child: Text(
                                  '... and ${exportController.filteredLoans.length - 10} more loans',
                                  style: TextStyle(
                                    color: notifier.text.withOpacity(0.6),
                                    fontSize: 12,
                                    fontStyle: FontStyle.italic,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          ...List.generate(6, (index) => const TableCell(child: SizedBox.shrink())),
                        ],
                      ),
                  ],
                ),
              ),
            );
          }),
        ),
      ],
    );
  }

  Widget _buildActionButtons(ColorNotifier notifier, LoanExportController exportController) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        // Cancel Button
        OutlinedButton(
          onPressed: () => Get.back(),
          child: const Text('Cancel'),
        ),

        const SizedBox(width: 12),

        // Generate Excel Button
        Obx(() => ElevatedButton.icon(
              onPressed: exportController.isLoading.value || exportController.filteredLoans.isEmpty
                  ? null
                  : () => exportController.generateExcel(),
              icon: exportController.isLoading.value
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.file_download),
              label: Text(exportController.isLoading.value ? 'Generating...' : 'Export Loans'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
            )),
      ],
    );
  }

  String _getLoanType(loan) {
    if (loan.notes?.toLowerCase().contains('personal') ?? false) return 'Personal';
    if (loan.notes?.toLowerCase().contains('business') ?? false) return 'Business';
    if (loan.notes?.toLowerCase().contains('emergency') ?? false) return 'Emergency';
    if (loan.notes?.toLowerCase().contains('investment') ?? false) return 'Investment';
    if (loan.amount <= 100000) return 'Personal';
    return 'Business';
  }
}
