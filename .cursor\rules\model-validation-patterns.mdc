---
description:
globs:
alwaysApply: false
---
# Model Validation and Data Patterns

## Model Structure Requirements

### Base Model Pattern
All models should follow this structure based on the codebase patterns:

```dart
class ExampleModel {
  final String id;
  final String name;
  final DateTime createdAt;
  final String uid; // For user-scoped data

  const ExampleModel({
    required this.id,
    required this.name, 
    required this.createdAt,
    required this.uid,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'createdAt': createdAt.millisecondsSinceEpoch,
    'uid': uid,
  };

  factory ExampleModel.fromJson(Map<String, dynamic> json) => ExampleModel(
    id: json['id'] ?? '',
    name: json['name'] ?? '',
    createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt'] ?? 0),
    uid: json['uid'] ?? '',
  );
}
```

## Financial Model Patterns

### Account Model Requirements
Reference: [account_model.dart](mdc:lib/models/finance/account_model.dart)
- Must have `availableBalance` and `initialBalance`
- Include bank details: `accountNumber`, `branchCode`, `branchAddress`
- Always require `createdAt` timestamp

### Transaction Models
- **DepositModel**: For money coming in
  - Requires: `accountId`, `amount`, `payerId`, `categoryId`
  - Optional: `referenceNumber`, `notes`
  
- **ExpenseModel**: For money going out
  - Requires: `accountId`, `amount`, `payeeId`, `categoryId` 
  - Optional: `referenceNumber`, `notes`

- **AccountTransactionModel**: For account history
  - Requires: `accountId`, `amount`, `transactionDate`, `type`
  - Include reference fields for traceability

## Validation Patterns

### Form Validation in Controllers
```dart
String? validateAmount(String? value) {
  if (value == null || value.isEmpty) {
    return 'Amount is required';
  }
  final amount = double.tryParse(value);
  if (amount == null || amount <= 0) {
    return 'Amount must be a positive number';
  }
  return null;
}

String? validateAccountSelection(AccountModel? account) {
  if (account == null || account.id.isEmpty) {
    return 'Please select an account';
  }
  return null;
}
```

### Business Logic Validation
- Validate broker fees only when `brokerType == 'Outsource'`
- Ensure selected accounts exist before creating transactions
- Check account balance sufficiency for expenses
- Validate voucher number uniqueness

## Data Consistency Rules

### Timestamp Handling
- Always use `createdAt` field name (not `dateTime`)
- Store as `millisecondsSinceEpoch` in Firebase
- Convert to `DateTime` objects in models

### User Scoping
- All user data must include `uid` field
- Filter all queries by current user's UID
- Validate user permissions before operations

### Reference Integrity
- Use proper reference numbers for traceability (e.g., `V-${voucherNumber}`)
- Maintain consistent naming for categories
- Link related transactions with proper IDs

## State Management Validation

### Controller State Consistency
- Always validate form state before processing
- Clear error states after successful operations
- Update loading states appropriately
- Refresh related data after changes

### Observable Data Updates
```dart
// Correct pattern for updating observable lists
items.value = newItemsList; // Creates new reference
// NOT: items.addAll(newItems); // May not trigger reactivity
```

## Error Prevention Patterns

### Null Safety
- Use null-aware operators consistently
- Provide sensible defaults in `fromJson()` methods
- Validate required fields before operations

### Transaction Safety
- Only create financial transactions after successful voucher creation
- Use try-catch blocks for all async operations
- Implement rollback strategies for failed multi-step operations

### Firebase Query Safety
- Always include user UID in queries
- Handle empty result sets gracefully
- Use proper error handling for network issues

