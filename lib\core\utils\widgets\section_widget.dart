import 'package:flutter/material.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/widgets/form_field_widget.dart';
import 'package:logestics/main.dart';

class SectionWidget extends StatelessWidget {
  final String title;
  final List<Widget> fields;
  final bool isSmallScreen;
  final bool isMediumScreen;

  const SectionWidget({
    super.key,
    required this.title,
    required this.fields,
    required this.isSmallScreen,
    required this.isMediumScreen,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(isSmallScreen ? 16 : 24),
      decoration: BoxDecoration(
        color: notifier.getBgColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: notifier.text.withAlpha(10),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: notifier.text.withAlpha(10),
            blurRadius: 10,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: notifier.text.withAlpha(10),
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(vertical: 8),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: notifier.text.withAlpha(10),
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: notifier.text.withAlpha(10),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getSectionIcon(title),
                    size: isSmallScreen ? 20 : 24,
                    color: notifier.text,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: AppTextStyles.subtitleStyle.copyWith(
                    color: notifier.text,
                    fontWeight: FontWeight.w600,
                    fontSize: isSmallScreen ? 16 : 18,
                    letterSpacing: 0.3,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          Wrap(
            spacing: 16,
            runSpacing: 16,
            children: fields
                .map((field) => FormFieldWidget(
                      constraints: BoxConstraints(
                          maxWidth: MediaQuery.of(context).size.width),
                      isSmallScreen: isSmallScreen,
                      isMediumScreen: isMediumScreen,
                      field: field,
                    ))
                .toList(),
          ),
        ],
      ),
    );
  }

  IconData _getSectionIcon(String title) {
    switch (title) {
      case "Invoice Information":
        return Icons.receipt_long;
      case "Product Details":
        return Icons.inventory;
      case "Customer & Transport Details":
        return Icons.local_shipping;
      case "Consignor & Delivery Details":
        return Icons.person;
      case "Location & Distance Management":
        return Icons.location_on;
      default:
        return Icons.folder;
    }
  }
}
