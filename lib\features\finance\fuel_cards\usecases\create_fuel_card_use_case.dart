import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/models/finance/fuel_card_model.dart';
import 'package:logestics/features/finance/fuel_cards/repositories/fuel_card_repository.dart';

class CreateFuelCardUseCase {
  final FuelCardRepository repository;

  CreateFuelCardUseCase(this.repository);

  Future<Either<FailureObj, SuccessObj>> call(FuelCardModel fuelCard) async {
    try {
      return await repository.createFuelCard(fuelCard);
    } catch (e) {
      return Left(
        FailureObj(
          code: 'unexpected-error',
          message: 'An unexpected error occurred: $e',
        ),
      );
    }
  }
}
