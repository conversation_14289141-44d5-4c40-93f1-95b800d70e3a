import 'package:either_dart/either.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/firebase_service/finance/loan_firebase_service.dart';
import 'package:logestics/models/finance/loan_model.dart';
import 'package:logestics/core/services/loan_accounting_hook_service.dart';

abstract class LoanRepository {
  // Request a loan
  Future<Either<FailureObj, SuccessObj>> requestLoan(LoanModel loan);

  // Get all loan requests made to current user
  Future<Either<FailureObj, List<LoanModel>>> getIncomingLoanRequests();

  // Get all loan requests made by current user
  Future<Either<FailureObj, List<LoanModel>>> getOutgoingLoanRequests();

  // Approve a loan request
  Future<Either<FailureObj, SuccessObj>> approveLoanRequest(
      String loanId, String fromAccountId);

  // Reject a loan request
  Future<Either<FailureObj, SuccessObj>> rejectLoanRequest(
      String loanId, String reason);

  // Repay a loan
  Future<Either<FailureObj, SuccessObj>> repayLoan(
      String loanId, String fromAccountId);

  // Get all active loans (approved but not repaid)
  Future<Either<FailureObj, List<LoanModel>>> getActiveLoans();

  // Get loan history (all loans)
  Future<Either<FailureObj, List<LoanModel>>> getLoanHistory();

  // Real-time streams
  Stream<List<LoanModel>> listenToIncomingLoanRequests();
  Stream<List<LoanModel>> listenToOutgoingLoanRequests();
  Stream<List<LoanModel>> listenToActiveLoans();
  Stream<List<LoanModel>> listenToLoanHistory();
}

class LoanRepositoryImpl implements LoanRepository {
  final LoanFirebaseService _loanFirebaseService;
  final LoanAccountingHookService _hookService;

  LoanRepositoryImpl(this._loanFirebaseService)
      : _hookService = LoanAccountingHookService();

  @override
  Future<Either<FailureObj, SuccessObj>> requestLoan(LoanModel loan) {
    return _loanFirebaseService.requestLoan(loan);
  }

  @override
  Future<Either<FailureObj, List<LoanModel>>> getIncomingLoanRequests() {
    return _loanFirebaseService.getIncomingLoanRequests();
  }

  @override
  Future<Either<FailureObj, List<LoanModel>>> getOutgoingLoanRequests() {
    return _loanFirebaseService.getOutgoingLoanRequests();
  }

  @override
  Future<Either<FailureObj, SuccessObj>> approveLoanRequest(
      String loanId, String fromAccountId) async {
    final result =
        await _loanFirebaseService.approveLoanRequest(loanId, fromAccountId);

    // If approval was successful, trigger accounting hook
    result.fold(
      (failure) {
        // Do nothing on failure
      },
      (success) async {
        // Get the updated loan and trigger accounting hook
        final loan = await _loanFirebaseService.getLoanById(loanId);
        if (loan != null) {
          await _hookService.onLoanApproved(loan);
        }
      },
    );

    return result;
  }

  @override
  Future<Either<FailureObj, SuccessObj>> rejectLoanRequest(
      String loanId, String reason) async {
    // Get the loan before rejection to check if it had journal entries
    final loan = await _loanFirebaseService.getLoanById(loanId);

    final result = await _loanFirebaseService.rejectLoanRequest(loanId, reason);

    // If rejection was successful and loan existed, trigger cancellation hook
    result.fold(
      (failure) {
        // Do nothing on failure
      },
      (success) async {
        if (loan != null) {
          await _hookService.onLoanCancelled(loan);
        }
      },
    );

    return result;
  }

  @override
  Future<Either<FailureObj, SuccessObj>> repayLoan(
      String loanId, String fromAccountId) async {
    final result = await _loanFirebaseService.repayLoan(loanId, fromAccountId);

    // If repayment was successful, trigger accounting hook
    result.fold(
      (failure) {
        // Do nothing on failure
      },
      (success) async {
        // Get the updated loan and trigger accounting hook
        final loan = await _loanFirebaseService.getLoanById(loanId);
        if (loan != null) {
          await _hookService.onLoanRepaid(loan);
        }
      },
    );

    return result;
  }

  @override
  Future<Either<FailureObj, List<LoanModel>>> getActiveLoans() {
    return _loanFirebaseService.getActiveLoans();
  }

  @override
  Future<Either<FailureObj, List<LoanModel>>> getLoanHistory() {
    return _loanFirebaseService.getLoanHistory();
  }

  @override
  Stream<List<LoanModel>> listenToIncomingLoanRequests() {
    return _loanFirebaseService.listenToIncomingLoanRequests();
  }

  @override
  Stream<List<LoanModel>> listenToOutgoingLoanRequests() {
    return _loanFirebaseService.listenToOutgoingLoanRequests();
  }

  @override
  Stream<List<LoanModel>> listenToActiveLoans() {
    return _loanFirebaseService.listenToActiveLoans();
  }

  @override
  Stream<List<LoanModel>> listenToLoanHistory() {
    return _loanFirebaseService.listenToLoanHistory();
  }
}
