import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/trial_balance_controller.dart';

/// Form widget for Trial Balance generation parameters
class TrialBalanceFormWidget extends StatelessWidget {
  const TrialBalanceFormWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<TrialBalanceController>();

    return Form(
      key: controller.formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Company name field
          TextFormField(
            controller: controller.companyNameController,
            decoration: const InputDecoration(
              labelText: 'Company Name *',
              hintText: 'Enter company name',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.business),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Company name is required';
              }
              return null;
            },
          ),
          
          const SizedBox(height: 16),
          
          // Date range section
          Row(
            children: [
              // Start date
              Expanded(
                child: TextF<PERSON><PERSON><PERSON>(
                  controller: controller.startDateController,
                  decoration: const InputDecoration(
                    labelText: 'Start Date *',
                    hintText: 'Select start date',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => controller.selectDateRange(context),
                  validator: (value) {
                    if (controller.startDate.value == null) {
                      return 'Start date is required';
                    }
                    return null;
                  },
                ),
              ),
              
              const SizedBox(width: 16),
              
              // End date
              Expanded(
                child: TextFormField(
                  controller: controller.endDateController,
                  decoration: const InputDecoration(
                    labelText: 'End Date *',
                    hintText: 'Select end date',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () => controller.selectDateRange(context),
                  validator: (value) {
                    if (controller.endDate.value == null) {
                      return 'End date is required';
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Filter options
          Card(
            elevation: 1,
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Filter Options',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  
                  // Include inactive accounts
                  Obx(() => CheckboxListTile(
                    title: const Text('Include Inactive Accounts'),
                    subtitle: const Text('Include accounts that are marked as inactive'),
                    value: controller.includeInactiveAccounts.value,
                    onChanged: (value) {
                      controller.includeInactiveAccounts.value = value ?? false;
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                    contentPadding: EdgeInsets.zero,
                  )),
                  
                  // Include zero balances
                  Obx(() => CheckboxListTile(
                    title: const Text('Include Zero Balances'),
                    subtitle: const Text('Include accounts with zero balance'),
                    value: controller.includeZeroBalances.value,
                    onChanged: (value) {
                      controller.includeZeroBalances.value = value ?? false;
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                    contentPadding: EdgeInsets.zero,
                  )),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Generate button
          SizedBox(
            width: double.infinity,
            child: Obx(() => ElevatedButton.icon(
              onPressed: controller.isGenerating.value 
                ? null 
                : controller.generateTrialBalance,
              icon: controller.isGenerating.value
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.assessment),
              label: Text(
                controller.isGenerating.value 
                  ? 'Generating...' 
                  : 'Generate Trial Balance',
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            )),
          ),
          
          const SizedBox(height: 12),
          
          // Quick date range buttons
          Wrap(
            spacing: 8,
            children: [
              _buildQuickDateButton(
                context,
                'This Month',
                () => _setThisMonth(controller),
              ),
              _buildQuickDateButton(
                context,
                'Last Month',
                () => _setLastMonth(controller),
              ),
              _buildQuickDateButton(
                context,
                'This Quarter',
                () => _setThisQuarter(controller),
              ),
              _buildQuickDateButton(
                context,
                'This Year',
                () => _setThisYear(controller),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build quick date range button
  Widget _buildQuickDateButton(
    BuildContext context,
    String label,
    VoidCallback onPressed,
  ) {
    return OutlinedButton(
      onPressed: onPressed,
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(6),
        ),
      ),
      child: Text(
        label,
        style: const TextStyle(fontSize: 12),
      ),
    );
  }

  /// Set date range to this month
  void _setThisMonth(TrialBalanceController controller) {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);
    
    controller.startDate.value = startOfMonth;
    controller.endDate.value = endOfMonth;
    controller.startDateController.text = controller._formatDate(startOfMonth);
    controller.endDateController.text = controller._formatDate(endOfMonth);
  }

  /// Set date range to last month
  void _setLastMonth(TrialBalanceController controller) {
    final now = DateTime.now();
    final startOfLastMonth = DateTime(now.year, now.month - 1, 1);
    final endOfLastMonth = DateTime(now.year, now.month, 0);
    
    controller.startDate.value = startOfLastMonth;
    controller.endDate.value = endOfLastMonth;
    controller.startDateController.text = controller._formatDate(startOfLastMonth);
    controller.endDateController.text = controller._formatDate(endOfLastMonth);
  }

  /// Set date range to this quarter
  void _setThisQuarter(TrialBalanceController controller) {
    final now = DateTime.now();
    final quarter = ((now.month - 1) ~/ 3) + 1;
    final startOfQuarter = DateTime(now.year, (quarter - 1) * 3 + 1, 1);
    final endOfQuarter = DateTime(now.year, quarter * 3 + 1, 0);
    
    controller.startDate.value = startOfQuarter;
    controller.endDate.value = endOfQuarter;
    controller.startDateController.text = controller._formatDate(startOfQuarter);
    controller.endDateController.text = controller._formatDate(endOfQuarter);
  }

  /// Set date range to this year
  void _setThisYear(TrialBalanceController controller) {
    final now = DateTime.now();
    final startOfYear = DateTime(now.year, 1, 1);
    final endOfYear = DateTime(now.year, 12, 31);
    
    controller.startDate.value = startOfYear;
    controller.endDate.value = endOfYear;
    controller.startDateController.text = controller._formatDate(startOfYear);
    controller.endDateController.text = controller._formatDate(endOfYear);
  }
}

/// Extension to access private method
extension TrialBalanceControllerExtension on TrialBalanceController {
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}
