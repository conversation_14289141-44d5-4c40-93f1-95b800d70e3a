class DepositModel {
  final String id;
  final String accountId;
  final String accountName;
  final double amount;

  final String categoryId;
  final String categoryName;
  final String payerId;
  final String payerName;
  final String referenceNumber;
  final String notes;
  final DateTime createdAt;
  final String uid; // User ID who owns this deposit

  DepositModel({
    required this.id,
    required this.accountId,
    required this.accountName,
    required this.amount,
    required this.categoryId,
    required this.categoryName,
    required this.payerId,
    required this.payerName,
    required this.referenceNumber,
    this.notes = '',
    required this.createdAt,
    this.uid = '', // Default empty string for backward compatibility
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'accountId': accountId,
      'accountName': accountName,
      'amount': amount,

      'categoryId': categoryId,
      'categoryName': categoryName,
      'payerId': payerId,
      'payerName': payerName,
      'referenceNumber': referenceNumber,
      'notes': notes,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'uid': uid, // Include UID in JSON
    };
  }

  factory DepositModel.fromJson(Map<String, dynamic> json) {
    return DepositModel(
      id: json['id'] ?? '',
      accountId: json['accountId'] ?? '',
      accountName: json['accountName'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt'] ?? 0),
      categoryId: json['categoryId'] ?? '',
      categoryName: json['categoryName'] ?? '',
      payerId: json['payerId'] ?? '',
      payerName: json['payerName'] ?? '',
      referenceNumber: json['referenceNumber'] ?? '',
      notes: json['notes'] ?? '',
      uid: json['uid'] ?? '', // Extract UID from JSON
    );
  }

  // Create a copy of this model with updated fields
  DepositModel copyWith({
    String? id,
    String? accountId,
    String? accountName,
    double? amount,
    String? categoryId,
    String? categoryName,
    String? payerId,
    String? payerName,
    String? referenceNumber,
    String? notes,
    DateTime? createdAt,
    String? uid,
  }) {
    return DepositModel(
      id: id ?? this.id,
      accountId: accountId ?? this.accountId,
      accountName: accountName ?? this.accountName,
      amount: amount ?? this.amount,
      categoryId: categoryId ?? this.categoryId,
      categoryName: categoryName ?? this.categoryName,
      payerId: payerId ?? this.payerId,
      payerName: payerName ?? this.payerName,
      referenceNumber: referenceNumber ?? this.referenceNumber,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      uid: uid ?? this.uid,
    );
  }
}
