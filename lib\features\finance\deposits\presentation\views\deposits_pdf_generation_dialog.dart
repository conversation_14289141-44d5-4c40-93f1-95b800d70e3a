import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/features/finance/deposits/presentation/controllers/deposits_pdf_generation_controller.dart';
import 'package:logestics/features/finance/deposits/repository/deposit_repository.dart';
import 'package:logestics/features/finance/accounts/repositories/account_repository.dart';
import 'package:logestics/features/finance/payers/repository/payer_repository.dart';
import 'package:logestics/features/finance/deposit_categories/repositories/deposit_category_repository.dart';
import 'package:logestics/firebase_service/finance/deposit_firebase_service.dart';
import 'package:logestics/firebase_service/finance/account_firebase_service.dart';
import 'package:logestics/firebase_service/finance/payer_firebase_service.dart';
import 'package:logestics/firebase_service/finance/deposit_category_firebase_service.dart';
import 'package:logestics/services/pdf_generation_service.dart';
import 'package:logestics/firebase_service/firebase_auth_service.dart';
import 'package:logestics/firebase_service/finance/company_firebase_service.dart';

import 'package:logestics/features/home/<USER>/theme.dart';
import 'package:provider/provider.dart';

class DepositsPDFGenerationDialog extends StatefulWidget {
  final DateTime? initialStartDate;
  final DateTime? initialEndDate;

  const DepositsPDFGenerationDialog({
    super.key,
    this.initialStartDate,
    this.initialEndDate,
  });

  @override
  State<DepositsPDFGenerationDialog> createState() =>
      _DepositsPDFGenerationDialogState();
}

class _DepositsPDFGenerationDialogState
    extends State<DepositsPDFGenerationDialog> {
  late DepositsPDFGenerationController controller;

  @override
  void initState() {
    super.initState();
    _initializeController();
  }

  void _initializeController() {
    // Initialize Firebase services
    final depositFirebaseService = DepositFirebaseService();
    final accountFirebaseService = AccountFirebaseService();
    final payerFirebaseService = PayerFirebaseService();
    final categoryFirebaseService = DepositCategoryFirebaseService();

    // Initialize repositories
    final depositRepository = DepositRepositoryImpl(depositFirebaseService);
    final accountRepository = AccountRepositoryImpl(accountFirebaseService);
    final payerRepository = PayerRepositoryImpl(payerFirebaseService);
    final categoryRepository =
        DepositCategoryRepositoryImpl(categoryFirebaseService);

    // Initialize controller with dependency injection
    controller = Get.put(
      DepositsPDFGenerationController(
        depositRepository: depositRepository,
        accountRepository: accountRepository,
        payerRepository: payerRepository,
        categoryRepository: categoryRepository,
        pdfService: Get.find<PDFGenerationService>(),
        authService: Get.find<FirebaseAuthService>(),
        companyService: Get.find<CompanyFirebaseService>(),
      ),
      tag: 'deposits_pdf_${DateTime.now().millisecondsSinceEpoch}',
    );

    // Set initial date range if provided
    if (widget.initialStartDate != null && widget.initialEndDate != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        controller.setDateRange(
            widget.initialStartDate!, widget.initialEndDate!);
      });
    }
  }

  @override
  void dispose() {
    Get.delete<DepositsPDFGenerationController>(
        tag: 'deposits_pdf_${DateTime.now().millisecondsSinceEpoch}');
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final notifier = Provider.of<ColorNotifier>(context);

    return Dialog(
      backgroundColor: notifier.getcardColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: 900,
          maxHeight: MediaQuery.of(context).size.height * 0.9,
        ),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.8,
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Generate Deposits Report PDF',
                    style: AppTextStyles.titleStyle.copyWith(
                      color: notifier.text,
                      fontSize: 20,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Get.back(),
                    icon: Icon(Icons.close, color: notifier.text),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Content
              Flexible(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Information message
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.blue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                              color: Colors.blue.withValues(alpha: 0.3)),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.info_outline,
                                color: Colors.blue, size: 20),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                'Date range is required for PDF generation. All other filters are optional and will be applied if selected.',
                                style: TextStyle(
                                  color: Colors.blue.shade700,
                                  fontSize: 13,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 16),

                      // User Status Section
                      _buildUserStatusSection(notifier),

                      const SizedBox(height: 24),

                      // Filter Section
                      _buildFilterSection(notifier),

                      const SizedBox(height: 24),

                      // Date Range Section
                      _buildDateRangeSection(notifier),

                      const SizedBox(height: 24),

                      // Amount Range Section
                      _buildAmountRangeSection(notifier),

                      const SizedBox(height: 24),

                      // Customization Section
                      _buildCustomizationSection(notifier),

                      const SizedBox(height: 24),

                      // Preview Section
                      _buildPreviewSection(notifier),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Action Buttons
              _buildActionButtons(notifier),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFilterSection(ColorNotifier notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Filters',
              style: AppTextStyles.titleStyle.copyWith(
                color: notifier.text,
                fontSize: 16,
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
              ),
              child: Text(
                'Optional',
                style: TextStyle(
                  color: Colors.blue,
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Account Filter
        _buildAccountFilter(notifier),
        const SizedBox(height: 16),

        // Payer Filter
        _buildPayerFilter(notifier),
        const SizedBox(height: 16),

        // Category Filter
        _buildCategoryFilter(notifier),
      ],
    );
  }

  Widget _buildAccountFilter(ColorNotifier notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Accounts',
          style: TextStyle(
            color: notifier.text,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Obx(() {
          if (controller.isLoadingData.value) {
            return const CircularProgressIndicator();
          }

          return Wrap(
            spacing: 8,
            runSpacing: 8,
            children: controller.accounts.map((account) {
              final isSelected = controller.selectedAccounts.contains(account);
              return FilterChip(
                label: Text(account.name),
                selected: isSelected,
                onSelected: (selected) {
                  controller.toggleAccountSelection(account);
                },
                selectedColor: Colors.blue.withValues(alpha: 0.3),
                checkmarkColor: Colors.blue,
              );
            }).toList(),
          );
        }),
      ],
    );
  }

  Widget _buildPayerFilter(ColorNotifier notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Payers',
          style: TextStyle(
            color: notifier.text,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Obx(() {
          if (controller.isLoadingData.value) {
            return const CircularProgressIndicator();
          }

          return Wrap(
            spacing: 8,
            runSpacing: 8,
            children: controller.payers.map((payer) {
              final isSelected = controller.selectedPayers.contains(payer);
              return FilterChip(
                label: Text(payer.name),
                selected: isSelected,
                onSelected: (selected) {
                  controller.togglePayerSelection(payer);
                },
                selectedColor: Colors.green.withValues(alpha: 0.3),
                checkmarkColor: Colors.green,
              );
            }).toList(),
          );
        }),
      ],
    );
  }

  Widget _buildCategoryFilter(ColorNotifier notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Categories',
          style: TextStyle(
            color: notifier.text,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Obx(() {
          if (controller.isLoadingData.value) {
            return const CircularProgressIndicator();
          }

          return Wrap(
            spacing: 8,
            runSpacing: 8,
            children: controller.categories.map((category) {
              final isSelected =
                  controller.selectedCategories.contains(category);
              return FilterChip(
                label: Text(category.name),
                selected: isSelected,
                onSelected: (selected) {
                  controller.toggleCategorySelection(category);
                },
                selectedColor: Colors.orange.withValues(alpha: 0.3),
                checkmarkColor: Colors.orange,
              );
            }).toList(),
          );
        }),
      ],
    );
  }

  Widget _buildDateRangeSection(ColorNotifier notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Date Range',
              style: AppTextStyles.titleStyle.copyWith(
                color: notifier.text,
                fontSize: 16,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              '*',
              style: TextStyle(
                color: Colors.red,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
              ),
              child: Text(
                'Required',
                style: TextStyle(
                  color: Colors.red,
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            // Start Date
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Start Date',
                    style: TextStyle(
                      color: notifier.text,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Obx(() => InkWell(
                        onTap: () => _selectStartDate(context),
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            border: Border.all(
                                color: notifier.text.withValues(alpha: 0.3)),
                            borderRadius: BorderRadius.circular(8),
                            color: notifier.getBgColor,
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.calendar_today,
                                  color: notifier.text.withValues(alpha: 0.6),
                                  size: 16),
                              const SizedBox(width: 8),
                              Text(
                                controller.startDate.value != null
                                    ? DateFormat('dd/MM/yyyy')
                                        .format(controller.startDate.value!)
                                    : 'Select Date',
                                style: TextStyle(color: notifier.text),
                              ),
                            ],
                          ),
                        ),
                      )),
                ],
              ),
            ),

            const SizedBox(width: 16),

            // End Date
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'End Date',
                    style: TextStyle(
                      color: notifier.text,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Obx(() => InkWell(
                        onTap: () => _selectEndDate(context),
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            border: Border.all(
                                color: notifier.text.withValues(alpha: 0.3)),
                            borderRadius: BorderRadius.circular(8),
                            color: notifier.getBgColor,
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.calendar_today,
                                  color: notifier.text.withValues(alpha: 0.6),
                                  size: 16),
                              const SizedBox(width: 8),
                              Text(
                                controller.endDate.value != null
                                    ? DateFormat('dd/MM/yyyy')
                                        .format(controller.endDate.value!)
                                    : 'Select Date',
                                style: TextStyle(color: notifier.text),
                              ),
                            ],
                          ),
                        ),
                      )),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Obx(() => !controller.isDateRangeValid &&
                (controller.startDate.value != null ||
                    controller.endDate.value != null)
            ? Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.warning, color: Colors.red, size: 16),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Please select both start and end dates. Start date must be before or equal to end date.',
                        style: TextStyle(
                          color: Colors.red,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              )
            : const SizedBox.shrink()),
      ],
    );
  }

  Widget _buildAmountRangeSection(ColorNotifier notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Amount Range',
              style: AppTextStyles.titleStyle.copyWith(
                color: notifier.text,
                fontSize: 16,
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
              ),
              child: Text(
                'Optional',
                style: TextStyle(
                  color: Colors.blue,
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            // Min Amount
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Minimum Amount',
                    style: TextStyle(
                      color: notifier.text,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: controller.minAmountController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      hintText: 'Enter minimum amount',
                      hintStyle: TextStyle(
                          color: notifier.text.withValues(alpha: 0.5)),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                            color: notifier.text.withValues(alpha: 0.3)),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                            color: notifier.text.withValues(alpha: 0.3)),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(color: Color(0xFF0f7bf4)),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 12),
                    ),
                    style: TextStyle(color: notifier.text),
                  ),
                ],
              ),
            ),

            const SizedBox(width: 16),

            // Max Amount
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Maximum Amount',
                    style: TextStyle(
                      color: notifier.text,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: controller.maxAmountController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      hintText: 'Enter maximum amount',
                      hintStyle: TextStyle(
                          color: notifier.text.withValues(alpha: 0.5)),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                            color: notifier.text.withValues(alpha: 0.3)),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                            color: notifier.text.withValues(alpha: 0.3)),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(color: Color(0xFF0f7bf4)),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 12),
                    ),
                    style: TextStyle(color: notifier.text),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCustomizationSection(ColorNotifier notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Customization',
          style: AppTextStyles.titleStyle.copyWith(
            color: notifier.text,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 16),

        // Company Name
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Company Name (Optional)',
              style: TextStyle(
                color: notifier.text,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: controller.companyNameController,
              decoration: InputDecoration(
                hintText: 'Enter custom company name',
                hintStyle:
                    TextStyle(color: notifier.text.withValues(alpha: 0.5)),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide:
                      BorderSide(color: notifier.text.withValues(alpha: 0.3)),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide:
                      BorderSide(color: notifier.text.withValues(alpha: 0.3)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFF0f7bf4)),
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
              ),
              style: TextStyle(color: notifier.text),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Company Address
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Company Address (Optional)',
              style: TextStyle(
                color: notifier.text,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: controller.companyAddressController,
              maxLines: 2,
              decoration: InputDecoration(
                hintText: 'Enter company address',
                hintStyle:
                    TextStyle(color: notifier.text.withValues(alpha: 0.5)),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide:
                      BorderSide(color: notifier.text.withValues(alpha: 0.3)),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide:
                      BorderSide(color: notifier.text.withValues(alpha: 0.3)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFF0f7bf4)),
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
              ),
              style: TextStyle(color: notifier.text),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPreviewSection(ColorNotifier notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Preview',
          style: AppTextStyles.titleStyle.copyWith(
            color: notifier.text,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 16),
        Obx(() {
          if (controller.isLoadingData.value) {
            return const Center(child: CircularProgressIndicator());
          }

          return Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: notifier.getBgColor,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: notifier.text.withValues(alpha: 0.2)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Filtered Results',
                      style: TextStyle(
                        color: notifier.text,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    TextButton(
                      onPressed: controller.clearAllFilters,
                      child: const Text('Clear All Filters'),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  'Total Deposits: ${controller.filteredDeposits.length}',
                  style: TextStyle(color: notifier.text),
                ),
                Text(
                  'Total Amount: PKR ${NumberFormat('#,##0.00').format(controller.filteredDeposits.fold(0.0, (sum, d) => sum + d.amount))}',
                  style: TextStyle(
                    color: notifier.text,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (controller.filteredDeposits.isEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Text(
                      'No deposits match the current filters',
                      style: TextStyle(
                        color: Colors.orange,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
              ],
            ),
          );
        }),
      ],
    );
  }

  Widget _buildActionButtons(ColorNotifier notifier) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        TextButton(
          onPressed: () => Get.back(),
          child: Text(
            'Cancel',
            style: TextStyle(color: notifier.text.withValues(alpha: 0.7)),
          ),
        ),
        const SizedBox(width: 12),
        Obx(() => ElevatedButton.icon(
              onPressed:
                  !controller.canGeneratePDF ? null : controller.sharePDF,
              icon: controller.isLoading.value
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.share),
              label: Text(
                  controller.isLoading.value ? 'Generating...' : 'Share PDF'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              ),
            )),
        const SizedBox(width: 12),
        Obx(() => ElevatedButton.icon(
              onPressed:
                  !controller.canGeneratePDF ? null : controller.generatePDF,
              icon: controller.isLoading.value
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.picture_as_pdf),
              label: Text(controller.isLoading.value
                  ? 'Generating...'
                  : 'Generate PDF'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF0f7bf4),
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              ),
            )),
      ],
    );
  }

  Widget _buildUserStatusSection(ColorNotifier notifier) {
    return Obx(() {
      if (controller.isLoadingUser.value) {
        return Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.orange.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
          ),
          child: Row(
            children: [
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Loading user information...',
                  style: TextStyle(
                    color: Colors.orange.shade700,
                    fontSize: 13,
                  ),
                ),
              ),
            ],
          ),
        );
      }

      if (controller.userLoadError.value != null) {
        return Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.red.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.error_outline, color: Colors.red, size: 20),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'User Information Error',
                      style: TextStyle(
                        color: Colors.red.shade700,
                        fontSize: 13,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                controller.userLoadError.value!,
                style: TextStyle(
                  color: Colors.red.shade600,
                  fontSize: 12,
                ),
              ),
              const SizedBox(height: 8),
              ElevatedButton.icon(
                onPressed: controller.retryLoadUser,
                icon: Icon(Icons.refresh, size: 16),
                label: Text('Retry'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  minimumSize: Size(0, 32),
                ),
              ),
            ],
          ),
        );
      }

      if (controller.currentUser.value != null) {
        return Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.green.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
          ),
          child: Row(
            children: [
              Icon(Icons.check_circle_outline, color: Colors.green, size: 20),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'User: ${controller.currentUser.value!.companyName}',
                  style: TextStyle(
                    color: Colors.green.shade700,
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );
      }

      return const SizedBox.shrink();
    });
  }

  Future<void> _selectStartDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: controller.startDate.value ??
          DateTime.now().subtract(const Duration(days: 30)),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      controller.startDate.value = picked;
      controller.applyFilters();
    }
  }

  Future<void> _selectEndDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: controller.endDate.value ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      controller.endDate.value = picked;
      controller.applyFilters();
    }
  }
}
