import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/assets/app_assets.dart';
import 'package:provider/provider.dart';

import '../Controllers/drawer_controllers.dart';
import '../theme/theme.dart';
import 'drawer_item.dart';
import 'drawer_item_expentiontile.dart';

class MyDrawer extends StatelessWidget {
  const MyDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    final colorNotifier = Provider.of<ColorNotifier>(context, listen: true);

    return LayoutBuilder(
      builder: (context, constraints) {
        return GetBuilder<MainDrawerController>(
          builder: (controller) {
            return Drawer(
              width: Get.width / 6,
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.zero,
              ),
              backgroundColor: colorNotifier.getBgColor,
              child: SafeArea(
                child: SingleChildScrollView(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 10,
                      vertical: 20,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildLogoHeader(controller, colorNotifier),
                        const SizedBox(height: 30),
                        ..._buildDrawerSections(controller, colorNotifier),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildLogoHeader(
      MainDrawerController controller, ColorNotifier colorNotifier) {
    return InkWell(
      hoverColor: Colors.transparent,
      onTap: () {
        if (Get.width < 1200) {
          // controller.updateSelectedScreen(
          //     controller.drawerSections[0].items[0].screen!);
          WidgetsBinding.instance.addPostFrameCallback((_) {
            controller.scrollController.jumpTo(0.0);
          });
          Navigator.pop(Get.context!);
        } else {
          // controller.updateSelectedScreen(
          //     controller.drawerSections[0].items[0].screen!);
          WidgetsBinding.instance.addPostFrameCallback((_) {
            controller.scrollController.jumpTo(0.0);
          });
        }
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SvgPicture.asset(
            AppAssets.groupLogo,
            height: 35,
          ),
          const SizedBox(width: 10),
          Text(
            "logistics",
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontSize: Get.height / 30,
              color: colorNotifier.text,
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
        ],
      ),
    );
  }

  List<Widget> _buildDrawerSections(
      MainDrawerController controller, ColorNotifier colorNotifier) {
    return controller.drawerSections.map((section) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            section.title,
            overflow: TextOverflow.ellipsis,
            style: const TextStyle(
              color: Colors.grey,
              fontSize: 15,
            ),
          ),
          ...section.items.map((item) {
            if (item.children != null) {
              return DrawerItemExpentiontile(
                title: item.title,
                images: [item.icon],
                text: item.children!.map((e) => e.title).toList(),
                sectionPath:
                    '${section.title.toLowerCase().replaceAll(' ', '_')}/${item.title.toLowerCase().replaceAll(' ', '_')}',
                onItemTap: (index) {
                  if (item.children![index].navigateTo != null) {
                    controller.updateSelectedScreen(
                      null,
                      '${section.title.toLowerCase().replaceAll(' ', '_')}/${item.title.toLowerCase().replaceAll(' ', '_')}/${item.children![index].title.toLowerCase().replaceAll(' ', '_')}',
                      navigateTo: item.children![index].navigateTo,
                    );
                    if (Get.width < 1200) {
                      Navigator.pop(Get.context!);
                    }
                  } else if (item.children![index].screen != null) {
                    controller.updateSelectedScreen(
                      item.children![index].screen!,
                      '${section.title.toLowerCase().replaceAll(' ', '_')}/${item.title.toLowerCase().replaceAll(' ', '_')}/${item.children![index].title.toLowerCase().replaceAll(' ', '_')}',
                    );
                    if (Get.width < 1200) {
                      Navigator.pop(Get.context!);
                    }
                  }
                },
                // titleHover: section.title == "- MAIN"
                //     ? controller.dashboardsTitleHover.value
                //     : controller.locationsTitleHover.value,
                // hoverColor: section.title == "- MAIN"
                //     ? controller.dashboardHoverColor
                //     : controller.locationsHoverColor,
              );
            } else {
              return DrawerItem(
                imageList: [item.icon],
                titleList: [item.title],
                sectionPath:
                    '${section.title.toLowerCase().replaceAll(' ', '_')}/${item.title.toLowerCase().replaceAll(' ', '_')}',
                onItemTap: () {
                  if (item.navigateTo != null) {
                    controller.updateSelectedScreen(
                      null,
                      '${section.title.toLowerCase().replaceAll(' ', '_')}/${item.title.toLowerCase().replaceAll(' ', '_')}',
                      navigateTo: item.navigateTo,
                    );
                    // Close drawer for mobile/tablet view
                    if (Get.width < 1200) {
                      Navigator.pop(Get.context!);
                    }
                  } else if (item.screen != null) {
                    controller.updateSelectedScreen(
                      item.screen!,
                      '${section.title.toLowerCase().replaceAll(' ', '_')}/${item.title.toLowerCase().replaceAll(' ', '_')}',
                    );
                    if (Get.width < 1200) {
                      Navigator.pop(Get.context!);
                    }
                  }
                },
                // hoverColor: controller.appsHoverColor,
              );
            }
          }),
        ],
      );
    }).toList();
  }
}
