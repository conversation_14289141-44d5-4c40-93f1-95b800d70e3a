import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/profit_loss_controller.dart';

/// Form widget for Profit & Loss report parameters
class ProfitLossFormWidget extends StatelessWidget {
  const ProfitLossFormWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ProfitLossController>();

    return Form(
      key: controller.formKey,
      child: Column(
        children: [
          // Date Range Row
          Row(
            children: [
              // Start Date
              Expanded(
                child: TextForm<PERSON>ield(
                  controller: controller.startDateController,
                  decoration: const InputDecoration(
                    labelText: 'Start Date',
                    hintText: 'DD/MM/YYYY',
                    prefixIcon: Icon(Icons.calendar_today),
                    border: OutlineInputBorder(),
                  ),
                  validator: controller.validateDate,
                  onTap: () => _selectDate(context, controller.startDateController),
                  readOnly: true,
                ),
              ),
              const SizedBox(width: 16),
              // End Date
              Expanded(
                child: Text<PERSON><PERSON><PERSON><PERSON>(
                  controller: controller.endDateController,
                  decoration: const InputDecoration(
                    labelText: 'End Date',
                    hintText: 'DD/MM/YYYY',
                    prefixIcon: Icon(Icons.calendar_today),
                    border: OutlineInputBorder(),
                  ),
                  validator: controller.validateDate,
                  onTap: () => _selectDate(context, controller.endDateController),
                  readOnly: true,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Company Name
          TextFormField(
            controller: controller.companyNameController,
            decoration: const InputDecoration(
              labelText: 'Company Name',
              prefixIcon: Icon(Icons.business),
              border: OutlineInputBorder(),
            ),
            validator: controller.validateCompanyName,
          ),

          const SizedBox(height: 16),

          // Options Row
          Row(
            children: [
              // Include Inactive Accounts
              Expanded(
                child: Obx(() => CheckboxListTile(
                  title: const Text('Include Inactive Accounts'),
                  subtitle: const Text('Include accounts marked as inactive'),
                  value: controller.includeInactiveAccounts,
                  onChanged: (value) => controller.toggleIncludeInactiveAccounts(),
                  controlAffinity: ListTileControlAffinity.leading,
                )),
              ),
              const SizedBox(width: 16),
              // Include Zero Balances
              Expanded(
                child: Obx(() => CheckboxListTile(
                  title: const Text('Include Zero Balances'),
                  subtitle: const Text('Include accounts with zero balance'),
                  value: controller.includeZeroBalances,
                  onChanged: (value) => controller.toggleIncludeZeroBalances(),
                  controlAffinity: ListTileControlAffinity.leading,
                )),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Generate Button
          SizedBox(
            width: double.infinity,
            child: Obx(() => ElevatedButton.icon(
              onPressed: controller.isLoading ? null : controller.generateReport,
              icon: controller.isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.assessment),
              label: Text(controller.isLoading ? 'Generating...' : 'Generate Report'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
              ),
            )),
          ),

          const SizedBox(height: 16),

          // Quick Date Filters
          Wrap(
            spacing: 8,
            children: [
              _buildQuickDateButton(
                context,
                'This Month',
                () => _setThisMonth(controller),
              ),
              _buildQuickDateButton(
                context,
                'Last Month',
                () => _setLastMonth(controller),
              ),
              _buildQuickDateButton(
                context,
                'This Quarter',
                () => _setThisQuarter(controller),
              ),
              _buildQuickDateButton(
                context,
                'This Year',
                () => _setThisYear(controller),
              ),
              _buildQuickDateButton(
                context,
                'Last Year',
                () => _setLastYear(controller),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build quick date filter button
  Widget _buildQuickDateButton(
    BuildContext context,
    String label,
    VoidCallback onPressed,
  ) {
    return OutlinedButton(
      onPressed: onPressed,
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      child: Text(label),
    );
  }

  /// Select date using date picker
  Future<void> _selectDate(BuildContext context, TextEditingController controller) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      controller.text = '${picked.day.toString().padLeft(2, '0')}/${picked.month.toString().padLeft(2, '0')}/${picked.year}';
    }
  }

  /// Set date range to this month
  void _setThisMonth(ProfitLossController controller) {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);

    controller.startDateController.text = _formatDate(startOfMonth);
    controller.endDateController.text = _formatDate(endOfMonth);
  }

  /// Set date range to last month
  void _setLastMonth(ProfitLossController controller) {
    final now = DateTime.now();
    final startOfLastMonth = DateTime(now.year, now.month - 1, 1);
    final endOfLastMonth = DateTime(now.year, now.month, 0);

    controller.startDateController.text = _formatDate(startOfLastMonth);
    controller.endDateController.text = _formatDate(endOfLastMonth);
  }

  /// Set date range to this quarter
  void _setThisQuarter(ProfitLossController controller) {
    final now = DateTime.now();
    final quarter = ((now.month - 1) ~/ 3) + 1;
    final startOfQuarter = DateTime(now.year, (quarter - 1) * 3 + 1, 1);
    final endOfQuarter = DateTime(now.year, quarter * 3 + 1, 0);

    controller.startDateController.text = _formatDate(startOfQuarter);
    controller.endDateController.text = _formatDate(endOfQuarter);
  }

  /// Set date range to this year
  void _setThisYear(ProfitLossController controller) {
    final now = DateTime.now();
    final startOfYear = DateTime(now.year, 1, 1);
    final endOfYear = DateTime(now.year, 12, 31);

    controller.startDateController.text = _formatDate(startOfYear);
    controller.endDateController.text = _formatDate(endOfYear);
  }

  /// Set date range to last year
  void _setLastYear(ProfitLossController controller) {
    final now = DateTime.now();
    final startOfLastYear = DateTime(now.year - 1, 1, 1);
    final endOfLastYear = DateTime(now.year - 1, 12, 31);

    controller.startDateController.text = _formatDate(startOfLastYear);
    controller.endDateController.text = _formatDate(endOfLastYear);
  }

  /// Format date for display
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}
