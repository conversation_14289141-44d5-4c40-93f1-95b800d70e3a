import 'package:flutter_test/flutter_test.dart';
import 'package:logestics/models/voucher_model.dart';

void main() {
  group('Tax Authority VoucherModel Tests', () {
    test('should include selected tax authorities in voucher model', () {
      final voucher = VoucherModel(
        voucherStatus: 'pending',
        departureDate: '01/01/2024',
        driverName: 'Test Driver',
        invoiceTasNumberList: [],
        invoiceBiltyNumberList: [],
        weightInTons: 10,
        voucherNumber: '12345',
        productName: 'Test Product',
        totalNumberOfBags: 100,
        brokerType: 'own',
        brokerName: 'Test Broker',
        brokerFees: 1000.0,
        munshianaFees: 500.0,
        brokerAccount: 'test-account',
        munshianaAccount: 'test-munshiana',
        driverPhoneNumber: '***********',
        truckNumber: 'ABC123',
        conveyNoteNumber: 'CN123',
        totalFreight: 50000.0,
        selectedTaxAuthorities: [
          'SRB (Sindh Revenue Board)',
          'PRA (Punjab Revenue Authority)'
        ],
      );

      expect(voucher.selectedTaxAuthorities.length, 2);
      expect(
          voucher.selectedTaxAuthorities.contains('SRB (Sindh Revenue Board)'),
          true);
      expect(
          voucher.selectedTaxAuthorities
              .contains('PRA (Punjab Revenue Authority)'),
          true);
    });

    test('should serialize and deserialize tax authorities correctly', () {
      final originalVoucher = VoucherModel(
        voucherStatus: 'pending',
        departureDate: '01/01/2024',
        driverName: 'Test Driver',
        invoiceTasNumberList: [],
        invoiceBiltyNumberList: [],
        weightInTons: 10,
        voucherNumber: '12345',
        productName: 'Test Product',
        totalNumberOfBags: 100,
        brokerType: 'own',
        brokerName: 'Test Broker',
        brokerFees: 1000.0,
        munshianaFees: 500.0,
        brokerAccount: 'test-account',
        munshianaAccount: 'test-munshiana',
        driverPhoneNumber: '***********',
        truckNumber: 'ABC123',
        conveyNoteNumber: 'CN123',
        totalFreight: 50000.0,
        selectedTaxAuthorities: [
          'SRB (Sindh Revenue Board)',
          'KRA (Khyber Revenue Authority)'
        ],
      );

      // Convert to JSON
      final json = originalVoucher.toJson();
      expect(json['selectedTaxAuthorities'], isA<List>());
      expect(json['selectedTaxAuthorities'].length, 2);
      expect(
          json['selectedTaxAuthorities'].contains('SRB (Sindh Revenue Board)'),
          true);
      expect(
          json['selectedTaxAuthorities']
              .contains('KRA (Khyber Revenue Authority)'),
          true);

      // Convert back from JSON
      final deserializedVoucher = VoucherModel.fromJson(json);
      expect(deserializedVoucher.selectedTaxAuthorities.length, 2);
      expect(
          deserializedVoucher.selectedTaxAuthorities
              .contains('SRB (Sindh Revenue Board)'),
          true);
      expect(
          deserializedVoucher.selectedTaxAuthorities
              .contains('KRA (Khyber Revenue Authority)'),
          true);
    });

    test('should handle empty tax authorities list', () {
      final voucher = VoucherModel(
        voucherStatus: 'pending',
        departureDate: '01/01/2024',
        driverName: 'Test Driver',
        invoiceTasNumberList: [],
        invoiceBiltyNumberList: [],
        weightInTons: 10,
        voucherNumber: '12345',
        productName: 'Test Product',
        totalNumberOfBags: 100,
        brokerType: 'own',
        brokerName: 'Test Broker',
        brokerFees: 1000.0,
        munshianaFees: 500.0,
        brokerAccount: 'test-account',
        munshianaAccount: 'test-munshiana',
        driverPhoneNumber: '***********',
        truckNumber: 'ABC123',
        conveyNoteNumber: 'CN123',
        totalFreight: 50000.0,
        selectedTaxAuthorities: [],
      );

      expect(voucher.selectedTaxAuthorities.length, 0);
      expect(voucher.selectedTaxAuthorities.isEmpty, true);
    });

    test('should handle null tax authorities in JSON deserialization', () {
      final json = {
        'voucherStatus': 'pending',
        'departureDate': '01/01/2024',
        'driverName': 'Test Driver',
        'invoiceTasNumberList': <String>[],
        'invoiceBiltyNumberList': <String>[],
        'weightInTons': 10,
        'voucherNumber': '12345',
        'productName': 'Test Product',
        'totalNumberOfBags': 100,
        'brokerType': 'own',
        'brokerName': 'Test Broker',
        'brokerFees': 1000.0,
        'munshianaFees': 500.0,
        'brokerAccount': 'test-account',
        'munshianaAccount': 'test-munshiana',
        'driverPhoneNumber': '***********',
        'truckNumber': 'ABC123',
        'conveyNoteNumber': 'CN123',
        'totalFreight': 50000.0,
        // selectedTaxAuthorities is missing from JSON
      };

      final voucher = VoucherModel.fromJson(json);
      expect(voucher.selectedTaxAuthorities.length, 0);
      expect(voucher.selectedTaxAuthorities.isEmpty, true);
    });
  });
}
