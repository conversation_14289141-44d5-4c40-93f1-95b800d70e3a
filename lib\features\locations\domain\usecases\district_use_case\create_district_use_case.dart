import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/models/locations/district_model.dart';
import 'package:logestics/features/locations/repositories/district_repository.dart';

class CreateDistrictUseCase {
  final DistrictRepository _districtRepository;

  CreateDistrictUseCase(this._districtRepository);

  /// Executes the use case to create an District.
  Future<Either<FailureObj, SuccessObj>> call({
    required DistrictModel district,
  }) async {
    // if (district.districtId.isEmpty) {
    //   return Left(
    //     FailureObj(
    //       code: 'invalid-District',
    //       message: 'District "districtId" must not be empty or null.',
    //     ),
    //   );
    // }

    // Call the repository
    return await _districtRepository.createDistrict(
      district: district,
    );
  }
}
