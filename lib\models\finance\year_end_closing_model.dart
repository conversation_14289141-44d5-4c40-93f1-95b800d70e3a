import 'package:cloud_firestore/cloud_firestore.dart';

/// Enum for year-end closing status
enum YearEndClosingStatus {
  pending('Pending'),
  inProgress('In Progress'),
  completed('Completed'),
  failed('Failed'),
  reversed('Reversed');

  const YearEndClosingStatus(this.displayName);
  final String displayName;

  static YearEndClosingStatus fromString(String value) {
    return YearEndClosingStatus.values.firstWhere(
      (status) => status.name == value,
      orElse: () => YearEndClosingStatus.pending,
    );
  }
}

/// Model for year-end closing process
class YearEndClosingModel {
  final String id;
  final String fiscalYearId;
  final String fiscalYearName;
  final DateTime fiscalYearEndDate;
  final YearEndClosingStatus status;
  final DateTime? startedAt;
  final DateTime? completedAt;
  final String? startedBy;
  final String? completedBy;
  final List<String> closingJournalEntryIds; // Journal entries created during closing
  final double totalRevenuesClosed;
  final double totalExpensesClosed;
  final double netIncomeTransferred; // Net income transferred to retained earnings
  final String? retainedEarningsAccountId;
  final String? notes;
  final String? errorMessage; // If closing failed
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String uid; // Company isolation

  YearEndClosingModel({
    required this.id,
    required this.fiscalYearId,
    required this.fiscalYearName,
    required this.fiscalYearEndDate,
    required this.status,
    this.startedAt,
    this.completedAt,
    this.startedBy,
    this.completedBy,
    required this.closingJournalEntryIds,
    required this.totalRevenuesClosed,
    required this.totalExpensesClosed,
    required this.netIncomeTransferred,
    this.retainedEarningsAccountId,
    this.notes,
    this.errorMessage,
    required this.createdAt,
    this.updatedAt,
    required this.uid,
  });

  /// Convert to JSON for Firebase storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fiscalYearId': fiscalYearId,
      'fiscalYearName': fiscalYearName,
      'fiscalYearEndDate': fiscalYearEndDate.millisecondsSinceEpoch,
      'status': status.name,
      'startedAt': startedAt?.millisecondsSinceEpoch,
      'completedAt': completedAt?.millisecondsSinceEpoch,
      'startedBy': startedBy,
      'completedBy': completedBy,
      'closingJournalEntryIds': closingJournalEntryIds,
      'totalRevenuesClosed': totalRevenuesClosed,
      'totalExpensesClosed': totalExpensesClosed,
      'netIncomeTransferred': netIncomeTransferred,
      'retainedEarningsAccountId': retainedEarningsAccountId,
      'notes': notes,
      'errorMessage': errorMessage,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt?.millisecondsSinceEpoch,
      'uid': uid,
    };
  }

  /// Create from JSON
  factory YearEndClosingModel.fromJson(Map<String, dynamic> json) {
    return YearEndClosingModel(
      id: json['id'] ?? '',
      fiscalYearId: json['fiscalYearId'] ?? '',
      fiscalYearName: json['fiscalYearName'] ?? '',
      fiscalYearEndDate: DateTime.fromMillisecondsSinceEpoch(json['fiscalYearEndDate'] ?? 0),
      status: YearEndClosingStatus.fromString(json['status'] ?? 'pending'),
      startedAt: json['startedAt'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(json['startedAt'])
          : null,
      completedAt: json['completedAt'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(json['completedAt'])
          : null,
      startedBy: json['startedBy'],
      completedBy: json['completedBy'],
      closingJournalEntryIds: List<String>.from(json['closingJournalEntryIds'] ?? []),
      totalRevenuesClosed: (json['totalRevenuesClosed'] ?? 0.0).toDouble(),
      totalExpensesClosed: (json['totalExpensesClosed'] ?? 0.0).toDouble(),
      netIncomeTransferred: (json['netIncomeTransferred'] ?? 0.0).toDouble(),
      retainedEarningsAccountId: json['retainedEarningsAccountId'],
      notes: json['notes'],
      errorMessage: json['errorMessage'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt'] ?? 0),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(json['updatedAt'])
          : null,
      uid: json['uid'] ?? '',
    );
  }

  /// Create from Firestore document
  factory YearEndClosingModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return YearEndClosingModel(
      id: doc.id,
      fiscalYearId: data['fiscalYearId'] ?? '',
      fiscalYearName: data['fiscalYearName'] ?? '',
      fiscalYearEndDate: (data['fiscalYearEndDate'] as Timestamp).toDate(),
      status: YearEndClosingStatus.fromString(data['status'] ?? 'pending'),
      startedAt: data['startedAt'] != null 
          ? (data['startedAt'] as Timestamp).toDate()
          : null,
      completedAt: data['completedAt'] != null 
          ? (data['completedAt'] as Timestamp).toDate()
          : null,
      startedBy: data['startedBy'],
      completedBy: data['completedBy'],
      closingJournalEntryIds: List<String>.from(data['closingJournalEntryIds'] ?? []),
      totalRevenuesClosed: (data['totalRevenuesClosed'] ?? 0.0).toDouble(),
      totalExpensesClosed: (data['totalExpensesClosed'] ?? 0.0).toDouble(),
      netIncomeTransferred: (data['netIncomeTransferred'] ?? 0.0).toDouble(),
      retainedEarningsAccountId: data['retainedEarningsAccountId'],
      notes: data['notes'],
      errorMessage: data['errorMessage'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: data['updatedAt'] != null 
          ? (data['updatedAt'] as Timestamp).toDate()
          : null,
      uid: data['uid'] ?? '',
    );
  }

  /// Copy with method for immutability
  YearEndClosingModel copyWith({
    String? id,
    String? fiscalYearId,
    String? fiscalYearName,
    DateTime? fiscalYearEndDate,
    YearEndClosingStatus? status,
    DateTime? startedAt,
    DateTime? completedAt,
    String? startedBy,
    String? completedBy,
    List<String>? closingJournalEntryIds,
    double? totalRevenuesClosed,
    double? totalExpensesClosed,
    double? netIncomeTransferred,
    String? retainedEarningsAccountId,
    String? notes,
    String? errorMessage,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? uid,
  }) {
    return YearEndClosingModel(
      id: id ?? this.id,
      fiscalYearId: fiscalYearId ?? this.fiscalYearId,
      fiscalYearName: fiscalYearName ?? this.fiscalYearName,
      fiscalYearEndDate: fiscalYearEndDate ?? this.fiscalYearEndDate,
      status: status ?? this.status,
      startedAt: startedAt ?? this.startedAt,
      completedAt: completedAt ?? this.completedAt,
      startedBy: startedBy ?? this.startedBy,
      completedBy: completedBy ?? this.completedBy,
      closingJournalEntryIds: closingJournalEntryIds ?? this.closingJournalEntryIds,
      totalRevenuesClosed: totalRevenuesClosed ?? this.totalRevenuesClosed,
      totalExpensesClosed: totalExpensesClosed ?? this.totalExpensesClosed,
      netIncomeTransferred: netIncomeTransferred ?? this.netIncomeTransferred,
      retainedEarningsAccountId: retainedEarningsAccountId ?? this.retainedEarningsAccountId,
      notes: notes ?? this.notes,
      errorMessage: errorMessage ?? this.errorMessage,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      uid: uid ?? this.uid,
    );
  }

  /// Check if closing is in progress
  bool get isInProgress => status == YearEndClosingStatus.inProgress;

  /// Check if closing is completed
  bool get isCompleted => status == YearEndClosingStatus.completed;

  /// Check if closing failed
  bool get hasFailed => status == YearEndClosingStatus.failed;

  /// Get net income (revenues - expenses)
  double get netIncome => totalRevenuesClosed - totalExpensesClosed;

  /// Get duration of closing process
  Duration? get closingDuration {
    if (startedAt != null && completedAt != null) {
      return completedAt!.difference(startedAt!);
    }
    return null;
  }

  @override
  String toString() {
    return 'YearEndClosingModel(id: $id, fiscalYear: $fiscalYearName, status: ${status.displayName}, netIncome: $netIncomeTransferred)';
  }
}

/// Model for year-end closing summary
class YearEndClosingSummary {
  final int totalClosingsCompleted;
  final int totalClosingsFailed;
  final double totalNetIncomeTransferred;
  final DateTime? lastClosingDate;
  final String? lastClosedFiscalYear;

  YearEndClosingSummary({
    required this.totalClosingsCompleted,
    required this.totalClosingsFailed,
    required this.totalNetIncomeTransferred,
    this.lastClosingDate,
    this.lastClosedFiscalYear,
  });

  factory YearEndClosingSummary.fromClosings(List<YearEndClosingModel> closings) {
    final completed = closings.where((c) => c.isCompleted).toList();
    final failed = closings.where((c) => c.hasFailed).toList();
    
    final totalNetIncome = completed.fold<double>(
      0.0, 
      (sum, closing) => sum + closing.netIncomeTransferred,
    );

    YearEndClosingModel? lastCompleted;
    if (completed.isNotEmpty) {
      completed.sort((a, b) => b.completedAt!.compareTo(a.completedAt!));
      lastCompleted = completed.first;
    }

    return YearEndClosingSummary(
      totalClosingsCompleted: completed.length,
      totalClosingsFailed: failed.length,
      totalNetIncomeTransferred: totalNetIncome,
      lastClosingDate: lastCompleted?.completedAt,
      lastClosedFiscalYear: lastCompleted?.fiscalYearName,
    );
  }
}
