import 'package:flutter/material.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';

/// A reusable popup menu item for data tables
class DataTablePopupMenuItem extends PopupMenuItem {
  DataTablePopupMenuItem({
    super.key,
    required String text,
    IconData? icon,
    Color? textColor,
    Color? iconColor,
    required VoidCallback onTap,
    bool isDanger = false,
  }) : super(
          onTap: onTap,
          child: _buildChild(text, icon, textColor, iconColor, isDanger),
        );

  static Widget _buildChild(
    String text,
    IconData? icon,
    Color? textColor,
    Color? iconColor,
    bool isDanger,
  ) {
    return Builder(
      builder: (context) {
        final color = isDanger
            ? Colors.red
            : (textColor ?? Theme.of(context).textTheme.bodyLarge?.color);

        return Row(
          children: [
            if (icon != null) ...[
              Icon(
                icon,
                size: 18,
                color: isDanger ? Colors.red : (iconColor ?? color),
              ),
              const SizedBox(width: 8),
            ],
            Text(
              text,
              style: TextStyle(
                color: color,
                fontWeight: isDanger ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        );
      },
    );
  }
}

/// A reusable header cell for data tables
class DataTableHeaderCell extends StatelessWidget {
  final String text;
  final TextAlign textAlign;
  final Color? textColor;

  const DataTableHeaderCell({
    super.key,
    required this.text,
    this.textAlign = TextAlign.left,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Text(
          text,
          textAlign: textAlign,
          style: AppTextStyles.invoiceHeaderStyle.copyWith(
            color: textColor,
          ),
        ),
      ),
    );
  }
}

/// A reusable data cell for data tables
class DataTableCell extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final TextAlign textAlign;
  final VoidCallback? onTap;

  const DataTableCell({
    super.key,
    required this.text,
    this.style,
    this.textAlign = TextAlign.left,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: onTap != null
            ? GestureDetector(
                onTap: onTap,
                child: Text(
                  text,
                  textAlign: textAlign,
                  style: style ?? AppTextStyles.invoiceDataStyle,
                ),
              )
            : Text(
                text,
                textAlign: textAlign,
                style: style ?? AppTextStyles.invoiceDataStyle,
              ),
      ),
    );
  }
}

/// A reusable checkbox cell for data tables
class DataTableCheckboxCell extends StatelessWidget {
  final bool value;
  final ValueChanged<bool?> onChanged;
  final Color? activeColor;
  final Color? borderColor;

  const DataTableCheckboxCell({
    super.key,
    required this.value,
    required this.onChanged,
    this.activeColor,
    this.borderColor,
  });

  @override
  Widget build(BuildContext context) {
    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Transform.scale(
          scale: 1,
          child: Checkbox(
            overlayColor: WidgetStateProperty.all<Color>(
              Colors.transparent,
            ),
            activeColor: activeColor ?? const Color(0xff0f79f3),
            side: BorderSide(
              width: 2,
              color: borderColor ?? Colors.grey,
            ),
            value: value,
            onChanged: onChanged,
          ),
        ),
      ),
    );
  }
}

/// A reusable actions cell with a popup menu
class DataTableActionsCell extends StatelessWidget {
  final List<PopupMenuEntry<dynamic>> menuItems;
  final IconData icon;
  final Color? iconColor;

  const DataTableActionsCell({
    super.key,
    required this.menuItems,
    this.icon = Icons.more_vert,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Builder(builder: (context) {
        return IconButton(
          icon: Icon(icon, color: iconColor),
          onPressed: () {
            final RenderBox button = context.findRenderObject() as RenderBox;
            final RenderBox overlay =
                Overlay.of(context).context.findRenderObject() as RenderBox;
            final RelativeRect position = RelativeRect.fromRect(
              Rect.fromPoints(
                button.localToGlobal(Offset.zero, ancestor: overlay),
                button.localToGlobal(button.size.bottomRight(Offset.zero),
                    ancestor: overlay),
              ),
              Offset.zero & overlay.size,
            );

            showMenu(
              context: context,
              position: position,
              items: menuItems,
            );
          },
        );
      }),
    );
  }
}
