import 'dart:async';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/core/utils/mixins/auto_refresh_mixin.dart';
import 'package:logestics/features/finance/deposit_categories/repositories/deposit_category_repository.dart';
import 'package:logestics/models/finance/deposit_category_model.dart';
import 'package:uuid/uuid.dart';

class DepositCategoryController extends GetxController with AutoRefreshMixin {
  final DepositCategoryRepository repository;

  // Form controllers
  final nameController = TextEditingController();
  final descriptionController = TextEditingController();
  final searchController = TextEditingController();
  final formKey = GlobalKey<FormState>();

  // Observable variables
  final categories = <DepositCategoryModel>[].obs;
  final filteredCategories = <DepositCategoryModel>[].obs;
  final isLoading = false.obs;
  final isDialogOpen = false.obs;
  final isCreatingCategory = false.obs;
  final isDeletingCategory = false.obs;
  final isUpdatingCategory = false.obs;
  final selectedCategory = Rxn<DepositCategoryModel>();
  final searchQuery = ''.obs;

  // Stream subscription for real-time updates
  StreamSubscription? _categoriesSubscription;

  DepositCategoryController({required this.repository});

  // Get current user ID
  String get currentUserId => FirebaseAuth.instance.currentUser?.uid ?? '';

  @override
  void onInit() {
    super.onInit();
    searchController.addListener(_onSearchChanged);
    _setupRealTimeUpdates();
  }

  void _onSearchChanged() {
    searchQuery.value = searchController.text;
    _filterCategories();
  }

  @override
  void onClose() {
    nameController.dispose();
    descriptionController.dispose();
    searchController.dispose();
    _categoriesSubscription?.cancel();
    super.onClose();
  }

  /// Implementation of AutoRefreshMixin.refreshData
  @override
  Future<void> refreshData() async {
    await fetchCategories();
  }

  /// Set up real-time category updates
  void _setupRealTimeUpdates() {
    // Cancel existing subscription
    _categoriesSubscription?.cancel();

    // Set up real-time listener
    _categoriesSubscription = repository.listenToCategories().listen(
      (categoriesList) {
        log('Real-time update: received ${categoriesList.length} categories');
        categories.value = categoriesList;
        _filterCategories();
      },
      onError: (error) {
        log('Error in real-time categories stream: $error');
        // Fallback to manual fetch on error
        fetchCategories();
      },
    );

    // Listen for changes in categories and update filtered list
    ever(categories, (_) => _filterCategories());
    ever(searchQuery, (_) => _filterCategories());

    // Load initial data
    fetchCategories();
  }

  /// Fetch all categories with loading indicator
  Future<void> fetchCategories() async {
    try {
      isLoading.value = true;
      log('Fetching deposit categories...');

      final result = await repository.getCategories();

      result.fold(
        (failure) {
          log('Failed to fetch categories: ${failure.message}');
          SnackbarUtils.showError(
            AppStrings.errorS,
            'Failed to load categories: ${failure.message}',
          );
        },
        (categoriesList) {
          log('Successfully loaded ${categoriesList.length} categories');
          categories.value = categoriesList;
          _filterCategories();
        },
      );
    } catch (e) {
      log('Error loading categories: $e');
      SnackbarUtils.showError(
        AppStrings.errorS,
        'Failed to load categories. Please try again.',
      );
    } finally {
      isLoading.value = false;
    }
  }

  void _filterCategories() {
    final query = searchQuery.value.toLowerCase().trim();
    if (query.isEmpty) {
      filteredCategories.value = categories;
    } else {
      filteredCategories.value = categories.where((category) {
        return category.name.toLowerCase().contains(query) ||
            (category.description.toLowerCase().contains(query));
      }).toList();
    }
  }

  Future<void> addCategory() async {
    if (formKey.currentState == null || !formKey.currentState!.validate()) {
      return;
    }

    if (currentUserId.isEmpty) {
      SnackbarUtils.showError(
        AppStrings.errorS,
        'User not authenticated. Please login again.',
      );
      return;
    }

    isCreatingCategory.value = true;
    try {
      final result = await repository.addCategory(
        DepositCategoryModel(
          id: const Uuid().v4(),
          name: nameController.text.trim(),
          description: descriptionController.text.trim(),
          createdAt: DateTime.now(),
          uid: currentUserId, // Ensure user ID is set
        ),
      );

      result.fold(
        (failure) {
          SnackbarUtils.showError(
            AppStrings.errorS,
            failure.message,
          );
        },
        (data) {
          SnackbarUtils.showSuccess(
            AppStrings.success,
            'Category created successfully',
          );
          clearForm();
          closeDialog();
          // Real-time listener will automatically update the UI
          // Force refresh to ensure immediate update
          Future.delayed(const Duration(milliseconds: 500), () {
            fetchCategories();
          });
        },
      );
    } catch (e) {
      SnackbarUtils.showError(
        AppStrings.errorS,
        'Failed to create category: ${e.toString()}',
      );
    } finally {
      isCreatingCategory.value = false;
    }
  }

  Future<void> deleteCategory(String id) async {
    isDeletingCategory.value = true;
    try {
      final result = await repository.deleteCategory(id);
      result.fold(
        (failure) {
          SnackbarUtils.showError(AppStrings.error, failure.message);
        },
        (success) {
          SnackbarUtils.showSuccess(
            AppStrings.success,
            'Category deleted successfully',
          );
          // Force refresh to ensure immediate update
          Future.delayed(const Duration(milliseconds: 500), () {
            fetchCategories();
          });
        },
      );
    } catch (e) {
      SnackbarUtils.showError(
          AppStrings.error, 'Failed to delete category: ${e.toString()}');
    } finally {
      isDeletingCategory.value = false;
    }
  }

  Future<void> updateCategory() async {
    if (formKey.currentState == null ||
        !formKey.currentState!.validate() ||
        selectedCategory.value == null) {
      return;
    }

    isUpdatingCategory.value = true;
    try {
      final updatedCategory = selectedCategory.value!.copyWith(
        name: nameController.text.trim(),
        description: descriptionController.text.trim(),
        uid: selectedCategory.value!.uid.isNotEmpty
            ? selectedCategory.value!.uid
            : currentUserId, // Preserve existing UID or set current user
      );

      final result = await repository.updateCategory(updatedCategory);
      result.fold(
        (failure) {
          SnackbarUtils.showError(AppStrings.error, failure.message);
        },
        (data) {
          SnackbarUtils.showSuccess(
            AppStrings.success,
            'Category updated successfully',
          );
          clearForm();
          closeDialog();
          // Force refresh to ensure immediate update
          Future.delayed(const Duration(milliseconds: 500), () {
            fetchCategories();
          });
        },
      );
    } catch (e) {
      SnackbarUtils.showError(
          AppStrings.error, 'Failed to update category: ${e.toString()}');
    } finally {
      isUpdatingCategory.value = false;
    }
  }

  void clearForm() {
    nameController.clear();
    descriptionController.clear();
    selectedCategory.value = null;
  }

  void openDialog() {
    clearForm();
    isDialogOpen.value = true;
  }

  void closeDialog() {
    clearForm();
    isDialogOpen.value = false;
  }

  void openEditDialog(DepositCategoryModel category) {
    selectedCategory.value = category;
    nameController.text = category.name;
    descriptionController.text = category.description;
    isDialogOpen.value = true;
  }

  String? validateName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Name is required';
    }
    return null;
  }

  /// Refresh categories manually
  @override
  Future<void> forceRefresh() async {
    log('Force refreshing deposit categories...');
    _categoriesSubscription?.cancel();
    await fetchCategories();
    _setupRealTimeUpdates();
  }

  /// Refresh categories when returning to screen
  void refreshCategories() {
    fetchCategories();
  }
}
