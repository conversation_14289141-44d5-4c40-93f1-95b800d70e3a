import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../../models/accounting/financial_report_models.dart';
import '../controllers/trial_balance_controller.dart';

/// Table widget for displaying Trial Balance data
class TrialBalanceTableWidget extends StatelessWidget {
  const TrialBalanceTableWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<TrialBalanceController>();

    return Obx(() {
      if (controller.filteredAccounts.isEmpty) {
        return const Center(
          child: Padding(
            padding: EdgeInsets.all(32.0),
            child: Column(
              children: [
                Icon(
                  Icons.account_balance,
                  size: 64,
                  color: Colors.grey,
                ),
                SizedBox(height: 16),
                Text(
                  'No accounts found',
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.grey,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Try adjusting your filters or generate a new report',
                  style: TextStyle(
                    color: Colors.grey,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      }

      return Column(
        children: [
          // Table header
          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Table(
              columnWidths: const {
                0: FlexColumnWidth(1.5), // Account Number
                1: FlexColumnWidth(3.0), // Account Name
                2: FlexColumnWidth(1.5), // Account Type
                3: FlexColumnWidth(1.5), // Debit
                4: FlexColumnWidth(1.5), // Credit
              },
              children: [
                TableRow(
                  children: [
                    _buildHeaderCell('Account Number'),
                    _buildHeaderCell('Account Name'),
                    _buildHeaderCell('Account Type'),
                    _buildHeaderCell('Debit'),
                    _buildHeaderCell('Credit'),
                  ],
                ),
              ],
            ),
          ),
          
          // Table body
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(8),
                bottomRight: Radius.circular(8),
              ),
            ),
            child: Table(
              columnWidths: const {
                0: FlexColumnWidth(1.5), // Account Number
                1: FlexColumnWidth(3.0), // Account Name
                2: FlexColumnWidth(1.5), // Account Type
                3: FlexColumnWidth(1.5), // Debit
                4: FlexColumnWidth(1.5), // Credit
              },
              children: [
                // Data rows
                ...controller.filteredAccounts.map((account) => 
                  _buildDataRow(context, account)
                ).toList(),
                
                // Total row
                _buildTotalRow(context, controller),
              ],
            ),
          ),
        ],
      );
    });
  }

  /// Build header cell
  Widget _buildHeaderCell(String text) {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Text(
        text,
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 14,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  /// Build data row
  TableRow _buildDataRow(BuildContext context, TrialBalanceAccount account) {
    return TableRow(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      children: [
        _buildDataCell(account.accountNumber),
        _buildDataCell(account.accountName, alignment: TextAlign.left),
        _buildDataCell(account.accountType),
        _buildDataCell(
          account.debitBalance > 0 
            ? _formatCurrency(account.debitBalance)
            : '-',
          isAmount: true,
        ),
        _buildDataCell(
          account.creditBalance > 0 
            ? _formatCurrency(account.creditBalance)
            : '-',
          isAmount: true,
        ),
      ],
    );
  }

  /// Build total row
  TableRow _buildTotalRow(BuildContext context, TrialBalanceController controller) {
    return TableRow(
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.05),
        border: Border(
          top: BorderSide(color: Theme.of(context).primaryColor, width: 2),
        ),
      ),
      children: [
        const Padding(
          padding: EdgeInsets.all(12.0),
          child: Text(
            '',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        const Padding(
          padding: EdgeInsets.all(12.0),
          child: Text(
            'TOTAL',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
            textAlign: TextAlign.left,
          ),
        ),
        const Padding(
          padding: EdgeInsets.all(12.0),
          child: Text(
            '',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(12.0),
          child: Text(
            _formatCurrency(controller.totalDebits),
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
            textAlign: TextAlign.right,
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(12.0),
          child: Text(
            _formatCurrency(controller.totalCredits),
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
            textAlign: TextAlign.right,
          ),
        ),
      ],
    );
  }

  /// Build data cell
  Widget _buildDataCell(
    String text, {
    TextAlign alignment = TextAlign.center,
    bool isAmount = false,
  }) {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 13,
          fontWeight: isAmount ? FontWeight.w500 : FontWeight.normal,
          fontFamily: isAmount ? 'monospace' : null,
        ),
        textAlign: isAmount ? TextAlign.right : alignment,
      ),
    );
  }

  /// Format currency amount
  String _formatCurrency(double amount) {
    if (amount == 0) return '0.00';
    
    // Format with commas and 2 decimal places
    final formatted = amount.toStringAsFixed(2);
    final parts = formatted.split('.');
    final integerPart = parts[0];
    final decimalPart = parts[1];
    
    // Add commas to integer part
    final regex = RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))');
    final integerWithCommas = integerPart.replaceAllMapped(
      regex,
      (Match match) => '${match[1]},',
    );
    
    return '$integerWithCommas.$decimalPart';
  }
}
