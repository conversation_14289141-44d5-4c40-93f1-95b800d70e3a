import 'package:flutter/material.dart';
import '../../../../../models/finance/journal_entry_model.dart';

class JournalEntryCard extends StatelessWidget {
  final JournalEntryModel journalEntry;
  final VoidCallback? onTap;
  final VoidCallback? onPost;
  final VoidCallback? onDelete;

  const JournalEntryCard({
    Key? key,
    required this.journalEntry,
    this.onTap,
    this.onPost,
    this.onDelete,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Row
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          journalEntry.entryNumber,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          journalEntry.entryDate.toString().split(' ')[0],
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  _buildStatusChip(context),
                ],
              ),

              const SizedBox(height: 12),

              // Description
              Text(
                journalEntry.description,
                style: Theme.of(context).textTheme.bodyMedium,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 12),

              // Amount Summary
              Row(
                children: [
                  Expanded(
                    child: _buildAmountInfo(
                      context,
                      'Total Debits',
                      journalEntry.totalDebits,
                      Colors.green,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildAmountInfo(
                      context,
                      'Total Credits',
                      journalEntry.totalCredits,
                      Colors.red,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Lines Count and Balance Status
              Row(
                children: [
                  Icon(
                    Icons.list_alt,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${journalEntry.lines.length} line${journalEntry.lines.length != 1 ? 's' : ''}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  const Spacer(),
                  _buildBalanceIndicator(context),
                ],
              ),

              // Action Buttons (only for draft entries)
              if (journalEntry.status == JournalEntryStatus.draft) ...[
                const SizedBox(height: 12),
                const Divider(),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    if (onDelete != null)
                      TextButton.icon(
                        onPressed: onDelete,
                        icon: const Icon(Icons.delete, size: 16),
                        label: const Text('Delete'),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.red,
                        ),
                      ),
                    const SizedBox(width: 8),
                    if (onPost != null && journalEntry.isBalanced)
                      ElevatedButton.icon(
                        onPressed: onPost,
                        icon: const Icon(Icons.check, size: 16),
                        label: const Text('Post'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                        ),
                      ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(BuildContext context) {
    Color backgroundColor;
    Color textColor;
    IconData icon;

    switch (journalEntry.status) {
      case JournalEntryStatus.draft:
        backgroundColor = Colors.orange.withOpacity(0.1);
        textColor = Colors.orange[700]!;
        icon = Icons.edit_note;
        break;
      case JournalEntryStatus.posted:
        backgroundColor = Colors.green.withOpacity(0.1);
        textColor = Colors.green[700]!;
        icon = Icons.check_circle;
        break;
      case JournalEntryStatus.reversed:
        backgroundColor = Colors.red.withOpacity(0.1);
        textColor = Colors.red[700]!;
        icon = Icons.undo;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: textColor),
          const SizedBox(width: 4),
          Text(
            journalEntry.status.displayName,
            style: TextStyle(
              color: textColor,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAmountInfo(
    BuildContext context,
    String label,
    double amount,
    Color color,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 2),
        Text(
          '\$${amount.toStringAsFixed(2)}',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildBalanceIndicator(BuildContext context) {
    final isBalanced = journalEntry.isBalanced;
    final balanceDifference = journalEntry.balanceDifference;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isBalanced 
            ? Colors.green.withOpacity(0.1) 
            : Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isBalanced ? Icons.check_circle : Icons.warning,
            size: 14,
            color: isBalanced ? Colors.green[700] : Colors.red[700],
          ),
          const SizedBox(width: 4),
          Text(
            isBalanced 
                ? 'Balanced' 
                : 'Off by \$${balanceDifference.abs().toStringAsFixed(2)}',
            style: TextStyle(
              color: isBalanced ? Colors.green[700] : Colors.red[700],
              fontSize: 11,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
