class DepositCategoryModel {
  final String id;
  final String name;
  final String description;
  final DateTime createdAt;
  final String uid; // User ID who owns this category

  DepositCategoryModel({
    required this.id,
    required this.name,
    required this.description,
    required this.createdAt,
    this.uid = '', // Default empty string for backward compatibility
  });

  factory DepositCategoryModel.fromJson(Map<String, dynamic> json) {
    return DepositCategoryModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String? ?? '',
      createdAt: json['createdAt'] != null
          ? (json['createdAt'] is String
              ? DateTime.parse(json['createdAt'] as String)
              : DateTime.fromMillisecondsSinceEpoch(json['createdAt']))
          : DateTime.now(),
      uid: json['uid'] as String? ?? '', // Extract UID from JSON
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'createdAt': createdAt.toIso8601String(),
      'uid': uid, // Include UID in JSON
    };
  }

  // Create a copy of this model with updated fields
  DepositCategoryModel copyWith({
    String? id,
    String? name,
    String? description,
    DateTime? createdAt,
    String? uid,
  }) {
    return DepositCategoryModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      uid: uid ?? this.uid,
    );
  }
}
