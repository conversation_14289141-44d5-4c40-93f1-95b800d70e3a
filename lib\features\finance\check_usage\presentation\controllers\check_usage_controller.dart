import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/mixins/pagination_mixin.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/features/finance/check_usage/repositories/check_usage_repository.dart';
import 'package:logestics/firebase_service/finance/company_firebase_service.dart';
import 'package:logestics/models/finance/check_usage_model.dart';
import 'package:logestics/models/user_model.dart';

class CheckUsageController extends GetxController with PaginationMixin {
  final CheckUsageRepository repository;

  CheckUsageController({required this.repository});

  // Observable lists
  final RxList<CheckUsageModel> checkUsages = <CheckUsageModel>[].obs;
  final RxList<CheckUsageModel> activeChecks = <CheckUsageModel>[].obs;
  final RxList<CheckUsageModel> filteredCheckUsages = <CheckUsageModel>[].obs;
  final RxList<CheckUsageModel> filteredActiveChecks = <CheckUsageModel>[].obs;
  final RxBool isLoading = false.obs;

  // Lazy loading properties
  final RxMap<int, List<CheckUsageModel>> pageCache =
      <int, List<CheckUsageModel>>{}.obs;
  final RxSet<int> loadedPages = <int>{}.obs;
  final RxBool isLoadingPage = false.obs;
  final RxString lastError = ''.obs;

  // Filter properties
  final RxString selectedBankFilter = ''.obs;
  final RxString selectedStatusFilter = ''.obs;
  final RxString selectedCheckTypeFilter = ''.obs;
  final Rx<DateTime?> startDateFilter = Rx<DateTime?>(null);
  final Rx<DateTime?> endDateFilter = Rx<DateTime?>(null);
  final RxString searchQuery = ''.obs;
  final RxBool showFilters = false.obs;

  // New filter properties for enhancements
  final RxString selectedCompanyFilter = ''.obs;
  final RxString expiryDateSortOrder = 'none'.obs; // 'none', 'asc', 'desc'
  final Rx<DateTime?> selectedExpiryDateFilter = Rx<DateTime?>(null);
  final RxList<UserModel> availableCompanies = <UserModel>[].obs;

  // Available filter options
  final List<String> statusOptions = [
    'All',
    'issued',
    'cleared',
    'bounced',
    'cancelled'
  ];
  final List<String> checkTypeOptions = ['All', 'own', 'other'];
  final RxList<String> bankOptions = <String>['All'].obs;
  final List<String> sortOptions = [
    'None',
    'Expiry Date (Earliest)',
    'Expiry Date (Latest)'
  ];

  @override
  void onInit() {
    super.onInit();
    loadInitialData();
    loadCompanies();
    _setupFilterListeners();
  }

  void _setupFilterListeners() {
    // Listen to filter changes and apply filters automatically
    ever(selectedBankFilter, (_) => _applyFilters());
    ever(selectedStatusFilter, (_) => _applyFilters());
    ever(selectedCheckTypeFilter, (_) => _applyFilters());
    ever(startDateFilter, (_) => _applyFilters());
    ever(endDateFilter, (_) => _applyFilters());
    ever(searchQuery, (_) => _applyFilters());
    ever(selectedCompanyFilter, (_) => _applyFilters());
    ever(expiryDateSortOrder, (_) => _applyFilters());
    ever(selectedExpiryDateFilter, (_) => _applyFilters());
  }

  // Load initial data (first page only)
  Future<void> loadInitialData() async {
    isLoading.value = true;
    lastError.value = '';

    try {
      // Load all data initially for filtering purposes
      await loadCheckUsages();
      await loadActiveChecks();

      // Set up initial pagination
      setItemsPerPage(10); // Default items per page
      setCurrentPage(1);

      isLoading.value = false;
    } catch (e) {
      lastError.value = e.toString();
      isLoading.value = false;
      log('Error loading initial data: $e');
      SnackbarUtils.showError('Error', 'Failed to load initial data: $e');
    }
  }

  Future<void> loadCheckUsages() async {
    isLoading.value = true;
    try {
      final result = await repository.getCheckUsages();
      result.fold(
        (failure) {
          log('Failed to load check usages: ${failure.message}');
          SnackbarUtils.showError(
            'Error',
            'Failed to load check usage records: ${failure.message}',
          );
        },
        (checkUsageList) {
          checkUsages.value = checkUsageList;
          _updateBankOptions();
          _applyFilters();
          log('Successfully loaded ${checkUsageList.length} check usage records');
        },
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> loadActiveChecks() async {
    isLoading.value = true;
    try {
      final result = await repository.getActiveChecks();
      result.fold(
        (failure) {
          log('Failed to load active checks: ${failure.message}');
          SnackbarUtils.showError(
            'Error',
            'Failed to load active checks: ${failure.message}',
          );
        },
        (activeCheckList) {
          activeChecks.value = activeCheckList;
          _applyFilters();
          log('Successfully loaded ${activeCheckList.length} active checks');
        },
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> createCheckUsage(CheckUsageModel checkUsage) async {
    try {
      final result = await repository.createCheckUsage(checkUsage);
      result.fold(
        (failure) {
          log('Failed to create check usage: ${failure.message}');
          SnackbarUtils.showError(
            'Error',
            'Failed to record check usage: ${failure.message}',
          );
        },
        (_) {
          log('Successfully created check usage record: ${checkUsage.checkNumber}');
          SnackbarUtils.showSuccess(
            'Success',
            'Check usage recorded successfully',
          );
        },
      );
    } catch (e) {
      log('Error creating check usage: $e');
      SnackbarUtils.showError(
        'Error',
        'Failed to record check usage: $e',
      );
    }
  }

  Future<void> updateCheckStatus(String checkUsageId, String newStatus) async {
    try {
      final result =
          await repository.updateCheckStatus(checkUsageId, newStatus);
      result.fold(
        (failure) {
          log('Failed to update check status: ${failure.message}');
          SnackbarUtils.showError(
            'Error',
            'Failed to update check status: ${failure.message}',
          );
        },
        (_) {
          log('Successfully updated check status: $checkUsageId to $newStatus');
          SnackbarUtils.showSuccess(
            'Success',
            'Check status updated to ${newStatus.toUpperCase()}',
          );
        },
      );
    } catch (e) {
      log('Error updating check status: $e');
      SnackbarUtils.showError(
        'Error',
        'Failed to update check status: $e',
      );
    }
  }

  Future<void> deleteCheckUsage(String checkUsageId) async {
    // Show confirmation dialog
    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('Delete Check Record'),
        content: const Text(
            'Are you sure you want to delete this check usage record?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      final result = await repository.deleteCheckUsage(checkUsageId);
      result.fold(
        (failure) {
          log('Failed to delete check usage: ${failure.message}');
          SnackbarUtils.showError(
            'Error',
            'Failed to delete check usage record: ${failure.message}',
          );
        },
        (_) {
          log('Successfully deleted check usage: $checkUsageId');
          SnackbarUtils.showSuccess(
            'Success',
            'Check usage record deleted successfully',
          );
        },
      );
    } catch (e) {
      log('Error deleting check usage: $e');
      SnackbarUtils.showError(
        'Error',
        'Failed to delete check usage record: $e',
      );
    }
  }

  // Get check usage statistics
  Map<String, int> getCheckStatistics() {
    final stats = <String, int>{
      'total': checkUsages.length,
      'issued': 0,
      'cleared': 0,
      'bounced': 0,
      'cancelled': 0,
    };

    for (final check in checkUsages) {
      stats[check.status] = (stats[check.status] ?? 0) + 1;
    }

    return stats;
  }

  // Get total amount of active checks
  double getTotalActiveAmount() {
    return activeChecks.fold(0.0, (sum, check) => sum + check.amount);
  }

  // Get checks by status
  List<CheckUsageModel> getChecksByStatus(String status) {
    return checkUsages.where((check) => check.status == status).toList();
  }

  // Get paginated check usages with lazy loading
  List<CheckUsageModel> get paginatedCheckUsages {
    final startIndex = (currentPage.value - 1) * itemsPerPage.value;
    final endIndex = startIndex + itemsPerPage.value;

    if (startIndex >= filteredCheckUsages.length) {
      return [];
    }

    final actualEndIndex = endIndex > filteredCheckUsages.length
        ? filteredCheckUsages.length
        : endIndex;

    return filteredCheckUsages.sublist(startIndex, actualEndIndex);
  }

  // Override setCurrentPage to implement lazy loading
  @override
  void setCurrentPage(int page) {
    if (page == currentPage.value) return;

    super.setCurrentPage(page);
    loadPageIfNeeded(page);
  }

  // Load page data if not already loaded
  Future<void> loadPageIfNeeded(int page) async {
    if (loadedPages.contains(page) || isLoadingPage.value) {
      return;
    }

    isLoadingPage.value = true;
    lastError.value = '';

    try {
      // Simulate page loading delay for demonstration
      await Future.delayed(const Duration(milliseconds: 300));

      // Mark page as loaded
      loadedPages.add(page);

      log('Loaded page $page');
      isLoadingPage.value = false;
    } catch (e) {
      lastError.value = e.toString();
      isLoadingPage.value = false;
      log('Error loading page $page: $e');
      SnackbarUtils.showError('Error', 'Failed to load page $page: $e');
    }
  }

  // Filter methods
  void _updateBankOptions() {
    final banks = checkUsages.map((check) => check.bankName).toSet().toList();
    banks.sort();
    bankOptions.value = ['All', ...banks];
  }

  void _applyFilters() {
    // Filter check usages
    List<CheckUsageModel> filtered = List.from(checkUsages);

    // Apply bank filter
    if (selectedBankFilter.value.isNotEmpty &&
        selectedBankFilter.value != 'All') {
      filtered = filtered
          .where((check) => check.bankName == selectedBankFilter.value)
          .toList();
    }

    // Apply status filter
    if (selectedStatusFilter.value.isNotEmpty &&
        selectedStatusFilter.value != 'All') {
      filtered = filtered
          .where((check) => check.status == selectedStatusFilter.value)
          .toList();
    }

    // Apply check type filter
    if (selectedCheckTypeFilter.value.isNotEmpty &&
        selectedCheckTypeFilter.value != 'All') {
      filtered = filtered
          .where((check) => check.checkType == selectedCheckTypeFilter.value)
          .toList();
    }

    // Apply company filter
    if (selectedCompanyFilter.value.isNotEmpty &&
        selectedCompanyFilter.value != 'All') {
      filtered = filtered.where((check) {
        if (check.checkType == 'other') {
          return check.externalCompanyName == selectedCompanyFilter.value;
        }
        return true; // For 'own' checks, no company filtering needed
      }).toList();
    }

    // Apply date range filter
    if (startDateFilter.value != null) {
      filtered = filtered
          .where((check) =>
              check.usageDate.isAfter(startDateFilter.value!) ||
              check.usageDate.isAtSameMomentAs(startDateFilter.value!))
          .toList();
    }

    if (endDateFilter.value != null) {
      filtered = filtered
          .where((check) =>
              check.usageDate.isBefore(endDateFilter.value!) ||
              check.usageDate.isAtSameMomentAs(endDateFilter.value!))
          .toList();
    }

    // Apply search query
    if (searchQuery.value.isNotEmpty) {
      final query = searchQuery.value.toLowerCase();
      filtered = filtered
          .where((check) =>
              check.checkNumber.toLowerCase().contains(query) ||
              check.payeeName.toLowerCase().contains(query) ||
              check.voucherNumber.toLowerCase().contains(query) ||
              check.bankName.toLowerCase().contains(query) ||
              (check.externalCompanyName?.toLowerCase().contains(query) ??
                  false))
          .toList();
    }

    // Apply expiry date exact match filter
    if (selectedExpiryDateFilter.value != null) {
      final selectedDate = selectedExpiryDateFilter.value!;
      filtered = filtered.where((check) {
        if (check.expiryDate == null) return false;

        // Compare only the date part (ignore time)
        final checkExpiryDate = DateTime(
          check.expiryDate!.year,
          check.expiryDate!.month,
          check.expiryDate!.day,
        );
        final filterDate = DateTime(
          selectedDate.year,
          selectedDate.month,
          selectedDate.day,
        );

        return checkExpiryDate.isAtSameMomentAs(filterDate);
      }).toList();
    }

    // Apply expiry date sorting
    if (expiryDateSortOrder.value != 'none') {
      // Regular expiry date sorting
      filtered.sort((a, b) {
        final aExpiry = a.expiryDate;
        final bExpiry = b.expiryDate;

        // Handle null expiry dates - put them at the end
        if (aExpiry == null && bExpiry == null) return 0;
        if (aExpiry == null) return 1;
        if (bExpiry == null) return -1;

        if (expiryDateSortOrder.value == 'asc') {
          return aExpiry.compareTo(bExpiry);
        } else {
          return bExpiry.compareTo(aExpiry);
        }
      });
    }

    filteredCheckUsages.value = filtered;
    setTotalItems(filtered.length);

    // Clear page cache when filters change
    _clearPageCache();

    // Filter active checks
    List<CheckUsageModel> filteredActive = List.from(activeChecks);

    // Apply same filters to active checks
    if (selectedBankFilter.value.isNotEmpty &&
        selectedBankFilter.value != 'All') {
      filteredActive = filteredActive
          .where((check) => check.bankName == selectedBankFilter.value)
          .toList();
    }

    if (selectedCheckTypeFilter.value.isNotEmpty &&
        selectedCheckTypeFilter.value != 'All') {
      filteredActive = filteredActive
          .where((check) => check.checkType == selectedCheckTypeFilter.value)
          .toList();
    }

    if (startDateFilter.value != null) {
      filteredActive = filteredActive
          .where((check) =>
              check.usageDate.isAfter(startDateFilter.value!) ||
              check.usageDate.isAtSameMomentAs(startDateFilter.value!))
          .toList();
    }

    if (endDateFilter.value != null) {
      filteredActive = filteredActive
          .where((check) =>
              check.usageDate.isBefore(endDateFilter.value!) ||
              check.usageDate.isAtSameMomentAs(endDateFilter.value!))
          .toList();
    }

    if (searchQuery.value.isNotEmpty) {
      final query = searchQuery.value.toLowerCase();
      filteredActive = filteredActive
          .where((check) =>
              check.checkNumber.toLowerCase().contains(query) ||
              check.payeeName.toLowerCase().contains(query) ||
              check.voucherNumber.toLowerCase().contains(query) ||
              check.bankName.toLowerCase().contains(query) ||
              (check.externalCompanyName?.toLowerCase().contains(query) ??
                  false))
          .toList();
    }

    filteredActiveChecks.value = filteredActive;
  }

  // Filter control methods
  void toggleFilters() {
    showFilters.value = !showFilters.value;
  }

  void clearFilters() {
    selectedBankFilter.value = '';
    selectedStatusFilter.value = '';
    selectedCheckTypeFilter.value = '';
    startDateFilter.value = null;
    endDateFilter.value = null;
    searchQuery.value = '';
    selectedCompanyFilter.value = '';
    expiryDateSortOrder.value = 'none';
    selectedExpiryDateFilter.value = null;
    _clearPageCache();
  }

  // Clear page cache when filters change
  void _clearPageCache() {
    pageCache.clear();
    loadedPages.clear();
    setCurrentPage(1);
  }

  // Load companies for filtering
  Future<void> loadCompanies() async {
    try {
      final companyService = CompanyFirebaseService();
      final result = await companyService.getAllUsers();
      result.fold(
        (failure) => log('Failed to load companies: ${failure.message}'),
        (companies) {
          availableCompanies.value = companies;
          log('Loaded ${companies.length} companies for filtering');
          log('Company options: $companyOptions');
        },
      );
    } catch (e) {
      log('Error loading companies: $e');
    }
  }

  // Set expiry date sort order
  void setExpiryDateSortOrder(String sortOrder) {
    switch (sortOrder) {
      case 'None':
        expiryDateSortOrder.value = 'none';
        break;
      case 'Expiry Date (Earliest)':
        expiryDateSortOrder.value = 'asc';
        break;
      case 'Expiry Date (Latest)':
        expiryDateSortOrder.value = 'desc';
        break;
      default:
        expiryDateSortOrder.value = 'none';
    }
  }

  // Set selected expiry date for filtering
  void setSortByExpiryDate(DateTime selectedDate) {
    selectedExpiryDateFilter.value = selectedDate;
  }

  // Clear only the expiry date filter
  void clearExpiryDateFilter() {
    selectedExpiryDateFilter.value = null;
  }

  // Refresh data and clear cache
  Future<void> refreshData() async {
    _clearPageCache();
    await loadInitialData();
  }

  // Get company options for dropdown (reactive)
  List<String> get companyOptions {
    final companies = <String>['All'];

    // Add companies from availableCompanies
    for (final company in availableCompanies) {
      if (!companies.contains(company.companyName)) {
        companies.add(company.companyName);
      }
    }

    // Also add external company names from existing checks
    for (final check in checkUsages) {
      if (check.externalCompanyName != null &&
          check.externalCompanyName!.isNotEmpty &&
          !companies.contains(check.externalCompanyName!)) {
        companies.add(check.externalCompanyName!);
      }
    }

    return companies;
  }

  void setDateRange(DateTime? start, DateTime? end) {
    startDateFilter.value = start;
    endDateFilter.value = end;
  }

  // Get expired checks
  List<CheckUsageModel> getExpiredChecks() {
    final now = DateTime.now();
    return activeChecks.where((check) {
      return check.expiryDate != null && now.isAfter(check.expiryDate!);
    }).toList();
  }
}
