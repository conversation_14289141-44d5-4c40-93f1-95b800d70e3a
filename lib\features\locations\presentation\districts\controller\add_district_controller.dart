import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/core/utils/constants/constants.dart';
import 'package:logestics/core/utils/constants/custom_dialogs.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/firebase_service/locations/region_firebase_service.dart';
import 'package:logestics/models/locations/district_model.dart';

import 'dart:developer';

import 'package:logestics/features/locations/domain/usecases/district_use_case/create_district_use_case.dart';
import 'package:logestics/features/locations/presentation/districts/controller/district_list_controller.dart';
import 'package:logestics/models/locations/region_model.dart';

class AddDistrictController extends GetxController {
  final String? currentDistrictName;
  final CreateDistrictUseCase createDistrictUseCase;

  AddDistrictController({
    this.currentDistrictName,
    required this.createDistrictUseCase,
  });

  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  late TextEditingController districtNameController;

  // For Region selection
  final regions = <RegionModel>[].obs;
  final selectedRegion = Rxn<RegionModel>();
  final isLoadingRegions = false.obs;
  final isLoading = false.obs; // Add loading state for create/update operations

  var editMode = false;

  @override
  void onInit() {
    super.onInit();
    districtNameController =
        TextEditingController(text: currentDistrictName ?? '');
    currentDistrictName == null ? editMode = false : editMode = true;
    fetchRegions();
  }

  @override
  void onClose() {
    districtNameController.dispose();
    super.onClose();
  }

  Future<void> fetchRegions() async {
    if (isLoadingRegions.value) return;

    isLoadingRegions.value = true;
    try {
      log('Fetching regions...');
      final regionFirebaseService = RegionFirebaseService();
      final fetchedRegions = await regionFirebaseService.getRegions();
      regions.value = fetchedRegions;
      log('Successfully fetched ${fetchedRegions.length} regions');
    } catch (e) {
      log('Error fetching regions: $e');
      showUnexpectedErrorDialog();
    } finally {
      isLoadingRegions.value = false;
    }
  }

  void setSelectedRegion(RegionModel region) {
    selectedRegion.value = region;
  }

  String? validateDistrictName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'District name is required.';
    }
    return null;
  }

  String? validateRegion(String? value) {
    if (selectedRegion.value == null) {
      return 'Region selection is required.';
    }
    return null;
  }

  Future<void> saveDistrict() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    if (selectedRegion.value == null) {
      showErrorSnackbar('Please select a Region.');
      return;
    }

    var districtName = districtNameController.text.trim();

    await createDistrict(
      district: DistrictModel(
        districtId: '',
        districtName: districtName.capitalizeFirstLetter(),
        regionId: selectedRegion.value!.regionId,
        regionName: selectedRegion.value!.regionName.capitalizeFirstLetter(),
        zoneId: selectedRegion.value!.zoneId,
        zoneName: selectedRegion.value!.zoneName.capitalizeFirstLetter(),
        regionCode: selectedRegion.value!.regionCode,
        createdAt: DateTime.now(),
      ),
    );
  }

  Future<void> createDistrict({required DistrictModel district}) async {
    if (isLoading.value) return;

    isLoading.value = true;
    try {
      log('Creating district: ${district.districtName}');
      final result = await createDistrictUseCase.call(district: district);
      result.fold(
        (failure) {
          log('Failed to create district: ${failure.message}');
          showErrorDialog(failure);
        },
        (success) {
          log('Successfully created district');
          _showSuccessSnackbar(success, district);
        },
      );
    } catch (e) {
      log('Unexpected error creating district: $e');
      showUnexpectedErrorDialog();
    } finally {
      isLoading.value = false;
    }
  }

  // Future<void> updateDistrict({required String DistrictName}) async {
  //   Get.dialog(
  //     const Center(child: CircularProgressIndicator()),
  //     barrierDismissible: false,
  //   );

  //   try {
  //     final result = await firebaseServices.updateDistrict(DistrictName: DistrictName);
  //     Get.back();
  //     result.fold(
  //       (failure) => showErrorDialog(failure),
  //       (success) => _showSuccessSnackbar(success),
  //     );
  //   } catch (e) {
  //     Get.back();
  //     Get.defaultDialog(
  //       title: "Unexpected Error",
  //       middleText: "Something went wrong. Please try again later.",
  //       textConfirm: "OK",
  //       confirmTextColor: Colors.white,
  //       onConfirm: () => Get.back(),
  //     );
  //   }
  // }

  void showErrorSnackbar(String errorMessage) {
    if (errorMessage.isNotEmpty) {
      (failure) => SnackbarUtils.showError(
            "Error",
            errorMessage,
          );
    }
  }

  void _showSuccessSnackbar(SuccessObj success, DistrictModel district) {
    try {
      final districtListController = Get.find<DistrictListController>();
      // Add the new district to the list
      districtListController.addDistrictToList(district);
    } catch (e) {
      log('DistrictListController not found: $e');
    } finally {
      clearControllers();
      Get.back();
      displaySuccessSnackbar(success);
    }
  }

  void clearControllers() {
    districtNameController.clear();
    selectedRegion.value = null;
  }

  // Call this method to refresh regions when returning to the view
  void refreshRegions() {
    fetchRegions();
  }
}
