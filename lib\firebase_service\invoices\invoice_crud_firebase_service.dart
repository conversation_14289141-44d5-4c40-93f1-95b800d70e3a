import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';
import 'package:logestics/models/invoice_model.dart';

class InvoiceCrudFirebaseService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Creates an invoice in Firestore using flat structure.
  Future<void> createInvoiceToFirebase({
    required String uid,
    required InvoiceModel invoice,
  }) async {
    try {
      // Create a reference to the Firestore document in flat structure
      final invoiceRef = _firestore
          .collection(AppCollection.invoicesCollection)
          .doc(invoice.tasNumber);

      // Check if document with this TAS number already exists
      final docSnapshot = await invoiceRef.get();
      if (docSnapshot.exists) {
        throw Exception(
            "Invoice with TAS number ${invoice.tasNumber} already exists.");
      }

      // Add the company ID to the invoice for filtering
      final invoiceWithCompany = InvoiceModel(
        invoiceNumber: invoice.invoiceNumber,
        invoiceStatus: invoice.invoiceStatus,
        tasNumber: invoice.tasNumber,
        productName: invoice.productName,
        numberOfBags: invoice.numberOfBags,
        weightPerBag: invoice.weightPerBag,
        customerName: invoice.customerName,
        truckNumber: invoice.truckNumber,
        conveyNoteNumber: invoice.conveyNoteNumber,
        biltyNumber: invoice.biltyNumber,
        orderDate: invoice.orderDate,
        consignorName: invoice.consignorName,
        deliveryMode: invoice.deliveryMode,
        districtId: invoice.districtId,
        districtName: invoice.districtName,
        stationId: invoice.stationId,
        stationName: invoice.stationName,
        fromPlaceId: invoice.fromPlaceId,
        fromPlaceName: invoice.fromPlaceName,
        distanceInKilometers: invoice.distanceInKilometers,
        consignorPickUpAddress: invoice.consignorPickUpAddress,
        uid: uid, // Use uid instead of user uid
      );

      // Save the document with a timeout of 10 seconds
      await invoiceRef.set(invoiceWithCompany.toJson()).timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw TimeoutException("The operation timed out. Please try again.");
        },
      );

      log(
        "Invoice ${invoice.tasNumber} saved successfully for company $uid.",
      );
    } on SocketException {
      log("No internet connection. Please check your network.");
      rethrow;
    } on TimeoutException {
      log("Timeout now You are on Offline mode");
      rethrow;
    } on FirebaseException catch (e) {
      log("Firebase error: ${e.message}");
      rethrow;
    } catch (e) {
      log("Unexpected error: $e");
      rethrow;
    }
  }

  Future<int> getHighestInvoiceNumber({required String uid}) async {
    try {
      final querySnapshot = await _firestore
          .collection(AppCollection.invoicesCollection)
          .where('uid', isEqualTo: uid) // Filter by company ID
          .orderBy('invoiceNumber', descending: true)
          .limit(1)
          .get();

      if (querySnapshot.docs.isEmpty) {
        return 0; // Start from 1 for the first invoice
      }

      final highestInvoice = querySnapshot.docs.first.data();
      return (highestInvoice['invoiceNumber'] as int?) ?? 0;
    } catch (e) {
      log('Error getting highest invoice number: $e');
      rethrow;
    }
  }

  Stream<List<DocumentChange>> listenToInvoices({required String uid}) {
    return _firestore
        .collection(AppCollection.invoicesCollection)
        .where('uid', isEqualTo: uid) // Filter by company ID
        .orderBy('orderDate', descending: true)
        .snapshots()
        .map(
          (snapshot) => snapshot.docChanges,
        );
  }

  Future<List<InvoiceModel>> getInvoicesForCompany(String uid) async {
    try {
      final querySnapshot = await _firestore
          .collection(AppCollection.invoicesCollection)
          .where('uid', isEqualTo: uid) // Filter by company ID
          .orderBy('orderDate', descending: true)
          .get();

      log('Fetched ${querySnapshot.docs.length} invoices for company $uid');
      return querySnapshot.docs
          .map((doc) => InvoiceModel.fromJson(doc.data()))
          .toList();
    } catch (e) {
      log('Error getting invoices for company: $e');
      rethrow;
    }
  }

  /// Updates an existing invoice in Firestore using flat structure.
  Future<void> updateInvoiceToFirebase({
    required String uid,
    required InvoiceModel invoice,
  }) async {
    try {
      // Ensure the `tasNumber` field exists in the invoice data
      if (invoice.tasNumber.isEmpty) {
        throw ArgumentError('The "tasNumber" field must not be null or empty.');
      }

      // Reference to the invoice document in flat structure
      final invoiceRef = _firestore
          .collection(AppCollection.invoicesCollection)
          .doc(invoice.tasNumber);

      // Check if the invoice belongs to the current company
      final invoiceDoc = await invoiceRef.get();
      if (!invoiceDoc.exists) {
        throw Exception('Invoice not found');
      }

      final invoiceData = invoiceDoc.data() as Map<String, dynamic>;
      if (invoiceData['uid'] != uid) {
        throw Exception('You do not have permission to update this invoice');
      }

      // Preserve the original company ID
      final updatedInvoice = InvoiceModel(
        invoiceNumber: invoice.invoiceNumber,
        invoiceStatus: invoice.invoiceStatus,
        tasNumber: invoice.tasNumber,
        productName: invoice.productName,
        numberOfBags: invoice.numberOfBags,
        weightPerBag: invoice.weightPerBag,
        customerName: invoice.customerName,
        truckNumber: invoice.truckNumber,
        conveyNoteNumber: invoice.conveyNoteNumber,
        biltyNumber: invoice.biltyNumber,
        orderDate: invoice.orderDate,
        consignorName: invoice.consignorName,
        deliveryMode: invoice.deliveryMode,
        districtId: invoice.districtId,
        districtName: invoice.districtName,
        stationId: invoice.stationId,
        stationName: invoice.stationName,
        fromPlaceId: invoice.fromPlaceId,
        fromPlaceName: invoice.fromPlaceName,
        distanceInKilometers: invoice.distanceInKilometers,
        consignorPickUpAddress: invoice.consignorPickUpAddress,
        uid: invoiceData['uid'], // Preserve the original company ID
      );

      // Update the invoice with a timeout of 10 seconds
      await invoiceRef.update(updatedInvoice.toJson()).timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw TimeoutException("Now you are on offline Mode");
        },
      );

      log(
        "Invoice ${invoice.tasNumber} updated successfully for company $uid.",
      );
    } on SocketException {
      log("No internet connection. Please check your network.");
      rethrow;
    } on TimeoutException catch (e) {
      log("Timeout error: ${e.message}");
      rethrow;
    } on FirebaseException catch (e) {
      if (e.code == 'invoice-not-found') {
        log("Invoice not found: ${e.message}");
      } else {
        log("Firebase error: ${e.message}");
      }
      rethrow;
    } on ArgumentError catch (e) {
      log("Argument error: ${e.message}");
      rethrow;
    } catch (e) {
      log("Unexpected error: $e");
      rethrow;
    }
  }

  /// Deletes an existing invoice in Firestore using flat structure.
  Future<void> deleteInvoiceFromFirebase({
    required String uid,
    required String tasNumber,
  }) async {
    try {
      // Ensure the `tasNumber` is not null or empty
      if (tasNumber.isEmpty) {
        throw ArgumentError('The "tasNumber" field must not be null or empty.');
      }

      // Reference to the invoice document in flat structure
      final invoiceRef = _firestore
          .collection(AppCollection.invoicesCollection)
          .doc(tasNumber);

      // Check if the invoice belongs to the current company
      final invoiceDoc = await invoiceRef.get();
      if (!invoiceDoc.exists) {
        throw Exception('Invoice not found');
      }

      final invoiceData = invoiceDoc.data() as Map<String, dynamic>;
      if (invoiceData['uid'] != uid) {
        throw Exception('You do not have permission to delete this invoice');
      }

      // Delete the invoice with a timeout of 10 seconds
      await invoiceRef.delete().timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw TimeoutException("The operation timed out. Please try again.");
        },
      );

      log(
        "Invoice $tasNumber deleted successfully for company $uid.",
      );
    } on SocketException {
      log("No internet connection. Please check your network.");
      rethrow;
    } on TimeoutException catch (e) {
      log("Timeout error: ${e.message}");
      throw Exception('TimeOut you are on offline mode ');
    } on FirebaseException catch (e) {
      if (e.code == 'not-found') {
        log("Invoice not found: ${e.message}");
      } else {
        log("Firebase error: ${e.message}");
      }
      rethrow;
    } on ArgumentError catch (e) {
      log("Argument error: ${e.message}");
      rethrow;
    } catch (e) {
      log("Unexpected error: $e");
      rethrow;
    }
  }

  Future<void> updateInvoiceStatus({
    required String invoiceNumber,
    required String uid,
    required String newStatus,
  }) async {
    try {
      // Validate inputs
      if (invoiceNumber.isEmpty) {
        throw ArgumentError(
            'The "invoiceNumber" field must not be null or empty.');
      }

      if (uid.isEmpty) {
        throw ArgumentError('The "uid" field must not be null or empty.');
      }

      if (newStatus.isEmpty) {
        throw ArgumentError('The "newStatus" field must not be null or empty.');
      }

      // Reference to the invoice document in flat structure
      final invoiceRef = _firestore
          .collection(AppCollection.invoicesCollection)
          .doc(invoiceNumber);

      // Check if the invoice exists and belongs to the current company
      final invoiceDoc = await invoiceRef.get();
      if (!invoiceDoc.exists) {
        throw Exception('Invoice not found');
      }

      final invoiceData = invoiceDoc.data() as Map<String, dynamic>;
      if (invoiceData['uid'] != uid) {
        throw Exception('You do not have permission to update this invoice');
      }

      // Update the invoice status with a timeout of 10 seconds
      await invoiceRef.update({'invoiceStatus': newStatus}).timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw TimeoutException("The operation timed out. Please try again.");
        },
      );

      log(
        "Invoice status for $invoiceNumber updated successfully to $newStatus for company $uid.",
      );
    } on SocketException {
      log("No internet connection. Please check your network.");
      rethrow;
    } on TimeoutException {
      log("Timeout error: You are on offline mode");
      throw Exception('TimeOut you are on offline mode');
    } on FirebaseException catch (e) {
      if (e.code == 'not-found') {
        log("Invoice not found: ${e.message}");
      } else {
        log("Firebase error: ${e.message}");
      }
      rethrow;
    } on ArgumentError catch (e) {
      log("Argument error: ${e.message}");
      rethrow;
    } catch (e) {
      log("Unexpected error: $e");
      throw Exception('Failed to update invoice status: $e');
    }
  }
}
