import 'dart:developer';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../../core/utils/snackbar_utils.dart';
import '../../../../../models/accounting/financial_report_models.dart';
import '../../../../../services/financial_report_export_service.dart';
import '../../repositories/profit_loss_repository.dart';

/// GetX Controller for Profit & Loss report management
class ProfitLossController extends GetxController {
  final ProfitLossRepository _repository = ProfitLossRepository();
  final FinancialReportExportService exportService =
      FinancialReportExportService();

  // Form controllers
  final startDateController = TextEditingController();
  final endDateController = TextEditingController();
  final companyNameController = TextEditingController();

  // Reactive variables
  final _isLoading = false.obs;
  final _currentReport = Rxn<ProfitLossReport>();
  final _savedReports = <ProfitLossReport>[].obs;
  final _includeInactiveAccounts = false.obs;
  final _includeZeroBalances = false.obs;

  // Form validation
  final formKey = GlobalKey<FormState>();

  // Getters
  bool get isLoading => _isLoading.value;
  ProfitLossReport? get currentReport => _currentReport.value;
  List<ProfitLossReport> get savedReports => _savedReports;
  bool get includeInactiveAccounts => _includeInactiveAccounts.value;
  bool get includeZeroBalances => _includeZeroBalances.value;

  // Computed properties
  bool get hasReport => _currentReport.value != null;
  bool get hasRevenueData =>
      _currentReport.value?.revenueGroups.isNotEmpty ?? false;
  bool get hasExpenseData =>
      _currentReport.value?.expenseGroups.isNotEmpty ?? false;
  bool get isProfitable => _currentReport.value?.isProfitable ?? false;
  double get netMargin => _currentReport.value?.netMargin ?? 0.0;

  @override
  void onInit() {
    super.onInit();
    _initializeForm();
    loadSavedReports();
  }

  @override
  void onClose() {
    startDateController.dispose();
    endDateController.dispose();
    companyNameController.dispose();
    super.onClose();
  }

  /// Initialize form with default values
  void _initializeForm() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);

    startDateController.text = _formatDate(startOfMonth);
    endDateController.text = _formatDate(endOfMonth);
    companyNameController.text =
        'Your Company Name'; // TODO: Get from user settings
  }

  /// Format date for display
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  /// Parse date from string
  DateTime? _parseDate(String dateStr) {
    try {
      final parts = dateStr.split('/');
      if (parts.length == 3) {
        final day = int.parse(parts[0]);
        final month = int.parse(parts[1]);
        final year = int.parse(parts[2]);
        return DateTime(year, month, day);
      }
    } catch (e) {
      log('Error parsing date: $e');
    }
    return null;
  }

  /// Toggle include inactive accounts
  void toggleIncludeInactiveAccounts() {
    _includeInactiveAccounts.value = !_includeInactiveAccounts.value;
  }

  /// Toggle include zero balances
  void toggleIncludeZeroBalances() {
    _includeZeroBalances.value = !_includeZeroBalances.value;
  }

  /// Generate Profit & Loss report
  Future<void> generateReport() async {
    if (!formKey.currentState!.validate()) {
      SnackbarUtils.showError('Error', 'Please fix form errors');
      return;
    }

    try {
      _isLoading.value = true;

      final startDate = _parseDate(startDateController.text);
      final endDate = _parseDate(endDateController.text);

      if (startDate == null || endDate == null) {
        SnackbarUtils.showError('Error', 'Invalid date format');
        return;
      }

      log('Generating P&L report from $startDate to $endDate');

      final result = await _repository.generateProfitLoss(
        startDate: startDate,
        endDate: endDate,
        companyName: companyNameController.text.trim(),
        includeInactiveAccounts: _includeInactiveAccounts.value,
        includeZeroBalances: _includeZeroBalances.value,
      );

      result.fold(
        (failure) {
          log('P&L generation failed: ${failure.message}');
          SnackbarUtils.showError('Error', failure.message);
        },
        (report) {
          log('P&L generated successfully');
          _currentReport.value = report;
          SnackbarUtils.showSuccess(
              'Success', 'P&L report generated successfully');
        },
      );
    } catch (e) {
      log('Error generating P&L: $e');
      SnackbarUtils.showError('Error', 'Failed to generate P&L report');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Save current report
  Future<void> saveReport() async {
    if (_currentReport.value == null) {
      SnackbarUtils.showError('Error', 'No report to save');
      return;
    }

    try {
      _isLoading.value = true;

      final result =
          await _repository.saveProfitLossReport(_currentReport.value!);

      result.fold(
        (failure) {
          log('P&L save failed: ${failure.message}');
          SnackbarUtils.showError('Error', failure.message);
        },
        (success) {
          log('P&L saved successfully');
          SnackbarUtils.showSuccess('Success', 'P&L report saved successfully');
          loadSavedReports(); // Refresh saved reports list
        },
      );
    } catch (e) {
      log('Error saving P&L: $e');
      SnackbarUtils.showError('Error', 'Failed to save P&L report');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Load saved reports
  Future<void> loadSavedReports() async {
    try {
      final result = await _repository.getSavedProfitLossReports();

      result.fold(
        (failure) {
          log('Failed to load saved P&L reports: ${failure.message}');
        },
        (reports) {
          log('Loaded ${reports.length} saved P&L reports');
          _savedReports.value = reports;
        },
      );
    } catch (e) {
      log('Error loading saved P&L reports: $e');
    }
  }

  /// Load specific report
  Future<void> loadReport(String reportId) async {
    try {
      _isLoading.value = true;

      final result = await _repository.getProfitLossReport(reportId);

      result.fold(
        (failure) {
          log('Failed to load P&L report: ${failure.message}');
          SnackbarUtils.showError('Error', failure.message);
        },
        (report) {
          log('P&L report loaded successfully');
          _currentReport.value = report;

          // Update form with loaded report data
          startDateController.text = _formatDate(report.startDate);
          endDateController.text = _formatDate(report.endDate);
          companyNameController.text = report.companyName;

          SnackbarUtils.showSuccess(
              'Success', 'P&L report loaded successfully');
        },
      );
    } catch (e) {
      log('Error loading P&L report: $e');
      SnackbarUtils.showError('Error', 'Failed to load P&L report');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Delete report
  Future<void> deleteReport(String reportId) async {
    try {
      _isLoading.value = true;

      final result = await _repository.deleteProfitLossReport(reportId);

      result.fold(
        (failure) {
          log('Failed to delete P&L report: ${failure.message}');
          SnackbarUtils.showError('Error', failure.message);
        },
        (success) {
          log('P&L report deleted successfully');
          SnackbarUtils.showSuccess(
              'Success', 'P&L report deleted successfully');
          loadSavedReports(); // Refresh saved reports list

          // Clear current report if it was deleted
          if (_currentReport.value?.reportId == reportId) {
            _currentReport.value = null;
          }
        },
      );
    } catch (e) {
      log('Error deleting P&L report: $e');
      SnackbarUtils.showError('Error', 'Failed to delete P&L report');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Clear current report
  void clearReport() {
    _currentReport.value = null;
    _initializeForm();
  }

  /// Validate date field
  String? validateDate(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Date is required';
    }

    final date = _parseDate(value);
    if (date == null) {
      return 'Invalid date format (DD/MM/YYYY)';
    }

    return null;
  }

  /// Validate company name
  String? validateCompanyName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Company name is required';
    }

    if (value.trim().length < 2) {
      return 'Company name must be at least 2 characters';
    }

    return null;
  }

  /// Set loading state
  void setLoading(bool loading) {
    _isLoading.value = loading;
  }

  /// Export current report to PDF
  Future<Uint8List> exportToPDF() async {
    if (_currentReport.value == null) {
      throw Exception('No report available to export');
    }

    return await exportService.generateProfitLossPDF(_currentReport.value!);
  }

  /// Export current report to Excel
  Future<void> exportToExcel() async {
    if (_currentReport.value == null) {
      throw Exception('No report available to export');
    }

    await exportService.generateProfitLossExcel(_currentReport.value!);
  }
}
