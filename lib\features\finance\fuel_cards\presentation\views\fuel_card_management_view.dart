import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/core/utils/widgets/my_text_field.dart';
import 'package:logestics/features/finance/fuel_cards/presentation/controllers/fuel_card_controller.dart';
import 'package:logestics/features/finance/fuel_cards/presentation/views/fuel_usage_history_view.dart';
import 'package:logestics/main.dart';
import 'package:logestics/models/finance/fuel_card_model.dart';
import 'package:provider/provider.dart';

class FuelCardManagementView extends StatelessWidget {
  const FuelCardManagementView({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<FuelCardController>();
    notifier = Provider.of(context, listen: false);

    // Call loadFuelCards when view is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.loadFuelCards();
    });

    return SizedBox(
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Title and actions row
          Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Fuel Card Management',
                  style: AppTextStyles.voucherTitleStyle.copyWith(
                    color: notifier.text,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: () => controller.loadFuelCards(),
                  tooltip: 'Refresh',
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),

          // Main content
          Flexible(
            fit: FlexFit.loose,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildHeader('Fuel Cards'),
                  const SizedBox(height: 16),
                  _buildActionButtons(context, controller),
                  const SizedBox(height: 16),
                  Obx(() {
                    if (controller.isLoading.value) {
                      return const Center(child: CircularProgressIndicator());
                    }
                    if (controller.error.isNotEmpty) {
                      return Center(
                        child: Text(
                          controller.error.value,
                          style: const TextStyle(color: Colors.red),
                        ),
                      );
                    }
                    if (controller.fuelCards.isEmpty) {
                      return Container(
                        padding: const EdgeInsets.all(32),
                        alignment: Alignment.center,
                        child: Text(
                          'No fuel cards found.',
                          style: TextStyle(color: notifier.text),
                        ),
                      );
                    }
                    return _buildFuelCardsList(controller);
                  }),
                  const SizedBox(height: 32),
                  _buildHeader('Fuel Rates'),
                  const SizedBox(height: 16),
                  _buildRatesSection(controller),

                  // Add FAB action as a normal button since we can't use FAB in a nested content view
                  const SizedBox(height: 32),
                  Align(
                    alignment: Alignment.centerRight,
                    child: ElevatedButton.icon(
                      icon: const Icon(Icons.add),
                      label: const Text('Add Fuel Card'),
                      onPressed: () =>
                          _showAddFuelCardDialog(context, controller),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(String title) {
    return Text(
      title,
      style: AppTextStyles.voucherTitleStyle.copyWith(
        fontSize: 20,
        color: notifier.text,
      ),
    );
  }

  Widget _buildActionButtons(
      BuildContext context, FuelCardController controller) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        OutlinedButton.icon(
          icon: const Icon(Icons.history),
          label: const Text('Usage History'),
          onPressed: () => _showUsageHistoryView(context),
          style: OutlinedButton.styleFrom(
            foregroundColor: Colors.green,
          ),
        ),
        const SizedBox(width: 8),
        OutlinedButton.icon(
          icon: const Icon(Icons.add_chart),
          label: const Text('Add Rate'),
          onPressed: () => _showAddRateDialog(context, controller),
          style: OutlinedButton.styleFrom(
            foregroundColor: Colors.blue,
          ),
        ),
      ],
    );
  }

  Widget _buildFuelCardsList(FuelCardController controller) {
    // Format number with commas
    final formatter = NumberFormat('#,##0.00', 'en_US');

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: controller.fuelCards.length,
      itemBuilder: (context, index) {
        final card = controller.fuelCards[index];
        final remainingPercentage = card.remainingCapacity / card.totalCapacity;

        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            card.companyName,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Card #: ${card.cardNumber}',
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Text(
                      'PKR ${formatter.format(card.currentRate)}/L',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  'Fuel Capacity',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: notifier.text,
                  ),
                ),
                const SizedBox(height: 8),
                Stack(
                  children: [
                    Container(
                      height: 10,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade300,
                        borderRadius: BorderRadius.circular(5),
                      ),
                    ),
                    Container(
                      height: 10,
                      width: remainingPercentage *
                          MediaQuery.of(context).size.width *
                          0.8,
                      decoration: BoxDecoration(
                        color: remainingPercentage > 0.2
                            ? Colors.green
                            : Colors.red,
                        borderRadius: BorderRadius.circular(5),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '${formatter.format(card.remainingCapacity)} L remaining',
                      style: TextStyle(
                        fontSize: 14,
                        color: remainingPercentage > 0.2
                            ? Colors.green
                            : Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'of ${formatter.format(card.totalCapacity)} L',
                      style: TextStyle(
                        fontSize: 14,
                        color: notifier.text.withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Align(
                  alignment: Alignment.centerRight,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      OutlinedButton.icon(
                        icon: const Icon(Icons.edit_note, size: 18),
                        label: const Text('Update Rate'),
                        onPressed: () =>
                            _showUpdateRateDialog(context, controller, card),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.blue,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 0),
                          visualDensity: VisualDensity.compact,
                        ),
                      ),
                      const SizedBox(width: 8),
                      OutlinedButton.icon(
                        icon: const Icon(Icons.history, size: 18),
                        label: const Text('Usage'),
                        onPressed: () =>
                            _showUsageHistoryForCard(context, card),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.orange,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 0),
                          visualDensity: VisualDensity.compact,
                        ),
                      ),
                      const SizedBox(width: 8),
                      OutlinedButton.icon(
                        icon: const Icon(Icons.local_gas_station, size: 18),
                        label: const Text('Refill'),
                        onPressed: () =>
                            _showRefillDialog(context, controller, card),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.green,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 0),
                          visualDensity: VisualDensity.compact,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildRatesSection(FuelCardController controller) {
    return Obx(() {
      // Group fuel cards by company
      final companies =
          controller.fuelCards.map((card) => card.companyName).toSet().toList();

      return companies.isEmpty
          ? Center(
              child: Text(
                'No companies found.',
                style: TextStyle(color: notifier.text),
              ),
            )
          : ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: companies.length,
              itemBuilder: (context, index) {
                final companyName = companies[index];

                // Find the current rate for this company
                final latestRate = controller.fuelCards
                    .where((card) => card.companyName == companyName)
                    .map((card) => card.currentRate)
                    .first;

                return Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  child: ListTile(
                    title: Text(
                      companyName,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Text(
                      'Current Rate: PKR ${latestRate.toStringAsFixed(2)}/L',
                      style: const TextStyle(
                        color: Colors.green,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          icon: const Icon(Icons.history),
                          onPressed: () {
                            // Show rate history dialog
                            _showRateHistoryDialog(
                                context, controller, companyName);
                          },
                          tooltip: 'Rate History',
                        ),
                        IconButton(
                          icon: const Icon(Icons.edit),
                          onPressed: () {
                            // Show update rate dialog
                            _showUpdateRateDialog(
                                context,
                                controller,
                                controller.fuelCards.firstWhere(
                                    (card) => card.companyName == companyName));
                          },
                          tooltip: 'Update Rate',
                        ),
                      ],
                    ),
                  ),
                );
              },
            );
    });
  }

  void _showAddFuelCardDialog(
      BuildContext context, FuelCardController controller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add New Fuel Card'),
        content: Form(
          key: controller.fuelCardFormKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                MyTextFormField(
                  controller: controller.cardNumberController,
                  titleText: 'Card Number',
                  labelText: 'Card Number',
                  hintText: 'Enter card number',
                  validator: controller.validateCardNumber,
                ),
                const SizedBox(height: 16),
                MyTextFormField(
                  controller: controller.companyNameController,
                  titleText: 'Company Name',
                  labelText: 'Company Name',
                  hintText: 'Enter company name',
                  validator: controller.validateCompanyName,
                ),
                const SizedBox(height: 16),
                MyTextFormField(
                  controller: controller.totalCapacityController,
                  titleText: 'Total Capacity (Liters)',
                  labelText: 'Total Capacity (Liters)',
                  hintText: 'Enter total capacity',
                  keyboardType: TextInputType.number,
                  validator: controller.validateTotalCapacity,
                ),
                const SizedBox(height: 16),
                MyTextFormField(
                  controller: controller.currentRateController,
                  titleText: 'Current Rate (PKR per Liter)',
                  labelText: 'Current Rate (PKR per Liter)',
                  hintText: 'Enter current rate',
                  keyboardType: TextInputType.number,
                  validator: controller.validateCurrentRate,
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('CANCEL'),
          ),
          Obx(() => ElevatedButton(
                onPressed: controller.isLoading.value
                    ? null
                    : () {
                        controller.createFuelCard();
                      },
                child: controller.isLoading.value
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Text('SAVE'),
              )),
        ],
      ),
    );
  }

  void _showAddRateDialog(BuildContext context, FuelCardController controller) {
    final rateController = TextEditingController();
    final selectedCompany = ''.obs;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add New Rate'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                labelText: 'Company',
                border: OutlineInputBorder(),
              ),
              items: controller.fuelCards
                  .map((card) => DropdownMenuItem(
                        value: card.companyName,
                        child: Text(card.companyName),
                      ))
                  .toList(),
              onChanged: (value) {
                if (value != null) {
                  selectedCompany.value = value;
                }
              },
            ),
            const SizedBox(height: 16),
            TextField(
              controller: rateController,
              decoration: const InputDecoration(
                labelText: 'Rate (PKR per Liter)',
                hintText: 'Enter rate',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('CANCEL'),
          ),
          Obx(() => ElevatedButton(
                onPressed: controller.isLoading.value
                    ? null
                    : () {
                        if (rateController.text.isNotEmpty) {
                          final rate =
                              double.tryParse(rateController.text) ?? 0;
                          if (rate > 0) {
                            controller
                                .addFuelRate(selectedCompany.value, rate)
                                .then((_) {
                              // Force UI update after adding rate
                              controller.loadFuelCards();
                            });
                            Navigator.pop(context);
                          } else {
                            SnackbarUtils.showError(AppStrings.error,
                                'Rate must be greater than 0');
                          }
                        } else {
                          SnackbarUtils.showError(
                              AppStrings.error, 'Rate is required');
                        }
                      },
                child: controller.isLoading.value
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Text('SAVE'),
              )),
        ],
      ),
    );
  }

  void _showUpdateRateDialog(
      BuildContext context, FuelCardController controller, FuelCardModel card) {
    final rateController =
        TextEditingController(text: card.currentRate.toString());

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Update Rate for ${card.companyName}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: rateController,
              decoration: const InputDecoration(
                labelText: 'New Rate (PKR per Liter)',
                hintText: 'Enter new rate',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('CANCEL'),
          ),
          Obx(() => ElevatedButton(
                onPressed: controller.isLoading.value
                    ? null
                    : () {
                        if (rateController.text.isNotEmpty) {
                          final rate =
                              double.tryParse(rateController.text) ?? 0;
                          if (rate > 0) {
                            controller
                                .addFuelRate(card.companyName, rate)
                                .then((_) {
                              // Force UI update after adding rate
                              controller.loadFuelCards();
                            });
                            Navigator.pop(context);
                          } else {
                            SnackbarUtils.showError(AppStrings.error,
                                'Rate must be greater than 0');
                          }
                        } else {
                          SnackbarUtils.showError(
                              AppStrings.error, 'Rate is required');
                        }
                      },
                child: controller.isLoading.value
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Text('UPDATE'),
              )),
        ],
      ),
    );
  }

  void _showRefillDialog(
      BuildContext context, FuelCardController controller, FuelCardModel card) {
    final refillController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Refill Card ${card.cardNumber}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Current Capacity: ${card.remainingCapacity.toStringAsFixed(2)} L / ${card.totalCapacity.toStringAsFixed(2)} L',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: refillController,
              decoration: const InputDecoration(
                labelText: 'Refill Amount (Liters)',
                hintText: 'Enter liters to add',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('CANCEL'),
          ),
          ElevatedButton(
            onPressed: () {
              if (refillController.text.isNotEmpty) {
                final liters = double.tryParse(refillController.text) ?? 0;
                if (liters > 0) {
                  final updatedCard = card.copyWith(
                    remainingCapacity: card.remainingCapacity + liters,
                    updatedAt: DateTime.now(),
                  );
                  controller.repository
                      .updateFuelCard(updatedCard)
                      .then((result) {
                    result.fold(
                      (failure) {
                        SnackbarUtils.showError(
                            AppStrings.error, failure.message);
                      },
                      (success) {
                        // Update local list
                        final index = controller.fuelCards
                            .indexWhere((c) => c.id == card.id);
                        if (index != -1) {
                          controller.fuelCards[index] = updatedCard;
                          controller.update();
                        }
                        SnackbarUtils.showSuccess(
                            AppStrings.success, 'Card refilled successfully');
                      },
                    );
                    // ignore: use_build_context_synchronously
                    Navigator.pop(context);
                  });
                } else {
                  SnackbarUtils.showError(
                      AppStrings.error, 'Liters must be greater than 0');
                }
              } else {
                SnackbarUtils.showError(
                    AppStrings.error, 'Refill amount is required');
              }
            },
            child: const Text('REFILL'),
          ),
        ],
      ),
    );
  }

  void _showRateHistoryDialog(
      BuildContext context, FuelCardController controller, String companyName) {
    // Fetch rate history before showing dialog
    controller.getFuelRateHistory(companyName);

    showDialog(
      context: context,
      builder: (context) => Obx(() {
        if (controller.isRateHistoryLoading.value) {
          return const Dialog(
            child: Padding(
              padding: EdgeInsets.all(20.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Loading rate history...'),
                ],
              ),
            ),
          );
        }

        if (controller.error.isNotEmpty) {
          return AlertDialog(
            title: const Text(AppStrings.errorS),
            content:
                Text('Failed to load rate history: ${controller.error.value}'),
            actions: [
              TextButton(
                onPressed: () {
                  controller.error.value = '';
                  Navigator.pop(context);
                },
                child: const Text('CLOSE'),
              ),
            ],
          );
        }

        final rates = controller.rateHistory;

        return Dialog(
          child: Container(
            width: double.infinity,
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.6,
              maxWidth: 600,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    '$companyName - Rate History',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                const Divider(height: 1),
                if (rates.isEmpty)
                  const Padding(
                    padding: EdgeInsets.all(24.0),
                    child: Center(
                      child: Text('No rate history available'),
                    ),
                  )
                else
                  Flexible(
                    child: Column(
                      children: [
                        // Header row
                        Container(
                          color: Theme.of(context).primaryColor.withAlpha(20),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16.0,
                            vertical: 12.0,
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                flex: 2,
                                child: Text(
                                  'Date',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Theme.of(context).primaryColor,
                                  ),
                                ),
                              ),
                              Expanded(
                                flex: 2,
                                child: Text(
                                  'Rate (PKR)',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Theme.of(context).primaryColor,
                                  ),
                                ),
                              ),
                              Expanded(
                                flex: 1,
                                child: Text(
                                  'Status',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Theme.of(context).primaryColor,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        // List of rates
                        Expanded(
                          child: ListView.separated(
                            itemCount: rates.length,
                            separatorBuilder: (_, __) =>
                                const Divider(height: 1),
                            itemBuilder: (context, index) {
                              final rate = rates[index];
                              final formattedDate =
                                  DateFormat('dd MMM yyyy, HH:mm')
                                      .format(rate.effectiveDate);

                              // Calculate change from previous rate
                              String changeText = '';
                              if (index < rates.length - 1) {
                                final prevRate = rates[index + 1].rate;
                                final change = rate.rate - prevRate;
                                final percentChange = (change / prevRate) * 100;

                                changeText = change > 0
                                    ? '+${change.toStringAsFixed(2)} (${percentChange.toStringAsFixed(1)}%)'
                                    : '${change.toStringAsFixed(2)} (${percentChange.toStringAsFixed(1)}%)';
                              }

                              return Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16.0,
                                  vertical: 12.0,
                                ),
                                child: Row(
                                  children: [
                                    Expanded(
                                      flex: 2,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            formattedDate,
                                            style: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          if (changeText.isNotEmpty)
                                            Text(
                                              changeText,
                                              style: TextStyle(
                                                fontSize: 12,
                                                color:
                                                    changeText.startsWith('+')
                                                        ? Colors.green
                                                        : Colors.red,
                                              ),
                                            ),
                                        ],
                                      ),
                                    ),
                                    Expanded(
                                      flex: 2,
                                      child: Text(
                                        '${rate.rate.toStringAsFixed(2)}/L',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          color: Theme.of(context).primaryColor,
                                        ),
                                      ),
                                    ),
                                    Expanded(
                                      flex: 1,
                                      child: index == 0
                                          ? Container(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                horizontal: 8,
                                                vertical: 4,
                                              ),
                                              decoration: BoxDecoration(
                                                color: Colors.green,
                                                borderRadius:
                                                    BorderRadius.circular(12),
                                              ),
                                              child: const Text(
                                                'Current',
                                                style: TextStyle(
                                                  color: Colors.white,
                                                  fontSize: 12,
                                                ),
                                                textAlign: TextAlign.center,
                                              ),
                                            )
                                          : const SizedBox(),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                const Divider(height: 1),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('CLOSE'),
                  ),
                ),
              ],
            ),
          ),
        );
      }),
    );
  }

  // Show usage history for all fuel cards
  void _showUsageHistoryView(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const FuelUsageHistoryView(),
      ),
    );
  }

  // Show usage history for a specific fuel card
  void _showUsageHistoryForCard(BuildContext context, FuelCardModel card) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FuelUsageHistoryView(
          fuelCardId: card.id,
          fuelCardNumber: card.cardNumber,
        ),
      ),
    );
  }
}
