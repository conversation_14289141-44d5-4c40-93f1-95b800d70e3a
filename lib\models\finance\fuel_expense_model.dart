
class FuelExpenseModel {
  final String id;
  final String tripId; // Associated trip/freight ID
  final String fuelCardId; // ID of the fuel card used
  final String fuelCardNumber; // Card number for display
  final String fuelStationName; // PSO, Shell, etc.
  final double fuelAmount; // Amount of fuel in liters
  final double fuelRate; // Rate per liter
  final double fuelCost; // Total cost (amount * rate)
  final double freightAmount; // Total freight amount for this trip
  final double otherExpenses; // Other expenses for this trip
  final double profitOrLoss; // Calculated profit or loss
  final DateTime expenseDate; // When the expense occurred
  final DateTime createdAt; // When the record was created
  final DateTime? updatedAt; // Last update time

  FuelExpenseModel({
    required this.id,
    required this.tripId,
    required this.fuelCardId,
    required this.fuelCardNumber,
    required this.fuelStationName,
    required this.fuelAmount,
    required this.fuelRate,
    required this.fuelCost,
    required this.freightAmount,
    this.otherExpenses = 0.0,
    required this.profitOrLoss,
    required this.expenseDate,
    required this.createdAt,
    this.updatedAt,
  });

  // Create from Firestore map
  factory FuelExpenseModel.fromMap(Map<String, dynamic> map) {
    return FuelExpenseModel(
      id: map['id'] ?? '',
      tripId: map['tripId'] ?? '',
      fuelCardId: map['fuelCardId'] ?? '',
      fuelCardNumber: map['fuelCardNumber'] ?? '',
      fuelStationName: map['fuelStationName'] ?? '',
      fuelAmount: (map['fuelAmount'] ?? 0.0).toDouble(),
      fuelRate: (map['fuelRate'] ?? 0.0).toDouble(),
      fuelCost: (map['fuelCost'] ?? 0.0).toDouble(),
      freightAmount: (map['freightAmount'] ?? 0.0).toDouble(),
      otherExpenses: (map['otherExpenses'] ?? 0.0).toDouble(),
      profitOrLoss: (map['profitOrLoss'] ?? 0.0).toDouble(),
      expenseDate: (map['expenseDate'] as DateTime),
      createdAt: (map['createdAt'] as DateTime),
      updatedAt:
          map['updatedAt'] != null ? (map['updatedAt'] as DateTime) : null,
    );
  }

  // Convert to Firestore map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'tripId': tripId,
      'fuelCardId': fuelCardId,
      'fuelCardNumber': fuelCardNumber,
      'fuelStationName': fuelStationName,
      'fuelAmount': fuelAmount,
      'fuelRate': fuelRate,
      'fuelCost': fuelCost,
      'freightAmount': freightAmount,
      'otherExpenses': otherExpenses,
      'profitOrLoss': profitOrLoss,
      'expenseDate': expenseDate,
      'createdAt': createdAt,
      'updatedAt': updatedAt ?? DateTime.now(),
    };
  }

  // Calculate profit or loss
  static double calculateProfitOrLoss(
      double freightAmount, double fuelCost, double otherExpenses) {
    return freightAmount - fuelCost - otherExpenses;
  }

  // Create a copy with updated fields
  FuelExpenseModel copyWith({
    String? id,
    String? tripId,
    String? fuelCardId,
    String? fuelCardNumber,
    String? fuelStationName,
    double? fuelAmount,
    double? fuelRate,
    double? fuelCost,
    double? freightAmount,
    double? otherExpenses,
    double? profitOrLoss,
    DateTime? expenseDate,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return FuelExpenseModel(
      id: id ?? this.id,
      tripId: tripId ?? this.tripId,
      fuelCardId: fuelCardId ?? this.fuelCardId,
      fuelCardNumber: fuelCardNumber ?? this.fuelCardNumber,
      fuelStationName: fuelStationName ?? this.fuelStationName,
      fuelAmount: fuelAmount ?? this.fuelAmount,
      fuelRate: fuelRate ?? this.fuelRate,
      fuelCost: fuelCost ?? this.fuelCost,
      freightAmount: freightAmount ?? this.freightAmount,
      otherExpenses: otherExpenses ?? this.otherExpenses,
      profitOrLoss: profitOrLoss ?? this.profitOrLoss,
      expenseDate: expenseDate ?? this.expenseDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Recalculate fuel cost and profit/loss based on changes to fuel amount or rate
  FuelExpenseModel recalculate({
    double? newFuelAmount,
    double? newFuelRate,
    double? newFreightAmount,
    double? newOtherExpenses,
  }) {
    final amount = newFuelAmount ?? fuelAmount;
    final rate = newFuelRate ?? fuelRate;
    final freight = newFreightAmount ?? freightAmount;
    final expenses = newOtherExpenses ?? otherExpenses;

    final cost = amount * rate;
    final profit = calculateProfitOrLoss(freight, cost, expenses);

    return copyWith(
      fuelAmount: amount,
      fuelRate: rate,
      fuelCost: cost,
      freightAmount: freight,
      otherExpenses: expenses,
      profitOrLoss: profit,
      updatedAt: DateTime.now(),
    );
  }
}
