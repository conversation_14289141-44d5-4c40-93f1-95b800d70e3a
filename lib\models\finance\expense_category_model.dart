class ExpenseCategoryModel {
  final String id;
  final String name;
  final String description;
  final DateTime createdAt;
  final String uid;

  const ExpenseCategoryModel({
    required this.id,
    required this.name,
    this.description = '',
    required this.createdAt,
    this.uid = '',
  });

  factory ExpenseCategoryModel.fromJson(Map<String, dynamic> json) {
    final createdAt = json['createdAt'];
    return ExpenseCategoryModel(
      id: json['id']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
      description: json['description']?.toString() ?? '',
      createdAt: createdAt == null
          ? DateTime.now()
          : createdAt is String
              ? DateTime.parse(createdAt)
              : DateTime.fromMillisecondsSinceEpoch(createdAt as int),
      uid: json['uid']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'description': description,
        'createdAt': createdAt.millisecondsSinceEpoch,
        'uid': uid,
      };

  ExpenseCategoryModel copyWith({
    String? id,
    String? name,
    String? description,
    DateTime? createdAt,
    String? uid,
  }) =>
      ExpenseCategoryModel(
        id: id ?? this.id,
        name: name ?? this.name,
        description: description ?? this.description,
        createdAt: createdAt ?? this.createdAt,
        uid: uid ?? this.uid,
      );

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ExpenseCategoryModel &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          name == other.name &&
          description == other.description &&
          createdAt == other.createdAt &&
          uid == other.uid;

  @override
  int get hashCode => Object.hash(id, name, description, createdAt, uid);
}
