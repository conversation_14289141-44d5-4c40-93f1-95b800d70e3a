import 'package:either_dart/either.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/models/finance/loan_model.dart';
import 'package:logestics/features/finance/loans/repositories/loan_repository.dart';

class RequestLoanUseCase {
  final LoanRepository _loanRepository;

  RequestLoanUseCase(this._loanRepository);

  Future<Either<FailureObj, SuccessObj>> execute(LoanModel loan) {
    return _loanRepository.requestLoan(loan);
  }
}
