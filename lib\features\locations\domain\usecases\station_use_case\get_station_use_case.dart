import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/models/locations/station_model.dart';
import 'package:logestics/features/locations/repositories/station_repository.dart';

class GetStationByDistrictIdUseCase {
  final StationRepository repository;

  GetStationByDistrictIdUseCase(this.repository);

  /// Executes the use case to get all Stations.
  Future<Either<FailureObj, List<StationModel>>> call(
      {required String districtId}) async {
    try {
      return await repository.getStationsByDistrict(districtId);
    } catch (e) {
      return Left(FailureObj(
        code: 'unexpected-error',
        message: 'An unexpected error occurred.',
      ));
    }
  }
}
