import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/constants/custom_dialogs.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/core/utils/mixins/pagination_mixin.dart';
import 'package:logestics/models/locations/from_place_model.dart';
import 'package:logestics/models/locations/station_model.dart';
import 'package:logestics/features/locations/domain/usecases/station_use_case/get_stations_use_case.dart';
import 'package:logestics/features/locations/domain/usecases/station_use_case/delete_station_use_case.dart';

class StationListController extends GetxController with PaginationMixin {
  final GetStationsUseCase getStationsUseCase;
  final DeleteStationUseCase deleteStationUseCase;

  StationListController({
    required this.getStationsUseCase,
    required this.deleteStationUseCase,
  });

  var stations = <StationModel>[].obs;
  var filteredStations = <StationModel>[].obs;
  var isLoading = false.obs;
  final searchQuery = ''.obs;

  var searchController = TextEditingController();

  @override
  void onInit() {
    super.onInit();
    searchController.addListener(_onSearchChanged);
    fetchStations();
  }

  void _onSearchChanged() {
    searchQuery.value = searchController.text;
    _filterStations();
  }

  void _filterStations() {
    if (searchQuery.value.isEmpty) {
      filteredStations.value = stations;
    } else {
      final query = searchQuery.value.toLowerCase();
      filteredStations.value = stations.where((station) {
        // Search in station details
        final matchesStation =
            station.stationName.toLowerCase().contains(query) ||
                station.zoneName.toLowerCase().contains(query) ||
                station.regionName.toLowerCase().contains(query) ||
                station.districtName.toLowerCase().contains(query);

        // Search in places
        final matchesPlaces = station.places
            .any((place) => place.fromPlace.toLowerCase().contains(query));

        return matchesStation || matchesPlaces;
      }).toList();
    }
    setTotalItems(filteredStations.length);
  }

  List<StationModel> get paginatedStations => paginateList(filteredStations);

  Future<void> fetchStations() async {
    if (isLoading.value) return;

    isLoading.value = true;
    try {
      log('Fetching stations...');
      final result = await getStationsUseCase.call();
      result.fold(
        (failure) {
          log('Failed to fetch stations: ${failure.message}');
          showErrorDialog(failure);
        },
        (stationList) {
          log('Successfully fetched ${stationList.length} stations');
          stations.value = stationList;
          _filterStations();
        },
      );
    } catch (e) {
      log('Unexpected error occurred while fetching stations: $e');
      showUnexpectedErrorDialog();
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> deleteStation(String stationId) async {
    try {
      log('Deleting station: $stationId');
      // Remove station from local list first
      stations.removeWhere((station) => station.stationId == stationId);
      setTotalItems(stations.length);

      final result = await deleteStationUseCase.call(stationId: stationId);
      result.fold(
        (failure) {
          log('Failed to delete station: ${failure.message}');
          showErrorDialog(failure);
        },
        (success) {
          log('Successfully deleted station');
          SnackbarUtils.showSuccess(
            AppStrings.success,
            'Station deleted successfully',
          );
        },
      );
    } catch (e) {
      log('Unexpected error occurred while deleting station: $e');
      showUnexpectedErrorDialog();
    } finally {
      isLoading.value = false;
    }
  }

  void addStationToList(StationModel station) {
    // Create a new station with all data including places
    final newStation = StationModel(
      stationId: station.stationId,
      stationName: station.stationName,
      zoneId: station.zoneId,
      zoneName: station.zoneName,
      regionId: station.regionId,
      regionName: station.regionName,
      regionCode: station.regionCode,
      districtId: station.districtId,
      districtName: station.districtName,
      createdAt: station.createdAt,
      places:
          List<FromPlaceModel>.from(station.places), // Ensure places are copied
    );

    stations.insert(0, newStation);
    setTotalItems(stations.length);
  }

  void updateStationInList(StationModel station) {
    final index = stations.indexWhere((s) => s.stationId == station.stationId);
    if (index != -1) {
      // Create a new station with all data including places
      final updatedStation = StationModel(
        stationId: station.stationId,
        stationName: station.stationName,
        zoneId: station.zoneId,
        zoneName: station.zoneName,
        regionId: station.regionId,
        regionName: station.regionName,
        regionCode: station.regionCode,
        districtId: station.districtId,
        districtName: station.districtName,
        createdAt: station.createdAt,
        places: List<FromPlaceModel>.from(
            station.places), // Ensure places are copied
      );

      stations[index] = updatedStation;
      setTotalItems(stations.length);
    }
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }
}
