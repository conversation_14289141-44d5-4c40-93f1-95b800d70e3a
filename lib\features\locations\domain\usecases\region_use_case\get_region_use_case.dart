import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/features/locations/repositories/region_repository.dart';
import 'package:logestics/models/locations/region_model.dart';

class GetRegionsUseCase {
  final RegionRepository _regionRepository;

  GetRegionsUseCase(this._regionRepository);

  /// Executes the use case to get all Regions.
  Future<Either<FailureObj, List<RegionModel>>> call() async {
    try {
      return await _regionRepository.getRegions();
    } catch (e) {
      return Left(FailureObj(
        code: 'unexpected-error',
        message: 'An unexpected error occurred.',
      ));
    }
  }
}
