import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/bindings/app_bindings.dart';

import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/widgets/data_table_cell.dart';
import 'package:logestics/core/utils/widgets/pagination_widget.dart';
import 'package:logestics/core/utils/widgets/my_text_field.dart';
import 'package:logestics/core/utils/widgets/searchfield.dart';
import 'package:logestics/features/locations/repositories/station_repository.dart';
import 'package:logestics/features/locations/domain/usecases/station_use_case/delete_station_use_case.dart';
import 'package:logestics/features/locations/domain/usecases/station_use_case/get_stations_use_case.dart';
import 'package:logestics/firebase_service/locations/station_firebase_service.dart';

import 'package:logestics/main.dart';
import 'package:provider/provider.dart';

import '../controller/station_list_controller.dart';
import 'add_station_view.dart';
import 'station_details_view.dart';

class StationList extends StatelessWidget {
  const StationList({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    // Remove the conditional binding initialization
    // Just find the controller if it exists, or create a simple instance if not
    StationListController stationListController;
    try {
      stationListController = Get.find<StationListController>();
    } catch (e) {
      // If controller not found, create a simple instance without dependencies
      // This will be replaced when navigating to the screen with proper bindings
      log('Creating temporary controller instance');
      stationListController = StationListController(
        getStationsUseCase: GetStationsUseCase(
          StationRepositoryImpl(
            StationFirebaseService(),
          ),
        ),
        deleteStationUseCase: DeleteStationUseCase(
          StationRepositoryImpl(
            StationFirebaseService(),
          ),
        ),
      );
    }

    notifier = Provider.of(context, listen: true);

    return GetBuilder<StationListController>(
      init: stationListController,
      builder: (stationListController) => LayoutBuilder(
        builder: (context, constraints) {
          return StatefulBuilder(
            builder: (context, setState) {
              return Container(
                decoration: BoxDecoration(
                  color: notifier.getBgColor,
                  borderRadius: BorderRadius.circular(10),
                ),
                padding: const EdgeInsets.symmetric(vertical: 15),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 15),
                      child: Get.width < 650
                          ? Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                InkWell(
                                  onTap: () {
                                    Get.to(
                                        () => AddStationView(
                                              existingStation: null,
                                            ),
                                        binding: AppBindings());
                                  },
                                  child: Text(
                                    AppStrings.addNewStationButton,
                                    style: AppTextStyles.addNewInvoiceStyle,
                                  ),
                                ),
                                MyTextFormField(
                                  labelText: AppStrings.searchHint,
                                  hintText: AppStrings.searchHint,
                                  controller:
                                      stationListController.searchController,
                                ),
                              ],
                            )
                          : Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                InkWell(
                                  onTap: () {
                                    Get.to(
                                        () => AddStationView(
                                              existingStation: null,
                                            ),
                                        binding: AppBindings());
                                  },
                                  child: Text(
                                    AppStrings.addNewStationButton,
                                    style: AppTextStyles.addNewInvoiceStyle,
                                  ),
                                ),
                                SizedBox(
                                  width: Get.width < 850
                                      ? Get.width / 2
                                      : Get.width / 3.5,
                                  child: const Searchfield(),
                                ),
                              ],
                            ),
                    ),
                    const SizedBox(height: 20),
                    Expanded(
                      child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: SizedBox(
                          width: constraints.maxWidth,
                          child: Obx(
                            () => stationListController.isLoading.value
                                ? Center(
                                    child: CircularProgressIndicator(),
                                  )
                                : ListView(
                                    shrinkWrap: true,
                                    children: [
                                      Table(
                                        border: TableBorder(
                                          horizontalInside: BorderSide(
                                            color: notifier.getfillborder,
                                          ),
                                        ),
                                        children: [
                                          TableRow(
                                            decoration: BoxDecoration(
                                              color: notifier.getHoverColor,
                                            ),
                                            children: [
                                              DataTableHeaderCell(
                                                text: 'Sr No.',
                                                textColor: notifier.text,
                                              ),
                                              DataTableHeaderCell(
                                                text: AppStrings.stationName,
                                                textColor: notifier.text,
                                              ),
                                              DataTableHeaderCell(
                                                text: 'Station Code',
                                                textColor: notifier.text,
                                              ),
                                              DataTableHeaderCell(
                                                text: 'Zone Name',
                                                textColor: notifier.text,
                                              ),
                                              DataTableHeaderCell(
                                                text: 'Places',
                                                textColor: notifier.text,
                                              ),
                                              DataTableHeaderCell(
                                                text: 'Actions',
                                                textColor: notifier.text,
                                              ),
                                            ],
                                          ),
                                          for (var i = 0;
                                              i <
                                                  stationListController
                                                      .paginatedStations.length;
                                              i++)
                                            TableRow(
                                              children: [
                                                // DataTableCheckboxCell(
                                                  //   value: isActive,
                                                //   onChanged: (val) {
                                                //     setState(() {
                                                //       isActive = val!;
                                                //     });
                                                //   },
                                                //   borderColor:
                                                //       notifier.chakboxborder,
                                                // ),
                                                DataTableCell(
                                                  text: ((stationListController
                                                                      .currentPage
                                                                      .value -
                                                                  1) *
                                                              stationListController
                                                                  .itemsPerPage
                                                                  .value +
                                                          i +
                                                          1)
                                                      .toString(),
                                                ),
                                                DataTableCell(
                                                  text: stationListController
                                                      .paginatedStations[i]
                                                      .stationName,
                                                ),
                                                DataTableCell(
                                                  text: stationListController
                                                      .paginatedStations[i]
                                                      .regionCode,
                                                ),
                                                DataTableCell(
                                                  text: stationListController
                                                      .paginatedStations[i]
                                                      .zoneName,
                                                ),
                                                DataTableCell(
                                                  text:
                                                      '${stationListController.paginatedStations[i].places.length} places',
                                                ),
                                                DataTableActionsCell(
                                                  menuItems: [
                                                    DataTablePopupMenuItem(
                                                      text: 'View Details',
                                                      icon: Icons.visibility,
                                                      onTap: () {
                                                        Get.to(() =>
                                                            StationDetailsView(
                                                              station:
                                                                  stationListController
                                                                      .paginatedStations[i],
                                                            ));
                                                      },
                                                    ),
                                                    DataTablePopupMenuItem(
                                                      text: 'Edit',
                                                      icon: Icons.edit,
                                                      onTap: () {
                                                        Get.to(
                                                          () => AddStationView(
                                                            existingStation:
                                                                stationListController
                                                                    .paginatedStations[i],
                                                          ),
                                                          binding:
                                                              AppBindings(),
                                                        );
                                                      },
                                                    ),
                                                    DataTablePopupMenuItem(
                                                      text: 'Delete',
                                                      icon:
                                                          Icons.delete_outline,
                                                      isDanger: true,
                                                      onTap: () async {
                                                        Get.dialog(
                                                          AlertDialog(
                                                            title: Text(
                                                                'Confirm Delete'),
                                                            content: Text(
                                                                'Are you sure you want to delete this station?'),
                                                            actions: [
                                                              TextButton(
                                                                onPressed: () =>
                                                                    Get.back(),
                                                                child: Text(
                                                                    'Cancel'),
                                                              ),
                                                              TextButton(
                                                                onPressed:
                                                                    () async {
                                                                  Get.back();
                                                                  await stationListController
                                                                      .deleteStation(
                                                                    stationListController
                                                                        .paginatedStations[
                                                                            i]
                                                                        .stationId,
                                                                  );
                                                                },
                                                                child: Text(
                                                                  'Delete',
                                                                  style: TextStyle(
                                                                      color: Colors
                                                                          .red),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        );
                                                      },
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                        ],
                                      ),
                                    ],
                                  ),
                          ),
                        ),
                      ),
                    ),
                    Obx(() => PaginationWidget(
                          currentPage: stationListController.currentPage.value,
                          totalPages: stationListController.totalPages,
                          itemsPerPage:
                              stationListController.itemsPerPage.value,
                          onPageChanged: (page) =>
                              stationListController.setCurrentPage(page),
                          onItemsPerPageChanged: (count) =>
                              stationListController.setItemsPerPage(count),
                        )),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }
}

/*arguments: {
                                          'invoice': Invoice(
                                              consignorName: 'sdf',
                                              consignorPickUpAddress: 'sdf',
                                              conveyNoteNumber: 'sdfd',
                                              customerCNIC: '34534534534534',
                                          customerGstNumber: 'gsdf',
                                            deliveryMode: 'By Road',
                                            destinationAddress: 'ssdf',
                                            customerName: 'ali',
                                            distanceInKilometers: 324,
                                            invoiceNumber: 34,
                                            invoiceStatus: 'pending',
                                            numberOfBags: 343,
                                            orderDate: '12/12/1223',
                                            orderNumber: '123',
                                            productName: 'sona',
                                            StationNumber: 'shahid',
                                            shipmentDate: '12/12/1212',
                                             shipmentNumber: '234',
                                            tasNumber: '343',
                                            truckNumber: '343'
                                          ),
                                          // Pass currentInvoice here
                                        }*/
