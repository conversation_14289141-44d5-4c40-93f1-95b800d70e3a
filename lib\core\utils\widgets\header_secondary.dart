import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/features/home/<USER>/drawer_controllers.dart';
import 'package:logestics/features/home/<USER>/theme.dart';
import 'package:logestics/features/home/<USER>/dashbord/e_commerece/e_commerce.dart';

class HeaderSecondary extends StatelessWidget {
  const HeaderSecondary({
    super.key,
    required this.notifier,
    this.option1,
    this.option2,
    this.option3,
  });

  final ColorNotifier notifier;
  final String? option1;
  final String? option2;
  final String? option3;
  @override
  Widget build(BuildContext context) {
    MainDrawerController mainDrawerController = Get.put(MainDrawerController());
    return Row(
      children: [
        InkWell(
          onTap: () => mainDrawerController.updateSelectedScreen(
              const ECommercePageView(), "main/dashboard"),
          child: Row(
            children: [
              Image.asset(
                "assets/images/home.png",
                height: 15,
                color: const Color(0xFF0f7bf4),
              ),
              Text(
                option1 ?? " Dashboard",
                style: TextStyle(
                  fontFamily: "Outfit",
                  fontSize: 15,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 10),
          height: 5,
          width: 5,
          decoration: const BoxDecoration(
            color: Colors.grey,
            shape: BoxShape.circle,
          ),
        ),
        Text(
          option2 ?? "system",
          style: TextStyle(
            fontFamily: "Outfit",
            fontSize: 15,
            color: Colors.grey,
          ),
        ),
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 10),
          height: 5,
          width: 5,
          decoration: const BoxDecoration(
            color: Colors.grey,
            shape: BoxShape.circle,
          ),
        ),
        Text(
          option3 ?? "Zones",
          overflow: TextOverflow.ellipsis,
          style: TextStyle(
            fontFamily: "Outfit",
            fontSize: 15,
            color: notifier.text,
          ),
        ),
      ],
    );
  }
}
