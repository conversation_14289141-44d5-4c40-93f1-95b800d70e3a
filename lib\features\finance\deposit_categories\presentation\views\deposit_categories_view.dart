import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/widgets/data_table_cell.dart';
import 'package:logestics/firebase_service/finance/deposit_category_firebase_service.dart';
import 'package:logestics/features/finance/deposit_categories/repositories/deposit_category_repository.dart';
import 'package:logestics/features/finance/deposit_categories/presentation/controllers/deposit_category_controller.dart';
import 'package:logestics/main.dart';
import 'package:logestics/models/finance/deposit_category_model.dart';
import 'package:provider/provider.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';

class DepositCategoriesView extends StatefulWidget {
  const DepositCategoriesView({super.key});

  @override
  State<DepositCategoriesView> createState() => _DepositCategoriesViewState();
}

class _DepositCategoriesViewState extends State<DepositCategoriesView> {
  late final DepositCategoryController depositCategoryController;

  @override
  void initState() {
    super.initState();
    final firebaseService = DepositCategoryFirebaseService();
    final repository = DepositCategoryRepositoryImpl(firebaseService);
    // Create the controller and ensure it's registered with Get
    depositCategoryController = Get.put(
        DepositCategoryController(repository: repository),
        permanent: true);

    // Load categories to ensure they're available
    if (depositCategoryController.categories.isEmpty) {
      depositCategoryController.fetchCategories();
    }
  }

  @override
  Widget build(BuildContext context) {
    var width = Get.width;
    notifier = Provider.of(context, listen: true);

    return LayoutBuilder(
      builder: (context, constraints) {
        return RefreshIndicator(
          onRefresh: () async {
            await depositCategoryController.forceRefresh();
          },
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Column(
              children: [
                SizedBox(
                  height: width < 650 ? 55 : 40,
                  width: width,
                  child: width < 650
                      ? Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Deposit Categories',
                              overflow: TextOverflow.ellipsis,
                              style: AppTextStyles.titleStyle
                                  .copyWith(color: notifier.text),
                            ),
                            const Spacer(),
                          ],
                        )
                      : Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Deposit Categories',
                              overflow: TextOverflow.ellipsis,
                              style: AppTextStyles.titleStyle
                                  .copyWith(color: notifier.text),
                            ),
                            const Spacer(),
                            IconButton(
                              onPressed: () async {
                                await depositCategoryController.forceRefresh();
                              },
                              icon: Icon(
                                Icons.refresh,
                                color: notifier.text,
                              ),
                              tooltip: 'Refresh Categories',
                            ),
                          ],
                        ),
                ),
                const SizedBox(height: 20),
                SizedBox(
                  height: 570,
                  child: _buildCategoriesList(),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCategoriesList() {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            color: notifier.getBgColor,
            borderRadius: BorderRadius.circular(10),
          ),
          padding: const EdgeInsets.symmetric(vertical: 15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 15),
                child: Get.width < 650
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          InkWell(
                            onTap: () => depositCategoryController.openDialog(),
                            child: Text(
                              'Add New Category',
                              style: AppTextStyles.addNewInvoiceStyle,
                            ),
                          ),
                          TextField(
                            controller:
                                depositCategoryController.searchController,
                            decoration: InputDecoration(
                              hintText: 'Search categories...',
                              prefixIcon: const Icon(Icons.search),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                              filled: true,
                              fillColor: notifier.textFileColor,
                            ),
                          ),
                        ],
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          InkWell(
                            onTap: () => depositCategoryController.openDialog(),
                            child: Text(
                              'Add New Category',
                              style: AppTextStyles.addNewInvoiceStyle,
                            ),
                          ),
                          SizedBox(
                            width: Get.width < 850
                                ? Get.width / 2
                                : Get.width / 3.5,
                            child: TextField(
                              controller:
                                  depositCategoryController.searchController,
                              decoration: InputDecoration(
                                hintText: 'Search categories...',
                                prefixIcon: const Icon(Icons.search),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8.0),
                                ),
                                filled: true,
                                fillColor: notifier.textFileColor,
                              ),
                            ),
                          ),
                        ],
                      ),
              ),
              const SizedBox(height: 20),
              Expanded(
                child: SizedBox(
                  width: Get.width,
                  child: Obx(() {
                    if (depositCategoryController.isLoading.value) {
                      return const Center(
                        child: CircularProgressIndicator(),
                      );
                    }

                    if (depositCategoryController.filteredCategories.isEmpty) {
                      return Center(
                        child: Text(
                          'No categories found',
                          style: AppTextStyles.invoiceDataStyle.copyWith(
                            color: notifier.text,
                          ),
                        ),
                      );
                    }

                    return ListView(
                      shrinkWrap: true,
                      children: [
                        Table(
                          columnWidths: const {
                            0: FlexColumnWidth(0.5), // S.No
                            1: FlexColumnWidth(2.0), // Name
                            2: FlexColumnWidth(2.0), // Description
                            3: FlexColumnWidth(1.0), // Actions
                          },
                          border: TableBorder(
                            horizontalInside: BorderSide(
                              color: notifier.getfillborder,
                            ),
                          ),
                          children: [
                            TableRow(
                              decoration: BoxDecoration(
                                color: notifier.getHoverColor,
                              ),
                              children: [
                                DataTableCell(
                                  text: 'S.No',
                                  style:
                                      AppTextStyles.invoiceDataStyle.copyWith(
                                    color: notifier.text,
                                  ),
                                ),
                                DataTableCell(
                                  text: 'Name',
                                  style:
                                      AppTextStyles.invoiceDataStyle.copyWith(
                                    color: notifier.text,
                                  ),
                                ),
                                DataTableCell(
                                  text: 'Description',
                                  style:
                                      AppTextStyles.invoiceDataStyle.copyWith(
                                    color: notifier.text,
                                  ),
                                ),
                                DataTableCell(
                                  text: 'Actions',
                                  style:
                                      AppTextStyles.invoiceDataStyle.copyWith(
                                    color: notifier.text,
                                  ),
                                ),
                              ],
                            ),
                            for (var i = 0;
                                i <
                                    depositCategoryController
                                        .filteredCategories.length;
                                i++)
                              TableRow(
                                children: [
                                  DataTableCell(
                                    text: '${i + 1}',
                                    style:
                                        AppTextStyles.invoiceDataStyle.copyWith(
                                      color: notifier.text,
                                    ),
                                  ),
                                  DataTableCell(
                                    onTap: () => _showCategoryDetails(
                                        depositCategoryController
                                            .filteredCategories[i]),
                                    text: depositCategoryController
                                        .filteredCategories[i].name,
                                    style:
                                        AppTextStyles.invoiceDataStyle.copyWith(
                                      color: notifier.text,
                                      decoration: TextDecoration.underline,
                                    ),
                                  ),
                                  DataTableCell(
                                    text: depositCategoryController
                                        .filteredCategories[i].description,
                                    style:
                                        AppTextStyles.invoiceDataStyle.copyWith(
                                      color: notifier.text,
                                    ),
                                  ),
                                  DataTableActionsCell(
                                    menuItems: [
                                      DataTablePopupMenuItem(
                                        text: 'Edit',
                                        icon: Icons.edit_outlined,
                                        onTap: () => _showCategoryDetails(
                                            depositCategoryController
                                                .filteredCategories[i]),
                                      ),
                                      DataTablePopupMenuItem(
                                        text: 'Delete',
                                        icon: Icons.delete_outline,
                                        isDanger: true,
                                        onTap: () async {
                                          if (!depositCategoryController
                                              .isLoading.value) {
                                            await _confirmDelete(
                                                depositCategoryController
                                                    .filteredCategories[i].id);
                                          }
                                        },
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                          ],
                        ),
                      ],
                    );
                  }),
                ),
              ),
            ],
          ),
        ),
        Obx(() {
          if (!depositCategoryController.isDialogOpen.value) {
            return const SizedBox.shrink();
          }

          return Positioned(
            right: 0,
            top: 0,
            bottom: 0,
            width: Get.width < 650 ? Get.width : 400,
            child: Container(
              decoration: BoxDecoration(
                color: notifier.getBgColor,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(10),
                    blurRadius: 10,
                    offset: const Offset(0, 0),
                  ),
                ],
              ),
              child: Form(
                key: depositCategoryController.formKey,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Add New Category',
                            style: AppTextStyles.titleStyle.copyWith(
                              color: notifier.text,
                            ),
                          ),
                          IconButton(
                            onPressed: depositCategoryController.closeDialog,
                            icon: const Icon(Icons.close),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      TextFormField(
                        controller: depositCategoryController.nameController,
                        decoration: InputDecoration(
                          labelText: 'Category Name',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          filled: true,
                          fillColor: notifier.textFileColor,
                        ),
                        validator: depositCategoryController.validateName,
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller:
                            depositCategoryController.descriptionController,
                        decoration: InputDecoration(
                          labelText: 'Description',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          filled: true,
                          fillColor: notifier.textFileColor,
                        ),
                        maxLines: 3,
                      ),
                      const SizedBox(height: 24),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: depositCategoryController.addCategory,
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text('Add Category'),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }),
      ],
    );
  }

  Future<void> _confirmDelete(String id) {
    return Get.dialog(
      AlertDialog(
        title: const Text('Confirm Delete'),
        content: const Text('Are you sure you want to delete this category?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              try {
                Get.back(); // Close confirmation dialog
                Get.dialog(
                  const Center(
                    child: CircularProgressIndicator(),
                  ),
                  barrierDismissible: false,
                );
                await depositCategoryController.deleteCategory(id);
                if (Get.isDialogOpen ?? false) {
                  Get.back(); // Close loading dialog only if it's open
                }
              } catch (e) {
                if (Get.isDialogOpen ?? false) {
                  Get.back(); // Close loading dialog if there was an error
                }
                SnackbarUtils.showError(
                  AppStrings.errorS,
                  'Failed to delete category: $e',
                );
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showCategoryDetails(DepositCategoryModel category) {
    depositCategoryController.nameController.text = category.name;
    depositCategoryController.descriptionController.text = category.description;

    Get.dialog(
      Dialog(
        insetPadding: const EdgeInsets.all(20),
        child: Container(
          width: Get.width < 650 ? Get.width : 500,
          padding: const EdgeInsets.all(20),
          child: Form(
            key: depositCategoryController.formKey,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Category Details',
                        style: AppTextStyles.titleStyle.copyWith(
                          color: notifier.text,
                        ),
                      ),
                      IconButton(
                        onPressed: () => Get.back(),
                        icon: const Icon(Icons.close),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  TextFormField(
                    controller: depositCategoryController.nameController,
                    decoration: InputDecoration(
                      labelText: 'Category Name',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      filled: true,
                      fillColor: notifier.textFileColor,
                    ),
                    validator: depositCategoryController.validateName,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: depositCategoryController.descriptionController,
                    decoration: InputDecoration(
                      labelText: 'Description',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      filled: true,
                      fillColor: notifier.textFileColor,
                    ),
                    maxLines: 3,
                  ),
                  const SizedBox(height: 24),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        if (depositCategoryController.formKey.currentState!
                            .validate()) {
                          final updatedCategory = DepositCategoryModel(
                            id: category.id,
                            name: depositCategoryController.nameController.text,
                            description: depositCategoryController
                                .descriptionController.text,
                            createdAt: category.createdAt,
                          );
                          depositCategoryController.selectedCategory.value =
                              updatedCategory;
                          depositCategoryController.updateCategory();
                          Get.back();
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('Update Category'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // String _formatDate(DateTime date) {
  //   return '${date.day}/${date.month}/${date.year}';
  // }
}
