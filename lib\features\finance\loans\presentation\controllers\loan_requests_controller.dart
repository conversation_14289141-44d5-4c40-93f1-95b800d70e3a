import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:logestics/core/utils/mixins/pagination_mixin.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/features/finance/loans/usecases/get_loan_requests_use_case.dart';
import 'package:logestics/features/finance/loans/usecases/loan_action_use_cases.dart';
import 'package:logestics/features/finance/accounts/repositories/account_repository.dart';
import 'package:logestics/models/finance/loan_model.dart';
import 'package:logestics/models/finance/account_model.dart';

class LoanRequestsController extends GetxController with PaginationMixin {
  final GetIncomingLoanRequestsUseCase getIncomingLoanRequestsUseCase;
  final GetOutgoingLoanRequestsUseCase getOutgoingLoanRequestsUseCase;
  final ApproveLoanUseCase approveLoanUseCase;
  final RejectLoanUseCase rejectLoanUseCase;
  final AccountRepository accountRepository;

  LoanRequestsController({
    required this.getIncomingLoanRequestsUseCase,
    required this.getOutgoingLoanRequestsUseCase,
    required this.approveLoanUseCase,
    required this.rejectLoanUseCase,
    required this.accountRepository,
  });

  // Loading states
  final isLoading = false.obs;
  final isInitialLoading = true.obs;
  final isRefreshing = false.obs;
  final hasError = false.obs;
  final errorMessage = ''.obs;
  final isProcessing = false.obs;

  // Data
  final allIncomingRequests = <LoanModel>[].obs;
  final allOutgoingRequests = <LoanModel>[].obs;
  final filteredIncomingRequests = <LoanModel>[].obs;
  final filteredOutgoingRequests = <LoanModel>[].obs;

  // Search functionality
  final searchQuery = ''.obs;
  final searchController = TextEditingController();
  Timer? _searchDebounceTimer;

  // Filter states
  final selectedStatus = 'All'.obs;
  final selectedRequestType = 'All'.obs;
  final dateFilterType =
      'all'.obs; // all, today, week, month, last30days, custom
  final selectedStartDate = Rxn<DateTime>();
  final selectedEndDate = Rxn<DateTime>();

  // Tab management
  final currentTabIndex = 0.obs;

  // Notification count
  final unreadIncomingCount = 0.obs;

  // Performance optimization
  final _isFilteringInProgress = false.obs;

  // Account management for approval
  final accounts = <AccountModel>[].obs;
  final selectedAccount = Rxn<AccountModel>();
  final isLoadingAccounts = false.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeFilters();
    _setupSearchListener();
    loadLoanRequests();
    loadAccounts();
  }

  @override
  void onClose() {
    _searchDebounceTimer?.cancel();
    searchController.dispose();
    super.onClose();
  }

  void _initializeFilters() {
    selectedStatus.value = 'All';
    selectedRequestType.value = 'All';
    dateFilterType.value = 'all';
    selectedStartDate.value = null;
    selectedEndDate.value = null;
  }

  void _setupSearchListener() {
    searchController.addListener(() {
      // Cancel previous timer
      _searchDebounceTimer?.cancel();

      // Set new timer with 300ms delay
      _searchDebounceTimer = Timer(const Duration(milliseconds: 300), () {
        searchQuery.value = searchController.text;
        _applyFiltersDebounced();
      });
    });
  }

  /// Load loan requests data
  Future<void> loadLoanRequests() async {
    if (isLoading.value) return;

    isLoading.value = true;
    hasError.value = false;
    errorMessage.value = '';

    if (allIncomingRequests.isEmpty && allOutgoingRequests.isEmpty) {
      isInitialLoading.value = true;
    }

    try {
      // Load both incoming and outgoing requests in parallel
      final results = await Future.wait([
        getIncomingLoanRequestsUseCase.execute(),
        getOutgoingLoanRequestsUseCase.execute(),
      ]);

      final incomingResult = results[0];
      final outgoingResult = results[1];

      // Handle incoming requests
      incomingResult.fold(
        (failure) {
          hasError.value = true;
          errorMessage.value = failure.message;
          SnackbarUtils.showError(
              'Error', 'Failed to load incoming requests: ${failure.message}');
        },
        (incomingRequests) {
          allIncomingRequests.value = incomingRequests;
          _updateUnreadCount();
        },
      );

      // Handle outgoing requests
      outgoingResult.fold(
        (failure) {
          hasError.value = true;
          errorMessage.value = failure.message;
          SnackbarUtils.showError(
              'Error', 'Failed to load outgoing requests: ${failure.message}');
        },
        (outgoingRequests) {
          allOutgoingRequests.value = outgoingRequests;
        },
      );

      _applyFilters();
    } catch (e) {
      hasError.value = true;
      errorMessage.value = 'Failed to load loan requests: $e';
      SnackbarUtils.showError('Error', 'Failed to load loan requests: $e');
    } finally {
      isLoading.value = false;
      isInitialLoading.value = false;
    }
  }

  /// Refresh data
  Future<void> refreshData() async {
    isRefreshing.value = true;
    await loadLoanRequests();
    isRefreshing.value = false;
  }

  /// Retry loading data after error
  Future<void> retryLoading() async {
    hasError.value = false;
    errorMessage.value = '';
    await loadLoanRequests();
  }

  /// Load accounts for loan approval
  Future<void> loadAccounts() async {
    isLoadingAccounts.value = true;
    try {
      final result = await accountRepository.getAccounts();
      result.fold(
        (failure) {
          SnackbarUtils.showError(
              'Error', 'Failed to load accounts: ${failure.message}');
        },
        (accountsList) {
          accounts.value = accountsList;
          if (accounts.isNotEmpty && selectedAccount.value == null) {
            selectedAccount.value = accounts.first;
          }
        },
      );
    } catch (e) {
      SnackbarUtils.showError('Error', 'Failed to load accounts: $e');
    } finally {
      isLoadingAccounts.value = false;
    }
  }

  void _updateUnreadCount() {
    unreadIncomingCount.value = allIncomingRequests
        .where((request) => request.status == 'pending')
        .length;
  }

  void _applyFilters() {
    if (_isFilteringInProgress.value) return;
    _isFilteringInProgress.value = true;

    try {
      // Filter incoming requests
      var filteredIncoming = _filterLoanList(allIncomingRequests, true);

      // Filter outgoing requests
      var filteredOutgoing = _filterLoanList(allOutgoingRequests, false);

      // Sort by request date (newest first)
      filteredIncoming.sort((a, b) => b.requestDate.compareTo(a.requestDate));
      filteredOutgoing.sort((a, b) => b.requestDate.compareTo(a.requestDate));

      filteredIncomingRequests.value = filteredIncoming;
      filteredOutgoingRequests.value = filteredOutgoing;
    } finally {
      _isFilteringInProgress.value = false;
    }
  }

  void _applyFiltersDebounced() {
    _applyFilters();
  }

  List<LoanModel> _filterLoanList(List<LoanModel> loans, bool isIncoming) {
    return loans.where((loan) {
      // Status filter
      if (selectedStatus.value != 'All' &&
          loan.status.toLowerCase() != selectedStatus.value.toLowerCase()) {
        return false;
      }

      // Date filter
      if (!_passesDateFilter(loan.requestDate)) {
        return false;
      }

      // Search filter
      if (searchQuery.value.isNotEmpty) {
        final query = searchQuery.value.toLowerCase();
        final nameToSearch =
            isIncoming ? loan.requestedByName : loan.requestedToName;
        return nameToSearch.toLowerCase().contains(query) ||
            loan.amount.toString().contains(query) ||
            loan.status.toLowerCase().contains(query) ||
            (loan.notes?.toLowerCase().contains(query) ?? false);
      }

      return true;
    }).toList();
  }

  bool _passesDateFilter(DateTime requestDate) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    switch (dateFilterType.value) {
      case 'today':
        final requestDay =
            DateTime(requestDate.year, requestDate.month, requestDate.day);
        return requestDay.isAtSameMomentAs(today);
      case 'week':
        final weekStart = today.subtract(Duration(days: today.weekday - 1));
        return requestDate.isAfter(weekStart.subtract(const Duration(days: 1)));
      case 'month':
        final monthStart = DateTime(now.year, now.month, 1);
        return requestDate
            .isAfter(monthStart.subtract(const Duration(days: 1)));
      case 'last30days':
        final thirtyDaysAgo = today.subtract(const Duration(days: 30));
        return requestDate
            .isAfter(thirtyDaysAgo.subtract(const Duration(days: 1)));
      case 'custom':
        if (selectedStartDate.value != null && selectedEndDate.value != null) {
          return requestDate.isAfter(
                  selectedStartDate.value!.subtract(const Duration(days: 1))) &&
              requestDate.isBefore(
                  selectedEndDate.value!.add(const Duration(days: 1)));
        }
        return true;
      default:
        return true;
    }
  }

  // Filter methods
  void filterByStatus(String status) {
    selectedStatus.value = status;
    _applyFilters();
  }

  void filterByDateRange(String filterType,
      {DateTime? startDate, DateTime? endDate}) {
    dateFilterType.value = filterType;
    if (filterType == 'custom') {
      selectedStartDate.value = startDate;
      selectedEndDate.value = endDate;
    }
    _applyFilters();
  }

  void searchRequests(String query) {
    // Only update searchQuery, don't modify controller.text to avoid text selection
    searchQuery.value = query;
    _applyFilters();
  }

  void clearFilters() {
    selectedStatus.value = 'All';
    selectedRequestType.value = 'All';
    dateFilterType.value = 'all';
    selectedStartDate.value = null;
    selectedEndDate.value = null;
    searchController.clear();
    _applyFilters();
  }

  // Tab management
  void switchTab(int index) {
    currentTabIndex.value = index;
  }

  /// Check if account has sufficient balance for loan amount (for display purposes only)
  bool hasEnoughBalance(AccountModel account, double loanAmount) {
    return account.availableBalance >= loanAmount;
  }

  /// Validate account for loan approval (always returns true - allows negative balances)
  bool validateAccountBalance(AccountModel account, double loanAmount) {
    return true; // Allow any account regardless of balance
  }

  /// Set selected account for approval
  void setSelectedAccount(AccountModel account) {
    selectedAccount.value = account;
  }

  // Action methods
  Future<bool> approveLoanRequest(String loanId, String fromAccountId) async {
    isProcessing.value = true;
    try {
      final result = await approveLoanUseCase.execute(loanId, fromAccountId);

      return result.fold(
        (failure) {
          SnackbarUtils.showError('Error', failure.message);
          return false;
        },
        (success) {
          SnackbarUtils.showSuccess(
              'Success', 'Loan request approved successfully');
          refreshData(); // Refresh data to show updated status
          return true;
        },
      );
    } catch (e) {
      SnackbarUtils.showError('Error', 'Failed to approve loan request: $e');
      return false;
    } finally {
      isProcessing.value = false;
    }
  }

  Future<bool> rejectLoanRequest(String loanId, String reason) async {
    isProcessing.value = true;
    try {
      final result = await rejectLoanUseCase.execute(loanId, reason);

      return result.fold(
        (failure) {
          SnackbarUtils.showError('Error', failure.message);
          return false;
        },
        (success) {
          SnackbarUtils.showSuccess(
              'Success', 'Loan request rejected successfully');
          refreshData(); // Refresh data to show updated status
          return true;
        },
      );
    } catch (e) {
      SnackbarUtils.showError('Error', 'Failed to reject loan request: $e');
      return false;
    } finally {
      isProcessing.value = false;
    }
  }

  // Utility methods
  String formatCurrency(double amount) {
    final formatter = NumberFormat.currency(symbol: 'PKR ', decimalDigits: 2);
    return formatter.format(amount);
  }

  String formatDate(DateTime date) {
    return DateFormat('MMM dd, yyyy').format(date);
  }

  String formatDateTime(DateTime date) {
    return DateFormat('MMM dd, yyyy hh:mm a').format(date);
  }

  Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'approved':
        return Colors.green;
      case 'rejected':
        return Colors.red;
      case 'cancelled':
        return Colors.orange;
      case 'pending':
      default:
        return Colors.amber;
    }
  }

  String getStatusDisplayText(String status) {
    return status.capitalizeFirst ?? 'Pending';
  }

  bool isLoanOverdue(LoanModel loan) {
    if (loan.status != 'approved') return false;
    return DateTime.now().isAfter(loan.dueDate);
  }

  // Get current tab data
  List<LoanModel> get currentTabData {
    return currentTabIndex.value == 0
        ? filteredIncomingRequests
        : filteredOutgoingRequests;
  }

  // Get total counts
  int get totalIncomingRequests => allIncomingRequests.length;
  int get totalOutgoingRequests => allOutgoingRequests.length;
  int get pendingIncomingRequests =>
      allIncomingRequests.where((r) => r.status == 'pending').length;
  int get pendingOutgoingRequests =>
      allOutgoingRequests.where((r) => r.status == 'pending').length;
}
