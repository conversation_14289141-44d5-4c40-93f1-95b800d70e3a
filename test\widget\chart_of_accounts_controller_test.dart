import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:logestics/features/accounting/chart_of_accounts/presentation/controllers/chart_of_accounts_controller.dart';
import 'package:logestics/models/finance/chart_of_accounts_model.dart';

void main() {
  group('Chart of Accounts Controller Tests', () {
    late ChartOfAccountsController controller;

    setUp(() {
      Get.testMode = true;
    });

    tearDown(() {
      Get.reset();
    });

    test('Controller openDrawer should set isDrawerOpen to true', () {
      // Create controller without repository for basic testing
      controller = ChartOfAccountsController(repository: null as dynamic);
      
      // Initial state
      expect(controller.isDrawerOpen.value, false);

      // Call openDrawer
      controller.openDrawer();

      // Verify state changed
      expect(controller.isDrawerOpen.value, true);
      print('✓ openDrawer sets isDrawerOpen to true');
    });

    test('Controller closeDrawer should set isDrawerOpen to false', () {
      controller = ChartOfAccountsController(repository: null as dynamic);
      
      // Set initial state to open
      controller.isDrawerOpen.value = true;
      expect(controller.isDrawerOpen.value, true);

      // Call closeDrawer
      controller.closeDrawer();

      // Verify state changed
      expect(controller.isDrawerOpen.value, false);
      print('✓ closeDrawer sets isDrawerOpen to false');
    });

    test('Controller clearForm should clear all form fields', () {
      controller = ChartOfAccountsController(repository: null as dynamic);
      
      // Set some values
      controller.accountNameController.text = 'Test Account';
      controller.accountNumberController.text = '1001';
      controller.descriptionController.text = 'Test Description';
      controller.selectedCategory.value = AccountCategory.assets;
      controller.selectedAccountType.value = AccountType.currentAssets;
      controller.isEditMode.value = true;
      controller.editingAccountId.value = 'test-id';

      // Call clearForm
      controller.clearForm();

      // Verify all fields are cleared
      expect(controller.accountNameController.text, isEmpty);
      expect(controller.accountNumberController.text, isEmpty);
      expect(controller.descriptionController.text, isEmpty);
      expect(controller.selectedCategory.value, isNull);
      expect(controller.selectedAccountType.value, isNull);
      expect(controller.selectedParentAccount.value, isNull);
      expect(controller.isEditMode.value, false);
      expect(controller.editingAccountId.value, isEmpty);
      print('✓ clearForm clears all form fields');
    });

    test('Controller should handle search functionality', () {
      controller = ChartOfAccountsController(repository: null as dynamic);
      
      // Set search query
      controller.searchQuery.value = 'test search';

      // Verify search query is set
      expect(controller.searchQuery.value, 'test search');

      // Clear search
      controller.searchQuery.value = '';
      expect(controller.searchQuery.value, isEmpty);
      print('✓ Search functionality works');
    });

    test('Controller should handle pagination state', () {
      controller = ChartOfAccountsController(repository: null as dynamic);
      
      // Test pagination properties
      expect(controller.itemsPerPage.value, 25); // Default value
      expect(controller.hasNextPage.value, false);
      expect(controller.isLoadingPage.value, false);

      // Test setting pagination values
      controller.itemsPerPage.value = 50;
      expect(controller.itemsPerPage.value, 50);

      controller.hasNextPage.value = true;
      expect(controller.hasNextPage.value, true);
      print('✓ Pagination state management works');
    });

    test('Controller should handle loading states', () {
      controller = ChartOfAccountsController(repository: null as dynamic);
      
      // Test initial loading state
      expect(controller.isLoading.value, false);
      expect(controller.isDeleting.value, false);

      // Test setting loading states
      controller.isLoading.value = true;
      expect(controller.isLoading.value, true);

      controller.isDeleting.value = true;
      expect(controller.isDeleting.value, true);
      print('✓ Loading state management works');
    });

    test('Controller should handle account selection', () {
      controller = ChartOfAccountsController(repository: null as dynamic);
      
      // Create test account
      final testAccount = ChartOfAccountsModel(
        id: 'test-id',
        accountName: 'Test Account',
        accountNumber: '1001',
        category: AccountCategory.assets,
        accountType: AccountType.currentAssets,
        description: 'Test Description',
        isActive: true,
        createdAt: DateTime.now(),
        uid: 'test-uid',
      );

      // Test editing account
      controller.startEditing(testAccount);

      expect(controller.editingAccount.value, testAccount);
      expect(controller.isEditMode.value, true);
      expect(controller.editingAccountId.value, testAccount.id);
      expect(controller.accountNameController.text, testAccount.accountName);
      expect(controller.accountNumberController.text, testAccount.accountNumber);
      expect(controller.descriptionController.text, testAccount.description);
      expect(controller.selectedCategory.value, testAccount.category);
      expect(controller.selectedAccountType.value, testAccount.accountType);
      expect(controller.isActive.value, testAccount.isActive);
      print('✓ Account selection and editing works');
    });

    test('Controller should handle form validation', () {
      controller = ChartOfAccountsController(repository: null as dynamic);
      
      // Test form key
      expect(controller.formKey, isNotNull);
      expect(controller.formKey.currentState, isNull); // Not attached to widget yet
      print('✓ Form validation setup works');
    });

    test('Controller should handle category filtering', () {
      controller = ChartOfAccountsController(repository: null as dynamic);
      
      // Test category filter
      expect(controller.selectedCategoryFilter.value, isNull);

      // Set category filter
      controller.selectedCategoryFilter.value = AccountCategory.assets;
      expect(controller.selectedCategoryFilter.value, AccountCategory.assets);

      // Clear category filter
      controller.selectedCategoryFilter.value = null;
      expect(controller.selectedCategoryFilter.value, isNull);
      print('✓ Category filtering works');
    });

    test('Controller should handle inactive accounts toggle', () {
      controller = ChartOfAccountsController(repository: null as dynamic);
      
      // Test initial state
      expect(controller.showInactiveAccounts.value, false);

      // Toggle inactive accounts
      controller.showInactiveAccounts.value = true;
      expect(controller.showInactiveAccounts.value, true);

      // Toggle back
      controller.showInactiveAccounts.value = false;
      expect(controller.showInactiveAccounts.value, false);
      print('✓ Inactive accounts toggle works');
    });
  });
}
