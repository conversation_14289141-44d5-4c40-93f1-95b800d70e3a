import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/models/finance/bill_model.dart';
import '../repositories/bill_repository.dart';

class UpdateBillUseCase {
  final BillRepository _billRepository;

  UpdateBillUseCase(this._billRepository);

  /// Executes the use case to update a bill.
  Future<Either<FailureObj, SuccessObj>> call({
    required String uid,
    required BillModel bill,
  }) async {
    // Validate inputs
    if (uid.isEmpty) {
      return Left(FailureObj(
        code: 'invalid-company-id',
        message: 'Company ID cannot be empty.',
      ));
    }

    if (bill.billId.isEmpty) {
      return Left(FailureObj(
        code: 'invalid-bill',
        message: 'Bill ID must not be empty.',
      ));
    }

    if (bill.billNumber.isEmpty) {
      return Left(FailureObj(
        code: 'invalid-bill-number',
        message: 'Bill number must not be empty.',
      ));
    }

    // Call the repository
    return await _billRepository.updateBill(
      uid: uid,
      bill: bill,
    );
  }
}
