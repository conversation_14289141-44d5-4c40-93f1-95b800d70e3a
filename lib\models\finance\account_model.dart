class AccountModel {
  final String id;
  final String name;
  final double initialBalance;
  final double availableBalance;
  final String accountNumber;
  final String branchCode;
  final String branchAddress;
  final DateTime createdAt;
  final String uid; // User ID who owns this account

  AccountModel({
    required this.id,
    required this.name,
    required this.initialBalance,
    required this.accountNumber,
    required this.branchCode,
    required this.branchAddress,
    required this.availableBalance,
    required this.createdAt,
    this.uid = '', // Default empty string for backward compatibility
  });

  Map<String, dynamic> toJson() {
    try {
      // Validate all fields before serialization
      final result = {
        'id': id,
        'name': name,
        'initialBalance': initialBalance,
        'availableBalance': availableBalance,
        'accountNumber': accountNumber,
        'branchCode': branchCode,
        'branchAddress': branchAddress,
        'createdAt': createdAt.toIso8601String(),
        'uid': uid, // Include UID in JSON
      };

      // Validate the result doesn't contain any null values
      return result;
    } catch (e) {
      throw Exception('Error in AccountModel.toJson(): $e');
    }
  }

  factory AccountModel.fromJson(Map<String, dynamic> json) {
    return AccountModel(
      id: json['id'] as String? ?? '',
      name: json['name'] as String? ?? '',
      initialBalance: (json['initialBalance'] as num?)?.toDouble() ?? 0.0,
      availableBalance: (json['availableBalance'] as num?)?.toDouble() ?? 0.0,
      accountNumber: json['accountNumber'] as String? ?? '',
      branchCode: json['branchCode'] as String? ?? '',
      branchAddress: json['branchAddress'] as String? ?? '',
      createdAt: json['createdAt'] != null
          ? (json['createdAt'] is String
              ? DateTime.parse(json['createdAt'] as String)
              : DateTime.fromMillisecondsSinceEpoch(json['createdAt']))
          : DateTime.now(),
      uid: json['uid'] as String? ?? '', // Extract UID from JSON
    );
  }

  // Create a copy of this model with updated fields
  AccountModel copyWith({
    String? id,
    String? name,
    double? initialBalance,
    double? availableBalance,
    String? accountNumber,
    String? branchCode,
    String? branchAddress,
    DateTime? createdAt,
    String? uid,
  }) {
    return AccountModel(
      id: id ?? this.id,
      name: name ?? this.name,
      initialBalance: initialBalance ?? this.initialBalance,
      availableBalance: availableBalance ?? this.availableBalance,
      accountNumber: accountNumber ?? this.accountNumber,
      branchCode: branchCode ?? this.branchCode,
      branchAddress: branchAddress ?? this.branchAddress,
      createdAt: createdAt ?? this.createdAt,
      uid: uid ?? this.uid,
    );
  }

  @override
  String toString() {
    return 'AccountModel{id: $id, name: $name, initialBalance: $initialBalance, availableBalance: $availableBalance, accountNumber: $accountNumber, branchCode: $branchCode, branchAddress: $branchAddress, createdAt: $createdAt, uid: $uid}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AccountModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
