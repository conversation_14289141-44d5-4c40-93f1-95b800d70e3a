import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/balance_sheet_controller.dart';

/// Widget for displaying saved Balance Sheet reports
class SavedBalanceSheetReportsWidget extends StatelessWidget {
  const SavedBalanceSheetReportsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<BalanceSheetController>();

    return Obx(() {
      if (controller.savedReports.isEmpty) {
        return _buildEmptyState(context);
      }

      return Column(
        children: [
          // Reports List
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: controller.savedReports.length,
            itemBuilder: (context, index) {
              final report = controller.savedReports[index];
              return _buildReportCard(context, report, controller);
            },
          ),
        ],
      );
    });
  }

  /// Build empty state when no saved reports
  Widget _buildEmptyState(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(32.0),
      child: Column(
        children: [
          Icon(
            Icons.folder_open_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No Saved Reports',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'Generate and save Balance Sheet reports to see them here.',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
        ],
      ),
    );
  }

  /// Build individual report card
  Widget _buildReportCard(
      BuildContext context, dynamic report, BalanceSheetController controller) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: report.isBalanced ? Colors.green : Colors.red,
          child: Icon(
            report.isBalanced ? Icons.check : Icons.error,
            color: Colors.white,
            size: 20,
          ),
        ),
        title: Text(
          report.companyName,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('As of: ${_formatDate(report.endDate)}'),
            const SizedBox(height: 4),
            Row(
              children: [
                _buildAmountChip('Assets', report.totalAssets, Colors.blue),
                const SizedBox(width: 8),
                _buildAmountChip(
                    'Liabilities', report.totalLiabilities, Colors.orange),
                const SizedBox(width: 8),
                _buildAmountChip('Equity', report.totalEquity, Colors.green),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              'Generated: ${_formatDateTime(report.generatedAt)}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'load':
                controller.loadReport(report.reportId);
                break;
              case 'delete':
                _showDeleteConfirmation(context, report, controller);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'load',
              child: Row(
                children: [
                  Icon(Icons.open_in_new, size: 20),
                  SizedBox(width: 8),
                  Text('Load Report'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, size: 20, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Delete', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
        onTap: () => controller.loadReport(report.reportId),
      ),
    );
  }

  /// Build amount chip
  Widget _buildAmountChip(String label, double amount, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        '$label: ${_formatCurrency(amount)}',
        style: TextStyle(
          fontSize: 10,
          color: color,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// Show delete confirmation dialog
  void _showDeleteConfirmation(
      BuildContext context, dynamic report, BalanceSheetController controller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Report'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Are you sure you want to delete this Balance Sheet report?'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Company: ${report.companyName}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Text('As of: ${_formatDate(report.endDate)}'),
                  Text('Generated: ${_formatDateTime(report.generatedAt)}'),
                ],
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'This action cannot be undone.',
              style: TextStyle(
                color: Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              controller.deleteReport(report.reportId);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  /// Format date for display
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  /// Format date and time for display
  String _formatDateTime(DateTime dateTime) {
    return '${_formatDate(dateTime)} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// Format currency amount
  String _formatCurrency(double amount) {
    final isNegative = amount < 0;
    final absAmount = amount.abs();

    String formatted;
    if (absAmount >= 1000000) {
      formatted = '${(absAmount / 1000000).toStringAsFixed(1)}M';
    } else if (absAmount >= 1000) {
      formatted = '${(absAmount / 1000).toStringAsFixed(0)}K';
    } else {
      formatted = absAmount.toStringAsFixed(0);
    }

    return isNegative ? '($formatted)' : formatted;
  }
}
