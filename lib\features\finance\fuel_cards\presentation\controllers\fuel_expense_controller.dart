import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/features/finance/fuel_cards/repositories/fuel_card_repository.dart';
import 'package:logestics/models/finance/fuel_card_model.dart';
import 'package:logestics/models/finance/fuel_expense_model.dart';
import 'package:uuid/uuid.dart';

class FuelExpenseController extends GetxController {
  final FuelCardRepository fuelCardRepository;

  // Company information
  final currentCompanyId =
      ''.obs; // Replace with actual company ID in production

  // Observable lists and selected items
  final fuelCards = <FuelCardModel>[].obs;
  final filteredFuelCards = <FuelCardModel>[].obs;
  final fuelExpenses = <FuelExpenseModel>[].obs;
  final fuelStations = <String>[].obs;

  final selectedFuelStation = Rxn<String>();
  final selectedFuelCard = Rxn<FuelCardModel>();

  // Form controllers
  final fuelAmountController = TextEditingController();
  final freightAmountController = TextEditingController();
  final otherExpensesController = TextEditingController();
  final tripIdController = TextEditingController();

  // UI state
  final isLoading = false.obs;
  final isSaving = false.obs;
  final error = ''.obs;

  // Form key
  final formKey = GlobalKey<FormState>();

  // Observable values for reactive calculations
  final fuelAmount = 0.0.obs;
  final fuelRate = 0.0.obs;
  final fuelCost = 0.0.obs;
  final freightAmount = 0.0.obs;
  final otherExpenses = 0.0.obs;
  final profitOrLoss = 0.0.obs;

  FuelExpenseController({
    required this.fuelCardRepository,
  });

  @override
  void onInit() {
    super.onInit();

    // Add listeners to form controllers for reactive updates
    fuelAmountController.addListener(_updateFuelAmount);
    freightAmountController.addListener(_updateFreightAmount);
    otherExpensesController.addListener(_updateOtherExpenses);

    // Load data
    loadFuelCards();

    // Setup worker for reactive calculations
    ever(fuelAmount, (_) => _calculateFuelCost());
    ever(fuelRate, (_) => _calculateFuelCost());
    ever(fuelCost, (_) => _calculateProfitOrLoss());
    ever(freightAmount, (_) => _calculateProfitOrLoss());
    ever(otherExpenses, (_) => _calculateProfitOrLoss());
  }

  @override
  void onClose() {
    // Dispose controllers
    fuelAmountController.dispose();
    freightAmountController.dispose();
    otherExpensesController.dispose();
    tripIdController.dispose();
    super.onClose();
  }

  // Load fuel cards for the current company
  Future<void> loadFuelCards() async {
    try {
      isLoading.value = true;
      error.value = '';

      final result = await fuelCardRepository.getFuelCards();
      result.fold(
        (failure) {
          error.value = failure.message;
          SnackbarUtils.showError(AppStrings.errorS, failure.message);
        },
        (cards) {
          // Filter cards by company ID if provided
          if (currentCompanyId.value.isNotEmpty) {
            fuelCards.value = cards
                .where((card) => card.uid == currentCompanyId.value)
                .toList();
          } else {
            fuelCards.value = cards;
          }

          // Extract unique fuel stations
          fuelStations.value =
              fuelCards.map((card) => card.fuelStationName).toSet().toList();

          _filterFuelCardsByStation();
        },
      );
    } catch (e) {
      error.value = e.toString();
      SnackbarUtils.showError(
          AppStrings.errorS, 'Failed to load fuel cards: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Filter fuel cards by selected station
  void _filterFuelCardsByStation() {
    if (selectedFuelStation.value != null &&
        selectedFuelStation.value!.isNotEmpty) {
      filteredFuelCards.value = fuelCards
          .where((card) => card.fuelStationName == selectedFuelStation.value)
          .toList();
    } else {
      filteredFuelCards.value = fuelCards;
    }

    // Clear selected card if it's not in the filtered list
    if (selectedFuelCard.value != null &&
        !filteredFuelCards
            .any((card) => card.id == selectedFuelCard.value!.id)) {
      selectedFuelCard.value = null;
    }
  }

  // Select a fuel station
  void selectFuelStation(String? stationName) {
    selectedFuelStation.value = stationName;
    _filterFuelCardsByStation();
  }

  // Select a fuel card
  void selectFuelCard(FuelCardModel? card) {
    selectedFuelCard.value = card;
    if (card != null) {
      fuelRate.value = card.currentRate;
    } else {
      fuelRate.value = 0.0;
    }
  }

  // Form field updates
  void _updateFuelAmount() {
    if (fuelAmountController.text.isNotEmpty) {
      try {
        final amount = double.parse(fuelAmountController.text);
        fuelAmount.value = amount;
      } catch (e) {
        fuelAmount.value = 0.0;
      }
    } else {
      fuelAmount.value = 0.0;
    }
  }

  void _updateFreightAmount() {
    if (freightAmountController.text.isNotEmpty) {
      try {
        final amount = double.parse(freightAmountController.text);
        freightAmount.value = amount;
      } catch (e) {
        freightAmount.value = 0.0;
      }
    } else {
      freightAmount.value = 0.0;
    }
  }

  void _updateOtherExpenses() {
    if (otherExpensesController.text.isNotEmpty) {
      try {
        final amount = double.parse(otherExpensesController.text);
        otherExpenses.value = amount;
      } catch (e) {
        otherExpenses.value = 0.0;
      }
    } else {
      otherExpenses.value = 0.0;
    }
  }

  // Reactive calculations
  void _calculateFuelCost() {
    fuelCost.value = fuelAmount.value * fuelRate.value;
  }

  void _calculateProfitOrLoss() {
    profitOrLoss.value = FuelExpenseModel.calculateProfitOrLoss(
      freightAmount.value,
      fuelCost.value,
      otherExpenses.value,
    );
  }

  // Validation
  String? validateFuelAmount(String? value) {
    if (value == null || value.isEmpty) {
      return 'Fuel amount is required';
    }

    try {
      final amount = double.parse(value);

      if (amount <= 0) {
        return 'Amount must be greater than 0';
      }

      // Check if enough fuel is available on the card
      if (selectedFuelCard.value != null) {
        if (amount > selectedFuelCard.value!.remainingCapacity) {
          return 'Not enough fuel available on this card (${selectedFuelCard.value!.remainingCapacity.toStringAsFixed(2)} L remaining)';
        }
      }

      // Validate against freight amount
      if (amount * fuelRate.value > freightAmount.value) {
        return 'Fuel cost cannot exceed freight amount';
      }

      return null;
    } catch (e) {
      return 'Please enter a valid number';
    }
  }

  String? validateFreightAmount(String? value) {
    if (value == null || value.isEmpty) {
      return 'Freight amount is required';
    }

    try {
      final amount = double.parse(value);

      if (amount <= 0) {
        return 'Amount must be greater than 0';
      }

      // Validate against fuel cost
      if (fuelCost.value > amount) {
        return 'Freight amount must be greater than fuel cost';
      }

      return null;
    } catch (e) {
      return 'Please enter a valid number';
    }
  }

  String? validateFuelCard(FuelCardModel? value) {
    if (value == null) {
      return 'Please select a fuel card';
    }
    return null;
  }

  String? validateFuelStation(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please select a fuel station';
    }
    return null;
  }

  String? validateTripId(String? value) {
    if (value == null || value.isEmpty) {
      return 'Trip ID is required';
    }
    return null;
  }

  // Create a new fuel expense
  Future<void> createFuelExpense() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    if (isSaving.value) {
      return;
    }

    isSaving.value = true;

    try {
      // Create new expense model
      final newExpense = FuelExpenseModel(
        id: const Uuid().v4(),
        tripId: tripIdController.text,
        fuelCardId: selectedFuelCard.value!.id,
        fuelCardNumber: selectedFuelCard.value!.cardNumber,
        fuelStationName: selectedFuelCard.value!.fuelStationName,
        fuelAmount: fuelAmount.value,
        fuelRate: fuelRate.value,
        fuelCost: fuelCost.value,
        freightAmount: freightAmount.value,
        otherExpenses: otherExpenses.value,
        profitOrLoss: profitOrLoss.value,
        expenseDate: DateTime.now(),
        createdAt: DateTime.now(),
      );

      // In a real implementation, you would save this to Firebase
      // For now, just add to local list
      fuelExpenses.add(newExpense);

      // Update fuel card remaining capacity
      if (selectedFuelCard.value != null) {
        final updatedCard = selectedFuelCard.value!.copyWith(
          remainingCapacity:
              selectedFuelCard.value!.remainingCapacity - fuelAmount.value,
          updatedAt: DateTime.now(),
        );

        // In production, update in Firebase
        // For now, update local list
        final index = fuelCards.indexWhere((card) => card.id == updatedCard.id);
        if (index != -1) {
          fuelCards[index] = updatedCard;
          _filterFuelCardsByStation();
        }
      }

      // Reset form
      resetForm();

      SnackbarUtils.showSuccess(
        AppStrings.success,
        'Fuel expense added successfully',
      );
    } catch (e) {
      log('Error creating fuel expense: $e');
      SnackbarUtils.showError(
        AppStrings.error,
        'Failed to create fuel expense: $e',
      );
    } finally {
      isSaving.value = false;
    }
  }

  // Reset the form
  void resetForm() {
    fuelAmountController.clear();
    freightAmountController.clear();
    otherExpensesController.clear();
    tripIdController.clear();
    selectedFuelCard.value = null;
    selectedFuelStation.value = null;
  }
}
