import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/features/locations/repositories/zone_repository.dart';
import 'package:logestics/models/locations/zone_model.dart';

class GetZonesUseCase {
  final ZoneRepository _zoneRepository;

  GetZonesUseCase(this._zoneRepository);

  /// Executes the use case to get all Zones.
  Future<Either<FailureObj, List<ZoneModel>>> call() async {
    try {
      return await _zoneRepository.getZones();
    } catch (e) {
      return Left(FailureObj(
        code: 'unexpected-error',
        message: 'An unexpected error occurred.',
      ));
    }
  }
}
