import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/core/utils/mixins/pagination_mixin.dart';
import 'package:logestics/features/finance/payers/repository/payer_repository.dart';
import 'package:logestics/models/finance/payer_model.dart';
import 'package:uuid/uuid.dart';

class PayerController extends GetxController with PaginationMixin {
  final PayerRepository repository;

  // Form controllers
  final nameController = TextEditingController();
  final phoneNumberController = TextEditingController();

  // Observable variables
  final payers = <PayerModel>[].obs;
  final isLoading = false.obs;
  final isDeleting = false.obs;
  final isDialogOpen = false.obs;
  final searchQuery = ''.obs;
  final filteredPayers = <PayerModel>[].obs;

  final searchController = TextEditingController();
  final formKey = GlobalKey<FormState>();

  PayerController({required this.repository});

  @override
  void onInit() {
    super.onInit();
    searchController.addListener(_onSearchChanged);
    loadPayers();
  }

  Future<void> loadPayers() async {
    try {
      isLoading.value = true;
      final result = await repository.getPayers();
      result.fold(
        (failure) => SnackbarUtils.showError(
          AppStrings.errorS,
          failure.message,
        ),
        (payersList) {
          payers.value = payersList;
          _filterPayers();
        },
      );
    } catch (e) {
      log('Error loading payers: $e');
      SnackbarUtils.showError(
        AppStrings.errorS,
        'Failed to load payers',
      );
    } finally {
      isLoading.value = false;
    }
  }

  void _onSearchChanged() {
    searchQuery.value = searchController.text;
    _filterPayers();
  }

  void _filterPayers() {
    if (searchQuery.value.isEmpty) {
      filteredPayers.value = payers;
    } else {
      final query = searchQuery.value.toLowerCase();
      filteredPayers.value = payers.where((payer) {
        return payer.name.toLowerCase().contains(query) ||
            payer.phoneNumber.toLowerCase().contains(query);
      }).toList();
    }
    setTotalItems(filteredPayers.length);
  }

  List<PayerModel> get paginatedPayers => paginateList(filteredPayers);

  void openDialog() {
    clearForm();
    isDialogOpen.value = true;
  }

  void closeDialog() {
    isDialogOpen.value = false;
  }

  void clearForm() {
    nameController.clear();
    phoneNumberController.clear();
  }

  String? validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Name is required';
    }
    return null;
  }

  String? validatePhoneNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'Phone number is required';
    }
    // Simple validation for phone number format
    if (!RegExp(r'^\d{10,15}$').hasMatch(value)) {
      return 'Please enter a valid phone number';
    }
    return null;
  }

  Future<void> addPayer() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    if (isLoading.value) {
      return;
    }

    isLoading.value = true;

    try {
      final newPayer = PayerModel(
        id: const Uuid().v4(),
        name: nameController.text,
        phoneNumber: phoneNumberController.text,
        createdAt: DateTime.now(),
      );

      final result = await repository.createPayer(newPayer);
      result.fold(
        (failure) => SnackbarUtils.showError(
          AppStrings.errorS,
          failure.message,
        ),
        (success) {
          payers.add(newPayer);
          _filterPayers();
          clearForm();
          closeDialog();
          SnackbarUtils.showSuccess(
            AppStrings.success,
            success.message,
          );
          // Force refresh to ensure pagination updates immediately
          _refreshPayerData();
        },
      );
    } catch (e) {
      log('Error adding payer: $e');
      SnackbarUtils.showError(
        AppStrings.errorS,
        'Failed to add payer: $e',
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> deletePayer(String payerId) async {
    if (isDeleting.value) {
      log('Delete already in progress after dialog, ignoring request');
      return;
    }

    log('Starting payer deletion for ID: $payerId');
    isDeleting.value = true;

    try {
      final result = await repository.deletePayer(payerId);
      result.fold(
        (failure) {
          log('Failed to delete payer: ${failure.message}');
          SnackbarUtils.showError(
            AppStrings.errorS,
            failure.message,
          );
        },
        (success) {
          log('Successfully deleted payer: $payerId');
          payers.removeWhere((payer) => payer.id == payerId);
          _filterPayers();
          SnackbarUtils.showSuccess(
            AppStrings.success,
            success.message,
          );
          // Force refresh to ensure pagination updates immediately
          _refreshPayerData();
        },
      );
    } catch (e) {
      log('Error deleting payer: $e');
      SnackbarUtils.showError(
        AppStrings.errorS,
        'Failed to delete payer: $e',
      );
    } finally {
      log('Resetting deletion state');
      isDeleting.value = false;
    }
  }

  Future<void> updatePayer(PayerModel payer) async {
    if (isLoading.value) {
      return;
    }

    isLoading.value = true;

    try {
      final result = await repository.updatePayer(payer);
      result.fold(
        (failure) => SnackbarUtils.showError(
          AppStrings.errorS,
          failure.message,
        ),
        (success) {
          final index = payers.indexWhere((p) => p.id == payer.id);
          if (index != -1) {
            payers[index] = payer;
            _filterPayers();
          }
          SnackbarUtils.showSuccess(
            AppStrings.success,
            success.message,
          );
          // Force refresh to ensure pagination updates immediately
          _refreshPayerData();
        },
      );
    } catch (e) {
      log('Error updating payer: $e');
      SnackbarUtils.showError(
        AppStrings.errorS,
        'Failed to update payer: $e',
      );
    } finally {
      isLoading.value = false;
    }
  }

  void _refreshPayerData() {
    // Immediately refresh the filtered list and pagination
    _filterPayers();

    // Add a small delay to ensure any Firestore listeners have processed the change
    Future.delayed(const Duration(milliseconds: 500), () {
      // Force refresh the data from Firestore
      loadPayers();
    });
  }

  @override
  void onClose() {
    nameController.dispose();
    phoneNumberController.dispose();
    searchController.dispose();
    super.onClose();
  }
}
