import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';
import 'dart:developer';
import 'package:logestics/models/finance/expense_category_model.dart';

/// Optimized Firebase API for managing expense categories with user authentication and security
class ExpenseCategoryFirebaseService {
  static const String _collectionPath =
      AppCollection.expenseCategoriesCollection;
  final FirebaseFirestore _firestore;

  /// Get current user's UID or return 'anonymous' if not authenticated
  String get _currentUserId =>
      FirebaseAuth.instance.currentUser?.uid ?? 'anonymous';

  /// Constructor with optional FirebaseFirestore instance for testing
  ExpenseCategoryFirebaseService([FirebaseFirestore? firestore])
      : _firestore = firestore ?? FirebaseFirestore.instance;

  /// Creates a new expense category for the current user
  /// Returns the created category with generated ID
  Future<ExpenseCategoryModel> createExpenseCategory({
    required String name,
    required String description,
  }) async {
    try {
      log('Creating expense category: $name');

      // Create category with auto-generated ID
      final categoryRef = _firestore.collection(_collectionPath).doc();
      final categoryId = categoryRef.id;

      final newCategory = ExpenseCategoryModel(
        id: categoryId,
        name: name,
        description: description,
        createdAt: DateTime.now(),
        uid: _currentUserId,
      );

      await categoryRef.set(newCategory.toJson());

      log('Successfully created expense category: $categoryId');
      return newCategory;
    } catch (e) {
      log('Error creating expense category: $e', error: e);
      rethrow;
    }
  }

  /// Retrieves all expense categories for the current user
  /// Ordered by creation date (newest first) or name (alphabetical)
  Future<List<ExpenseCategoryModel>> getExpenseCategories({
    bool orderByName = false,
  }) async {
    try {
      log('Fetching expense categories from Firestore');

      Query query = _firestore
          .collection(_collectionPath)
          .where('uid', isEqualTo: _currentUserId);

      // Apply ordering
      if (orderByName) {
        query = query.orderBy('name');
      } else {
        query = query.orderBy('createdAt', descending: true);
      }

      final snapshot = await query.get();

      final categories = snapshot.docs
          .map((doc) =>
              ExpenseCategoryModel.fromJson(doc.data() as Map<String, dynamic>))
          .toList();

      log('Successfully fetched ${categories.length} expense categories');
      return categories;
    } catch (e) {
      log('Error fetching expense categories: $e', error: e);
      return [];
    }
  }

  /// Retrieves a specific expense category by ID
  /// Returns null if not found or user doesn't have permission
  Future<ExpenseCategoryModel?> getExpenseCategoryById(
      String categoryId) async {
    try {
      if (categoryId.isEmpty) {
        throw ArgumentError('Category ID cannot be empty');
      }

      final doc =
          await _firestore.collection(_collectionPath).doc(categoryId).get();

      if (!doc.exists) {
        log('Expense category not found: $categoryId');
        return null;
      }

      final data = doc.data() as Map<String, dynamic>;

      // Verify user permission
      if (data['uid'] != _currentUserId) {
        log('User does not have permission to access this category');
        return null;
      }

      return ExpenseCategoryModel.fromJson(data);
    } catch (e) {
      log('Error getting expense category by ID: $e', error: e);
      return null;
    }
  }

  /// Updates an existing expense category
  /// Validates user permission before updating
  Future<ExpenseCategoryModel> updateExpenseCategory(
      ExpenseCategoryModel category) async {
    try {
      if (category.id.isEmpty) {
        throw ArgumentError('Category ID cannot be empty');
      }

      log('Updating expense category: ${category.id}');

      // Verify category exists and user has permission
      final existingDoc =
          await _firestore.collection(_collectionPath).doc(category.id).get();

      if (!existingDoc.exists) {
        throw Exception('Category not found');
      }

      final existingData = existingDoc.data() as Map<String, dynamic>;
      if (existingData['uid'] != _currentUserId) {
        throw Exception('You do not have permission to update this category');
      }

      // Preserve original UID
      final updatedCategory = category.copyWith(
        uid: existingData['uid'],
      );

      await _firestore
          .collection(_collectionPath)
          .doc(category.id)
          .update(updatedCategory.toJson());

      log('Successfully updated expense category: ${category.id}');
      return updatedCategory;
    } catch (e) {
      log('Error updating expense category: $e', error: e);
      rethrow;
    }
  }

  /// Deletes an expense category
  /// Validates user permission before deletion
  Future<void> deleteExpenseCategory(String categoryId) async {
    try {
      if (categoryId.isEmpty) {
        throw ArgumentError('Category ID cannot be empty');
      }

      log('Deleting expense category: $categoryId');

      // Verify category exists and user has permission
      final categoryDoc =
          await _firestore.collection(_collectionPath).doc(categoryId).get();

      if (!categoryDoc.exists) {
        throw Exception('Category not found');
      }

      final categoryData = categoryDoc.data() as Map<String, dynamic>;
      if (categoryData['uid'] != _currentUserId) {
        throw Exception('You do not have permission to delete this category');
      }

      await _firestore.collection(_collectionPath).doc(categoryId).delete();

      log('Successfully deleted expense category: $categoryId');
    } catch (e) {
      log('Error deleting expense category: $e', error: e);
      rethrow;
    }
  }

  /// Checks if a category name already exists for the current user
  /// Useful for preventing duplicate category names
  Future<bool> categoryNameExists(String name, {String? excludeId}) async {
    try {
      Query query = _firestore
          .collection(_collectionPath)
          .where('uid', isEqualTo: _currentUserId)
          .where('name', isEqualTo: name);

      final snapshot = await query.get();

      // If excluding an ID (for updates), filter it out
      if (excludeId != null) {
        return snapshot.docs.any((doc) => doc.id != excludeId);
      }

      return snapshot.docs.isNotEmpty;
    } catch (e) {
      log('Error checking category name existence: $e', error: e);
      return false;
    }
  }

  /// Batch delete multiple categories
  /// Validates user permission for each category before deletion
  Future<void> deleteMultipleCategories(List<String> categoryIds) async {
    try {
      if (categoryIds.isEmpty) {
        throw ArgumentError('Category IDs list cannot be empty');
      }

      log('Batch deleting ${categoryIds.length} expense categories');

      final batch = _firestore.batch();

      // Verify permissions for all categories first
      for (final categoryId in categoryIds) {
        final categoryDoc =
            await _firestore.collection(_collectionPath).doc(categoryId).get();

        if (!categoryDoc.exists) {
          throw Exception('Category not found: $categoryId');
        }

        final categoryData = categoryDoc.data() as Map<String, dynamic>;
        if (categoryData['uid'] != _currentUserId) {
          throw Exception(
              'You do not have permission to delete category: $categoryId');
        }

        batch.delete(_firestore.collection(_collectionPath).doc(categoryId));
      }

      await batch.commit();

      log('Successfully batch deleted ${categoryIds.length} expense categories');
    } catch (e) {
      log('Error batch deleting expense categories: $e', error: e);
      rethrow;
    }
  }
}
