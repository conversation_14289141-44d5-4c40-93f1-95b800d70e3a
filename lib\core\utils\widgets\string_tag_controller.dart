import 'package:textfield_tags/textfield_tags.dart';

class StringTagController extends TextfieldTagsController<String> {
  @override
  List<String> getTags = [];

  @override
  bool? onTagSubmitted(String tag) {
    if (!getTags.contains(tag)) {
      getTags.add(tag);
      notifyListeners();
      return true;
    }
    return false;
  }

  @override
  bool? removeTag(String tag) {
    final result = getTags.remove(tag);
    notifyListeners();
    return result;
  }

  @override
  bool? clearTags() {
    getTags.clear();
    notifyListeners();
    return true;
  }
}
