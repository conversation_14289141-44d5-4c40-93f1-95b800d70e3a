import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../../../core/utils/app_constants/texts/app_strings.dart';
import '../../../../../core/utils/mixins/pagination_mixin.dart';
import '../../../../../core/utils/snackbar_utils.dart';
import '../../../../../models/finance/chart_of_accounts_model.dart';
import '../../../../../models/finance/journal_entry_model.dart';
import '../../services/account_journal_transaction_service.dart';

class AccountJournalTransactionController extends GetxController with PaginationMixin {
  final AccountJournalTransactionService _transactionService;

  // Observable variables
  final transactions = <AccountJournalTransaction>[].obs;
  final isLoading = false.obs;
  final isError = false.obs;
  final errorMessage = ''.obs;

  // Pagination variables
  final isLoadingPage = false.obs;
  final hasNextPage = false.obs;
  QueryDocumentSnapshot? _lastDocument;
  String? _currentAccountId;
  String? _currentUid;

  // Filter variables
  final selectedDateRange = Rx<DateTimeRange?>(null);
  final selectedStatuses = <JournalEntryStatus>[].obs;
  final searchQuery = ''.obs;

  // Active account
  final activeAccount = Rx<ChartOfAccountsModel?>(null);

  // Account balance
  final accountBalance = 0.0.obs;
  final isLoadingBalance = false.obs;

  AccountJournalTransactionController(this._transactionService);

  @override
  void onInit() {
    super.onInit();
    // Set default pagination settings
    setItemsPerPage(25); // Default to 25 transactions per page
  }

  /// Load transactions for a specific account with pagination
  Future<void> loadTransactionsForAccount(
    ChartOfAccountsModel account,
    String uid, {
    bool isFirstPage = true,
  }) async {
    try {
      if (isFirstPage) {
        isLoading.value = true;
        _currentAccountId = account.id;
        _currentUid = uid;
        activeAccount.value = account;
        _lastDocument = null;
        transactions.clear();
        setCurrentPage(1);
      } else {
        isLoadingPage.value = true;
      }

      isError.value = false;

      final result = await _transactionService.getAccountTransactionsPaginated(
        accountId: account.id,
        uid: uid,
        limit: itemsPerPage.value,
        lastDocument: _lastDocument,
        startDate: selectedDateRange.value?.start,
        endDate: selectedDateRange.value?.end,
        statusFilter: selectedStatuses.isNotEmpty ? selectedStatuses : null,
      );

      if (isFirstPage) {
        transactions.value = result.transactions;
      } else {
        transactions.addAll(result.transactions);
      }

      _lastDocument = result.nextPageCursor;
      hasNextPage.value = result.hasNextPage;

      // Load account balance
      if (isFirstPage) {
        _loadAccountBalance(account, uid);
      }
    } catch (e) {
      isError.value = true;
      errorMessage.value = e.toString();
      SnackbarUtils.showError(
        AppStrings.errorS,
        'Failed to load transactions: $e',
      );
    } finally {
      isLoading.value = false;
      isLoadingPage.value = false;
    }
  }

  /// Load account balance as of current date
  Future<void> _loadAccountBalance(ChartOfAccountsModel account, String uid) async {
    try {
      isLoadingBalance.value = true;
      final balance = await _transactionService.getAccountBalanceAsOfDate(
        accountId: account.id,
        uid: uid,
        asOfDate: DateTime.now(),
        accountCategory: account.accountCategory,
      );
      accountBalance.value = balance;
    } catch (e) {
      // Don't show error for balance loading, just log it
      print('Error loading account balance: $e');
    } finally {
      isLoadingBalance.value = false;
    }
  }

  /// Set date range filter
  void setDateRange(DateTimeRange? dateRange) {
    selectedDateRange.value = dateRange;
    if (_currentAccountId != null && _currentUid != null) {
      loadTransactionsForAccount(activeAccount.value!, _currentUid!, isFirstPage: true);
    }
  }

  /// Clear date range filter
  void clearDateRange() {
    selectedDateRange.value = null;
    if (_currentAccountId != null && _currentUid != null) {
      loadTransactionsForAccount(activeAccount.value!, _currentUid!, isFirstPage: true);
    }
  }

  /// Add status filter
  void addStatusFilter(JournalEntryStatus status) {
    if (!selectedStatuses.contains(status)) {
      selectedStatuses.add(status);
      if (_currentAccountId != null && _currentUid != null) {
        loadTransactionsForAccount(activeAccount.value!, _currentUid!, isFirstPage: true);
      }
    }
  }

  /// Remove status filter
  void removeStatusFilter(JournalEntryStatus status) {
    selectedStatuses.remove(status);
    if (_currentAccountId != null && _currentUid != null) {
      loadTransactionsForAccount(activeAccount.value!, _currentUid!, isFirstPage: true);
    }
  }

  /// Clear all status filters
  void clearStatusFilters() {
    selectedStatuses.clear();
    if (_currentAccountId != null && _currentUid != null) {
      loadTransactionsForAccount(activeAccount.value!, _currentUid!, isFirstPage: true);
    }
  }

  /// Set search query
  void setSearchQuery(String query) {
    searchQuery.value = query;
    // Note: Search functionality would need to be implemented in the service
    // For now, we'll just store the query
  }

  /// Get current page transactions for display
  List<AccountJournalTransaction> getCurrentPageTransactions() {
    final startIndex = (currentPage.value - 1) * itemsPerPage.value;
    final endIndex = startIndex + itemsPerPage.value;
    
    if (startIndex >= transactions.length) {
      return [];
    }
    
    return transactions.sublist(
      startIndex,
      endIndex > transactions.length ? transactions.length : endIndex,
    );
  }

  /// Override pagination methods to handle loading more data
  @override
  void setCurrentPage(int page) {
    super.setCurrentPage(page);

    // Calculate if we need to load more data
    final startIndex = (page - 1) * itemsPerPage.value;
    if (startIndex >= transactions.length && hasNextPage.value) {
      loadTransactionsForAccount(activeAccount.value!, _currentUid!, isFirstPage: false);
    }
  }

  @override
  void setItemsPerPage(int count) {
    super.setItemsPerPage(count);

    // Reload data with new page size
    if (_currentAccountId != null && _currentUid != null) {
      loadTransactionsForAccount(activeAccount.value!, _currentUid!, isFirstPage: true);
    }
  }

  /// Format currency amount
  String formatCurrency(double amount) {
    final formatter = NumberFormat.currency(symbol: 'Rs. ', decimalDigits: 2);
    return formatter.format(amount);
  }

  /// Format date
  String formatDate(DateTime date) {
    return DateFormat('MMM dd, yyyy').format(date);
  }

  /// Format date and time
  String formatDateTime(DateTime date) {
    return DateFormat('MMM dd, yyyy HH:mm').format(date);
  }

  /// Get status color
  Color getStatusColor(JournalEntryStatus status) {
    switch (status) {
      case JournalEntryStatus.draft:
        return Colors.orange;
      case JournalEntryStatus.posted:
        return Colors.green;
      case JournalEntryStatus.reversed:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  /// Get status display name
  String getStatusDisplayName(JournalEntryStatus status) {
    return status.displayName;
  }

  /// Get transaction type display (Debit/Credit)
  String getTransactionType(AccountJournalTransaction transaction) {
    return transaction.isDebit ? 'Debit' : 'Credit';
  }

  /// Get transaction type color
  Color getTransactionTypeColor(AccountJournalTransaction transaction) {
    return transaction.isDebit ? Colors.green : Colors.red;
  }

  /// Refresh transactions
  Future<void> refreshTransactions() async {
    if (_currentAccountId != null && _currentUid != null) {
      await loadTransactionsForAccount(activeAccount.value!, _currentUid!, isFirstPage: true);
    }
  }

  /// Get total transaction count
  int get totalTransactionCount => transactions.length;

  /// Check if there are any transactions
  bool get hasTransactions => transactions.isNotEmpty;

  /// Check if filters are applied
  bool get hasFiltersApplied => 
      selectedDateRange.value != null || 
      selectedStatuses.isNotEmpty || 
      searchQuery.value.isNotEmpty;
}
