import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';
import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/firebase_service/asset/asset_firebase_service.dart';
import 'package:logestics/firebase_service/asset/asset_maintenance_firebase_service.dart';
import 'package:logestics/models/asset/asset_model.dart';
import 'package:logestics/models/asset/asset_maintenance_model.dart';
import 'package:logestics/repositories/asset/asset_repository.dart';

class AssetRepositoryImpl implements AssetRepository {
  final AssetFirebaseService _assetFirebaseService;
  final AssetMaintenanceFirebaseService _maintenanceFirebaseService;

  AssetRepositoryImpl(
    this._assetFirebaseService,
    this._maintenanceFirebaseService,
  );

  @override
  Future<Either<FailureObj, SuccessObj>> createAsset(
    AssetModel asset, {
    List<File>? files,
    List<Uint8List>? fileBytes,
    List<String>? fileNames,
  }) async {
    try {
      log('Repository: Creating asset ${asset.name}');
      await _assetFirebaseService.createAsset(
        asset,
        files: files,
        fileBytes: fileBytes,
        fileNames: fileNames,
      );
      return Right(SuccessObj(message: 'Asset created successfully'));
    } catch (e) {
      log('Repository: Error creating asset: $e');
      return Left(FailureObj(
        code: 'asset-create-error',
        message: 'Failed to create asset: ${e.toString()}',
      ));
    }
  }

  @override
  Future<Either<FailureObj, List<AssetModel>>> getAssets() async {
    try {
      log('Repository: Fetching assets');
      final assets = await _assetFirebaseService.getAssets();
      return Right(assets);
    } catch (e) {
      log('Repository: Error fetching assets: $e');
      return Left(FailureObj(
        code: 'asset-fetch-error',
        message: 'Failed to fetch assets: ${e.toString()}',
      ));
    }
  }

  @override
  Stream<List<AssetModel>> listenToAssets() {
    try {
      log('AssetRepository: Setting up real-time assets listener');
      return _assetFirebaseService
          .listenToAssets()
          .map<List<AssetModel>>((assets) {
        log('AssetRepository: Stream received ${assets.length} assets');
        return assets;
      });
    } catch (e) {
      log('AssetRepository: Error setting up assets listener: $e');
      return Stream.value(<AssetModel>[]);
    }
  }

  @override
  Future<Either<FailureObj, AssetModel>> getAssetById(String assetId) async {
    try {
      log('Repository: Fetching asset by ID: $assetId');
      final asset = await _assetFirebaseService.getAssetById(assetId);
      if (asset == null) {
        return Left(FailureObj(
          code: 'asset-not-found',
          message: 'Asset not found',
        ));
      }
      return Right(asset);
    } catch (e) {
      log('Repository: Error fetching asset by ID: $e');
      return Left(FailureObj(
        code: 'asset-fetch-error',
        message: 'Failed to fetch asset: ${e.toString()}',
      ));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> updateAsset(
    AssetModel asset, {
    List<File>? files,
    List<Uint8List>? fileBytes,
    List<String>? fileNames,
  }) async {
    try {
      log('Repository: Updating asset ${asset.id}');
      await _assetFirebaseService.updateAsset(
        asset,
        files: files,
        fileBytes: fileBytes,
        fileNames: fileNames,
      );
      return Right(SuccessObj(message: 'Asset updated successfully'));
    } catch (e) {
      log('Repository: Error updating asset: $e');
      return Left(FailureObj(
        code: 'asset-update-error',
        message: 'Failed to update asset: ${e.toString()}',
      ));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> deleteAsset(String assetId) async {
    try {
      log('Repository: Deleting asset $assetId');
      await _assetFirebaseService.deleteAsset(assetId);
      return Right(SuccessObj(message: 'Asset deleted successfully'));
    } catch (e) {
      log('Repository: Error deleting asset: $e');
      return Left(FailureObj(
        code: 'asset-delete-error',
        message: 'Failed to delete asset: ${e.toString()}',
      ));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> removeAttachment(
    String assetId,
    String attachmentUrl,
  ) async {
    try {
      log('Repository: Removing attachment from asset $assetId');
      await _assetFirebaseService.removeAttachment(assetId, attachmentUrl);
      return Right(SuccessObj(message: 'Attachment removed successfully'));
    } catch (e) {
      log('Repository: Error removing attachment: $e');
      return Left(FailureObj(
        code: 'attachment-remove-error',
        message: 'Failed to remove attachment: ${e.toString()}',
      ));
    }
  }

  @override
  Future<Either<FailureObj, List<AssetModel>>> searchAssets(
      String query) async {
    try {
      log('Repository: Searching assets with query: $query');
      final assets = await _assetFirebaseService.searchAssets(query);
      return Right(assets);
    } catch (e) {
      log('Repository: Error searching assets: $e');
      return Left(FailureObj(
        code: 'asset-search-error',
        message: 'Failed to search assets: ${e.toString()}',
      ));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> createMaintenance(
    AssetMaintenanceModel maintenance, {
    List<File>? files,
    List<Uint8List>? fileBytes,
    List<String>? fileNames,
  }) async {
    try {
      log('Repository: Creating maintenance record for asset ${maintenance.assetId}');
      await _maintenanceFirebaseService.createMaintenance(
        maintenance,
        files: files,
        fileBytes: fileBytes,
        fileNames: fileNames,
      );
      return Right(
          SuccessObj(message: 'Maintenance record created successfully'));
    } catch (e) {
      log('Repository: Error creating maintenance record: $e');
      return Left(FailureObj(
        code: 'maintenance-create-error',
        message: 'Failed to create maintenance record: ${e.toString()}',
      ));
    }
  }

  @override
  Future<Either<FailureObj, List<AssetMaintenanceModel>>>
      getMaintenanceByAssetId(
    String assetId,
  ) async {
    try {
      log('Repository: Fetching maintenance records for asset: $assetId');
      final maintenanceRecords =
          await _maintenanceFirebaseService.getMaintenanceByAssetId(assetId);
      return Right(maintenanceRecords);
    } catch (e) {
      log('Repository: Error fetching maintenance records: $e');
      return Left(FailureObj(
        code: 'maintenance-fetch-error',
        message: 'Failed to fetch maintenance records: ${e.toString()}',
      ));
    }
  }

  @override
  Future<Either<FailureObj, List<AssetMaintenanceModel>>>
      getAllMaintenance() async {
    try {
      log('Repository: Fetching all maintenance records');
      final maintenanceRecords =
          await _maintenanceFirebaseService.getAllMaintenance();
      return Right(maintenanceRecords);
    } catch (e) {
      log('Repository: Error fetching all maintenance records: $e');
      return Left(FailureObj(
        code: 'maintenance-fetch-error',
        message: 'Failed to fetch maintenance records: ${e.toString()}',
      ));
    }
  }

  @override
  Future<Either<FailureObj, AssetMaintenanceModel>> getMaintenanceById(
    String maintenanceId,
  ) async {
    try {
      log('Repository: Fetching maintenance record by ID: $maintenanceId');
      final maintenance =
          await _maintenanceFirebaseService.getMaintenanceById(maintenanceId);
      if (maintenance == null) {
        return Left(FailureObj(
          code: 'maintenance-not-found',
          message: 'Maintenance record not found',
        ));
      }
      return Right(maintenance);
    } catch (e) {
      log('Repository: Error fetching maintenance record by ID: $e');
      return Left(FailureObj(
        code: 'maintenance-fetch-error',
        message: 'Failed to fetch maintenance record: ${e.toString()}',
      ));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> updateMaintenance(
    AssetMaintenanceModel maintenance, {
    List<File>? files,
    List<Uint8List>? fileBytes,
    List<String>? fileNames,
  }) async {
    try {
      log('Repository: Updating maintenance record ${maintenance.id}');
      await _maintenanceFirebaseService.updateMaintenance(
        maintenance,
        files: files,
        fileBytes: fileBytes,
        fileNames: fileNames,
      );
      return Right(
          SuccessObj(message: 'Maintenance record updated successfully'));
    } catch (e) {
      log('Repository: Error updating maintenance record: $e');
      return Left(FailureObj(
        code: 'maintenance-update-error',
        message: 'Failed to update maintenance record: ${e.toString()}',
      ));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> deleteMaintenance(
      String maintenanceId) async {
    try {
      log('Repository: Deleting maintenance record $maintenanceId');
      await _maintenanceFirebaseService.deleteMaintenance(maintenanceId);
      return Right(
          SuccessObj(message: 'Maintenance record deleted successfully'));
    } catch (e) {
      log('Repository: Error deleting maintenance record: $e');
      return Left(FailureObj(
        code: 'maintenance-delete-error',
        message: 'Failed to delete maintenance record: ${e.toString()}',
      ));
    }
  }

  @override
  Future<Either<FailureObj, double>> getTotalMaintenanceCost(
      String assetId) async {
    try {
      log('Repository: Calculating total maintenance cost for asset: $assetId');
      final totalCost =
          await _maintenanceFirebaseService.getTotalMaintenanceCost(assetId);
      return Right(totalCost);
    } catch (e) {
      log('Repository: Error calculating total maintenance cost: $e');
      return Left(FailureObj(
        code: 'maintenance-cost-error',
        message: 'Failed to calculate maintenance cost: ${e.toString()}',
      ));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> removeMaintenanceAttachment(
    String maintenanceId,
    String attachmentUrl,
  ) async {
    try {
      log('Repository: Removing attachment from maintenance record $maintenanceId');
      await _maintenanceFirebaseService.removeAttachment(
          maintenanceId, attachmentUrl);
      return Right(
          SuccessObj(message: 'Maintenance attachment removed successfully'));
    } catch (e) {
      log('Repository: Error removing maintenance attachment: $e');
      return Left(FailureObj(
        code: 'maintenance-attachment-remove-error',
        message: 'Failed to remove maintenance attachment: ${e.toString()}',
      ));
    }
  }
}
