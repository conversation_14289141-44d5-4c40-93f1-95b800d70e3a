import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/balance_sheet_controller.dart';

/// Summary cards widget for Balance Sheet report
class BalanceSheetSummaryWidget extends StatelessWidget {
  const BalanceSheetSummaryWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<BalanceSheetController>();

    return Obx(() {
      if (!controller.hasReport) {
        return const SizedBox.shrink();
      }

      final report = controller.currentReport!;

      return Column(
        children: [
          // Main Balance Sheet Equation Row
          Row(
            children: [
              // Total Assets Card
              Expanded(
                child: _buildSummaryCard(
                  context,
                  title: 'Total Assets',
                  amount: report.totalAssets,
                  icon: Icons.account_balance_wallet,
                  color: Colors.blue,
                  subtitle: '${report.assetGroups.length} asset categories',
                ),
              ),
              const SizedBox(width: 8),
              // Equals Sign
              Container(
                padding: const EdgeInsets.all(8),
                child: Text(
                  '=',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[600],
                  ),
                ),
              ),
              const SizedBox(width: 8),
              // Total Liabilities Card
              Expanded(
                child: _buildSummaryCard(
                  context,
                  title: 'Total Liabilities',
                  amount: report.totalLiabilities,
                  icon: Icons.credit_card,
                  color: Colors.orange,
                  subtitle: '${report.liabilityGroups.length} liability categories',
                ),
              ),
              const SizedBox(width: 8),
              // Plus Sign
              Container(
                padding: const EdgeInsets.all(8),
                child: Text(
                  '+',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[600],
                  ),
                ),
              ),
              const SizedBox(width: 8),
              // Total Equity Card
              Expanded(
                child: _buildSummaryCard(
                  context,
                  title: 'Total Equity',
                  amount: report.totalEquity,
                  icon: Icons.trending_up,
                  color: Colors.green,
                  subtitle: '${report.equityGroups.length} equity categories',
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Balance Validation and Ratios Row
          Row(
            children: [
              // Balance Status Card
              Expanded(
                child: _buildStatusCard(
                  context,
                  title: 'Balance Status',
                  status: report.isBalanced ? 'Balanced' : 'Unbalanced',
                  icon: report.isBalanced ? Icons.check_circle : Icons.error,
                  color: report.isBalanced ? Colors.green : Colors.red,
                  subtitle: report.isBalanced 
                      ? 'Assets = Liabilities + Equity'
                      : 'Balance equation does not match',
                ),
              ),
              const SizedBox(width: 16),
              // Debt to Equity Ratio Card
              Expanded(
                child: _buildRatioCard(
                  context,
                  title: 'Debt-to-Equity Ratio',
                  ratio: report.totalEquity > 0 ? report.totalLiabilities / report.totalEquity : 0,
                  icon: Icons.compare_arrows,
                  color: Colors.purple,
                  subtitle: 'Liabilities to equity ratio',
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Additional Metrics Row
          Row(
            children: [
              // Working Capital Card (Current Assets - Current Liabilities)
              Expanded(
                child: _buildWorkingCapitalCard(context, report),
              ),
              const SizedBox(width: 16),
              // Equity Percentage Card
              Expanded(
                child: _buildPercentageCard(
                  context,
                  title: 'Equity Percentage',
                  percentage: report.totalAssets > 0 ? (report.totalEquity / report.totalAssets) * 100 : 0,
                  icon: Icons.pie_chart,
                  color: Colors.teal,
                  subtitle: 'Equity as % of total assets',
                ),
              ),
            ],
          ),
        ],
      );
    });
  }

  /// Build summary card for monetary amounts
  Widget _buildSummaryCard(
    BuildContext context, {
    required String title,
    required double amount,
    required IconData icon,
    required Color color,
    required String subtitle,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              _formatCurrency(amount),
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build status card
  Widget _buildStatusCard(
    BuildContext context, {
    required String title,
    required String status,
    required IconData icon,
    required Color color,
    required String subtitle,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              status,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build ratio card
  Widget _buildRatioCard(
    BuildContext context, {
    required String title,
    required double ratio,
    required IconData icon,
    required Color color,
    required String subtitle,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              '${ratio.toStringAsFixed(2)}:1',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build percentage card
  Widget _buildPercentageCard(
    BuildContext context, {
    required String title,
    required double percentage,
    required IconData icon,
    required Color color,
    required String subtitle,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              '${percentage.toStringAsFixed(1)}%',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build working capital card
  Widget _buildWorkingCapitalCard(BuildContext context, dynamic report) {
    // Calculate working capital (current assets - current liabilities)
    final currentAssets = report.assetGroups
        .where((group) => group.groupType == 'current_assets')
        .fold(0.0, (sum, group) => sum + group.groupTotal);
    
    final currentLiabilities = report.liabilityGroups
        .where((group) => group.groupType == 'current_liabilities')
        .fold(0.0, (sum, group) => sum + group.groupTotal);
    
    final workingCapital = currentAssets - currentLiabilities;
    final isPositive = workingCapital >= 0;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isPositive ? Icons.trending_up : Icons.trending_down,
                  color: isPositive ? Colors.green : Colors.red,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Working Capital',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              _formatCurrency(workingCapital),
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: isPositive ? Colors.green : Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Current assets - current liabilities',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Format currency amount
  String _formatCurrency(double amount) {
    final isNegative = amount < 0;
    final absAmount = amount.abs();
    
    String formatted;
    if (absAmount >= 1000000) {
      formatted = 'PKR ${(absAmount / 1000000).toStringAsFixed(2)}M';
    } else if (absAmount >= 1000) {
      formatted = 'PKR ${(absAmount / 1000).toStringAsFixed(1)}K';
    } else {
      formatted = 'PKR ${absAmount.toStringAsFixed(2)}';
    }
    
    return isNegative ? '($formatted)' : formatted;
  }
}
