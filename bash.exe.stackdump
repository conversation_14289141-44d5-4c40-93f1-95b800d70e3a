Stack trace:
Frame         Function      Args
0007FFFF9B70  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFF9B70, 0007FFFF8A70) msys-2.0.dll+0x1FE8E
0007FFFF9B70  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9E48) msys-2.0.dll+0x67F9
0007FFFF9B70  000210046832 (000210286019, 0007FFFF9A28, 0007FFFF9B70, 000000000000) msys-2.0.dll+0x6832
0007FFFF9B70  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9B70  000210068E24 (0007FFFF9B80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9E50  00021006A225 (0007FFFF9B80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD461C0000 ntdll.dll
7FFD45C60000 KERNEL32.DLL
7FFD438A0000 KERNELBASE.dll
7FFD44050000 USER32.dll
7FFD437D0000 win32u.dll
7FFD45030000 GDI32.dll
7FFD43460000 gdi32full.dll
7FFD435A0000 msvcp_win.dll
7FFD43310000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD45FC0000 advapi32.dll
7FFD44AE0000 msvcrt.dll
7FFD45E30000 sechost.dll
7FFD45900000 RPCRT4.dll
7FFD42820000 CRYPTBASE.DLL
7FFD43800000 bcryptPrimitives.dll
7FFD45060000 IMM32.DLL
