import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/mixins/pagination_mixin.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/features/finance/accounts/repositories/account_transaction_repository.dart';
import 'package:logestics/features/finance/expense_categories/repositories/expense_category_repository.dart';
import 'package:intl/intl.dart';
import 'package:logestics/models/finance/account_model.dart';
import 'package:logestics/models/finance/account_transaction_model.dart';
import 'package:logestics/models/finance/expense_category_model.dart';
import 'package:logestics/models/finance/paginated_transaction_result.dart';

class AccountTransactionController extends GetxController with PaginationMixin {
  final AccountTransactionRepository repository;
  final ExpenseCategoryRepository?
      categoryRepository; // Make it optional for backward compatibility

  // Observable variables
  final transactions = <AccountTransactionModel>[].obs;
  final filteredTransactions = <AccountTransactionModel>[].obs;
  final isLoading = false.obs;
  final isError = false.obs;
  final errorMessage = ''.obs;

  // Pagination variables
  final paginatedTransactions = <AccountTransactionModel>[].obs;
  final isLoadingPage = false.obs;
  final hasNextPage = false.obs;
  QueryDocumentSnapshot? _lastDocument;
  String? _currentAccountId;

  // Expense categories
  final categories = <ExpenseCategoryModel>[].obs;
  final isCategoriesLoading = false.obs;
  final selectedCategory = Rxn<ExpenseCategoryModel>();

  // Filter variables
  final selectedDateRange = Rx<DateTimeRange?>(null);
  final selectedTypes = <TransactionType>[].obs;
  final searchQuery = ''.obs;

  // Active account
  final activeAccount = Rx<AccountModel?>(null);

  AccountTransactionController({
    required this.repository,
    this.categoryRepository, // Optional for backward compatibility
  });

  @override
  void onInit() {
    super.onInit();
    // Set default pagination settings
    setItemsPerPage(25); // Default to 25 transactions per page

    if (categoryRepository != null) {
      loadExpenseCategories();
    }
  }

  // Load expense categories from repository
  Future<void> loadExpenseCategories() async {
    if (categoryRepository == null) return;

    isCategoriesLoading.value = true;
    try {
      final result = await categoryRepository!.getExpenseCategories();
      result.fold(
        (failure) {
          SnackbarUtils.showError(
            AppStrings.errorS,
            'Failed to load expense categories: ${failure.message}',
          );
        },
        (categoriesList) {
          categories.value = categoriesList;
        },
      );
    } catch (e) {
      SnackbarUtils.showError(
        AppStrings.errorS,
        'Failed to load expense categories: $e',
      );
    } finally {
      isCategoriesLoading.value = false;
    }
  }

  // Select a category for a transaction
  void selectCategory(ExpenseCategoryModel category) {
    selectedCategory.value = category;
  }

  // Clear selected category
  void clearSelectedCategory() {
    selectedCategory.value = null;
  }

  // Update a transaction with a category
  Future<bool> updateTransactionCategory(
      String transactionId, String categoryId, String categoryName) async {
    try {
      isLoading.value = true;

      // Find the transaction
      final index = transactions.indexWhere((t) => t.id == transactionId);
      if (index == -1) {
        SnackbarUtils.showError(
          AppStrings.errorS,
          'Transaction not found',
        );
        return false;
      }

      // Update the transaction with the category
      final updatedTransaction = transactions[index].copyWith(
        category: categoryName,
        metadata: {
          ...transactions[index].metadata ?? {},
          'categoryId': categoryId,
        },
      );

      // Save to repository
      final result = await repository.updateTransaction(updatedTransaction);

      return result.fold(
        (failure) {
          SnackbarUtils.showError(
            AppStrings.errorS,
            'Failed to update transaction: ${failure.message}',
          );
          return false;
        },
        (_) {
          // Update local list
          transactions[index] = updatedTransaction;
          applyFilters(); // Re-apply filters to update the UI

          SnackbarUtils.showSuccess(
            AppStrings.success,
            'Transaction category updated',
          );
          return true;
        },
      );
    } catch (e) {
      SnackbarUtils.showError(
        AppStrings.errorS,
        'Error updating transaction: $e',
      );
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Get category for a transaction (either from the transaction itself or from metadata)
  ExpenseCategoryModel? getCategoryForTransaction(
      AccountTransactionModel transaction) {
    if (transaction.category == null || transaction.category!.isEmpty) {
      return null;
    }

    // Try to find by ID first (from metadata)
    String? categoryId;
    if (transaction.metadata != null &&
        transaction.metadata!.containsKey('categoryId')) {
      categoryId = transaction.metadata!['categoryId'] as String?;
    }

    if (categoryId != null && categoryId.isNotEmpty) {
      try {
        return categories.firstWhere((c) => c.id == categoryId);
      } catch (_) {
        // Category not found by ID, continue to name matching
      }
    }

    // Try to find by name
    try {
      return categories.firstWhere(
        (c) => c.name.toLowerCase() == transaction.category!.toLowerCase(),
      );
    } catch (_) {
      // Create a temporary category object
      return ExpenseCategoryModel(
        id: '',
        name: transaction.category!,
        description: '',
        createdAt: DateTime.now(),
      );
    }
  }

  // Set active account and load its transactions
  void setAccount(AccountModel account) {
    activeAccount.value = account;
    loadTransactionsForAccount(account.id);
  }

  // Load transactions for a specific account
  Future<void> loadTransactionsForAccount(String accountId) async {
    try {
      isLoading.value = true;
      isError.value = false;

      final result = await repository.getTransactionsForAccount(accountId);

      result.fold(
        (failure) {
          isError.value = true;
          errorMessage.value = failure.message;
          SnackbarUtils.showError(AppStrings.errorS, failure.message);
        },
        (transactionList) {
          transactions.value = transactionList;
          applyFilters(); // Apply any active filters
        },
      );
    } catch (e) {
      isError.value = true;
      errorMessage.value = e.toString();
      SnackbarUtils.showError(
          AppStrings.errorS, 'Failed to load transactions: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Load transactions for a specific account with pagination
  Future<void> loadTransactionsForAccountPaginated(String accountId,
      {bool isFirstPage = true}) async {
    try {
      if (isFirstPage) {
        isLoading.value = true;
        _currentAccountId = accountId;
        _lastDocument = null;
        paginatedTransactions.clear();
        setCurrentPage(1);
      } else {
        isLoadingPage.value = true;
      }

      isError.value = false;

      final result = await repository.getTransactionsForAccountPaginated(
        accountId: accountId,
        limit: itemsPerPage.value,
        lastDocument: _lastDocument,
      );

      result.fold(
        (failure) {
          isError.value = true;
          errorMessage.value = failure.message;
          SnackbarUtils.showError(AppStrings.errorS, failure.message);
        },
        (paginatedResult) {
          if (isFirstPage) {
            paginatedTransactions.value = paginatedResult.transactions;
          } else {
            paginatedTransactions.addAll(paginatedResult.transactions);
          }

          _lastDocument = paginatedResult.nextPageCursor;
          hasNextPage.value = paginatedResult.hasNextPage;

          // Load total count for pagination info
          _loadTransactionCount(accountId);
        },
      );
    } catch (e) {
      isError.value = true;
      errorMessage.value = e.toString();
      SnackbarUtils.showError(
          AppStrings.errorS, 'Failed to load transactions: $e');
    } finally {
      if (isFirstPage) {
        isLoading.value = false;
      } else {
        isLoadingPage.value = false;
      }
    }
  }

  // Load transaction count for pagination
  Future<void> _loadTransactionCount(String accountId) async {
    try {
      final result = await repository.getTransactionCountForAccount(accountId);
      result.fold(
        (failure) {
          // Don't show error for count, just log it
          print('Failed to load transaction count: ${failure.message}');
        },
        (count) {
          setTotalItems(count);
        },
      );
    } catch (e) {
      print('Error loading transaction count: $e');
    }
  }

  // Override pagination methods to load new pages
  @override
  void setCurrentPage(int page) {
    if (page == currentPage.value || _currentAccountId == null) return;

    super.setCurrentPage(page);

    // Calculate if we need to load more data
    final startIndex = (page - 1) * itemsPerPage.value;
    if (startIndex >= paginatedTransactions.length && hasNextPage.value) {
      loadTransactionsForAccountPaginated(_currentAccountId!,
          isFirstPage: false);
    }
  }

  @override
  void setItemsPerPage(int count) {
    super.setItemsPerPage(count);

    // Reload data with new page size
    if (_currentAccountId != null) {
      loadTransactionsForAccountPaginated(_currentAccountId!,
          isFirstPage: true);
    }
  }

  // Get current page transactions for display
  List<AccountTransactionModel> get currentPageTransactions {
    final startIndex = (currentPage.value - 1) * itemsPerPage.value;
    final endIndex = startIndex + itemsPerPage.value;

    if (startIndex >= paginatedTransactions.length) return [];

    final actualEndIndex = endIndex > paginatedTransactions.length
        ? paginatedTransactions.length
        : endIndex;

    return paginatedTransactions.sublist(startIndex, actualEndIndex);
  }

  // Load all transactions
  Future<void> loadAllTransactions() async {
    try {
      isLoading.value = true;
      isError.value = false;

      final result = await repository.getAllTransactions();

      result.fold(
        (failure) {
          isError.value = true;
          errorMessage.value = failure.message;
          SnackbarUtils.showError(AppStrings.errorS, failure.message);
        },
        (transactionList) {
          transactions.value = transactionList;
          applyFilters(); // Apply any active filters
        },
      );
    } catch (e) {
      isError.value = true;
      errorMessage.value = e.toString();
      SnackbarUtils.showError(
          AppStrings.errorS, 'Failed to load transactions: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Set date range filter
  void setDateRange(DateTimeRange? range) {
    selectedDateRange.value = range;
    applyFilters();
  }

  // Toggle transaction type filter
  void toggleTypeFilter(TransactionType type) {
    if (selectedTypes.contains(type)) {
      selectedTypes.remove(type);
    } else {
      selectedTypes.add(type);
    }
    applyFilters();
  }

  // Set search query
  void setSearchQuery(String query) {
    searchQuery.value = query;
    applyFilters();
  }

  // Apply all active filters
  void applyFilters() {
    List<AccountTransactionModel> result = List.from(transactions);

    // Apply date range filter
    if (selectedDateRange.value != null) {
      final startDate = selectedDateRange.value!.start;
      final endDate = selectedDateRange.value!.end
          .add(const Duration(days: 1)); // Include the end date

      result = result.where((transaction) {
        return transaction.transactionDate.isAfter(startDate) &&
            transaction.transactionDate.isBefore(endDate);
      }).toList();
    }

    // Apply transaction type filter
    if (selectedTypes.isNotEmpty) {
      result = result.where((transaction) {
        return selectedTypes.contains(transaction.type);
      }).toList();
    }

    // Apply search query
    if (searchQuery.value.isNotEmpty) {
      final query = searchQuery.value.toLowerCase();
      result = result.where((transaction) {
        return transaction.description.toLowerCase().contains(query) ||
            transaction.accountName.toLowerCase().contains(query) ||
            (transaction.payeeName?.toLowerCase().contains(query) ?? false) ||
            (transaction.payerName?.toLowerCase().contains(query) ?? false) ||
            (transaction.voucherNumber?.toLowerCase().contains(query) ?? false);
      }).toList();
    }

    filteredTransactions.value = result;
  }

  // Calculate totals
  double get totalInflow {
    return filteredTransactions
        .where((t) => t.amount > 0)
        .fold(0.0, (sum, transaction) => sum + transaction.amount);
  }

  double get totalOutflow {
    return filteredTransactions
        .where((t) => t.amount < 0)
        .fold(0.0, (sum, transaction) => sum + transaction.amount.abs());
  }

  double get netFlow {
    return filteredTransactions.fold(
        0.0, (sum, transaction) => sum + transaction.amount);
  }

  // Format currency
  String formatCurrency(double amount) {
    return NumberFormat.currency(symbol: '₨ ', decimalDigits: 2).format(amount);
  }

  // Format date
  String formatDate(DateTime date) {
    return DateFormat('dd MMM yyyy').format(date);
  }

  // Reset all filters
  void resetFilters() {
    selectedDateRange.value = null;
    selectedTypes.clear();
    searchQuery.value = '';
    applyFilters();
  }

  // Create a new transaction
  Future<void> createTransaction(AccountTransactionModel transaction) async {
    try {
      isLoading.value = true;

      final result = await repository.createTransaction(transaction);

      result.fold(
        (failure) {
          SnackbarUtils.showError(AppStrings.errorS, failure.message);
        },
        (createdTransaction) {
          transactions.add(createdTransaction);
          applyFilters();
          SnackbarUtils.showSuccess(
              AppStrings.success, 'Transaction created successfully');
        },
      );
    } catch (e) {
      SnackbarUtils.showError(
          AppStrings.errorS, 'Failed to create transaction: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Delete a transaction
  Future<void> deleteTransaction(String transactionId) async {
    try {
      isLoading.value = true;

      final result = await repository.deleteTransaction(transactionId);

      result.fold(
        (failure) {
          SnackbarUtils.showError(AppStrings.errorS, failure.message);
        },
        (success) {
          transactions.removeWhere((t) => t.id == transactionId);
          applyFilters();
          SnackbarUtils.showSuccess(AppStrings.success, success.message);
        },
      );
    } catch (e) {
      SnackbarUtils.showError(
          AppStrings.errorS, 'Failed to delete transaction: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Group transactions by date
  Map<String, List<AccountTransactionModel>> get transactionsByDate {
    final grouped = <String, List<AccountTransactionModel>>{};

    for (final transaction in filteredTransactions) {
      final dateString = formatDate(transaction.transactionDate);

      if (!grouped.containsKey(dateString)) {
        grouped[dateString] = [];
      }

      grouped[dateString]!.add(transaction);
    }

    return grouped;
  }

  // Get transaction type display name
  String getTransactionTypeName(TransactionType type) {
    switch (type) {
      case TransactionType.deposit:
        return 'Deposit';
      case TransactionType.expense:
        return 'Withdrawal';
      case TransactionType.voucherPayment:
        return 'Voucher Payment';
      case TransactionType.brokerFees:
        return 'Broker Fees';
      case TransactionType.munshiana:
        return 'Munshiana';
      case TransactionType.loan:
        return 'Loan';
      case TransactionType.other:
        return 'Other';
    }
  }

  // Get transaction type color
  Color getTransactionTypeColor(TransactionType type) {
    switch (type) {
      case TransactionType.deposit:
        return Colors.green.shade700;
      case TransactionType.expense:
        return Colors.red.shade700;
      case TransactionType.voucherPayment:
        return Colors.blue.shade700;
      case TransactionType.brokerFees:
        return Colors.orange.shade700;
      case TransactionType.munshiana:
        return Colors.purple.shade700;
      case TransactionType.loan:
        return Colors.teal.shade700;
      case TransactionType.other:
        return Colors.grey.shade700;
    }
  }

  // Get transaction type icon
  IconData getTransactionTypeIcon(TransactionType type) {
    switch (type) {
      case TransactionType.deposit:
        return Icons.arrow_downward;
      case TransactionType.expense:
        return Icons.arrow_upward;
      case TransactionType.voucherPayment:
        return Icons.receipt;
      case TransactionType.brokerFees:
        return Icons.person_outline;
      case TransactionType.munshiana:
        return Icons.payments_outlined;
      case TransactionType.loan:
        return Icons.account_balance;
      case TransactionType.other:
        return Icons.more_horiz;
    }
  }
}
