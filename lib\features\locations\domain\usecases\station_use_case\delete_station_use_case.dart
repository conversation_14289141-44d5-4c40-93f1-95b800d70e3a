import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/features/locations/repositories/station_repository.dart';

class DeleteStationUseCase {
  final StationRepository _stationRepository;

  DeleteStationUseCase(this._stationRepository);

  /// Executes the use case to delete an Station.
  Future<Either<FailureObj, SuccessObj>> call({
    required String stationId,
  }) async {
    // Validate inputs before calling the repository

    if (stationId.isEmpty) {
      return Left(FailureObj(
        code: 'invalid-Station',
        message: 'Station "stationId" must not be empty.',
      ));
    }

    // Call the repository
    return await _stationRepository.deleteStation(
      stationId: stationId,
    );
  }
}
