import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/widgets/pagination_widget.dart';
import 'package:logestics/features/accounting/aged_reports/presentation/controllers/aged_reports_controller.dart';
import 'package:logestics/features/accounting/aged_reports/presentation/widgets/aged_receivables_table.dart';
import 'package:logestics/features/accounting/aged_reports/presentation/widgets/aged_payables_table.dart';
import 'package:logestics/features/accounting/aged_reports/presentation/widgets/aged_reports_filters.dart';

/// Main screen for Aged Reports (Receivables and Payables)
/// Follows the existing Material Design patterns used in other financial modules
class AgedReportsScreen extends StatefulWidget {
  const AgedReportsScreen({super.key});

  @override
  State<AgedReportsScreen> createState() => _AgedReportsScreenState();
}

class _AgedReportsScreenState extends State<AgedReportsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(AgedReportsController());

    return Scaffold(
      appBar: AppBar(
        title: const Text('Aged Reports'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 2,
        actions: [
          // Refresh button
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: controller.refreshData,
            tooltip: 'Refresh Data',
          ),
          // Export button
          Obx(() => IconButton(
                icon: controller.isExporting.value
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Icon(Icons.file_download),
                onPressed: controller.isExporting.value
                    ? null
                    : controller.exportToExcel,
                tooltip: 'Export to Excel',
              )),
          // PDF button
          Obx(() => IconButton(
                icon: controller.isExporting.value
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Icon(Icons.picture_as_pdf),
                onPressed: controller.isExporting.value
                    ? null
                    : controller.generatePDF,
                tooltip: 'Generate PDF',
              )),
        ],
      ),
      body: Column(
        children: [
          // Report type tabs
          Container(
            color: Theme.of(context).cardColor,
            child: TabBar(
              controller: _tabController,
              tabs: const [
                Tab(text: 'Aged Receivables'),
                Tab(text: 'Aged Payables'),
              ],
              onTap: (index) {
                controller
                    .switchReportType(index == 0 ? 'receivables' : 'payables');
              },
            ),
          ),

          // Filters section
          const AgedReportsFilters(),

          // Messages section
          Obx(() {
            if (controller.errorMessage.value.isNotEmpty) {
              return Container(
                width: double.infinity,
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  border: Border.all(color: Colors.red.shade200),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.error, color: Colors.red.shade600),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        controller.errorMessage.value,
                        style: TextStyle(color: Colors.red.shade700),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: controller.clearMessages,
                      color: Colors.red.shade600,
                    ),
                  ],
                ),
              );
            }

            if (controller.successMessage.value.isNotEmpty) {
              return Container(
                width: double.infinity,
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  border: Border.all(color: Colors.green.shade200),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.green.shade600),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        controller.successMessage.value,
                        style: TextStyle(color: Colors.green.shade700),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: controller.clearMessages,
                      color: Colors.green.shade600,
                    ),
                  ],
                ),
              );
            }

            return const SizedBox.shrink();
          }),

          // Main content area
          Expanded(
            child: Obx(() {
              if (controller.isLoading.value) {
                return const Center(
                  child: CircularProgressIndicator(),
                );
              }

              return TabBarView(
                controller: _tabController,
                children: [
                  // Aged Receivables tab
                  _buildReceivablesTab(controller),

                  // Aged Payables tab
                  _buildPayablesTab(controller),
                ],
              );
            }),
          ),
        ],
      ),
    );
  }

  /// Build Aged Receivables tab content
  Widget _buildReceivablesTab(AgedReportsController controller) {
    return Column(
      children: [
        // Generate button
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: Obx(() => ElevatedButton.icon(
                      onPressed: controller.isGeneratingReceivables.value
                          ? null
                          : controller.generateAgedReceivablesReport,
                      icon: controller.isGeneratingReceivables.value
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Icon(Icons.analytics),
                      label: Text(
                        controller.isGeneratingReceivables.value
                            ? 'Generating...'
                            : 'Generate Aged Receivables Report',
                      ),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    )),
              ),
              const SizedBox(width: 16),
              Obx(() => ElevatedButton.icon(
                    onPressed: controller.currentReceivablesReport.value == null
                        ? null
                        : controller.saveAgedReceivablesReport,
                    icon: const Icon(Icons.save),
                    label: const Text('Save Report'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          vertical: 12, horizontal: 16),
                    ),
                  )),
            ],
          ),
        ),

        // Report content
        Expanded(
          child: Obx(() {
            final report = controller.currentReceivablesReport.value;
            if (report == null) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.analytics_outlined,
                      size: 64,
                      color: Colors.grey,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'No Aged Receivables Report Generated',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.grey,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Click "Generate Aged Receivables Report" to create a report',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              );
            }

            return Column(
              children: [
                // Report summary
                Container(
                  width: double.infinity,
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Aged Receivables Summary',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade800,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          Expanded(
                            child: _buildSummaryCard(
                              'Total Customers',
                              report.totalCustomers.toString(),
                              Icons.people,
                              Colors.blue,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: _buildSummaryCard(
                              'Total Invoices',
                              report.totalInvoices.toString(),
                              Icons.receipt,
                              Colors.green,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: _buildSummaryCard(
                              'Total Outstanding',
                              '\$${report.grandTotalOutstanding.toStringAsFixed(2)}',
                              Icons.account_balance_wallet,
                              Colors.orange,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // Report table
                const Expanded(
                  child: AgedReceivablesTable(),
                ),

                // Pagination
                PaginationWidget(
                  currentPage: controller.currentPage.value,
                  totalPages: (controller.totalItems.value /
                          controller.itemsPerPage.value)
                      .ceil(),
                  totalItems: controller.totalItems.value,
                  itemsPerPage: controller.itemsPerPage.value,
                  onPageChanged: (page) => controller.currentPage.value = page,
                  onItemsPerPageChanged: (items) =>
                      controller.itemsPerPage.value = items,
                ),
              ],
            );
          }),
        ),
      ],
    );
  }

  /// Build Aged Payables tab content
  Widget _buildPayablesTab(AgedReportsController controller) {
    return Column(
      children: [
        // Generate button
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: Obx(() => ElevatedButton.icon(
                      onPressed: controller.isGeneratingPayables.value
                          ? null
                          : controller.generateAgedPayablesReport,
                      icon: controller.isGeneratingPayables.value
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Icon(Icons.analytics),
                      label: Text(
                        controller.isGeneratingPayables.value
                            ? 'Generating...'
                            : 'Generate Aged Payables Report',
                      ),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    )),
              ),
              const SizedBox(width: 16),
              Obx(() => ElevatedButton.icon(
                    onPressed: controller.currentPayablesReport.value == null
                        ? null
                        : controller.saveAgedPayablesReport,
                    icon: const Icon(Icons.save),
                    label: const Text('Save Report'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          vertical: 12, horizontal: 16),
                    ),
                  )),
            ],
          ),
        ),

        // Report content
        Expanded(
          child: Obx(() {
            final report = controller.currentPayablesReport.value;
            if (report == null) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.analytics_outlined,
                      size: 64,
                      color: Colors.grey,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'No Aged Payables Report Generated',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.grey,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Click "Generate Aged Payables Report" to create a report',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              );
            }

            return Column(
              children: [
                // Report summary
                Container(
                  width: double.infinity,
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.red.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.red.shade200),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Aged Payables Summary',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.red.shade800,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          Expanded(
                            child: _buildSummaryCard(
                              'Total Vendors',
                              report.totalVendors.toString(),
                              Icons.business,
                              Colors.blue,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: _buildSummaryCard(
                              'Total Bills',
                              report.totalBills.toString(),
                              Icons.receipt_long,
                              Colors.green,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: _buildSummaryCard(
                              'Total Outstanding',
                              '\$${report.grandTotalOutstanding.toStringAsFixed(2)}',
                              Icons.account_balance_wallet,
                              Colors.orange,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // Report table
                const Expanded(
                  child: AgedPayablesTable(),
                ),

                // Pagination
                PaginationWidget(
                  currentPage: controller.currentPage.value,
                  totalPages: (controller.totalItems.value /
                          controller.itemsPerPage.value)
                      .ceil(),
                  totalItems: controller.totalItems.value,
                  itemsPerPage: controller.itemsPerPage.value,
                  onPageChanged: (page) => controller.currentPage.value = page,
                  onItemsPerPageChanged: (items) =>
                      controller.itemsPerPage.value = items,
                ),
              ],
            );
          }),
        ),
      ],
    );
  }

  /// Build summary card widget
  Widget _buildSummaryCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
