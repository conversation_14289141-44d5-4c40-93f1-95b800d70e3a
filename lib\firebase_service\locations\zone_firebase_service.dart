import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';
import 'package:logestics/models/locations/zone_model.dart';

class ZoneFirebaseService {
  late FirebaseFirestore _firestore;

  ZoneFirebaseService() {
    _firestore = FirebaseFirestore.instance;
  }

  Future<void> createZone({required ZoneModel zone}) async {
    log('Creating zone: ${zone.zoneName}');
    try {
      final zoneRef =
          _firestore.collection(AppCollection.zonesCollection).doc();
      zone.zoneId = zoneRef.id;

      await zoneRef.set(zone.toJson());
      log('Successfully created zone: ${zone.zoneId}');
    } catch (e) {
      log('Error creating zone: $e');
      rethrow;
    }
  }

  Future<List<ZoneModel>> getZones() async {
    log('Fetching zones from Firestore');
    try {
      final snapshot = await _firestore
          .collection(AppCollection.zonesCollection)
          .orderBy('createdAt', descending: true)
          .get();

      final zones =
          snapshot.docs.map((doc) => ZoneModel.fromJson(doc.data())).toList();

      log('Successfully fetched ${zones.length} zones');
      return zones;
    } catch (e) {
      log('Error fetching zones: $e');
      rethrow;
    }
  }

  Stream<List<ZoneModel>> listenToZones() {
    return _firestore.collection(AppCollection.zonesCollection).snapshots().map(
          (snapshot) => snapshot.docs
              .map((doc) => ZoneModel.fromJson(doc.data()))
              .toList(),
        );
  }

  Future<void> updateZone(ZoneModel zone) async {
    log('Updating zone: ${zone.zoneId}');
    try {
      // Check if zone exists
      final zoneDoc = await _firestore
          .collection(AppCollection.zonesCollection)
          .doc(zone.zoneId)
          .get();

      if (!zoneDoc.exists) {
        throw Exception('Zone not found');
      }

      await _firestore
          .collection(AppCollection.zonesCollection)
          .doc(zone.zoneId)
          .update(zone.toJson());

      log('Successfully updated zone: ${zone.zoneId}');
    } catch (e) {
      log('Error updating zone: $e');
      rethrow;
    }
  }

  Future<void> deleteZone(String zoneId) async {
    log('Deleting zone: $zoneId');
    try {
      // Check if zone exists
      final zoneDoc = await _firestore
          .collection(AppCollection.zonesCollection)
          .doc(zoneId)
          .get();

      if (!zoneDoc.exists) {
        throw Exception('Zone not found');
      }

      await _firestore
          .collection(AppCollection.zonesCollection)
          .doc(zoneId)
          .delete();
      log('Successfully deleted zone: $zoneId');
    } catch (e) {
      log('Error deleting zone: $e');
      rethrow;
    }
  }

  Future<bool> checkZoneExists(String zoneName) async {
    try {
      final snapshot = await _firestore
          .collection(AppCollection.zonesCollection)
          .where('zoneName', isEqualTo: zoneName)
          .limit(1)
          .get();

      return snapshot.docs.isNotEmpty;
    } catch (e) {
      log('Error checking zone existence: $e');
      rethrow;
    }
  }
}
