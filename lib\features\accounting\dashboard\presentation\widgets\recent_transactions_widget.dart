import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../controllers/financial_dashboard_controller.dart';

/// Widget displaying recent transactions
class RecentTransactionsWidget extends StatelessWidget {
  const RecentTransactionsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<FinancialDashboardController>();
    final currencyFormatter = NumberFormat.currency(symbol: 'PKR ', decimalDigits: 0);
    final dateFormatter = DateFormat('MMM dd, yyyy');

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Transactions',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.refresh, size: 20),
                      onPressed: controller.refreshDashboard,
                      tooltip: 'Refresh Transactions',
                    ),
                    IconButton(
                      icon: const Icon(Icons.list_alt, size: 20),
                      onPressed: () => controller.navigateToReport('journal_entries'),
                      tooltip: 'View All Transactions',
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            Obx(() {
              final transactions = controller.recentTransactions;
              if (transactions.isEmpty) {
                return const Center(
                  child: Padding(
                    padding: EdgeInsets.all(32),
                    child: Column(
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('Loading recent transactions...'),
                      ],
                    ),
                  ),
                );
              }

              return Column(
                children: [
                  // Transaction list
                  ...transactions.map((transaction) => _buildTransactionTile(
                        transaction,
                        currencyFormatter,
                        dateFormatter,
                        context,
                        controller,
                      )),
                  
                  const SizedBox(height: 16),
                  
                  // View all button
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton.icon(
                      onPressed: () => controller.navigateToReport('journal_entries'),
                      icon: const Icon(Icons.list_alt),
                      label: const Text('View All Transactions'),
                    ),
                  ),
                ],
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionTile(
    dynamic transaction,
    NumberFormat currencyFormatter,
    DateFormat dateFormatter,
    BuildContext context,
    FinancialDashboardController controller,
  ) {
    final isDebit = transaction.type == 'debit';
    final amountColor = isDebit ? Colors.red : Colors.green;
    final amountPrefix = isDebit ? '-' : '+';

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 1,
      child: ListTile(
        leading: CircleAvatar(
          radius: 20,
          backgroundColor: amountColor.withOpacity(0.1),
          child: Icon(
            isDebit ? Icons.remove : Icons.add,
            color: amountColor,
            size: 20,
          ),
        ),
        title: Text(
          transaction.description,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              transaction.accountName,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 2),
            Row(
              children: [
                Text(
                  'Entry #${transaction.entryNumber}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[500],
                    fontSize: 11,
                  ),
                ),
                if (transaction.sourceType != null) ...[
                  const Text(' • '),
                  Text(
                    _formatSourceType(transaction.sourceType!),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[500],
                      fontSize: 11,
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '$amountPrefix${currencyFormatter.format(transaction.amount)}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: amountColor,
              ),
            ),
            const SizedBox(height: 2),
            Text(
              dateFormatter.format(transaction.transactionDate),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[500],
                fontSize: 11,
              ),
            ),
          ],
        ),
        onTap: () => controller.navigateToTransactionDetails(transaction.transactionId),
      ),
    );
  }

  String _formatSourceType(String sourceType) {
    switch (sourceType.toLowerCase()) {
      case 'expense':
        return 'Expense';
      case 'deposit':
        return 'Deposit';
      case 'voucher':
        return 'Voucher';
      case 'loan':
        return 'Loan';
      case 'bill':
        return 'Bill';
      case 'manual':
        return 'Manual Entry';
      default:
        return sourceType.toUpperCase();
    }
  }
}
