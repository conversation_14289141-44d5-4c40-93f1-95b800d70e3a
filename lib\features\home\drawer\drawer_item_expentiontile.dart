import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';

import '../Controllers/drawer_controllers.dart';

class DrawerItemExpentiontile extends StatelessWidget {
  final String title;
  final List<String> images;
  final List<String> text;
  final String sectionPath;
  final Function(int) onItemTap;

  const DrawerItemExpentiontile({
    super.key,
    required this.title,
    required this.images,
    required this.text,
    required this.sectionPath,
    required this.onItemTap,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<MainDrawerController>();
    notifier = Provider.of(context, listen: true);

    return ExpansionTile(
      title: Text(
        title,
        style: TextStyle(
          fontFamily: "Outfit",
          fontSize: 15,
          fontWeight: FontWeight.w500,
          color: controller.isItemSelected(sectionPath)
              ? const Color(0xFF0f7bf4)
              : notifier.text,
        ),
      ),
      leading: Image.asset(
        images[0],
        height: 20,
        color: controller.isItemSelected(sectionPath)
            ? const Color(0xFF0f7bf4)
            : notifier.text,
      ),
      children: List.generate(
        text.length,
        (index) => InkWell(
          onTap: () => onItemTap(index),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            color: controller.isItemSelected(
                    '$sectionPath/${text[index].toLowerCase().replaceAll(' ', '_')}')
                ? const Color(0xFF0f7bf4)
                : Colors.transparent,
            child: Row(
              children: [
                Image.asset(
                  images[0],
                  height: 20,
                  color: controller.isItemSelected(
                          '$sectionPath/${text[index].toLowerCase().replaceAll(' ', '_')}')
                      ? Colors.white
                      : notifier.text,
                ),
                const SizedBox(width: 10),
                Text(
                  text[index],
                  style: TextStyle(
                    fontFamily: "Outfit",
                    fontSize: 15,
                    fontWeight: FontWeight.w500,
                    color: controller.isItemSelected(
                            '$sectionPath/${text[index].toLowerCase().replaceAll(' ', '_')}')
                        ? Colors.white
                        : notifier.text,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
