import 'package:flutter/material.dart';
import 'package:logestics/core/utils/app_constants/colors/app_colors.dart';
import '../app_constants/styles/app_text_styles.dart';

class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final Color backgroundColor;
  final Color? textColor;
  final Size minimumSize;
  final double borderRadius;
  final TextStyle? textStyle;
  final bool isLoading;
  final bool isDisabled;
  final EdgeInsetsGeometry? padding;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final double? elevation;
  final Border? border;

  const CustomButton({
    super.key,
    required this.text,
    required this.onPressed,
    required this.backgroundColor,
    this.textColor,
    this.minimumSize = const Size(130, 60),
    this.borderRadius = 5.0,
    this.textStyle,
    this.isLoading = false,
    this.isDisabled = false,
    this.padding,
    this.prefixIcon,
    this.suffixIcon,
    this.elevation,
    this.border,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: isDisabled ? null : onPressed,
      style: ElevatedButton.styleFrom(
        shape: StadiumBorder(
          side: border?.top ?? BorderSide.none,
        ),
        backgroundColor: isDisabled ? Colors.grey : backgroundColor,
        minimumSize: minimumSize,
        elevation: elevation,
        padding: padding,
      ),
      child: isLoading
          ? const SizedBox(
              width: 24,
              height: 24,
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.whiteColor),
                strokeWidth: 2.0,
              ),
            )
          : Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (prefixIcon != null) ...[
                  prefixIcon!,
                  const SizedBox(width: 8),
                ],
                Text(
                  text,
                  style: textStyle ??
                      AppTextStyles.buttonTextStyle.copyWith(
                        color: textColor ?? AppColors.whiteColor,
                      ),
                ),
                if (suffixIcon != null) ...[
                  const SizedBox(width: 8),
                  suffixIcon!,
                ],
              ],
            ),
    );
  }

  // Predefined button styles
  static CustomButton primary({
    required String text,
    required VoidCallback onPressed,
    Size minimumSize = const Size(130, 60),
    bool isLoading = false,
    bool isDisabled = false,
    Widget? prefixIcon,
    Widget? suffixIcon,
    double? elevation,
  }) {
    return CustomButton(
      text: text,
      onPressed: onPressed,
      backgroundColor: AppColors.primary,
      minimumSize: minimumSize,
      isLoading: isLoading,
      isDisabled: isDisabled,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      elevation: elevation,
    );
  }

  static CustomButton danger({
    required String text,
    required VoidCallback onPressed,
    Size minimumSize = const Size(130, 60),
    bool isLoading = false,
    bool isDisabled = false,
    Widget? prefixIcon,
    Widget? suffixIcon,
    double? elevation,
  }) {
    return CustomButton(
      text: text,
      onPressed: onPressed,
      backgroundColor: AppColors.onError,
      minimumSize: minimumSize,
      isLoading: isLoading,
      isDisabled: isDisabled,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      elevation: elevation,
    );
  }

  static CustomButton secondary({
    required String text,
    required VoidCallback onPressed,
    Size minimumSize = const Size(130, 60),
    bool isLoading = false,
    bool isDisabled = false,
    Widget? prefixIcon,
    Widget? suffixIcon,
    double? elevation,
  }) {
    return CustomButton(
      text: text,
      onPressed: onPressed,
      backgroundColor: AppColors.whiteColor,
      textColor: AppColors.primary,
      minimumSize: minimumSize,
      isLoading: isLoading,
      isDisabled: isDisabled,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      elevation: elevation,
      border: Border.all(
        color: AppColors.primary,
        width: 1.0,
      ),
    );
  }

  static CustomButton success({
    required String text,
    required VoidCallback onPressed,
    Size minimumSize = const Size(130, 60),
    bool isLoading = false,
    bool isDisabled = false,
    Widget? prefixIcon,
    Widget? suffixIcon,
    double? elevation,
  }) {
    return CustomButton(
      text: text,
      onPressed: onPressed,
      backgroundColor: AppColors.primary,
      minimumSize: minimumSize,
      isLoading: isLoading,
      isDisabled: isDisabled,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      elevation: elevation,
    );
  }
}
