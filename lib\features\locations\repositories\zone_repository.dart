import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/firebase_service/locations/zone_firebase_service.dart';
import 'package:logestics/models/locations/zone_model.dart';

abstract class ZoneRepository {
  /// Creates an Zone and returns either a success or error object.
  Future<Either<FailureObj, SuccessObj>> createZone({
    required ZoneModel zone,
  });
  Future<Either<FailureObj, List<ZoneModel>>> getZones();

  Stream<List<ZoneModel>> listenToZones();

  Future<Either<FailureObj, SuccessObj>> deleteZone({
    required String zoneId,
  });
}

class ZoneRepositoryImpl implements ZoneRepository {
  final ZoneFirebaseService firebaseService;

  ZoneRepositoryImpl(this.firebaseService);

  @override
  Future<Either<FailureObj, SuccessObj>> createZone({
    required ZoneModel zone,
  }) async {
    try {
      // Use optimized query to check for existing zone
      final existingZone = await firebaseService.checkZoneExists(zone.zoneName);
      if (existingZone) {
        return Left(FailureObj(
            code: 'zone-exists',
            message: 'A zone with the same name already exists.'));
      }

      await firebaseService.createZone(zone: zone);
      return Right(SuccessObj(message: 'Zone created successfully'));
    } catch (e) {
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, List<ZoneModel>>> getZones() async {
    try {
      final zones = await firebaseService.getZones();
      return Right(zones);
    } catch (e) {
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> deleteZone(
      {required String zoneId}) async {
    try {
      await firebaseService.deleteZone(zoneId);
      return Right(SuccessObj(message: 'Zone deleted successfully'));
    } catch (e) {
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Stream<List<ZoneModel>> listenToZones() {
    return firebaseService.listenToZones();
  }
}
