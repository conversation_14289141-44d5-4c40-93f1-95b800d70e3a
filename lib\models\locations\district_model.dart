class DistrictModel {
  final String zoneId;
  final String zoneName;
  final String regionId;
  final String regionName;
  final String regionCode;
  String districtId;
  final String districtName;
  final DateTime createdAt;

  DistrictModel({
    required this.zoneId,
    required this.zoneName,
    required this.regionId,
    required this.regionName,
    required this.regionCode,
    required this.districtId,
    required this.districtName,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'zoneId': zoneId,
      'zoneName': zoneName,
      'regionId': regionId,
      'regionName': regionName,
      'regionCode': regionCode,
      'districtId': districtId,
      'districtName': districtName,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory DistrictModel.fromJson(Map<String, dynamic> json) {
    return DistrictModel(
      zoneId: json['zoneId'] ?? '',
      zoneName: json['zoneName'] ?? '',
      regionId: json['regionId'] ?? '',
      regionName: json['regionName'] ?? '',
      regionCode: json['regionCode'] ?? '',
      districtId: json['districtId'] ?? '',
      districtName: json['districtName'] ?? '',
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  @override
  String toString() {
    return 'DistrictModel{zoneId: $zoneId, zoneName: $zoneName, regionId: $regionId, regionName: $regionName, regionCode: $regionCode, districtId: $districtId, districtName: $districtName, createdAt: $createdAt}';
  }
}
