import 'package:flutter/material.dart';
import 'package:logestics/core/utils/constants/constants.dart';

class InputField extends StatelessWidget {
  const InputField(
      {super.key,
      required this.onTap,
      required this.focus,
      required this.hint,
      required this.controller,
      this.correct,
      this.onChanged,
      this.suffix,
      this.hideText,
      this.prefix,
      this.onTapOutSide});

  final bool focus;
  final String hint;
  final TextEditingController controller;
  final VoidCallback onTap;
  final VoidCallback? onTapOutSide;
  final void Function(String)? onChanged;
  final bool? hideText;
  final bool? correct;
  final Widget? prefix;
  final Widget? suffix;

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      padding: const EdgeInsets.all(1),
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
              color: Colors.black.withAlpha(05),
              offset: Offset(2, 2),
              blurRadius: 10),
          BoxShadow(
              color: Colors.black.withAlpha(05),
              offset: Offset(-2, -2),
              blurRadius: 10)
        ],
        borderRadius: BorderRadius.circular(15),
        gradient: focus
            ? const LinearGradient(colors: [lightOrange, darkOrange])
            : null,
      ),
      child: TextFormField(
        controller: controller,
        onTap: onTap,
        onChanged: onChanged,
        onTapOutside: (event) {
          if (onTapOutSide != null) {
            onTapOutSide!();
          }
        },
        obscureText: hideText ?? false,
        style:
            const TextStyle(color: Colors.black, fontWeight: FontWeight.normal),
        decoration: InputDecoration(
          filled: true,
          prefixIcon: prefix,
          suffixIcon: suffix,
          fillColor: Colors.white,
          enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: BorderSide.none),
          focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: BorderSide.none),
          border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: BorderSide.none),
          hoverColor: Colors.white,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 30, vertical: 20),
          hintText: hint,
          hintStyle: const TextStyle(
              color: Colors.grey, fontWeight: FontWeight.normal, fontSize: 12),
        ),
      ),
    );
  }
}
