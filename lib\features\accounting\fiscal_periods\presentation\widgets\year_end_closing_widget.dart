import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../../../models/finance/fiscal_period_model.dart';
import '../../../../../models/finance/year_end_closing_model.dart';
import '../controllers/fiscal_period_controller.dart';

/// Widget for managing year-end closing procedures
class YearEndClosingWidget extends StatelessWidget {
  const YearEndClosingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<FiscalPeriodController>();

    return Card(
      margin: const EdgeInsets.all(16),
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.lock_clock,
                  color: Colors.orange[700],
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Year-End Closing',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.orange[700],
                  ),
                ),
                const Spacer(),
                Obx(() => controller.isPerformingYearEndClosing.value
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const SizedBox.shrink()),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Perform year-end closing procedures to close revenue and expense accounts and transfer net income to retained earnings.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 20),
            
            // Eligible fiscal years section
            Obx(() {
              final eligibleYears = controller.fiscalYearsEligibleForClosing;
              
              if (eligibleYears.isEmpty) {
                return Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.grey[600]),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'No fiscal years are eligible for year-end closing at this time.',
                          style: TextStyle(color: Colors.grey[600]),
                        ),
                      ),
                    ],
                  ),
                );
              }

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Eligible Fiscal Years',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 12),
                  ...eligibleYears.map((year) => _buildFiscalYearClosingCard(
                    context, 
                    controller, 
                    year,
                  )),
                ],
              );
            }),
            
            const SizedBox(height: 20),
            
            // Year-end closing history section
            Obx(() {
              final closings = controller.yearEndClosings;
              
              if (closings.isEmpty) {
                return const SizedBox.shrink();
              }

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Year-End Closing History',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Container(
                    constraints: const BoxConstraints(maxHeight: 200),
                    child: ListView.builder(
                      shrinkWrap: true,
                      itemCount: closings.length,
                      itemBuilder: (context, index) {
                        final closing = closings[index];
                        return _buildClosingHistoryCard(context, closing);
                      },
                    ),
                  ),
                ],
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildFiscalYearClosingCard(
    BuildContext context,
    FiscalPeriodController controller,
    FiscalYearModel fiscalYear,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    fiscalYear.yearName,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${DateFormat('MMM dd, yyyy').format(fiscalYear.startDate)} - '
                    '${DateFormat('MMM dd, yyyy').format(fiscalYear.endDate)}',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            ElevatedButton.icon(
              onPressed: controller.isPerformingYearEndClosing.value
                  ? null
                  : () => _showYearEndClosingConfirmation(
                        context,
                        controller,
                        fiscalYear,
                      ),
              icon: const Icon(Icons.lock_clock, size: 18),
              label: const Text('Close Year'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange[600],
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildClosingHistoryCard(
    BuildContext context,
    YearEndClosingModel closing,
  ) {
    Color statusColor;
    IconData statusIcon;
    
    switch (closing.status) {
      case YearEndClosingStatus.completed:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case YearEndClosingStatus.failed:
        statusColor = Colors.red;
        statusIcon = Icons.error;
        break;
      case YearEndClosingStatus.inProgress:
        statusColor = Colors.orange;
        statusIcon = Icons.hourglass_empty;
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = Icons.pending;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            Icon(statusIcon, color: statusColor, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Fiscal Year: ${closing.fiscalYearId}',
                    style: const TextStyle(fontWeight: FontWeight.w600),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Status: ${closing.status.name.toUpperCase()}',
                    style: TextStyle(color: statusColor, fontSize: 12),
                  ),
                  if (closing.completedAt != null)
                    Text(
                      'Completed: ${DateFormat('MMM dd, yyyy HH:mm').format(closing.completedAt!)}',
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                ],
              ),
            ),
            if (closing.isCompleted)
              Text(
                'Net Income: \$${closing.netIncomeTransferred.toStringAsFixed(2)}',
                style: TextStyle(
                  color: closing.netIncomeTransferred >= 0 ? Colors.green : Colors.red,
                  fontWeight: FontWeight.w600,
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _showYearEndClosingConfirmation(
    BuildContext context,
    FiscalPeriodController controller,
    FiscalYearModel fiscalYear,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Year-End Closing'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Are you sure you want to perform year-end closing for:'),
            const SizedBox(height: 8),
            Text(
              fiscalYear.yearName,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.warning, color: Colors.orange[700], size: 20),
                      const SizedBox(width: 8),
                      const Text(
                        'Important:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '• All revenue and expense accounts will be closed\n'
                    '• Net income will be transferred to retained earnings\n'
                    '• This action cannot be easily undone\n'
                    '• Ensure all transactions for the year are recorded',
                    style: TextStyle(fontSize: 13),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              controller.performYearEndClosing(fiscalYear);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange[600],
              foregroundColor: Colors.white,
            ),
            child: const Text('Proceed with Closing'),
          ),
        ],
      ),
    );
  }
}
