import 'dart:io';
import 'dart:async';
import 'dart:developer';

import 'package:dartz/dartz.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logestics/firebase_service/finance/bill_firebase_service.dart';
import 'package:logestics/models/finance/bill_model.dart';

import '../../../../core/shared_services/failure_obj.dart';
import '../../../../core/shared_services/success_obj.dart';
import '../../../../core/services/bill_accounting_hook_service.dart';

abstract class BillRepository {
  /// Creates a bill and returns either a success or error object.
  Future<Either<FailureObj, SuccessObj>> createBill({
    required String uid,
    required BillModel bill,
    required List<String> invoiceIds,
  });

  /// Updates a bill and returns either a success or error object.
  Future<Either<FailureObj, SuccessObj>> updateBill({
    required String uid,
    required BillModel bill,
  });

  /// Updates bill status and returns either a success or error object.
  Future<Either<FailureObj, SuccessObj>> updateBillStatus({
    required String uid,
    required String billId,
    required String newStatus,
  });

  /// Deletes a bill and returns either a success or error object.
  Future<Either<FailureObj, SuccessObj>> deleteBill({
    required String uid,
    required String billId,
  });

  /// Gets all bills for a company
  Future<List<BillModel>> getBillsForCompany(String uid);

  /// Listens to real-time bill changes
  Stream<List<DocumentChange>> listenToBills({required String uid});

  /// Gets the next bill number for auto-generation
  Future<Either<FailureObj, int>> getNextBillNumber({required String uid});
}

class BillRepositoryImpl implements BillRepository {
  final BillFirebaseService _billFirebaseService;
  final BillAccountingHookService _hookService;

  BillRepositoryImpl(this._billFirebaseService)
      : _hookService = BillAccountingHookService();

  @override
  Future<Either<FailureObj, SuccessObj>> createBill({
    required String uid,
    required BillModel bill,
    required List<String> invoiceIds,
  }) async {
    try {
      await _billFirebaseService.createBill(
        uid: uid,
        bill: bill,
        invoiceIds: invoiceIds,
      );

      // Trigger accounting hook for journal entry generation
      await _hookService.onBillCreated(bill);

      return Right(SuccessObj(message: 'Bill created successfully.'));
    } on TimeoutException {
      return Left(FailureObj(
        code: 'timeout',
        message: 'The operation timed out. Please try again.',
      ));
    } on SocketException {
      return Left(FailureObj(
        code: 'no-internet',
        message: 'No internet connection. Please check your network.',
      ));
    } on ArgumentError catch (e) {
      return Left(FailureObj(
        code: 'invalid-argument',
        message: e.message ?? 'Invalid argument provided.',
      ));
    } on FirebaseException catch (e) {
      return Left(FailureObj(
        code: e.code,
        message: e.message ?? 'Firebase error occurred.',
      ));
    } catch (e) {
      // Handle specific duplicate bill error
      if (e.toString().contains('already exists')) {
        return Left(FailureObj(
          code: 'duplicate-bill',
          message: 'A bill with this number already exists. Please try again.',
        ));
      }

      // Handle invoice already billed error
      if (e.toString().contains('already billed')) {
        return Left(FailureObj(
          code: 'invoice-already-billed',
          message: 'One or more selected invoices are already billed.',
        ));
      }

      return Left(FailureObj(
        code: 'unexpected-error',
        message: 'An unexpected error occurred: $e',
      ));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> updateBill({
    required String uid,
    required BillModel bill,
  }) async {
    try {
      await _billFirebaseService.updateBill(
        uid: uid,
        bill: bill,
      );
      return Right(SuccessObj(message: 'Bill updated successfully.'));
    } on SocketException {
      return Left(FailureObj(
        code: 'no-internet',
        message: 'No internet connection. Please check your network.',
      ));
    } on ArgumentError catch (e) {
      return Left(FailureObj(
        code: 'invalid-argument',
        message: e.message ?? 'Invalid argument provided.',
      ));
    } on FirebaseException catch (e) {
      return Left(FailureObj(
        code: e.code,
        message: e.message ?? 'Firebase error occurred.',
      ));
    } catch (e) {
      return Left(FailureObj(
        code: 'unexpected-error',
        message: 'An unexpected error occurred: $e',
      ));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> updateBillStatus({
    required String uid,
    required String billId,
    required String newStatus,
  }) async {
    try {
      // Get the bill first to capture old status
      final bill = await _billFirebaseService.getBillByNumber(
        uid: uid,
        billNumber: billId,
      );

      final oldStatus = bill?.billStatus ?? '';

      await _billFirebaseService.updateBillStatus(
        uid: uid,
        billNumber: billId,
        newStatus: newStatus,
      );

      // Trigger accounting hook for status update
      if (bill != null) {
        await _hookService.onBillStatusUpdated(
            billId, uid, oldStatus, newStatus);
      }

      return Right(SuccessObj(message: 'Bill status updated successfully.'));
    } on SocketException {
      return Left(FailureObj(
        code: 'no-internet',
        message: 'No internet connection. Please check your network.',
      ));
    } on ArgumentError catch (e) {
      return Left(FailureObj(
        code: 'invalid-argument',
        message: e.message ?? 'Invalid argument provided.',
      ));
    } on FirebaseException catch (e) {
      return Left(FailureObj(
        code: e.code,
        message: e.message ?? 'Firebase error occurred.',
      ));
    } catch (e) {
      return Left(FailureObj(
        code: 'unexpected-error',
        message: 'An unexpected error occurred: $e',
      ));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> deleteBill({
    required String uid,
    required String billId,
  }) async {
    try {
      // Get the bill first for hook service
      final bill = await _billFirebaseService.getBillByNumber(
        uid: uid,
        billNumber: billId,
      );

      await _billFirebaseService.deleteBill(
        uid: uid,
        billNumber: billId,
      );

      // Trigger accounting hook for bill deletion
      if (bill != null) {
        await _hookService.onBillDeleted(bill);
      }

      return Right(SuccessObj(message: 'Bill deleted successfully.'));
    } on SocketException {
      return Left(FailureObj(
        code: 'no-internet',
        message: 'No internet connection. Please check your network.',
      ));
    } on ArgumentError catch (e) {
      return Left(FailureObj(
        code: 'invalid-argument',
        message: e.message ?? 'Invalid argument provided.',
      ));
    } on FirebaseException catch (e) {
      return Left(FailureObj(
        code: e.code,
        message: e.message ?? 'Firebase error occurred.',
      ));
    } catch (e) {
      return Left(FailureObj(
        code: 'unexpected-error',
        message: 'An unexpected error occurred: $e',
      ));
    }
  }

  @override
  Future<List<BillModel>> getBillsForCompany(String uid) async {
    try {
      return await _billFirebaseService.getBills(uid: uid);
    } catch (e) {
      log('Error getting bills for company: $e');
      rethrow;
    }
  }

  @override
  Stream<List<DocumentChange>> listenToBills({required String uid}) {
    return _billFirebaseService.listenToBills(uid: uid);
  }

  @override
  Future<Either<FailureObj, int>> getNextBillNumber(
      {required String uid}) async {
    try {
      final nextNumber = await _billFirebaseService.getNextBillNumber(uid: uid);
      return Right(nextNumber);
    } on SocketException {
      return Left(FailureObj(
        code: 'no-internet',
        message: 'No internet connection. Please check your network.',
      ));
    } on FirebaseException catch (e) {
      return Left(FailureObj(
        code: e.code,
        message: e.message ?? 'Firebase error occurred.',
      ));
    } catch (e) {
      return Left(FailureObj(
        code: 'unexpected-error',
        message: 'An unexpected error occurred: $e',
      ));
    }
  }
}
