import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/models/locations/station_model.dart';
import 'package:logestics/features/locations/repositories/station_repository.dart';

class GetStationsUseCase {
  final StationRepository _stationRepository;

  GetStationsUseCase(this._stationRepository);

  /// Executes the use case to get all Stations.
  Future<Either<FailureObj, List<StationModel>>> call() async {
    try {
      return await _stationRepository.getStations();
    } catch (e) {
      return Left(FailureObj(
        code: 'unexpected-error',
        message: 'An unexpected error occurred.',
      ));
    }
  }
}
