import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/constants/custom_dialogs.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/core/utils/mixins/pagination_mixin.dart';
import 'package:logestics/features/locations/domain/usecases/region_use_case/delete_region_use_case.dart';
import 'package:logestics/features/locations/domain/usecases/region_use_case/get_region_use_case.dart';
import 'package:logestics/models/locations/region_model.dart';

class RegionListController extends GetxController with PaginationMixin {
  final GetRegionsUseCase getRegionsUseCase;
  final DeleteRegionUseCase deleteRegionUseCase;

  RegionListController({
    required this.getRegionsUseCase,
    required this.deleteRegionUseCase,
  });

  var regions = <RegionModel>[].obs;
  var filteredRegions = <RegionModel>[].obs;
  var isLoading = false.obs;
  final searchQuery = ''.obs;

  var searchController = TextEditingController();

  @override
  void onInit() {
    super.onInit();
    searchController.addListener(_onSearchChanged);
    fetchRegions();
  }

  void _onSearchChanged() {
    searchQuery.value = searchController.text;
    _filterRegions();
  }

  void _filterRegions() {
    if (searchQuery.value.isEmpty) {
      filteredRegions.value = regions;
    } else {
      final query = searchQuery.value.toLowerCase();
      filteredRegions.value = regions.where((region) {
        return region.regionName.toLowerCase().contains(query) ||
            region.regionCode.toLowerCase().contains(query) ||
            region.zoneName.toLowerCase().contains(query);
      }).toList();
    }
    setTotalItems(filteredRegions.length);
  }

  Future<void> fetchRegions() async {
    if (isLoading.value) return;

    isLoading.value = true;
    try {
      log('Fetching regions...');
      final result = await getRegionsUseCase.call();
      result.fold(
        (failure) {
          log('Failed to fetch regions: ${failure.message}');
          showErrorDialog(failure);
        },
        (regionList) {
          log('Successfully fetched ${regionList.length} regions');
          regions.value = regionList;
          _filterRegions();
        },
      );
    } catch (e) {
      log('Unexpected error occurred while fetching regions: $e');
      showUnexpectedErrorDialog();
    } finally {
      isLoading.value = false;
    }
  }

  List<RegionModel> get paginatedRegions => paginateList(filteredRegions);

  Future<void> deleteRegion(String regionId) async {
    try {
      log('Deleting region: $regionId');
      regions.removeWhere((region) => region.regionId == regionId);
      setTotalItems(regions.length);
      final result = await deleteRegionUseCase.call(regionId: regionId);
      result.fold(
        (failure) {
          log('Failed to delete region: ${failure.message}');
          showErrorDialog(failure);
        },
        (success) {
          log('Successfully deleted region');
          SnackbarUtils.showSuccess(
            AppStrings.success,
            'Region deleted successfully',
          );
        },
      );
    } catch (e) {
      log('Unexpected error occurred while deleting region: $e');
      showUnexpectedErrorDialog();
    } finally {
      isLoading.value = false;
    }
  }

  void addRegionToList(RegionModel region) {
    regions.insert(0, region);
    setTotalItems(regions.length);
  }
}
