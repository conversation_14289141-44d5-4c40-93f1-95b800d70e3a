import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';

void showErrorDialog(
  FailureObj failure, {
  String? message,
  String? title,
}) {
  Get.defaultDialog(
    title: title ?? "Error",
    middleText: message ?? failure.message,
    textConfirm: "OK",
    confirmTextColor: Colors.white,
    onConfirm: () => Get.back(),
  );
}

void showUnexpectedErrorDialog({
  String? message,
  String? title,
}) {
  Get.defaultDialog(
    title: title ?? AppStrings.error,
    middleText: message ?? "Something went wrong. Please try again later.",
    textConfirm: "OK",
    confirmTextColor: Colors.white,
    onConfirm: () => Get.back(),
  );
}

void displaySuccessSnackbar(SuccessObj success) {
  SnackbarUtils.showSuccess(
    AppStrings.success,
    success.message,
  );
}
