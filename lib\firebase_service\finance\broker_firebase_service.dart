import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';
import 'package:logestics/models/finance/broker_model.dart';

class BrokerFirebaseService {
  late FirebaseFirestore _firestore;

  // Get current user's UID or return 'anonymous' if not authenticated
  String get _uid => FirebaseAuth.instance.currentUser?.uid ?? 'anonymous';

  BrokerFirebaseService() {
    _firestore = FirebaseFirestore.instance;
  }

  Future<void> createBroker(BrokerModel broker) async {
    log('Creating broker: ${broker.name}');
    try {
      final brokerRef =
          _firestore.collection(AppCollection.brokersCollection).doc();
      final brokerId = brokerRef.id;

      final brokerData = broker.toJson();
      brokerData['id'] = brokerId;
      brokerData['uid'] = _uid; // Add current user's UID

      await brokerRef.set(brokerData);
      log('Successfully created broker: $brokerId');
    } catch (e) {
      log('Error creating broker: $e');
      rethrow;
    }
  }

  Future<List<BrokerModel>> getBrokers() async {
    log('Fetching brokers from Firestore');
    try {
      final snapshot = await _firestore
          .collection(AppCollection.brokersCollection)
          .where('uid', isEqualTo: _uid) // Filter by current user's UID
          .orderBy('createdAt', descending: true)
          .get();

      final brokers =
          snapshot.docs.map((doc) => BrokerModel.fromJson(doc.data())).toList();

      log('Successfully fetched ${brokers.length} brokers');
      return brokers;
    } catch (e) {
      log('Error fetching brokers: $e');
      rethrow;
    }
  }

  Future<void> deleteBroker(String brokerId) async {
    log('Deleting broker: $brokerId');
    try {
      if (brokerId.isEmpty) {
        throw ArgumentError('Broker ID cannot be empty');
      }

      // First check if the broker belongs to the current user
      final brokerDoc = await _firestore
          .collection(AppCollection.brokersCollection)
          .doc(brokerId)
          .get();

      if (!brokerDoc.exists) {
        throw Exception('Broker not found');
      }

      final brokerData = brokerDoc.data() as Map<String, dynamic>;
      if (brokerData['uid'] != _uid) {
        throw Exception('You do not have permission to delete this broker');
      }

      await _firestore
          .collection(AppCollection.brokersCollection)
          .doc(brokerId)
          .delete();
      log('Successfully deleted broker: $brokerId');
    } catch (e) {
      log('Error deleting broker: $e');
      rethrow;
    }
  }

  Future<void> updateBroker(BrokerModel broker) async {
    log('Updating broker: ${broker.id}');
    try {
      if (broker.id.isEmpty) {
        throw ArgumentError('Broker ID cannot be empty');
      }

      // First check if the broker belongs to the current user
      final brokerDoc = await _firestore
          .collection(AppCollection.brokersCollection)
          .doc(broker.id)
          .get();

      if (!brokerDoc.exists) {
        throw Exception('Broker not found');
      }

      final brokerData = brokerDoc.data() as Map<String, dynamic>;
      if (brokerData['uid'] != _uid) {
        throw Exception('You do not have permission to update this broker');
      }

      // Preserve the original UID
      final updatedBrokerData = broker.toJson();
      updatedBrokerData['uid'] = brokerData['uid'];

      await _firestore
          .collection(AppCollection.brokersCollection)
          .doc(broker.id)
          .update(updatedBrokerData);
      log('Successfully updated broker: ${broker.id}');
    } catch (e) {
      log('Error updating broker: $e');
      rethrow;
    }
  }

  /// Stream to listen for real-time updates to brokers
  Stream<List<BrokerModel>> listenToBrokers() {
    try {
      return _firestore
          .collection(AppCollection.brokersCollection)
          .where('uid', isEqualTo: _uid) // Filter by current user's UID
          .orderBy('createdAt', descending: true)
          .snapshots()
          .map((snapshot) => snapshot.docs
              .map((doc) => BrokerModel.fromJson(doc.data()))
              .toList());
    } catch (e) {
      log('Error listening to brokers: $e', error: e);
      return Stream.value([]);
    }
  }
}
