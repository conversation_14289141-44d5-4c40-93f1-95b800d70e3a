import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/fiscal_period_controller.dart';
import '../widgets/fiscal_year_card_widget.dart';
import '../widgets/fiscal_period_list_widget.dart';
import '../widgets/create_fiscal_year_dialog.dart';
import '../widgets/period_generation_dialog.dart';
import '../widgets/year_end_closing_widget.dart';
import '../../../../../models/finance/fiscal_period_model.dart';

/// Main Fiscal Period Management screen
class FiscalPeriodScreen extends StatelessWidget {
  const FiscalPeriodScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<FiscalPeriodController>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Fiscal Period Management'),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        elevation: 2,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => controller.initializeData(),
            tooltip: 'Refresh Data',
          ),
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: () => _showHelpDialog(context),
            tooltip: 'Help',
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('Loading fiscal period data...'),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () => controller.initializeData(),
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Current Status Section
                _buildCurrentStatusSection(controller),

                const SizedBox(height: 24),

                // Year-End Closing Section
                const YearEndClosingWidget(),

                const SizedBox(height: 24),

                // Fiscal Years Section
                _buildFiscalYearsSection(controller, context),

                const SizedBox(height: 24),

                // Fiscal Periods Section
                _buildFiscalPeriodsSection(controller, context),
              ],
            ),
          ),
        );
      }),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showCreateFiscalYearDialog(context, controller),
        icon: const Icon(Icons.add),
        label: const Text('New Fiscal Year'),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
      ),
    );
  }

  /// Build current status section
  Widget _buildCurrentStatusSection(FiscalPeriodController controller) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue[700]),
                const SizedBox(width: 8),
                Text(
                  'Current Status',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Active Fiscal Year
            Row(
              children: [
                const Icon(Icons.calendar_today, size: 20, color: Colors.grey),
                const SizedBox(width: 8),
                const Text('Active Fiscal Year:',
                    style: TextStyle(fontWeight: FontWeight.w500)),
                const SizedBox(width: 8),
                Expanded(
                  child: Obx(() => Text(
                        controller.activeFiscalYearText,
                        style: TextStyle(
                          color: controller.activeFiscalYear.value != null
                              ? Colors.green[700]
                              : Colors.red[700],
                          fontWeight: FontWeight.w500,
                        ),
                      )),
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Current Period
            Row(
              children: [
                const Icon(Icons.schedule, size: 20, color: Colors.grey),
                const SizedBox(width: 8),
                const Text('Current Period:',
                    style: TextStyle(fontWeight: FontWeight.w500)),
                const SizedBox(width: 8),
                Expanded(
                  child: Obx(() => Text(
                        controller.currentFiscalPeriodText,
                        style: TextStyle(
                          color: controller.currentFiscalPeriod.value != null
                              ? Colors.green[700]
                              : Colors.orange[700],
                          fontWeight: FontWeight.w500,
                        ),
                      )),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build fiscal years section
  Widget _buildFiscalYearsSection(
      FiscalPeriodController controller, BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Fiscal Years',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
            TextButton.icon(
              onPressed: () => _showCreateFiscalYearDialog(context, controller),
              icon: const Icon(Icons.add),
              label: const Text('Add Year'),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Obx(() {
          final years = controller.fiscalYears;

          if (years.isEmpty) {
            return Card(
              child: Padding(
                padding: const EdgeInsets.all(32),
                child: Column(
                  children: [
                    Icon(Icons.calendar_today,
                        size: 48, color: Colors.grey[400]),
                    const SizedBox(height: 16),
                    Text(
                      'No fiscal years found',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Create your first fiscal year to get started',
                      style: TextStyle(
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),
              ),
            );
          }

          return Column(
            children: years
                .map((year) => Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: FiscalYearCardWidget(
                        fiscalYear: year,
                        isSelected:
                            controller.selectedFiscalYear.value?.id == year.id,
                        onTap: () => controller.selectFiscalYear(year),
                        onSetActive: () => controller.setActiveFiscalYear(year),
                        onGeneratePeriods: () => _showPeriodGenerationDialog(
                            context, controller, year),
                      ),
                    ))
                .toList(),
          );
        }),
      ],
    );
  }

  /// Build fiscal periods section
  Widget _buildFiscalPeriodsSection(
      FiscalPeriodController controller, BuildContext context) {
    return Obx(() {
      final selectedYear = controller.selectedFiscalYear.value;

      if (selectedYear == null) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(32),
            child: Column(
              children: [
                Icon(Icons.schedule, size: 48, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  'Select a fiscal year to view periods',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        );
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Fiscal Periods - ${selectedYear.yearName}',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
              TextButton.icon(
                onPressed: () => _showPeriodGenerationDialog(
                    context, controller, selectedYear),
                icon: const Icon(Icons.auto_awesome),
                label: const Text('Generate Periods'),
              ),
            ],
          ),
          const SizedBox(height: 12),
          FiscalPeriodListWidget(
            fiscalPeriods: controller.fiscalPeriods,
            onClosePeriod: controller.closeFiscalPeriod,
            onReopenPeriod: controller.reopenFiscalPeriod,
            isLoading: controller.isClosingPeriod,
          ),
        ],
      );
    });
  }

  /// Show create fiscal year dialog
  void _showCreateFiscalYearDialog(
      BuildContext context, FiscalPeriodController controller) {
    showDialog(
      context: context,
      builder: (context) => CreateFiscalYearDialog(controller: controller),
    );
  }

  /// Show period generation dialog
  void _showPeriodGenerationDialog(
    BuildContext context,
    FiscalPeriodController controller,
    FiscalYearModel year,
  ) {
    showDialog(
      context: context,
      builder: (context) => PeriodGenerationDialog(
        controller: controller,
        fiscalYear: year,
      ),
    );
  }

  /// Show help dialog
  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Fiscal Period Management Help'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Fiscal Years',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              SizedBox(height: 8),
              Text('• Create fiscal years to organize your accounting periods'),
              Text('• Only one fiscal year can be active at a time'),
              Text('• Active fiscal year determines current accounting period'),
              SizedBox(height: 16),
              Text(
                'Fiscal Periods',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              SizedBox(height: 8),
              Text('• Generate monthly, quarterly, or yearly periods'),
              Text('• Close periods to prevent further transactions'),
              Text('• Reopen periods if needed for adjustments'),
              SizedBox(height: 16),
              Text(
                'Best Practices',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              SizedBox(height: 8),
              Text('• Close periods in chronological order'),
              Text('• Ensure all transactions are recorded before closing'),
              Text('• Review financial reports before period closure'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
