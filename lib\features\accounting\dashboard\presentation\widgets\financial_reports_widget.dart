import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/financial_dashboard_controller.dart';

/// Widget providing quick access to financial reports
class FinancialReportsWidget extends StatelessWidget {
  const FinancialReportsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<FinancialDashboardController>();

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Financial Reports',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                Icon(
                  Icons.assessment,
                  color: Theme.of(context).primaryColor,
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Report buttons grid
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 1.2,
              children: [
                _buildReportCard(
                  'Trial Balance',
                  'View account balances and verify books',
                  Icons.balance,
                  Colors.blue,
                  () => controller.navigateToReport('trial_balance'),
                  context,
                ),
                _buildReportCard(
                  'Profit & Loss',
                  'Revenue and expense summary',
                  Icons.trending_up,
                  Colors.green,
                  () => controller.navigateToReport('profit_loss'),
                  context,
                ),
                _buildReportCard(
                  'Balance Sheet',
                  'Assets, liabilities, and equity',
                  Icons.account_balance,
                  Colors.purple,
                  () => controller.navigateToReport('balance_sheet'),
                  context,
                ),
                _buildReportCard(
                  'Cash Flow',
                  'Cash receipts and payments',
                  Icons.water_drop,
                  Colors.teal,
                  () => controller.navigateToReport('cash_flow_statement'),
                  context,
                ),
                _buildReportCard(
                  'Journal Entries',
                  'All accounting transactions',
                  Icons.list_alt,
                  Colors.orange,
                  () => controller.navigateToReport('journal_entries'),
                  context,
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Chart of Accounts button
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () =>
                    controller.navigateToReport('chart_of_accounts'),
                icon: const Icon(Icons.account_tree),
                label: const Text('Chart of Accounts'),
              ),
            ),

            const SizedBox(height: 8),

            // Fiscal Periods button
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () => controller.navigateToReport('fiscal_periods'),
                icon: const Icon(Icons.calendar_today),
                label: const Text('Fiscal Periods'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReportCard(
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onTap,
    BuildContext context,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 32,
              color: color,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              description,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
