import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/features/slab/repositories/slab_repository.dart';
import 'package:logestics/models/slab/slab_model.dart';

class GetSlabsUseCase {
  final SlabRepository repository;

  GetSlabsUseCase(this.repository);

  Future<Either<FailureObj, List<SlabModel>>> call() async {
    return await repository.getSlabs();
  }
}
