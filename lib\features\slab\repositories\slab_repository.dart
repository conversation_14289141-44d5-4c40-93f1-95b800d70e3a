import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/models/slab/slab_model.dart';

abstract class SlabRepository {
  Future<Either<FailureObj, void>> createSlab({
    required SlabModel slab,
  });

  Future<Either<FailureObj, List<SlabModel>>> getSlabs();

  Future<Either<FailureObj, void>> updateSlab({
    required SlabModel slab,
  });

  Future<Either<FailureObj, void>> deleteSlab({
    required String slabId,
  });

  Future<Either<FailureObj, List<SlabModel>>> getActiveSlabsForDistrict({
    required String districtId,
    required DateTime date,
  });
}
