import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/widgets/searchfield.dart';
import 'package:logestics/firebase_service/invoices/invoice_crud_firebase_service.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/widgets/data_table_cell.dart';
import 'package:logestics/core/utils/widgets/pagination_widget.dart';
import 'package:logestics/core/utils/widgets/loading_indicator.dart';
import 'package:intl/intl.dart';
import 'package:logestics/bindings/app_bindings.dart';
import 'package:logestics/models/invoice_model.dart';

import '../../../company/presentation/conrollers/company_controller.dart';
import '../../repositories/invoice_repository.dart';
import '../../use_cases/delete_invoice_use_case.dart';
import '../../use_cases/update_invoice_status_use_case.dart';
import 'invoice_form_view.dart';
import '../controllers/invoice_list_controller.dart';

class InvoiceList extends StatelessWidget {
  const InvoiceList({super.key, required this.titleShow});

  String formatDate(DateTime? date) {
    if (date == null) return '';
    return DateFormat('dd/MM/yyyy').format(date);
  }

  void _navigateToInvoiceForm({InvoiceModel? invoice, bool readOnly = false}) {
    // Reset the bindings to ensure all dependencies are properly registered
    AppBindings().dependencies();

    // Navigate to the invoice form
    Get.to(
      () => InvoiceFormView(
        currentInvoice: invoice,
        readOnly: readOnly,
      ),
    )?.then((_) {
      // Refresh the invoice list when returning from the form
      final controller = Get.find<InvoiceListController>();
      controller.refreshData();
    });
  }

  final bool titleShow;
  @override
  Widget build(BuildContext context) {
    InvoiceListController invoiceListController = Get.put(
      InvoiceListController(
        deleteInvoiceUseCase: Get.put(
          DeleteInvoiceUseCase(
            Get.put(
              InvoiceRepositoryImpl(
                Get.put(
                  InvoiceCrudFirebaseService(),
                ),
              ),
            ),
          ),
        ),
        updateInvoiceStatusUseCase: Get.put(
          UpdateInvoiceStatusUseCase(
            Get.put(
              InvoiceRepositoryImpl(
                Get.put(
                  InvoiceCrudFirebaseService(),
                ),
              ),
            ),
          ),
        ),
      ),
    );

    // Handle screen activation and data refresh
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Call onScreenActivated to handle navigation scenarios
      invoiceListController.onScreenActivated();

      // Only call refreshData if we don't have any invoices loaded
      if (invoiceListController.companyController.company.invoices.isEmpty) {
        invoiceListController.refreshData();
      }
    });

    var width = Get.width;

    notifier = Provider.of(context, listen: true);

    return GetBuilder<CompanyController>(
      init: invoiceListController.companyController,
      builder: (companyController) => LayoutBuilder(
        builder: (context, constraints) {
          return StatefulBuilder(
            builder: (context, setState) {
              return Container(
                decoration: BoxDecoration(
                  color: notifier.getBgColor,
                  borderRadius: BorderRadius.circular(10),
                ),
                padding: const EdgeInsets.symmetric(vertical: 15),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 15),
                      child: width < 650
                          ? Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                titleShow == false
                                    ? InkWell(
                                        onTap: () {
                                          _navigateToInvoiceForm();
                                        },
                                        child: Text(
                                          AppStrings.addNewInvoiceButton,
                                          style:
                                              AppTextStyles.addNewInvoiceStyle,
                                        ),
                                      )
                                    : Text(
                                        AppStrings.invoiceTitle,
                                        overflow: TextOverflow.ellipsis,
                                        style: AppTextStyles.invoiceTitleStyle
                                            .copyWith(
                                          color: notifier.text,
                                        ),
                                      ),
                                const SizedBox(height: 5),
                                Searchfield(
                                  controller:
                                      invoiceListController.searchController,
                                  onChanged: (query) {
                                    invoiceListController.searchInvoices(query);
                                  },
                                )
                              ],
                            )
                          : Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                titleShow == false
                                    ? InkWell(
                                        onTap: () {
                                          _navigateToInvoiceForm();
                                        },
                                        child: Text(
                                          AppStrings.addNewInvoiceButton,
                                          style:
                                              AppTextStyles.addNewInvoiceStyle,
                                        ),
                                      )
                                    : Text(
                                        AppStrings.addNewInvoice,
                                        style: AppTextStyles.invoiceTitleStyle
                                            .copyWith(
                                          color: notifier.text,
                                        ),
                                      ),
                                SizedBox(
                                  width: width < 850 ? width / 2 : width / 3.5,
                                  child: Row(
                                    children: [
                                      Expanded(
                                        child: Searchfield(
                                          controller: invoiceListController
                                              .searchController,
                                          onChanged: (query) {
                                            invoiceListController
                                                .searchInvoices(query);
                                          },
                                        ),
                                      ),
                                      const SizedBox(width: 10),
                                      Container(
                                        height: 40,
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 10),
                                        decoration: BoxDecoration(
                                          color: notifier.getBgColor,
                                          borderRadius:
                                              BorderRadius.circular(5),
                                          border: Border.all(
                                            color: notifier.getfillborder,
                                          ),
                                        ),
                                        child: DropdownButton<String>(
                                          value: invoiceListController
                                              .selectedStatus.value,
                                          hint: Text(
                                            'Filter by Status',
                                            style: AppTextStyles
                                                .searchFieldStyle
                                                .copyWith(
                                              color: notifier.text
                                                  .withValues(alpha: 0.5),
                                            ),
                                          ),
                                          underline: Container(),
                                          isDense: true,
                                          style: AppTextStyles.searchFieldStyle,
                                          items: [
                                            const DropdownMenuItem<String>(
                                              value: '',
                                              child: Text('All Status'),
                                            ),
                                            ...AppStrings.invoiceStatusOptions
                                                .map((String status) {
                                              return DropdownMenuItem<String>(
                                                value: status,
                                                child: Text(status),
                                              );
                                            }),
                                          ],
                                          onChanged: (String? newValue) {
                                            invoiceListController
                                                .filterByStatus(newValue);
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                    ),
                    const SizedBox(height: 20),
                    Expanded(
                      child: RefreshIndicator(
                        onRefresh: () async {
                          await invoiceListController.manualRefresh();
                        },
                        child: Obx(() {
                          // Show loading indicator during initial load or when no data is available yet
                          if (invoiceListController.isInitialLoading.value ||
                              (companyController.isLoadingInvoices.value &&
                                  invoiceListController
                                      .filteredInvoices.isEmpty)) {
                            return const InvoiceLoadingIndicator();
                          }

                          return SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: SizedBox(
                              width: constraints.maxWidth < 800
                                  ? 800
                                  : constraints.maxWidth,
                              child: Stack(
                                children: [
                                  ListView(
                                    shrinkWrap: true,
                                    children: [
                                      Table(
                                        border: TableBorder(
                                          horizontalInside: BorderSide(
                                            color: notifier.getfillborder,
                                          ),
                                        ),
                                        children: [
                                          TableRow(
                                            decoration: BoxDecoration(
                                              color: notifier.getHoverColor,
                                            ),
                                            children: [
                                              DataTableHeaderCell(
                                                // text: 'Sr No.',
                                                text: 'Invoice Number',
                                                textColor: notifier.text,
                                              ),
                                              DataTableHeaderCell(
                                                text: 'TAS Number',
                                                textColor: notifier.text,
                                              ),
                                              DataTableHeaderCell(
                                                text: 'Convey Note',
                                                textColor: notifier.text,
                                              ),
                                              DataTableHeaderCell(
                                                text: 'Order Date',
                                                textColor: notifier.text,
                                              ),
                                              DataTableHeaderCell(
                                                text: 'Status',
                                                textColor: notifier.text,
                                              ),
                                              DataTableHeaderCell(
                                                text: 'Actions',
                                                textColor: notifier.text,
                                              ),
                                            ],
                                          ),
                                          ...invoiceListController
                                              .paginatedInvoices
                                              .asMap()
                                              .entries
                                              .map((entry) {
                                            final invoice = entry.value;
                                            return TableRow(
                                              children: [
                                                // DataTableCell(
                                                //   text: ((invoiceListController
                                                //                       .currentPage
                                                //                       .value -
                                                //                   1) *
                                                //               invoiceListController
                                                //                   .itemsPerPage.value +
                                                //           i +
                                                //           1)
                                                //       .toString(),
                                                // ),

                                                DataTableCell(
                                                  text: invoice.invoiceNumber
                                                      .toString(),
                                                ),
                                                DataTableCell(
                                                  text: invoice.tasNumber,
                                                ),
                                                DataTableCell(
                                                  text:
                                                      invoice.conveyNoteNumber,
                                                ),
                                                DataTableCell(
                                                  text: formatDate(
                                                      invoice.orderDate),
                                                ),
                                                TableCell(
                                                  child: Container(
                                                    padding:
                                                        const EdgeInsets.all(
                                                            4.0),
                                                    constraints:
                                                        const BoxConstraints(
                                                      minWidth: 100,
                                                      maxWidth: 140,
                                                    ),
                                                    child:
                                                        DropdownButton<String>(
                                                      value:
                                                          invoice.invoiceStatus,
                                                      underline: Container(),
                                                      isDense: true,
                                                      isExpanded: true,
                                                      style: AppTextStyles
                                                          .invoiceDataStyle
                                                          .copyWith(
                                                        color: invoiceListController
                                                                .statusColor[
                                                            invoice
                                                                .invoiceStatus],
                                                        fontSize: 12,
                                                      ),
                                                      items: AppStrings
                                                          .invoiceStatusOptions
                                                          .map((String status) {
                                                        return DropdownMenuItem<
                                                            String>(
                                                          value: status,
                                                          child: Text(
                                                            status,
                                                            overflow:
                                                                TextOverflow
                                                                    .ellipsis,
                                                            style:
                                                                const TextStyle(
                                                                    fontSize:
                                                                        12),
                                                          ),
                                                        );
                                                      }).toList(),
                                                      onChanged:
                                                          (String? newValue) {
                                                        if (newValue != null) {
                                                          invoiceListController
                                                              .updateInvoiceStatus(
                                                            tasNumber: invoice
                                                                .tasNumber,
                                                            newStatus: newValue,
                                                          );
                                                        }
                                                      },
                                                    ),
                                                  ),
                                                ),
                                                DataTableActionsCell(
                                                  menuItems: [
                                                    DataTablePopupMenuItem(
                                                      text: 'View Details',
                                                      icon: Icons.visibility,
                                                      onTap: () {
                                                        _navigateToInvoiceForm(
                                                          invoice: invoice,
                                                          readOnly: true,
                                                        );
                                                      },
                                                    ),
                                                    DataTablePopupMenuItem(
                                                      text: 'Edit',
                                                      icon: Icons.edit,
                                                      onTap: () {
                                                        _navigateToInvoiceForm(
                                                          invoice: invoice,
                                                        );
                                                      },
                                                    ),
                                                    DataTablePopupMenuItem(
                                                      text: 'Delete',
                                                      icon:
                                                          Icons.delete_outline,
                                                      isDanger: true,
                                                      onTap: () {
                                                        Get.dialog(
                                                          AlertDialog(
                                                            title: const Text(
                                                                'Confirm Delete'),
                                                            content: const Text(
                                                              'Are you sure you want to delete this invoice? This action cannot be undone.',
                                                            ),
                                                            actions: [
                                                              TextButton(
                                                                onPressed: () =>
                                                                    Get.back(),
                                                                child: const Text(
                                                                    'Cancel'),
                                                              ),
                                                              TextButton(
                                                                onPressed: () {
                                                                  Get.back();
                                                                  invoiceListController
                                                                      .deleteInvoice(
                                                                    tasNumber:
                                                                        invoice
                                                                            .tasNumber,
                                                                  );
                                                                },
                                                                child:
                                                                    const Text(
                                                                  'Delete',
                                                                  style: TextStyle(
                                                                      color: Colors
                                                                          .red),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        );
                                                      },
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            );
                                          }),
                                        ],
                                      ),
                                    ],
                                  ),
                                  // Show loading overlay during refresh operations
                                  if (invoiceListController.isRefreshing.value)
                                    Positioned.fill(
                                      child: Container(
                                        color:
                                            Colors.black.withValues(alpha: 0.1),
                                        child: const Center(
                                          child: LoadingIndicator.circular(
                                            message: 'Refreshing invoices...',
                                            size: LoadingIndicatorSize.medium,
                                          ),
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          );
                        }),
                      ),
                    ),
                    Obx(() => PaginationWidget(
                          currentPage: invoiceListController.currentPage.value,
                          totalPages: invoiceListController.totalPages,
                          itemsPerPage:
                              invoiceListController.itemsPerPage.value,
                          onPageChanged: (page) =>
                              invoiceListController.setCurrentPage(page),
                          onItemsPerPageChanged: (count) =>
                              invoiceListController.setItemsPerPage(count),
                        )),
                    titleShow == false
                        ? Container()
                        : Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 15),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                InkWell(
                                  onTap: () {
                                    _navigateToInvoiceForm();
                                  },
                                  child: Text(
                                    AppStrings.addNewInvoiceButton,
                                    style: AppTextStyles.addNewInvoiceStyle,
                                  ),
                                ),
                              ],
                            ),
                          ),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }

  // String calculateTotalAmount(InvoiceModel invoice) {
  //   // You can implement the calculation based on your business logic
  //   // For example: quantity * rate + tax
  //   double amount = (invoice.numberOfBags * 50.0); // Basic calculation
  //   return amount.toStringAsFixed(2);
  // }
}
