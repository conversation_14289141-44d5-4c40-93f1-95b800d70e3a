import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/firebase_service/finance/fuel_card_firebase_service.dart';
import 'package:logestics/models/finance/fuel_card_model.dart';
import 'package:logestics/models/finance/fuel_rate_model.dart';

abstract class FuelCardRepository {
  // Fuel Card operations
  Future<Either<FailureObj, SuccessObj>> createFuelCard(FuelCardModel fuelCard);
  Future<Either<FailureObj, SuccessObj>> updateFuelCard(FuelCardModel fuelCard);
  Future<Either<FailureObj, SuccessObj>> deleteFuelCard(String id);
  Future<Either<FailureObj, List<FuelCardModel>>> getFuelCards();
  Future<Either<FailureObj, List<FuelCardModel>>> getFuelCardsByCompany(
      String companyName);

  // Fuel Rate operations
  Future<Either<FailureObj, SuccessObj>> addFuelRate(FuelRateModel fuelRate);
  Future<Either<FailureObj, FuelRateModel>> getLatestFuelRate(
      String companyName);
  Future<Either<FailureObj, List<FuelRateModel>>> getFuelRateHistory(
      String companyName);
}

class FuelCardRepositoryImpl implements FuelCardRepository {
  final FuelCardFirebaseService fuelCardFirebaseService;

  FuelCardRepositoryImpl(this.fuelCardFirebaseService);

  @override
  Future<Either<FailureObj, SuccessObj>> createFuelCard(
      FuelCardModel fuelCard) {
    return fuelCardFirebaseService.createFuelCard(fuelCard);
  }

  @override
  Future<Either<FailureObj, SuccessObj>> updateFuelCard(
      FuelCardModel fuelCard) {
    return fuelCardFirebaseService.updateFuelCard(fuelCard);
  }

  @override
  Future<Either<FailureObj, SuccessObj>> deleteFuelCard(String id) {
    return fuelCardFirebaseService.deleteFuelCard(id);
  }

  @override
  Future<Either<FailureObj, List<FuelCardModel>>> getFuelCards() {
    return fuelCardFirebaseService.getFuelCards();
  }

  @override
  Future<Either<FailureObj, List<FuelCardModel>>> getFuelCardsByCompany(
      String companyName) {
    return fuelCardFirebaseService.getFuelCardsByCompany(companyName);
  }

  @override
  Future<Either<FailureObj, SuccessObj>> addFuelRate(FuelRateModel fuelRate) {
    return fuelCardFirebaseService.addFuelRate(fuelRate);
  }

  @override
  Future<Either<FailureObj, FuelRateModel>> getLatestFuelRate(
      String companyName) {
    return fuelCardFirebaseService.getLatestFuelRate(companyName);
  }

  @override
  Future<Either<FailureObj, List<FuelRateModel>>> getFuelRateHistory(
      String companyName) {
    return fuelCardFirebaseService.getFuelRateHistory(companyName);
  }
}
