# Database Indexing Guide for Two-Date System

## Overview
This document outlines the required database indexes for the new two-date system implemented across Invoice, Voucher, and Payment modules.

## Required Indexes

### 1. Invoice Collection Indexes

#### Primary Indexes
- `belongsToDate` (Ascending) - For filtering invoices by belongs-to date
- `createdAt` (Descending) - For sorting by creation date
- `uid` (Ascending) - For user-specific queries

#### Composite Indexes
- `uid` + `belongsToDate` (Ascending, Ascending) - For user-specific date filtering
- `uid` + `createdAt` (Ascending, Descending) - For user-specific creation date sorting
- `conveyNoteNumber` + `belongsToDate` (Ascending, Ascending) - For voucher linking

### 2. Voucher Collection Indexes

#### Primary Indexes
- `belongsToDate` (Ascending) - For filtering vouchers by belongs-to date
- `createdAt` (Descending) - For sorting by creation date
- `uid` (Ascending) - For user-specific queries

#### Composite Indexes
- `uid` + `belongsToDate` (Ascending, Ascending) - For user-specific date filtering
- `uid` + `createdAt` (Ascending, Descending) - For user-specific creation date sorting
- `voucherStatus` + `belongsToDate` (Ascending, Ascending) - For status-based filtering

### 3. Payment Transaction Collection Indexes

#### Primary Indexes
- `transactionDate` (Ascending) - For filtering payments by transaction date (belongs-to date)
- `createdAt` (Descending) - For sorting by creation date
- `voucherId` (Ascending) - For voucher-specific queries

#### Composite Indexes
- `voucherId` + `transactionDate` (Ascending, Ascending) - For voucher payment filtering
- `method` + `transactionDate` (Ascending, Ascending) - For payment method filtering
- `status` + `transactionDate` (Ascending, Ascending) - For payment status filtering

## Firebase Console Commands

### To create these indexes in Firebase Console:

1. Go to Firestore Database → Indexes
2. Click "Create Index"
3. Add the following indexes:

```
Collection: invoices
Fields: uid (Ascending), belongsToDate (Ascending)

Collection: invoices  
Fields: uid (Ascending), createdAt (Descending)

Collection: vouchers
Fields: uid (Ascending), belongsToDate (Ascending)

Collection: vouchers
Fields: uid (Ascending), createdAt (Descending)

Collection: paymentTransactions
Fields: voucherId (Ascending), transactionDate (Ascending)

Collection: paymentTransactions
Fields: method (Ascending), transactionDate (Ascending)
```

## Query Optimization

### Efficient Query Patterns

#### For Invoice Lists:
```dart
// Filter by belongs-to date range
query.where('uid', isEqualTo: userId)
     .where('belongsToDate', isGreaterThanOrEqualTo: startDate)
     .where('belongsToDate', isLessThanOrEqualTo: endDate)
     .orderBy('belongsToDate', descending: true)

// Sort by creation date
query.where('uid', isEqualTo: userId)
     .orderBy('createdAt', descending: true)
```

#### For Voucher Lists:
```dart
// Filter by belongs-to date
query.where('uid', isEqualTo: userId)
     .where('belongsToDate', isGreaterThanOrEqualTo: startDate)
     .where('belongsToDate', isLessThanOrEqualTo: endDate)
     .orderBy('belongsToDate', descending: true)
```

#### For Payment Lists:
```dart
// Filter by transaction date (belongs-to date)
query.where('voucherId', isEqualTo: voucherId)
     .where('transactionDate', isGreaterThanOrEqualTo: startDate)
     .where('transactionDate', isLessThanOrEqualTo: endDate)
     .orderBy('transactionDate', descending: true)
```

## Performance Considerations

1. **Index Size**: Each composite index increases storage requirements
2. **Write Performance**: More indexes can slow down write operations
3. **Query Limits**: Firestore has limits on the number of composite indexes

## Migration Notes

When deploying the two-date system:

1. Existing documents without `belongsToDate` will have `null` values
2. Existing documents without `createdAt` will default to current timestamp
3. Queries should handle `null` values gracefully
4. Consider running a migration script to populate missing dates

## Monitoring

Monitor query performance using:
- Firebase Console → Performance tab
- Query execution time metrics
- Index usage statistics

## Backup Strategy

Before implementing indexes:
1. Export current database
2. Test indexes on development environment
3. Monitor performance impact
4. Have rollback plan ready
