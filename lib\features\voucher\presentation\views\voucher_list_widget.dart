import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/widgets/data_table_cell.dart';
import 'package:logestics/core/utils/widgets/pagination_widget.dart';
import 'package:logestics/core/utils/widgets/searchfield.dart';
import 'package:logestics/features/company/presentation/conrollers/company_controller.dart';
import 'package:logestics/firebase_service/voucher/voucher_crud_firebase_service.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';

import '../../repositories/voucher_repository.dart';
import '../../use_cases/delete_voucher_use_case.dart';
import '../controllers/voucher_list_controller.dart';
import 'add_voucher_view.dart';

Widget voucherListWidget(context, {bool? titleShow}) {
  VoucherListController voucherListController = Get.put(
    VoucherListController(
      deleteVoucherUseCase: Get.put(
        DeleteVoucherUseCase(
          Get.put(
            VoucherRepositoryImp(
              Get.put(VoucherCrudFirebaseService()),
            ),
          ),
        ),
      ),
    ),
  );

  var width = Get.width;
  notifier = Provider.of(context, listen: true);

  Map<String, Color> statusBgColor = {
    'In Progress': notifier.liyellowColor,
    "Completed": notifier.ligreenColor,
    "Not Started": notifier.liblueColor,
    "Pending": notifier.liredColor,
  };

  return GetBuilder<CompanyController>(
    init: voucherListController.companyController,
    builder: (companyController) => LayoutBuilder(
      builder: (context, constraints) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Container(
              decoration: BoxDecoration(
                color: notifier.getBgColor,
                borderRadius: BorderRadius.circular(10),
              ),
              padding: const EdgeInsets.symmetric(vertical: 15),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 15),
                    child: width < 650
                        ? Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              titleShow == false
                                  ? InkWell(
                                      onTap: () {
                                        Get.toNamed('/add-voucher');
                                      },
                                      child: Text(
                                        AppStrings.addNewVoucherButton,
                                        style: AppTextStyles.addNewVoucherStyle,
                                      ),
                                    )
                                  : Text(
                                      AppStrings.voucherTitle,
                                      overflow: TextOverflow.ellipsis,
                                      style: AppTextStyles.voucherTitleStyle
                                          .copyWith(
                                        color: notifier.text,
                                      ),
                                    ),
                              const SizedBox(height: 5),
                              const Searchfield(),
                            ],
                          )
                        : Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              titleShow == false
                                  ? InkWell(
                                      onTap: () {
                                        Get.toNamed('/add-voucher');
                                      },
                                      child: Text(
                                        AppStrings.addNewVoucherButton,
                                        style: AppTextStyles.addNewVoucherStyle,
                                      ),
                                    )
                                  : Text(
                                      AppStrings.voucherTitle,
                                      style: AppTextStyles.voucherTitleStyle
                                          .copyWith(
                                        color: notifier.text,
                                      ),
                                    ),
                              SizedBox(
                                width: width < 850 ? width / 2 : width / 3.5,
                                child: Searchfield(
                                  onChanged: (query) {
                                    voucherListController.setSearchQuery(query);
                                  },
                                ),
                              ),
                            ],
                          ),
                  ),
                  const SizedBox(height: 20),
                  Expanded(
                    child: Obx(() {
                      if (voucherListController.isLoading.value) {
                        return const Center(
                          child: CircularProgressIndicator(),
                        );
                      }

                      return SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: SizedBox(
                          width: constraints.maxWidth < 800
                              ? 800
                              : constraints.maxWidth,
                          child: ListView(
                            shrinkWrap: true,
                            children: [
                              Table(
                                border: TableBorder(
                                  horizontalInside: BorderSide(
                                    color: notifier.getfillborder,
                                  ),
                                ),
                                children: [
                                  TableRow(
                                    decoration: BoxDecoration(
                                      color: notifier.getHoverColor,
                                    ),
                                    children: [
                                      DataTableHeaderCell(
                                        text: 'Sr No.',
                                        textColor: notifier.text,
                                      ),
                                      DataTableHeaderCell(
                                        text: AppStrings.voucherNumberLabel,
                                        textColor: notifier.text,
                                      ),
                                      DataTableHeaderCell(
                                        text: AppStrings.brokerNameLabel,
                                        textColor: notifier.text,
                                      ),
                                      DataTableHeaderCell(
                                        text: AppStrings.driverNameLabel,
                                        textColor: notifier.text,
                                      ),
                                      DataTableHeaderCell(
                                        text: AppStrings.totalFreightLabel,
                                        textColor: notifier.text,
                                      ),
                                      DataTableHeaderCell(
                                        text: AppStrings.voucherStatusLabel,
                                        textColor: notifier.text,
                                      ),
                                      DataTableHeaderCell(
                                        text: "Actions",
                                        textColor: notifier.text,
                                      ),
                                    ],
                                  ),
                                  ...voucherListController.paginatedVouchers
                                      .asMap()
                                      .entries
                                      .map((entry) {
                                    final i = entry.key;
                                    final voucher = entry.value;
                                    return TableRow(
                                      children: [
                                        DataTableCell(
                                          text: ((voucherListController
                                                              .currentPage
                                                              .value -
                                                          1) *
                                                      voucherListController
                                                          .itemsPerPage.value +
                                                  i +
                                                  1)
                                              .toString(),
                                        ),
                                        DataTableCell(
                                          text: voucher.voucherNumber,
                                        ),
                                        DataTableCell(
                                          text: voucher.brokerName,
                                        ),
                                        DataTableCell(
                                          text: voucher.driverName,
                                        ),
                                        DataTableCell(
                                          text: voucher.totalFreight.toString(),
                                        ),
                                        TableCell(
                                          child: Container(
                                            padding: const EdgeInsets.all(4.0),
                                            constraints: const BoxConstraints(
                                              minWidth: 100,
                                              maxWidth: 140,
                                            ),
                                            child: Container(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      vertical: 5),
                                              decoration: BoxDecoration(
                                                color: statusBgColor[
                                                    voucher.voucherStatus],
                                                borderRadius:
                                                    BorderRadius.circular(5),
                                              ),
                                              child: Center(
                                                child: Text(
                                                  voucher.voucherStatus,
                                                  style: TextStyle(
                                                    letterSpacing: 1,
                                                    fontFamily: "Outfit",
                                                    fontSize: 12,
                                                    color: voucherListController
                                                            .statusColor[
                                                        voucher.voucherStatus],
                                                    fontWeight: FontWeight.w300,
                                                  ),
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                        DataTableActionsCell(
                                          menuItems: [
                                            DataTablePopupMenuItem(
                                              text: 'Edit',
                                              icon: Icons.edit,
                                              onTap: () => Get.toNamed(
                                                '/add-voucher',
                                                arguments: {'voucher': voucher},
                                              ),
                                            ),
                                            DataTablePopupMenuItem(
                                              text: 'Delete',
                                              icon: Icons.delete_outline,
                                              isDanger: true,
                                              onTap: () => Get.dialog(
                                                AlertDialog(
                                                  title: const Text(
                                                      'Confirm Complete Voucher Deletion'),
                                                  content: Column(
                                                    mainAxisSize:
                                                        MainAxisSize.min,
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      const Text(
                                                        'This will permanently delete:',
                                                        style: TextStyle(
                                                            fontWeight:
                                                                FontWeight
                                                                    .bold),
                                                      ),
                                                      const SizedBox(height: 8),
                                                      const Text(
                                                          '• Voucher record'),
                                                      const Text(
                                                          '• All bank statement entries (broker fees, munshiana, taxes, profits)'),
                                                      const Text(
                                                          '• Company freight and sales tax entries'),
                                                      const Text(
                                                          '• Tax authority payment records'),
                                                      const Text(
                                                          '• Check and fuel card usage records'),
                                                      const SizedBox(
                                                          height: 12),
                                                      const Text(
                                                        'Account balances will be automatically adjusted.',
                                                        style: TextStyle(
                                                            fontStyle: FontStyle
                                                                .italic),
                                                      ),
                                                      const SizedBox(
                                                          height: 12),
                                                      const Text(
                                                        'This action cannot be undone!',
                                                        style: TextStyle(
                                                          color: Colors.red,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  actions: [
                                                    TextButton(
                                                      onPressed: () =>
                                                          Get.back(),
                                                      child:
                                                          const Text('Cancel'),
                                                    ),
                                                    TextButton(
                                                      onPressed: () {
                                                        Get.back();
                                                        voucherListController
                                                            .deleteVoucher(
                                                          voucherNumber: voucher
                                                              .voucherNumber,
                                                        );
                                                      },
                                                      child: const Text(
                                                        'Delete Everything',
                                                        style: TextStyle(
                                                            color: Colors.red,
                                                            fontWeight:
                                                                FontWeight
                                                                    .bold),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    );
                                  }),
                                ],
                              ),
                            ],
                          ),
                        ),
                      );
                    }),
                  ),
                  Obx(() => PaginationWidget(
                        currentPage: voucherListController.currentPage.value,
                        totalPages: voucherListController.totalPages,
                        itemsPerPage: voucherListController.itemsPerPage.value,
                        onPageChanged: (page) =>
                            voucherListController.setCurrentPage(page),
                        onItemsPerPageChanged: (count) =>
                            voucherListController.setItemsPerPage(count),
                      )),
                  titleShow == false
                      ? Container()
                      : Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 15),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              InkWell(
                                onTap: () {
                                  Get.to(() => AddVoucherView());
                                },
                                child: Text(
                                  AppStrings.addNewVoucherButton,
                                  style: AppTextStyles.addNewVoucherStyle,
                                ),
                              ),
                            ],
                          ),
                        ),
                ],
              ),
            );
          },
        );
      },
    ),
  );
}
