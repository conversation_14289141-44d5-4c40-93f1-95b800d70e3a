import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../controllers/profit_loss_controller.dart';
import '../widgets/profit_loss_form_widget.dart';
import '../widgets/profit_loss_table_widget.dart';
import '../widgets/profit_loss_summary_widget.dart';
import '../widgets/saved_reports_widget.dart';

/// Main Profit & Loss report screen
class ProfitLossScreen extends StatelessWidget {
  const ProfitLossScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(ProfitLossController());

    return Scaffold(
      appBar: AppBar(
        title: const Text('Profit & Loss Statement'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          Obx(() => controller.hasReport
              ? IconButton(
                  icon: const Icon(Icons.download),
                  onPressed: controller.isLoading
                      ? null
                      : () => _showExportOptions(context, controller),
                  tooltip: 'Export Report',
                )
              : const SizedBox.shrink()),
          Obx(() => controller.hasReport
              ? IconButton(
                  icon: const Icon(Icons.save),
                  onPressed:
                      controller.isLoading ? null : controller.saveReport,
                  tooltip: 'Save Report',
                )
              : const SizedBox.shrink()),
          Obx(() => controller.hasReport
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed:
                      controller.isLoading ? null : controller.clearReport,
                  tooltip: 'Clear Report',
                )
              : const SizedBox.shrink()),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('Generating Profit & Loss Report...'),
              ],
            ),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Form Section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Report Parameters',
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                      const SizedBox(height: 16),
                      const ProfitLossFormWidget(),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Report Results Section
              if (controller.hasReport) ...[
                // Summary Cards
                const ProfitLossSummaryWidget(),

                const SizedBox(height: 16),

                // Detailed Table
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Detailed Statement',
                              style: Theme.of(context).textTheme.headlineSmall,
                            ),
                            Row(
                              children: [
                                Text(
                                  'Period: ${controller.currentReport!.startDate.day}/${controller.currentReport!.startDate.month}/${controller.currentReport!.startDate.year} - ${controller.currentReport!.endDate.day}/${controller.currentReport!.endDate.month}/${controller.currentReport!.endDate.year}',
                                  style: Theme.of(context).textTheme.bodyMedium,
                                ),
                                const SizedBox(width: 16),
                                Chip(
                                  label: Text(
                                    controller.isProfitable
                                        ? 'Profitable'
                                        : 'Loss',
                                    style: TextStyle(
                                      color: controller.isProfitable
                                          ? Colors.green
                                          : Colors.red,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  backgroundColor: controller.isProfitable
                                      ? Colors.green.withOpacity(0.1)
                                      : Colors.red.withOpacity(0.1),
                                ),
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        const ProfitLossTableWidget(),
                      ],
                    ),
                  ),
                ),
              ] else ...[
                // Empty State
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(32.0),
                    child: Center(
                      child: Column(
                        children: [
                          Icon(
                            Icons.assessment_outlined,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No Report Generated',
                            style: Theme.of(context)
                                .textTheme
                                .headlineSmall
                                ?.copyWith(
                                  color: Colors.grey[600],
                                ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Set the report parameters above and click "Generate Report" to create your Profit & Loss statement.',
                            textAlign: TextAlign.center,
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  color: Colors.grey[600],
                                ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],

              const SizedBox(height: 16),

              // Saved Reports Section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Saved Reports',
                            style: Theme.of(context).textTheme.headlineSmall,
                          ),
                          IconButton(
                            icon: const Icon(Icons.refresh),
                            onPressed: controller.loadSavedReports,
                            tooltip: 'Refresh',
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      const SavedReportsWidget(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  /// Show export options dialog
  void _showExportOptions(
      BuildContext context, ProfitLossController controller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Options'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.picture_as_pdf),
              title: const Text('Export as PDF'),
              onTap: () {
                Navigator.of(context).pop();
                _exportToPDF(controller);
              },
            ),
            ListTile(
              leading: const Icon(Icons.table_chart),
              title: const Text('Export as Excel'),
              onTap: () {
                Navigator.of(context).pop();
                _exportToExcel(controller);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  /// Export to PDF
  void _exportToPDF(ProfitLossController controller) async {
    if (controller.currentReport == null) {
      Get.snackbar('Error', 'No report available to export');
      return;
    }

    try {
      controller.setLoading(true);

      final pdfBytes = await controller.exportToPDF();
      final fileName =
          'profit_loss_${DateFormat('yyyyMMdd').format(controller.currentReport!.endDate)}.pdf';

      await controller.exportService.sharePDF(pdfBytes, fileName);

      Get.snackbar(
        'Success',
        'PDF exported successfully',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to export PDF: $e',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      controller.setLoading(false);
    }
  }

  /// Export to Excel
  void _exportToExcel(ProfitLossController controller) async {
    if (controller.currentReport == null) {
      Get.snackbar('Error', 'No report available to export');
      return;
    }

    try {
      controller.setLoading(true);

      await controller.exportToExcel();

      Get.snackbar(
        'Success',
        'Excel file downloaded successfully',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to export Excel: $e',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      controller.setLoading(false);
    }
  }
}
