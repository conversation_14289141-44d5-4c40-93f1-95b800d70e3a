import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/core/utils/constants/constants.dart';
import 'package:logestics/core/utils/constants/custom_dialogs.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/features/locations/domain/usecases/zone_use_case/create_zone_use_case.dart';
import 'package:logestics/features/locations/presentation/zones/controller/zone_list_controller.dart';
import 'package:logestics/models/locations/zone_model.dart';

class AddZoneController extends GetxController {
  final String? currentZoneName;
  final CreateZoneUseCase createZoneUseCase;
  var isLoading = false.obs;

  AddZoneController({
    this.currentZoneName,
    required this.createZoneUseCase,
  });

  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  late TextEditingController zoneNameController;

  var editMode = false;

  @override
  void onInit() {
    super.onInit();
    zoneNameController = TextEditingController(text: currentZoneName ?? '');
    currentZoneName == null ? editMode = false : editMode = true;
  }

  @override
  void onClose() {
    zoneNameController.dispose();
    super.onClose();
  }

  String? validateZoneName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Zone name is required.';
    }
    return null;
  }

  saveZone() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    var zoneName = zoneNameController.text.trim();

    await createZone(
      zone: ZoneModel(
        zoneId: '',
        zoneName: zoneName.capitalizeFirstLetter(),
        createdAt: DateTime.now(),
      ),
    );
    // : await updateZone(zoneName: zoneName);
  }

  Future<void> createZone({required ZoneModel zone}) async {
    if (zone.zoneName.isEmpty) {
      showErrorSnackbar('Zone name must not be empty.');
      return;
    }

    isLoading.value = true;
    try {
      final result = await createZoneUseCase.call(zone: zone);
      result.fold(
        (failure) => showErrorDialog(failure),
        (success) => _showSuccessSnackbar(success, zone),
      );
    } catch (e) {
      showUnexpectedErrorDialog();
    } finally {
      isLoading.value = false;
    }
  }

  // Future<void> updateZone({required String zoneName}) async {
  //   Get.dialog(
  //     const Center(child: CircularProgressIndicator()),
  //     barrierDismissible: false,
  //   );

  //   try {
  //     final result = await firebaseServices.updateZone(zoneName: zoneName);
  //     Get.back();
  //     result.fold(
  //       (failure) => showErrorDialog(failure),
  //       (success) => _showSuccessSnackbar(success),
  //     );
  //   } catch (e) {
  //     Get.back();
  //     Get.defaultDialog(
  //       title: "Unexpected Error",
  //       middleText: "Something went wrong. Please try again later.",
  //       textConfirm: "OK",
  //       confirmTextColor: Colors.white,
  //       onConfirm: () => Get.back(),
  //     );
  //   }
  // }

  void showErrorSnackbar(String errorMessage) {
    if (errorMessage.isNotEmpty) {
      (failure) => SnackbarUtils.showError(
            "Error",
            errorMessage,
          );
    }
  }

  void _showSuccessSnackbar(SuccessObj success, ZoneModel zone) {
    try {
      final zoneListController = Get.find<ZoneListController>();
      // Add the new zone to the list
      zoneListController.addZoneToList(zone);
    } catch (e) {
      log('ZoneListController not found: $e');
    } finally {
      clearControllers();
      Get.back();
      displaySuccessSnackbar(success);
    }
  }

  void clearControllers() {
    zoneNameController.clear();
  }
}
