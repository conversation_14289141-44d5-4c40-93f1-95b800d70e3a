import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/bindings/app_bindings.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/widgets/data_table_cell.dart';
import 'package:logestics/core/utils/widgets/pagination_widget.dart';
import 'package:logestics/features/finance/expense_categories/presentation/controllers/expense_category_controller.dart';
import 'package:logestics/models/finance/expense_category_model.dart';

class ExpenseCategoriesView extends StatefulWidget {
  const ExpenseCategoriesView({super.key});

  @override
  State<ExpenseCategoriesView> createState() => _ExpenseCategoriesViewState();
}

class _ExpenseCategoriesViewState extends State<ExpenseCategoriesView> {
  late final ExpenseCategoryManagementController controller;

  @override
  void initState() {
    super.initState();

    AppBindings().dependencies();
    controller = Get.put(ExpenseCategoryManagementController(Get.find()),
        permanent: true);

    // Load categories to ensure they're available
    if (controller.categories.isEmpty) {
      controller.loadCategories();
    }
  }

  @override
  Widget build(BuildContext context) {
    var width = Get.width;
    final isDarkMode = Get.isDarkMode;

    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          child: Column(
            children: [
              SizedBox(
                height: width < 650 ? 55 : 40,
                width: width,
                child: width < 650
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Expense Categories',
                            overflow: TextOverflow.ellipsis,
                            style: AppTextStyles.titleStyle.copyWith(
                                color:
                                    isDarkMode ? Colors.white : Colors.black),
                          ),
                          const Spacer(),
                        ],
                      )
                    : Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Expense Categories',
                            overflow: TextOverflow.ellipsis,
                            style: AppTextStyles.titleStyle.copyWith(
                                color:
                                    isDarkMode ? Colors.white : Colors.black),
                          ),
                          const Spacer(),
                        ],
                      ),
              ),
              const SizedBox(height: 20),
              SizedBox(
                height: 570,
                child: _buildCategoriesList(),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCategoriesList() {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            color: Get.isDarkMode ? Colors.grey[900] : Colors.white,
            borderRadius: BorderRadius.circular(10),
          ),
          padding: const EdgeInsets.symmetric(vertical: 15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 15),
                child: Get.width < 650
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          InkWell(
                            onTap: () => controller.openDialog(),
                            child: Text(
                              'Add New Category',
                              style: AppTextStyles.addNewInvoiceStyle,
                            ),
                          ),
                          TextField(
                            controller: controller.searchController,
                            decoration: InputDecoration(
                              hintText: 'Search categories...',
                              prefixIcon: const Icon(Icons.search),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                              filled: true,
                              fillColor: Get.isDarkMode
                                  ? Colors.grey[800]
                                  : Colors.grey[100],
                            ),
                          ),
                        ],
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          InkWell(
                            onTap: () => controller.openDialog(),
                            child: Text(
                              'Add New Category',
                              style: AppTextStyles.addNewInvoiceStyle,
                            ),
                          ),
                          SizedBox(
                            width: Get.width < 850
                                ? Get.width / 2
                                : Get.width / 3.5,
                            child: TextField(
                              controller: controller.searchController,
                              decoration: InputDecoration(
                                hintText: 'Search categories...',
                                prefixIcon: const Icon(Icons.search),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8.0),
                                ),
                                filled: true,
                                fillColor: Get.isDarkMode
                                    ? Colors.grey[800]
                                    : Colors.grey[100],
                              ),
                            ),
                          ),
                        ],
                      ),
              ),
              const SizedBox(height: 20),
              Expanded(
                child: SizedBox(
                  width: Get.width,
                  child: Obx(() {
                    if (controller.isLoading) {
                      return const Center(
                        child: CircularProgressIndicator(),
                      );
                    }

                    if (controller.filteredCategories.isEmpty) {
                      return Center(
                        child: Text(
                          'No categories found',
                          style: TextStyle(
                            color: Get.isDarkMode ? Colors.white : Colors.black,
                          ),
                        ),
                      );
                    }

                    return ListView(
                      shrinkWrap: true,
                      children: [
                        Table(
                          border: TableBorder(
                            horizontalInside: BorderSide(
                              color: Get.isDarkMode
                                  ? Colors.grey[700]!
                                  : Colors.grey[300]!,
                            ),
                          ),
                          children: [
                            TableRow(
                              decoration: BoxDecoration(
                                color: Get.isDarkMode
                                    ? Colors.grey[800]
                                    : Colors.grey[100],
                              ),
                              children: [
                                DataTableHeaderCell(
                                  text: 'S.No',
                                  textColor: Get.isDarkMode
                                      ? Colors.white
                                      : Colors.black,
                                ),
                                DataTableHeaderCell(
                                  text: 'Name',
                                  textColor: Get.isDarkMode
                                      ? Colors.white
                                      : Colors.black,
                                ),
                                DataTableHeaderCell(
                                  text: 'Description',
                                  textColor: Get.isDarkMode
                                      ? Colors.white
                                      : Colors.black,
                                ),
                                DataTableHeaderCell(
                                  text: 'Actions',
                                  textColor: Get.isDarkMode
                                      ? Colors.white
                                      : Colors.black,
                                ),
                              ],
                            ),
                            for (var i = 0;
                                i < controller.paginatedCategories.length;
                                i++)
                              TableRow(
                                children: [
                                  DataTableCell(
                                    text:
                                        '${((controller.currentPage.value - 1) * controller.itemsPerPage.value) + i + 1}',
                                    style:
                                        AppTextStyles.invoiceDataStyle.copyWith(
                                      color: Get.isDarkMode
                                          ? Colors.white
                                          : Colors.black,
                                    ),
                                  ),
                                  DataTableCell(
                                    onTap: () => _showCategoryDetails(
                                        controller.paginatedCategories[i]),
                                    text:
                                        controller.paginatedCategories[i].name,
                                    style:
                                        AppTextStyles.invoiceDataStyle.copyWith(
                                      color: Get.isDarkMode
                                          ? Colors.white
                                          : Colors.black,
                                      decoration: TextDecoration.underline,
                                    ),
                                  ),
                                  DataTableCell(
                                    text: controller
                                        .paginatedCategories[i].description,
                                    style:
                                        AppTextStyles.invoiceDataStyle.copyWith(
                                      color: Get.isDarkMode
                                          ? Colors.white
                                          : Colors.black,
                                    ),
                                  ),
                                  DataTableActionsCell(
                                    menuItems: [
                                      DataTablePopupMenuItem(
                                        text: 'Edit',
                                        icon: Icons.edit_outlined,
                                        onTap: () => _showCategoryDetails(
                                            controller.paginatedCategories[i]),
                                      ),
                                      DataTablePopupMenuItem(
                                        text: 'Delete',
                                        icon: Icons.delete_outline,
                                        isDanger: true,
                                        onTap: () async {
                                          if (!controller.isLoading) {
                                            await _confirmDelete(controller
                                                .paginatedCategories[i].id);
                                          }
                                        },
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                          ],
                        ),
                      ],
                    );
                  }),
                ),
              ),
              // Pagination Widget
              Obx(() => PaginationWidget(
                    currentPage: controller.currentPage.value,
                    totalPages: controller.totalPages,
                    itemsPerPage: controller.itemsPerPage.value,
                    onPageChanged: (page) => controller.setCurrentPage(page),
                    onItemsPerPageChanged: (count) =>
                        controller.setItemsPerPage(count),
                  )),
            ],
          ),
        ),
        Obx(() {
          if (!controller.isDialogOpen) return const SizedBox.shrink();

          return Positioned(
            right: 0,
            top: 0,
            bottom: 0,
            width: Get.width < 650 ? Get.width : 400,
            child: Container(
              decoration: BoxDecoration(
                color: Get.isDarkMode ? Colors.grey[900] : Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(10),
                    blurRadius: 10,
                    offset: const Offset(0, 0),
                  ),
                ],
              ),
              child: Form(
                key: controller.formKey,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Add New Category',
                            style: AppTextStyles.titleStyle.copyWith(
                              color:
                                  Get.isDarkMode ? Colors.white : Colors.black,
                            ),
                          ),
                          IconButton(
                            onPressed: controller.closeDialog,
                            icon: const Icon(Icons.close),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      TextFormField(
                        controller: controller.nameController,
                        decoration: InputDecoration(
                          labelText: 'Category Name',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          filled: true,
                          fillColor: Get.isDarkMode
                              ? Colors.grey[800]
                              : Colors.grey[100],
                        ),
                        validator: controller.validateName,
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: controller.descriptionController,
                        decoration: InputDecoration(
                          labelText: 'Description',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          filled: true,
                          fillColor: Get.isDarkMode
                              ? Colors.grey[800]
                              : Colors.grey[100],
                        ),
                        maxLines: 3,
                      ),
                      const SizedBox(height: 24),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: controller.addCategory,
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text('Add Category'),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }),
      ],
    );
  }

  Future<void> _confirmDelete(String id) {
    return Get.dialog(
      AlertDialog(
        title: const Text('Confirm Delete'),
        content: const Text('Are you sure you want to delete this category?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Get.back();
              Get.dialog(
                const Center(
                  child: CircularProgressIndicator(),
                ),
                barrierDismissible: false,
              );
              await controller.deleteCategory(id);
              Get.back();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showCategoryDetails(ExpenseCategoryModel category) {
    controller.nameController.text = category.name;
    controller.descriptionController.text = category.description;

    Get.dialog(
      Dialog(
        insetPadding: const EdgeInsets.all(20),
        child: Container(
          width: Get.width < 650 ? Get.width : 500,
          padding: const EdgeInsets.all(20),
          child: Form(
            key: controller.formKey,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Category Details',
                        style: AppTextStyles.titleStyle.copyWith(
                          color: Get.isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                      IconButton(
                        onPressed: () => Get.back(),
                        icon: const Icon(Icons.close),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  TextFormField(
                    controller: controller.nameController,
                    decoration: InputDecoration(
                      labelText: 'Category Name',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      filled: true,
                      fillColor:
                          Get.isDarkMode ? Colors.grey[800] : Colors.grey[100],
                    ),
                    validator: controller.validateName,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: controller.descriptionController,
                    decoration: InputDecoration(
                      labelText: 'Description',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      filled: true,
                      fillColor:
                          Get.isDarkMode ? Colors.grey[800] : Colors.grey[100],
                    ),
                    maxLines: 3,
                  ),
                  const SizedBox(height: 24),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        if (controller.formKey.currentState!.validate()) {
                          final updatedCategory = ExpenseCategoryModel(
                            id: category.id,
                            name: controller.nameController.text,
                            description: controller.descriptionController.text,
                            createdAt: category.createdAt,
                          );
                          controller.updateCategory(updatedCategory);
                          Get.back();
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('Update Category'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // String _formatDate(DateTime date) {
  //   return '${date.day}/${date.month}/${date.year}';
  // }
}
