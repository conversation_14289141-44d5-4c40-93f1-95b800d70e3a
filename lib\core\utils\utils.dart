// import 'package:flutter/material.dart';

// class Utils {
//   static showSnackBar({
//     required BuildContext context,
//     required Widget icon,
//     required String title,
//     required String subtitle,
//   }) {
//     ScaffoldMessenger.of(context).showSnackBar(SnackBar(
//         backgroundColor: Colors.transparent,
//         closeIconColor: Colors.transparent,
//         elevation: 0,
//         content: Center(
//           child: Container(
//             width: 300,
//             margin: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
//             decoration: BoxDecoration(
//               boxShadow: [
//                 BoxShadow(
//                     color: Colors.black.withAlpha(5),
//                     offset: const Offset(4, 4),
//                     blurRadius: 10),
//                 BoxShadow(
//                     color: Colors.black.withAlpha(5),
//                     offset: const Offset(-4, -4),
//                     blurRadius: 10)
//               ],
//               borderRadius: BorderRadius.circular(10),
//               color: Colors.white,
//             ),
//             padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
//             child: Row(
//               children: [
//                 icon,
//                 const SizedBox(
//                   width: 10,
//                 ),
//                 Expanded(
//                   child: Column(
//                     mainAxisAlignment: MainAxisAlignment.center,
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       Text(
//                         title,
//                         style: const TextStyle(
//                             color: Colors.black, fontWeight: FontWeight.bold),
//                       ),
//                       Text(
//                         subtitle,
//                         style: const TextStyle(
//                             color: Colors.black54, fontSize: 12),
//                       ),
//                     ],
//                   ),
//                 )
//               ],
//             ),
//           ),
//         )));
//   }

//   static showWarning({required String message, required BuildContext context}) {
//     Utils.showSnackBar(
//         context: context,
//         icon: const Icon(
//           Icons.warning_amber,
//           color: Colors.red,
//         ),
//         title: 'Warning',
//         subtitle: message);
//   }

//   static showSuccess(
//       {required String message, required BuildContext context, String? title}) {
//     Utils.showSnackBar(
//         context: context,
//         icon: const Icon(
//           Icons.done_all,
//           color: Colors.orange,
//         ),
//         title: title ?? 'Success',
//         subtitle: message);
//   }
// }
