import 'dart:developer';
import 'package:either_dart/either.dart';
import '../../../../core/shared_services/failure_obj.dart';
import '../../../../core/shared_services/success_obj.dart';

import '../../../../firebase_service/accounting/trial_balance_firebase_service.dart';
import '../../../../models/accounting/financial_report_models.dart';

/// Abstract repository for Trial Balance operations
abstract class TrialBalanceRepository {
  /// Generate Trial Balance report for a specific date range
  Future<Either<FailureObj, TrialBalanceReport>> generateTrialBalance({
    required DateTime startDate,
    required DateTime endDate,
    required String companyName,
    bool includeInactiveAccounts = false,
    bool includeZeroBalances = false,
  });

  /// Save trial balance report to Firestore
  Future<Either<FailureObj, SuccessObj>> saveTrialBalanceReport(
      TrialBalanceReport report);

  /// Get saved trial balance reports
  Future<Either<FailureObj, List<TrialBalanceReport>>>
      getSavedTrialBalanceReports();

  /// Delete a saved trial balance report
  Future<Either<FailureObj, SuccessObj>> deleteTrialBalanceReport(
      String reportId);

  /// Get trial balance report by ID
  Future<Either<FailureObj, TrialBalanceReport>> getTrialBalanceReportById(
      String reportId);

  /// Validate trial balance (ensure debits equal credits)
  Future<Either<FailureObj, bool>> validateTrialBalance(DateTime asOfDate);
}

/// Implementation of Trial Balance repository
class TrialBalanceRepositoryImpl implements TrialBalanceRepository {
  final TrialBalanceFirebaseService _firebaseService;

  TrialBalanceRepositoryImpl(this._firebaseService);

  @override
  Future<Either<FailureObj, TrialBalanceReport>> generateTrialBalance({
    required DateTime startDate,
    required DateTime endDate,
    required String companyName,
    bool includeInactiveAccounts = false,
    bool includeZeroBalances = false,
  }) async {
    try {
      log('Repository: Generating trial balance for period: ${startDate.toIso8601String()} to ${endDate.toIso8601String()}');

      // Validate date range
      if (endDate.isBefore(startDate)) {
        return Left(FailureObj(
          code: 'invalid-date-range',
          message: 'End date cannot be before start date',
        ));
      }

      // Validate company name
      if (companyName.trim().isEmpty) {
        return Left(FailureObj(
          code: 'invalid-company-name',
          message: 'Company name cannot be empty',
        ));
      }

      final result = await _firebaseService.generateTrialBalance(
        startDate: startDate,
        endDate: endDate,
        companyName: companyName.trim(),
        includeInactiveAccounts: includeInactiveAccounts,
        includeZeroBalances: includeZeroBalances,
      );

      return result.fold(
        (failure) {
          log('Repository: Failed to generate trial balance: ${failure.message}');
          return Left(failure);
        },
        (report) {
          log('Repository: Successfully generated trial balance with ${report.accounts.length} accounts');
          return Right(report);
        },
      );
    } catch (e) {
      log('Repository: Error generating trial balance: $e');
      return Left(FailureObj(
        code: 'unexpected-error',
        message: 'Something went wrong',
      ));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> saveTrialBalanceReport(
      TrialBalanceReport report) async {
    try {
      log('Repository: Saving trial balance report: ${report.reportId}');

      // Validate report
      if (report.reportId.isEmpty) {
        return Left(FailureObj(
          code: 'invalid-report',
          message: 'Report ID cannot be empty',
        ));
      }

      if (report.companyName.trim().isEmpty) {
        return Left(FailureObj(
          code: 'invalid-report',
          message: 'Company name cannot be empty',
        ));
      }

      final result = await _firebaseService.saveTrialBalanceReport(report);

      return result.fold(
        (failure) {
          log('Repository: Failed to save trial balance report: ${failure.message}');
          return Left(failure);
        },
        (success) {
          log('Repository: Successfully saved trial balance report');
          return Right(success);
        },
      );
    } catch (e) {
      log('Repository: Error saving trial balance report: $e');
      return Left(FailureObj(
        code: 'unexpected-error',
        message: 'Something went wrong',
      ));
    }
  }

  @override
  Future<Either<FailureObj, List<TrialBalanceReport>>>
      getSavedTrialBalanceReports() async {
    try {
      log('Repository: Fetching saved trial balance reports');

      final result = await _firebaseService.getSavedTrialBalanceReports();

      return result.fold(
        (failure) {
          log('Repository: Failed to fetch saved trial balance reports: ${failure.message}');
          return Left(failure);
        },
        (reports) {
          log('Repository: Successfully fetched ${reports.length} saved trial balance reports');
          return Right(reports);
        },
      );
    } catch (e) {
      log('Repository: Error fetching saved trial balance reports: $e');
      return Left(FailureObj(
        code: 'unexpected-error',
        message: 'Something went wrong',
      ));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> deleteTrialBalanceReport(
      String reportId) async {
    try {
      log('Repository: Deleting trial balance report: $reportId');

      // Validate report ID
      if (reportId.trim().isEmpty) {
        return Left(FailureObj(
          code: 'invalid-report-id',
          message: 'Report ID cannot be empty',
        ));
      }

      final result =
          await _firebaseService.deleteTrialBalanceReport(reportId.trim());

      return result.fold(
        (failure) {
          log('Repository: Failed to delete trial balance report: ${failure.message}');
          return Left(failure);
        },
        (success) {
          log('Repository: Successfully deleted trial balance report');
          return Right(success);
        },
      );
    } catch (e) {
      log('Repository: Error deleting trial balance report: $e');
      return Left(FailureObj(
        code: 'unexpected-error',
        message: 'Something went wrong',
      ));
    }
  }

  @override
  Future<Either<FailureObj, TrialBalanceReport>> getTrialBalanceReportById(
      String reportId) async {
    try {
      log('Repository: Fetching trial balance report by ID: $reportId');

      // Validate report ID
      if (reportId.trim().isEmpty) {
        return Left(FailureObj(
          code: 'invalid-report-id',
          message: 'Report ID cannot be empty',
        ));
      }

      final result =
          await _firebaseService.getTrialBalanceReportById(reportId.trim());

      return result.fold(
        (failure) {
          log('Repository: Failed to fetch trial balance report: ${failure.message}');
          return Left(failure);
        },
        (report) {
          log('Repository: Successfully fetched trial balance report');
          return Right(report);
        },
      );
    } catch (e) {
      log('Repository: Error fetching trial balance report: $e');
      return Left(FailureObj(
        code: 'unexpected-error',
        message: 'Something went wrong',
      ));
    }
  }

  @override
  Future<Either<FailureObj, bool>> validateTrialBalance(
      DateTime asOfDate) async {
    try {
      log('Repository: Validating trial balance as of: ${asOfDate.toIso8601String()}');

      final result = await _firebaseService.validateTrialBalance(asOfDate);

      return result.fold(
        (failure) {
          log('Repository: Failed to validate trial balance: ${failure.message}');
          return Left(failure);
        },
        (isBalanced) {
          log('Repository: Trial balance validation result: ${isBalanced ? 'BALANCED' : 'UNBALANCED'}');
          return Right(isBalanced);
        },
      );
    } catch (e) {
      log('Repository: Error validating trial balance: $e');
      return Left(FailureObj(
        code: 'unexpected-error',
        message: 'Something went wrong',
      ));
    }
  }
}
