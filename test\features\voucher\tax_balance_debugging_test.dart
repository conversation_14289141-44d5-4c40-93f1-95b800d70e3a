import 'package:flutter_test/flutter_test.dart';
import 'package:logestics/models/finance/deposit_model.dart';
import 'package:logestics/models/finance/account_model.dart';

void main() {
  group('Tax Balance Debugging Tests', () {
    const voucherNumber = '001';
    const referenceNumber = 'V-001';
    
    // Helper function to create a test freight tax account
    AccountModel createFreightTaxAccount({double initialBalance = 10000.0}) {
      return AccountModel(
        id: 'freight_tax_account_1',
        name: '15% Freight Tax Account',
        initialBalance: initialBalance,
        availableBalance: initialBalance,
        accountNumber: 'FTA-001',
        branchCode: 'BR001',
        branchAddress: 'Main Branch',
        createdAt: DateTime.now(),
        uid: 'user1',
      );
    }

    // Helper function to simulate the exact voucher creation process
    List<DepositModel> simulateVoucherCreation({
      required AccountModel account,
      required double companyFreight,
      required List<String> selectedAuthorities,
    }) {
      final totalTaxAmount = companyFreight * 0.15; // 15% tax
      final deposits = <DepositModel>[];
      
      if (selectedAuthorities.length == 1) {
        // Single authority gets full 15% tax amount
        deposits.add(DepositModel(
          id: 'tax_dep_single',
          accountId: account.id,
          accountName: account.name,
          amount: totalTaxAmount,
          createdAt: DateTime.now(),
          categoryId: 'tax_authority_single',
          categoryName: 'Tax Authority Payment',
          payerId: '',
          payerName: selectedAuthorities[0],
          referenceNumber: referenceNumber,
          notes: 'Tax payment to ${selectedAuthorities[0]} - Voucher #$voucherNumber',
        ));
      } else if (selectedAuthorities.length == 2) {
        // Dual authorities split the 15% tax amount equally (7.5% each)
        final splitAmount = totalTaxAmount / 2;
        for (int i = 0; i < selectedAuthorities.length; i++) {
          deposits.add(DepositModel(
            id: 'tax_dep_split_${i + 1}',
            accountId: account.id,
            accountName: account.name,
            amount: splitAmount,
            createdAt: DateTime.now(),
            categoryId: 'tax_authority_split',
            categoryName: 'Tax Authority Payment (Split)',
            payerId: '',
            payerName: selectedAuthorities[i],
            referenceNumber: referenceNumber,
            notes: 'Tax payment to ${selectedAuthorities[i]} - Voucher #$voucherNumber (Split payment)',
          ));
        }
      }
      
      return deposits;
    }

    // Helper function to simulate deposit cleanup (voucher deletion)
    List<DepositModel> simulateDepositCleanup({
      required List<DepositModel> allDeposits,
      required String targetReferenceNumber,
    }) {
      return allDeposits
          .where((deposit) => deposit.referenceNumber == targetReferenceNumber)
          .toList();
    }

    // Helper function to calculate account balance changes
    double calculateBalanceAfterDeposits(double initialBalance, List<DepositModel> deposits) {
      return deposits.fold(initialBalance, (balance, deposit) => balance + deposit.amount);
    }

    double calculateBalanceAfterDeletions(double currentBalance, List<DepositModel> deposits) {
      return deposits.fold(currentBalance, (balance, deposit) => balance - deposit.amount);
    }

    test('should debug single tax authority payment scenario', () {
      final account = createFreightTaxAccount(initialBalance: 10000.0);
      const companyFreight = 45000.0;
      final selectedAuthorities = ['SRB (Sindh Revenue Board)'];
      
      print('\n=== SINGLE TAX AUTHORITY DEBUGGING ===');
      print('Initial account balance: ${account.availableBalance}');
      print('Company freight: $companyFreight');
      print('Selected authorities: $selectedAuthorities');
      
      // Step 1: Create voucher with tax authority payments
      final taxDeposits = simulateVoucherCreation(
        account: account,
        companyFreight: companyFreight,
        selectedAuthorities: selectedAuthorities,
      );
      
      print('Tax deposits created: ${taxDeposits.length}');
      for (int i = 0; i < taxDeposits.length; i++) {
        final deposit = taxDeposits[i];
        print('  Deposit ${i + 1}: ${deposit.categoryId} - ${deposit.payerName} - \$${deposit.amount}');
      }
      
      // Step 2: Calculate balance after voucher creation
      final balanceAfterCreation = calculateBalanceAfterDeposits(account.availableBalance, taxDeposits);
      print('Balance after voucher creation: $balanceAfterCreation');
      
      // Step 3: Simulate voucher deletion cleanup
      final depositsToDelete = simulateDepositCleanup(
        allDeposits: taxDeposits,
        targetReferenceNumber: referenceNumber,
      );
      
      print('Deposits found for deletion: ${depositsToDelete.length}');
      for (int i = 0; i < depositsToDelete.length; i++) {
        final deposit = depositsToDelete[i];
        print('  To delete ${i + 1}: ${deposit.categoryId} - ${deposit.payerName} - \$${deposit.amount}');
      }
      
      // Step 4: Calculate balance after voucher deletion
      final balanceAfterDeletion = calculateBalanceAfterDeletions(balanceAfterCreation, depositsToDelete);
      print('Balance after voucher deletion: $balanceAfterDeletion');
      
      // Verify results
      expect(taxDeposits.length, 1);
      expect(depositsToDelete.length, 1);
      expect(balanceAfterCreation, 16750.0); // 10000 + 6750
      expect(balanceAfterDeletion, 10000.0); // Back to original
      expect(balanceAfterDeletion >= 0, true);
      
      print('✅ Single tax authority scenario: PASSED');
    });

    test('should debug split tax authority payment scenario', () {
      final account = createFreightTaxAccount(initialBalance: 10000.0);
      const companyFreight = 45000.0;
      final selectedAuthorities = ['SRB (Sindh Revenue Board)', 'PRA (Punjab Revenue Authority)'];
      
      print('\n=== SPLIT TAX AUTHORITY DEBUGGING ===');
      print('Initial account balance: ${account.availableBalance}');
      print('Company freight: $companyFreight');
      print('Selected authorities: $selectedAuthorities');
      
      // Step 1: Create voucher with tax authority payments
      final taxDeposits = simulateVoucherCreation(
        account: account,
        companyFreight: companyFreight,
        selectedAuthorities: selectedAuthorities,
      );
      
      print('Tax deposits created: ${taxDeposits.length}');
      for (int i = 0; i < taxDeposits.length; i++) {
        final deposit = taxDeposits[i];
        print('  Deposit ${i + 1}: ${deposit.categoryId} - ${deposit.payerName} - \$${deposit.amount}');
      }
      
      // Step 2: Calculate balance after voucher creation
      final balanceAfterCreation = calculateBalanceAfterDeposits(account.availableBalance, taxDeposits);
      print('Balance after voucher creation: $balanceAfterCreation');
      
      // Step 3: Simulate voucher deletion cleanup
      final depositsToDelete = simulateDepositCleanup(
        allDeposits: taxDeposits,
        targetReferenceNumber: referenceNumber,
      );
      
      print('Deposits found for deletion: ${depositsToDelete.length}');
      for (int i = 0; i < depositsToDelete.length; i++) {
        final deposit = depositsToDelete[i];
        print('  To delete ${i + 1}: ${deposit.categoryId} - ${deposit.payerName} - \$${deposit.amount}');
      }
      
      // Step 4: Calculate balance after voucher deletion
      final balanceAfterDeletion = calculateBalanceAfterDeletions(balanceAfterCreation, depositsToDelete);
      print('Balance after voucher deletion: $balanceAfterDeletion');
      
      // Verify results
      expect(taxDeposits.length, 2);
      expect(depositsToDelete.length, 2);
      expect(balanceAfterCreation, 16750.0); // 10000 + 6750 (3375 + 3375)
      expect(balanceAfterDeletion, 10000.0); // Back to original
      expect(balanceAfterDeletion >= 0, true);
      
      print('✅ Split tax authority scenario: PASSED');
    });

    test('should identify partial deletion scenario (potential bug)', () {
      final account = createFreightTaxAccount(initialBalance: 10000.0);
      const companyFreight = 45000.0;
      final selectedAuthorities = ['SRB (Sindh Revenue Board)', 'PRA (Punjab Revenue Authority)'];
      
      print('\n=== PARTIAL DELETION SCENARIO (POTENTIAL BUG) ===');
      print('Initial account balance: ${account.availableBalance}');
      
      // Step 1: Create voucher with split tax authority payments
      final taxDeposits = simulateVoucherCreation(
        account: account,
        companyFreight: companyFreight,
        selectedAuthorities: selectedAuthorities,
      );
      
      print('Tax deposits created: ${taxDeposits.length}');
      final balanceAfterCreation = calculateBalanceAfterDeposits(account.availableBalance, taxDeposits);
      print('Balance after voucher creation: $balanceAfterCreation');
      
      // Step 2: Simulate partial deletion (only one of two deposits found)
      final partialDepositsToDelete = [taxDeposits[0]]; // Only delete first deposit
      
      print('Simulating partial deletion - only ${partialDepositsToDelete.length} of ${taxDeposits.length} deposits found');
      for (final deposit in partialDepositsToDelete) {
        print('  Deleting: ${deposit.categoryId} - ${deposit.payerName} - \$${deposit.amount}');
      }
      
      // Step 3: Calculate balance after partial deletion
      final balanceAfterPartialDeletion = calculateBalanceAfterDeletions(balanceAfterCreation, partialDepositsToDelete);
      print('Balance after partial deletion: $balanceAfterPartialDeletion');
      
      // This would leave the account with an incorrect balance
      expect(balanceAfterPartialDeletion, 13375.0); // 16750 - 3375 = 13375 (should be 10000)
      
      // Step 4: If user tries to delete the voucher again, it might try to delete the remaining deposit
      final remainingDeposit = taxDeposits[1];
      final balanceAfterSecondDeletion = calculateBalanceAfterDeletions(balanceAfterPartialDeletion, [remainingDeposit]);
      print('Balance after deleting remaining deposit: $balanceAfterSecondDeletion');
      
      expect(balanceAfterSecondDeletion, 10000.0); // This would be correct
      
      // But if the system tries to delete both deposits again...
      final balanceAfterDoubleDeletion = calculateBalanceAfterDeletions(balanceAfterCreation, taxDeposits);
      final balanceAfterTripleDeletion = calculateBalanceAfterDeletions(balanceAfterDoubleDeletion, taxDeposits);
      print('Balance after double deletion: $balanceAfterTripleDeletion');
      
      expect(balanceAfterTripleDeletion, 3250.0); // 10000 + 6750 - 6750 - 6750 = 3250
      
      print('🚨 POTENTIAL BUG: Partial or multiple deletions can cause incorrect balances');
    });

    test('should verify reference number filtering works correctly', () {
      final account = createFreightTaxAccount();
      
      // Create deposits for multiple vouchers
      final voucher1Deposits = simulateVoucherCreation(
        account: account,
        companyFreight: 30000.0,
        selectedAuthorities: ['SRB (Sindh Revenue Board)'],
      );
      
      final voucher2Deposits = simulateVoucherCreation(
        account: account,
        companyFreight: 20000.0,
        selectedAuthorities: ['PRA (Punjab Revenue Authority)', 'BRA (Balochistan Revenue Authority)'],
      );
      
      // Change reference number for voucher 2
      for (final deposit in voucher2Deposits) {
        final updatedDeposit = DepositModel(
          id: deposit.id,
          accountId: deposit.accountId,
          accountName: deposit.accountName,
          amount: deposit.amount,
          createdAt: deposit.createdAt,
          categoryId: deposit.categoryId,
          categoryName: deposit.categoryName,
          payerId: deposit.payerId,
          payerName: deposit.payerName,
          referenceNumber: 'V-002', // Different reference number
          notes: deposit.notes.replaceAll('V-001', 'V-002'),
        );
        voucher2Deposits[voucher2Deposits.indexOf(deposit)] = updatedDeposit;
      }
      
      final allDeposits = [...voucher1Deposits, ...voucher2Deposits];
      
      print('\n=== REFERENCE NUMBER FILTERING TEST ===');
      print('Total deposits: ${allDeposits.length}');
      print('Voucher 1 deposits: ${voucher1Deposits.length}');
      print('Voucher 2 deposits: ${voucher2Deposits.length}');
      
      // Test filtering for voucher 1
      final voucher1ToDelete = simulateDepositCleanup(
        allDeposits: allDeposits,
        targetReferenceNumber: 'V-001',
      );
      
      print('Deposits to delete for V-001: ${voucher1ToDelete.length}');
      expect(voucher1ToDelete.length, voucher1Deposits.length);
      
      // Test filtering for voucher 2
      final voucher2ToDelete = simulateDepositCleanup(
        allDeposits: allDeposits,
        targetReferenceNumber: 'V-002',
      );
      
      print('Deposits to delete for V-002: ${voucher2ToDelete.length}');
      expect(voucher2ToDelete.length, voucher2Deposits.length);
      
      // Verify no cross-contamination
      expect(voucher1ToDelete.every((d) => d.referenceNumber == 'V-001'), true);
      expect(voucher2ToDelete.every((d) => d.referenceNumber == 'V-002'), true);
      
      print('✅ Reference number filtering: PASSED');
    });
  });
}
