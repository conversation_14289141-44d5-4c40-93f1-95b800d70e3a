import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';
import 'package:logestics/models/locations/station_model.dart';

class StationFirebaseService {
  late FirebaseFirestore _firestore;

  StationFirebaseService() {
    _firestore = FirebaseFirestore.instance;
  }

  Future<void> createStation(StationModel station) async {
    log('Creating station: ${station.stationName}');
    try {
      final stationRef =
          _firestore.collection(AppCollection.stationsCollection).doc();
      station.stationId = stationRef.id;
      await stationRef.set(station.toJson());
      log('Successfully created station: ${station.stationId}');
    } catch (e) {
      log('Error creating station: $e');
      rethrow;
    }
  }

  Future<List<StationModel>> getStations() async {
    log('Fetching stations from Firestore');
    try {
      final snapshot = await _firestore
          .collection(AppCollection.stationsCollection)
          .orderBy('createdAt', descending: true)
          .get();

      final stations = snapshot.docs
          .map((doc) => StationModel.fromJson(doc.data()))
          .toList();

      log('Successfully fetched ${stations.length} stations');
      return stations;
    } catch (e) {
      log('Error fetching stations: $e');
      rethrow;
    }
  }

  Stream<List<StationModel>> listenToStations() {
    return _firestore
        .collection(AppCollection.stationsCollection)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map((doc) => StationModel.fromJson(doc.data()))
              .toList(),
        );
  }

  Future<void> deleteStation(String stationId) async {
    log('Deleting station: $stationId');
    try {
      await _firestore
          .collection(AppCollection.stationsCollection)
          .doc(stationId)
          .delete();
      log('Successfully deleted station: $stationId');
    } catch (e) {
      log('Error deleting station: $e');
      rethrow;
    }
  }

  Future<void> updateStation(StationModel station) async {
    log('Updating station: ${station.stationId}');
    try {
      await _firestore
          .collection(AppCollection.stationsCollection)
          .doc(station.stationId)
          .update(station.toJson());
      log('Successfully updated station: ${station.stationId}');
    } catch (e) {
      log('Error updating station: $e');
      rethrow;
    }
  }

  Future<bool> checkStationExists(String stationName) async {
    try {
      final snapshot = await _firestore
          .collection(AppCollection.stationsCollection)
          .where('stationName', isEqualTo: stationName)
          .limit(1)
          .get();

      return snapshot.docs.isNotEmpty;
    } catch (e) {
      log('Error checking station existence: $e');
      rethrow;
    }
  }

  Future<List<StationModel>> getStationsByDistrict(String districtId) async {
    try {
      final snapshot = await _firestore
          .collection(AppCollection.stationsCollection)
          .where('districtId', isEqualTo: districtId)
          .get();

      return snapshot.docs
          .map((doc) => StationModel.fromJson(doc.data()))
          .toList();
    } catch (e) {
      log('Error getting stations by district: $e');
      rethrow;
    }
  }
}
