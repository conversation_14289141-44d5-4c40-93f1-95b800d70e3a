import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dartz/dartz.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:uuid/uuid.dart';
import '../../core/shared_services/failure_obj.dart';
import '../../models/finance/cash_flow_statement_model.dart';
import '../../models/finance/chart_of_accounts_model.dart';
import '../../models/finance/journal_entry_model.dart';
import '../../core/utils/app_constants/firebase/collection_names.dart';

/// Firebase service for Cash Flow Statement operations
class CashFlowStatementFirebaseService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final Uuid _uuid = const Uuid();

  /// Generate Cash Flow Statement report
  Future<Either<FailureObj, CashFlowStatementReport>>
      generateCashFlowStatement({
    required DateTime startDate,
    required DateTime endDate,
    required String companyName,
    bool includeInactiveAccounts = false,
    bool includeZeroBalances = false,
    bool useDirectMethod = false,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return Left(FailureObj(
          code: 'user-not-authenticated',
          message: 'User not authenticated',
        ));
      }

      log('Generating Cash Flow Statement from $startDate to $endDate');

      // Get net income from P&L for the period
      final netIncome =
          await _getNetIncomeForPeriod(user.uid, startDate, endDate);

      // Get beginning and ending cash balances
      final beginningCashBalance =
          await _getCashBalanceAsOf(user.uid, startDate);
      final endingCashBalance = await _getCashBalanceAsOf(user.uid, endDate);

      // Generate activity sections
      final operatingActivities = await _generateOperatingActivities(
          user.uid, startDate, endDate, netIncome, useDirectMethod);
      final investingActivities =
          await _generateInvestingActivities(user.uid, startDate, endDate);
      final financingActivities =
          await _generateFinancingActivities(user.uid, startDate, endDate);

      // Create report
      final report = CashFlowStatementReport(
        reportId: _uuid.v4(),
        companyName: companyName,
        uid: user.uid,
        startDate: startDate,
        endDate: endDate,
        generatedAt: DateTime.now(),
        generatedBy: user.email ?? 'Unknown',
        operatingActivities: operatingActivities,
        investingActivities: investingActivities,
        financingActivities: financingActivities,
        netIncomeFromPL: netIncome,
        beginningCashBalance: beginningCashBalance,
        endingCashBalance: endingCashBalance,
      );

      log('Cash Flow Statement generated successfully');
      return Right(report);
    } catch (e) {
      log('Error generating Cash Flow Statement: $e');
      return Left(FailureObj(
        code: 'cash-flow-generation-error',
        message: 'Failed to generate Cash Flow Statement: $e',
      ));
    }
  }

  /// Get net income for the period from revenue and expense accounts
  Future<double> _getNetIncomeForPeriod(
      String uid, DateTime startDate, DateTime endDate) async {
    try {
      // Get all journal entries for the period
      final journalEntriesQuery = await _firestore
          .collection(AppCollection.journalEntriesCollection)
          .where('uid', isEqualTo: uid)
          .where('entryDate',
              isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
          .where('entryDate', isLessThanOrEqualTo: Timestamp.fromDate(endDate))
          .where('status', isEqualTo: 'posted')
          .get();

      double totalRevenue = 0.0;
      double totalExpenses = 0.0;

      // Get chart of accounts to categorize
      final accountsQuery = await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .where('uid', isEqualTo: uid)
          .get();

      final Map<String, AccountCategory> accountCategories = {};
      for (final doc in accountsQuery.docs) {
        final account =
            ChartOfAccountsModel.fromJson(doc.data() as Map<String, dynamic>);
        accountCategories[account.id] = account.category;
      }

      // Process journal entries
      for (final doc in journalEntriesQuery.docs) {
        final entry = JournalEntryModel.fromFirestore(doc);

        for (final line in entry.lines) {
          final category = accountCategories[line.accountId];

          if (category == AccountCategory.revenue) {
            // Revenue accounts: credits increase revenue
            totalRevenue += line.creditAmount - line.debitAmount;
          } else if (category == AccountCategory.expenses) {
            // Expense accounts: debits increase expenses
            totalExpenses += line.debitAmount - line.creditAmount;
          }
        }
      }

      return totalRevenue - totalExpenses;
    } catch (e) {
      log('Error calculating net income: $e');
      return 0.0;
    }
  }

  /// Get cash balance as of a specific date
  Future<double> _getCashBalanceAsOf(String uid, DateTime asOfDate) async {
    try {
      // Get all cash accounts
      final cashAccountsQuery = await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .where('uid', isEqualTo: uid)
          .where('accountType', isEqualTo: AccountType.cash.name)
          .get();

      double totalCashBalance = 0.0;

      for (final accountDoc in cashAccountsQuery.docs) {
        final account = ChartOfAccountsModel.fromJson(
            accountDoc.data() as Map<String, dynamic>);

        // Get journal entries for this account up to the date
        final journalEntriesQuery = await _firestore
            .collection(AppCollection.journalEntriesCollection)
            .where('uid', isEqualTo: uid)
            .where('entryDate',
                isLessThanOrEqualTo: Timestamp.fromDate(asOfDate))
            .where('status', isEqualTo: 'posted')
            .get();

        double accountBalance = 0.0;

        for (final entryDoc in journalEntriesQuery.docs) {
          final entry = JournalEntryModel.fromFirestore(entryDoc);

          for (final line in entry.lines) {
            if (line.accountId == account.id) {
              // Cash accounts: debits increase balance, credits decrease balance
              accountBalance += line.debitAmount - line.creditAmount;
            }
          }
        }

        totalCashBalance += accountBalance;
      }

      return totalCashBalance;
    } catch (e) {
      log('Error calculating cash balance: $e');
      return 0.0;
    }
  }

  /// Generate operating activities section
  Future<CashFlowActivitySection> _generateOperatingActivities(
      String uid,
      DateTime startDate,
      DateTime endDate,
      double netIncome,
      bool useDirectMethod) async {
    if (useDirectMethod) {
      return await _generateOperatingActivitiesDirect(uid, startDate, endDate);
    } else {
      return await _generateOperatingActivitiesIndirect(
          uid, startDate, endDate, netIncome);
    }
  }

  /// Generate operating activities using indirect method
  Future<CashFlowActivitySection> _generateOperatingActivitiesIndirect(
      String uid,
      DateTime startDate,
      DateTime endDate,
      double netIncome) async {
    final List<CashFlowLineItem> lineItems = [];

    // Start with net income
    lineItems.add(CashFlowLineItem(
      accountId: '',
      accountNumber: '',
      accountName: 'Net Income',
      description: 'Net Income from P&L Statement',
      amount: netIncome.abs(),
      activityType: CashFlowActivityType.operating,
      isInflow: netIncome >= 0,
    ));

    // Add adjustments for non-cash items (depreciation, etc.)
    // TODO: Add depreciation and other non-cash adjustments

    // Add changes in working capital
    // TODO: Add accounts receivable, accounts payable, inventory changes

    double totalInflows = lineItems
        .where((item) => item.isInflow)
        .fold(0.0, (sum, item) => sum + item.amount);
    double totalOutflows = lineItems
        .where((item) => !item.isInflow)
        .fold(0.0, (sum, item) => sum + item.amount);

    return CashFlowActivitySection(
      activityType: CashFlowActivityType.operating,
      lineItems: lineItems,
      totalInflows: totalInflows,
      totalOutflows: totalOutflows,
    );
  }

  /// Generate operating activities using direct method
  Future<CashFlowActivitySection> _generateOperatingActivitiesDirect(
      String uid, DateTime startDate, DateTime endDate) async {
    final List<CashFlowLineItem> lineItems = [];

    // TODO: Implement direct method
    // - Cash receipts from customers
    // - Cash payments to suppliers
    // - Cash payments for operating expenses
    // - Cash payments for interest and taxes

    double totalInflows = lineItems
        .where((item) => item.isInflow)
        .fold(0.0, (sum, item) => sum + item.amount);
    double totalOutflows = lineItems
        .where((item) => !item.isInflow)
        .fold(0.0, (sum, item) => sum + item.amount);

    return CashFlowActivitySection(
      activityType: CashFlowActivityType.operating,
      lineItems: lineItems,
      totalInflows: totalInflows,
      totalOutflows: totalOutflows,
    );
  }

  /// Generate investing activities section
  Future<CashFlowActivitySection> _generateInvestingActivities(
      String uid, DateTime startDate, DateTime endDate) async {
    final List<CashFlowLineItem> lineItems = [];

    // TODO: Implement investing activities
    // - Purchase/sale of property, plant, and equipment
    // - Purchase/sale of investments
    // - Loans made to/received from others

    double totalInflows = lineItems
        .where((item) => item.isInflow)
        .fold(0.0, (sum, item) => sum + item.amount);
    double totalOutflows = lineItems
        .where((item) => !item.isInflow)
        .fold(0.0, (sum, item) => sum + item.amount);

    return CashFlowActivitySection(
      activityType: CashFlowActivityType.investing,
      lineItems: lineItems,
      totalInflows: totalInflows,
      totalOutflows: totalOutflows,
    );
  }

  /// Generate financing activities section
  Future<CashFlowActivitySection> _generateFinancingActivities(
      String uid, DateTime startDate, DateTime endDate) async {
    final List<CashFlowLineItem> lineItems = [];

    // TODO: Implement financing activities
    // - Proceeds from/repayment of loans
    // - Issuance/repurchase of equity
    // - Dividend payments

    double totalInflows = lineItems
        .where((item) => item.isInflow)
        .fold(0.0, (sum, item) => sum + item.amount);
    double totalOutflows = lineItems
        .where((item) => !item.isInflow)
        .fold(0.0, (sum, item) => sum + item.amount);

    return CashFlowActivitySection(
      activityType: CashFlowActivityType.financing,
      lineItems: lineItems,
      totalInflows: totalInflows,
      totalOutflows: totalOutflows,
    );
  }
}
