import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/models/finance/fuel_rate_model.dart';
import 'package:logestics/features/finance/fuel_cards/repositories/fuel_card_repository.dart';

class GetLatestFuelRateUseCase {
  final FuelCardRepository repository;

  GetLatestFuelRateUseCase(this.repository);

  Future<Either<FailureObj, FuelRateModel>> call(String companyName) async {
    try {
      return await repository.getLatestFuelRate(companyName);
    } catch (e) {
      return Left(
        FailureObj(
          code: 'unexpected-error',
          message: 'An unexpected error occurred: $e',
        ),
      );
    }
  }
}
