import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/constants/custom_dialogs.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/core/utils/mixins/pagination_mixin.dart';
import 'package:logestics/features/locations/domain/usecases/zone_use_case/delete_zone_use_case.dart';
import 'package:logestics/features/locations/domain/usecases/zone_use_case/get_zones_use_case.dart';
import 'package:logestics/models/locations/zone_model.dart';

class ZoneListController extends GetxController with PaginationMixin {
  final GetZonesUseCase getZonesUseCase;
  final DeleteZoneUseCase deleteZoneUseCase;

  ZoneListController({
    required this.getZonesUseCase,
    required this.deleteZoneUseCase,
  });

  var zones = <ZoneModel>[].obs;
  var filteredZones = <ZoneModel>[].obs;
  var isLoading = false.obs;
  final searchQuery = ''.obs;

  var searchController = TextEditingController();

  @override
  void onInit() {
    super.onInit();
    searchController.addListener(_onSearchChanged);
    fetchZones();
  }

  void _onSearchChanged() {
    searchQuery.value = searchController.text;
    _filterZones();
  }

  void _filterZones() {
    if (searchQuery.value.isEmpty) {
      filteredZones.value = zones;
    } else {
      final query = searchQuery.value.toLowerCase();
      filteredZones.value = zones.where((zone) {
        return zone.zoneName.toLowerCase().contains(query);
      }).toList();
    }
    setTotalItems(filteredZones.length);
  }

  Future<void> fetchZones() async {
    if (isLoading.value) return;

    isLoading.value = true;
    try {
      log('Fetching zones...');
      final result = await getZonesUseCase.call();
      result.fold(
        (failure) {
          log('Failed to fetch zones: ${failure.message}');
          showErrorDialog(failure);
        },
        (zoneList) {
          log('Successfully fetched ${zoneList.length} zones');
          zones.value = zoneList;
          _filterZones();
        },
      );
    } catch (e) {
      log('Unexpected error occurred while fetching zones: $e');
      showUnexpectedErrorDialog();
    } finally {
      isLoading.value = false;
    }
  }

  List<ZoneModel> get paginatedZones => paginateList(filteredZones);

  Future<void> deleteZone(String zoneId) async {
    try {
      log('Deleting zone: $zoneId');
      zones.removeWhere((zone) => zone.zoneId == zoneId);
      setTotalItems(zones.length);
      final result = await deleteZoneUseCase.call(zoneId: zoneId);
      result.fold(
        (failure) {
          log('Failed to delete zone: ${failure.message}');
          showErrorDialog(failure);
        },
        (success) {
          log('Successfully deleted zone');
          SnackbarUtils.showSuccess(
            AppStrings.success,
            'Zone deleted successfully',
          );
        },
      );
    } catch (e) {
      log('Unexpected error occurred while deleting zone: $e');
      showUnexpectedErrorDialog();
    } finally {
      isLoading.value = false;
    }
  }

  void addZoneToList(ZoneModel zone) {
    zones.insert(0, zone);
    setTotalItems(zones.length);
  }
}
