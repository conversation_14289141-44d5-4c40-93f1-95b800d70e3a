import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/balance_sheet_controller.dart';

/// Table widget for displaying detailed Balance Sheet data
class BalanceSheetTableWidget extends StatelessWidget {
  const BalanceSheetTableWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<BalanceSheetController>();

    return Obx(() {
      if (!controller.hasReport) {
        return const SizedBox.shrink();
      }

      final report = controller.currentReport!;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Assets Section
          if (controller.hasAssetData) ...[
            _buildSectionHeader(context, 'ASSETS', Colors.blue),
            const SizedBox(height: 8),
            ...report.assetGroups
                .map((group) => _buildGroupSection(context, group)),
            _buildTotalRow(
                context, 'Total Assets', report.totalAssets, Colors.blue),
            const SizedBox(height: 24),
          ],

          // Liabilities Section
          if (controller.hasLiabilityData) ...[
            _buildSectionHeader(context, 'LIABILITIES', Colors.orange),
            const SizedBox(height: 8),
            ...report.liabilityGroups
                .map((group) => _buildGroupSection(context, group)),
            _buildTotalRow(context, 'Total Liabilities',
                report.totalLiabilities, Colors.orange),
            const SizedBox(height: 24),
          ],

          // Equity Section
          if (controller.hasEquityData) ...[
            _buildSectionHeader(context, 'EQUITY', Colors.green),
            const SizedBox(height: 8),
            ...report.equityGroups
                .map((group) => _buildGroupSection(context, group)),
            _buildTotalRow(
                context, 'Total Equity', report.totalEquity, Colors.green),
            const SizedBox(height: 24),
          ],

          // Total Liabilities and Equity
          const Divider(thickness: 2),
          const SizedBox(height: 16),
          _buildTotalLiabilitiesAndEquityRow(
              context, report.totalLiabilitiesAndEquity),

          const SizedBox(height: 16),

          // Balance Verification
          _buildBalanceVerificationRow(context, report.totalAssets,
              report.totalLiabilitiesAndEquity, report.isBalanced),
        ],
      );
    });
  }

  /// Build section header
  Widget _buildSectionHeader(BuildContext context, String title, Color color) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
      ),
    );
  }

  /// Build group section (e.g., Current Assets, Non-Current Assets)
  Widget _buildGroupSection(BuildContext context, dynamic group) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Group Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.grey.withValues(alpha: 0.05),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  group.groupName,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                Text(
                  _formatCurrency(group.groupTotal),
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: _getGroupColor(group.groupType),
                      ),
                ),
              ],
            ),
          ),

          // Account Items
          ...group.accounts
              .map<Widget>((account) => _buildAccountRow(context, account)),
        ],
      ),
    );
  }

  /// Build individual account row
  Widget _buildAccountRow(BuildContext context, dynamic account) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey, width: 0.2),
        ),
      ),
      child: Row(
        children: [
          // Account Number
          SizedBox(
            width: 80,
            child: Text(
              account.accountNumber,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontFamily: 'monospace',
                    color: Colors.grey[600],
                  ),
            ),
          ),
          const SizedBox(width: 16),
          // Account Name
          Expanded(
            child: Text(
              account.accountName,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
          // Amount
          SizedBox(
            width: 120,
            child: Text(
              _formatCurrency(account.amount),
              textAlign: TextAlign.right,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontFamily: 'monospace',
                  ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build total row for sections
  Widget _buildTotalRow(
      BuildContext context, String label, double amount, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          Text(
            _formatCurrency(amount),
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: color,
                  fontFamily: 'monospace',
                ),
          ),
        ],
      ),
    );
  }

  /// Build total liabilities and equity row
  Widget _buildTotalLiabilitiesAndEquityRow(
      BuildContext context, double amount) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'TOTAL LIABILITIES AND EQUITY',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          Text(
            _formatCurrency(amount),
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  fontFamily: 'monospace',
                ),
          ),
        ],
      ),
    );
  }

  /// Build balance verification row
  Widget _buildBalanceVerificationRow(BuildContext context, double assets,
      double liabilitiesAndEquity, bool isBalanced) {
    final difference = assets - liabilitiesAndEquity;
    final color = isBalanced ? Colors.green : Colors.red;

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color, width: 2),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(
                    isBalanced ? Icons.check_circle : Icons.error,
                    color: color,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'BALANCE VERIFICATION',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: color,
                        ),
                  ),
                ],
              ),
              Text(
                isBalanced ? 'BALANCED' : 'UNBALANCED',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
              ),
            ],
          ),
          if (!isBalanced) ...[
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Difference:',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: color,
                      ),
                ),
                Text(
                  _formatCurrency(difference.abs()),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: color,
                        fontFamily: 'monospace',
                      ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// Get color for group type
  Color _getGroupColor(String groupType) {
    switch (groupType) {
      case 'current_assets':
      case 'non_current_assets':
        return Colors.blue;
      case 'current_liabilities':
      case 'non_current_liabilities':
        return Colors.orange;
      case 'equity':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  /// Format currency amount
  String _formatCurrency(double amount) {
    final isNegative = amount < 0;
    final absAmount = amount.abs();

    String formatted;
    if (absAmount >= 1000000) {
      formatted = 'PKR ${(absAmount / 1000000).toStringAsFixed(2)}M';
    } else if (absAmount >= 1000) {
      formatted = 'PKR ${(absAmount / 1000).toStringAsFixed(1)}K';
    } else {
      formatted = 'PKR ${absAmount.toStringAsFixed(2)}';
    }

    return isNegative ? '($formatted)' : formatted;
  }
}
