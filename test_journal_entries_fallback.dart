import 'dart:developer';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// Quick test to verify Journal Entries fallback logic works
void main() async {
  try {
    // Initialize Firebase
    await Firebase.initializeApp();
    log('🔥 Firebase initialized successfully');
    
    // Check authentication
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) {
      log('❌ No authenticated user found');
      return;
    }
    
    log('👤 Testing with user: ${currentUser.email}');
    log('🆔 UID: ${currentUser.uid}');
    
    final firestore = FirebaseFirestore.instance;
    final uid = currentUser.uid;
    
    // Test the problematic query that was failing
    await testJournalEntriesQuery(firestore, uid);
    
    log('✅ Journal Entries fallback test completed');
    
  } catch (e) {
    log('❌ Error in journal entries fallback test: $e');
  }
}

/// Test the specific query that was causing the Firebase index error
Future<void> testJournalEntriesQuery(FirebaseFirestore firestore, String uid) async {
  log('\n📝 Testing Journal Entries Query...');
  
  try {
    // This is the exact query that was failing before
    log('🔍 Testing compound query (uid + orderBy entryDate)...');
    final snapshot = await firestore
        .collection('journal_entries')
        .where('uid', isEqualTo: uid)
        .orderBy('entryDate', descending: true)
        .limit(5)
        .get();
    
    log('✅ Compound query successful: ${snapshot.docs.length} entries found');
    
    // Test processing the documents
    for (final doc in snapshot.docs) {
      final data = doc.data();
      log('📄 Entry: ${data['entryNumber']} - ${data['description']}');
      
      // Test journal entry lines query
      try {
        final linesSnapshot = await firestore
            .collection('journal_entry_lines')
            .where('journalEntryId', isEqualTo: doc.id)
            .orderBy('createdAt')
            .get();
        
        log('   📋 Lines: ${linesSnapshot.docs.length}');
      } catch (lineError) {
        log('   ⚠️ Lines query failed (using fallback): $lineError');
        
        // Test fallback lines query
        final fallbackLinesSnapshot = await firestore
            .collection('journal_entry_lines')
            .where('journalEntryId', isEqualTo: doc.id)
            .get();
        
        log('   ✅ Fallback lines query: ${fallbackLinesSnapshot.docs.length}');
      }
    }
    
  } catch (e) {
    log('❌ Compound query failed: $e');
    
    // Test fallback query
    if (e.toString().contains('index') || e.toString().contains('FAILED_PRECONDITION')) {
      log('🔄 Testing fallback query...');
      
      try {
        // Simple query with just uid filter
        final fallbackSnapshot = await firestore
            .collection('journal_entries')
            .where('uid', isEqualTo: uid)
            .get();
        
        log('✅ Fallback query successful: ${fallbackSnapshot.docs.length} entries found');
        
        // Test sorting in memory
        var entries = fallbackSnapshot.docs.map((doc) {
          final data = doc.data();
          return {
            'id': doc.id,
            'entryDate': (data['entryDate'] as Timestamp).toDate(),
            'entryNumber': data['entryNumber'],
            'description': data['description'],
          };
        }).toList();
        
        // Sort by entryDate descending
        entries.sort((a, b) => (b['entryDate'] as DateTime).compareTo(a['entryDate'] as DateTime));
        
        log('📊 Sorted entries:');
        for (int i = 0; i < entries.length && i < 5; i++) {
          final entry = entries[i];
          log('   ${i + 1}. ${entry['entryNumber']} - ${entry['description']} (${entry['entryDate']})');
        }
        
      } catch (fallbackError) {
        log('❌ Fallback query also failed: $fallbackError');
      }
    }
  }
}
